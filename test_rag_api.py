#!/usr/bin/env python3
"""
测试RAG API的脚本
"""

import requests
import json

def test_rag_api():
    base_url = "http://localhost:8000/api/v1"
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post(
        f"{base_url}/login/access-token",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"响应: {login_response.text}")
        return
    
    login_result = login_response.json()
    token = login_result["access_token"]
    print(f"✅ 登录成功，用户: {login_result['user']['username']}")
    
    # 2. 测试RAG查询
    print("\n🔍 测试RAG查询...")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    rag_data = {
        "query": "什么是人工智能安全？",
        "top_k": 3,
        "temperature": 0.5,
        "max_tokens": 200
    }
    
    rag_response = requests.post(
        f"{base_url}/rag/query",
        json=rag_data,
        headers=headers
    )
    
    print(f"📊 RAG API响应状态: {rag_response.status_code}")
    print(f"📄 RAG API响应内容:")
    
    try:
        rag_result = rag_response.json()
        print(json.dumps(rag_result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"❌ 解析响应失败: {e}")
        print(f"原始响应: {rag_response.text}")
    
    # 3. 测试检索功能
    print("\n🔍 测试知识库检索...")
    retrieval_response = requests.post(
        f"{base_url}/rag/test-retrieval?query=人工智能&top_k=3",
        headers=headers
    )
    
    print(f"📊 检索API响应状态: {retrieval_response.status_code}")
    print(f"📄 检索API响应内容:")
    
    try:
        retrieval_result = retrieval_response.json()
        print(json.dumps(retrieval_result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"❌ 解析响应失败: {e}")
        print(f"原始响应: {retrieval_response.text}")

if __name__ == "__main__":
    test_rag_api()
