#!/usr/bin/env python3
"""
测试文档上传和处理功能
"""

import requests
import json
import time
import os

def test_document_upload():
    """测试文档上传和处理"""
    print("🔍 测试文档上传和处理功能")
    print("=" * 60)
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_response = requests.post(
        "http://localhost:8000/api/v1/login/access-token",
        data={"username": "admin", "password": "admin123"}
    )
    
    if login_response.status_code != 200:
        print(f"   ❌ 登录失败: {login_response.status_code}")
        print(f"   响应: {login_response.text}")
        return
    
    token = login_response.json()["access_token"]
    print("   ✅ 登录成功")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 2. 创建测试文档
    print("\n2. 创建测试文档...")
    timestamp = int(time.time())
    test_content = f"""
    AI安全系统测试文档 - {timestamp}
    
    这是一个用于测试文档上传和向量化处理的测试文档。
    创建时间: {timestamp}
    
    主要内容包括：
    1. 人工智能安全检测
    2. 内容审核机制
    3. 数据脱敏处理
    4. 模型路径配置
    
    测试路径：/test/ai-security/models/
    配置文件：config.json
    测试ID: {timestamp}
    """
    
    test_filename = f"test_document_{timestamp}.txt"
    test_file_path = f"/tmp/{test_filename}"
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"   ✅ 测试文档已创建: {test_file_path}")
    
    # 3. 上传文档
    print("\n3. 上传文档...")
    with open(test_file_path, 'rb') as f:
        files = {
            'file': (test_filename, f, 'text/plain')
        }
        data = {
            'title': f'AI安全系统测试文档_{timestamp}',
            'category_id': 1,
            'grade_id': 1,
            'description': '用于测试文档上传和处理功能的测试文档'
        }
        
        upload_response = requests.post(
            "http://localhost:8000/api/v1/knowledge-base/documents",
            headers=headers,
            files=files,
            data=data
        )
    
    print(f"   上传响应状态码: {upload_response.status_code}")
    
    if upload_response.status_code == 200:
        upload_data = upload_response.json()
        print("   ✅ 文档上传成功")
        print(f"   响应数据: {json.dumps(upload_data, ensure_ascii=False, indent=2)}")
        document_id = upload_data.get('document_id')  # 修正字段名
        print(f"   文档ID: {document_id}")
        
        # 4. 等待处理完成
        print("\n4. 等待文档处理...")
        max_wait = 30  # 最多等待30秒
        wait_time = 0
        
        while wait_time < max_wait:
            # 查询文档状态
            doc_response = requests.get(
                f"http://localhost:8000/api/v1/knowledge-base/documents/{document_id}",
                headers=headers
            )
            
            if doc_response.status_code == 200:
                doc_data = doc_response.json()
                status = doc_data.get('processing_status')
                print(f"   当前状态: {status}")
                
                if status == 'completed':
                    print("   ✅ 文档处理完成")
                    break
                elif status == 'failed':
                    print("   ❌ 文档处理失败")
                    error_msg = doc_data.get('error_message', '未知错误')
                    print(f"   错误信息: {error_msg}")
                    break
                else:
                    print(f"   ⏳ 处理中... ({wait_time}s)")
                    time.sleep(2)
                    wait_time += 2
            else:
                print(f"   ❌ 查询文档状态失败: {doc_response.status_code}")
                break
        
        if wait_time >= max_wait:
            print("   ⚠️  等待超时")
        
        # 5. 测试搜索功能
        print("\n5. 测试文档搜索...")
        search_response = requests.post(
            "http://localhost:8000/api/v1/rag/query",
            headers=headers,
            json={
                "query": "AI安全系统测试",
                "model_id": 13,
                "top_k": 5,
                "include_sources": True
            }
        )
        
        print(f"   搜索响应状态码: {search_response.status_code}")
        
        if search_response.status_code == 200:
            search_data = search_response.json()
            print("   ✅ 搜索成功")
            print(f"   检索到文档数: {search_data.get('documents_retrieved', 0)}")
            
            sources = search_data.get('sources', [])
            test_doc_sources = [s for s in sources if s.get('document_id') == document_id]
            print(f"   测试文档相关结果: {len(test_doc_sources)}")
            
            if test_doc_sources:
                print("   ✅ 找到测试文档相关内容")
                for i, source in enumerate(test_doc_sources[:2], 1):
                    print(f"     {i}. 相似度: {source.get('similarity', 0):.4f}")
                    content = source.get('content', '')[:100] + "..." if len(source.get('content', '')) > 100 else source.get('content', '')
                    print(f"        内容: {content}")
            else:
                print("   ⚠️  未找到测试文档相关内容")
        else:
            print(f"   ❌ 搜索失败: {search_response.status_code}")
            print(f"   错误信息: {search_response.text}")
        
        # 6. 清理测试文档
        print("\n6. 清理测试文档...")
        delete_response = requests.delete(
            f"http://localhost:8000/api/v1/knowledge-base/documents/{document_id}",
            headers=headers
        )
        
        if delete_response.status_code == 200:
            print("   ✅ 测试文档已删除")
        else:
            print(f"   ⚠️  删除失败: {delete_response.status_code}")
    
    else:
        print(f"   ❌ 文档上传失败: {upload_response.status_code}")
        print(f"   错误信息: {upload_response.text}")
    
    # 清理本地文件
    try:
        os.remove(test_file_path)
        print(f"   ✅ 本地测试文件已清理")
    except:
        pass

if __name__ == "__main__":
    test_document_upload()
