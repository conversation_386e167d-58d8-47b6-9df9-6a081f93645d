# AI 安全系统 - 启动脚本说明

## 📁 脚本文件列表

| 文件名 | 功能 | 说明 |
|--------|------|------|
| **start_all.bat** | 一键启动所有服务 | 同时启动后端和前端，推荐使用 |
| **stop_all.bat** | 一键关闭所有服务 | 关闭所有相关进程 |
| **start_backend_only.bat** | 只启动后端 | 用于后端开发调试 |
| **start_frontend_only.bat** | 只启动前端 | 用于前端开发调试 |
| **check_status.bat** | 检查服务状态 | 查看服务是否正在运行 |

## 🚀 快速开始

### 1. 首次使用
确保已经完成以下准备工作：
- ✅ Python 3.8+ 已安装
- ✅ Node.js 16+ 已安装
- ✅ 已执行 `pip install -r requirements.txt`
- ✅ 已执行 `cd ai-security-frontend && npm install`
- ✅ 已执行 `python init_db.py` 初始化数据库

### 2. 启动系统
双击运行 `start_all.bat`，将自动：
- 启动后端服务（端口 8000）
- 启动前端服务（端口 5173）

### 3. 访问系统
- 前端界面：http://localhost:5173
- 后端API文档：http://localhost:8000/docs

### 4. 测试账号
- 管理员：`admin` / `admin123`
- 普通用户：`user1` / `user123`

### 5. 关闭系统
双击运行 `stop_all.bat`

## 🔧 故障排查

### 检查服务状态
运行 `check_status.bat` 查看服务运行状态

### 常见问题

1. **端口被占用**
   - 错误信息：`[Errno 10048] error while attempting to bind on address`
   - 解决方法：运行 `stop_all.bat` 关闭所有服务，然后重新启动

2. **依赖未安装**
   - 错误信息：`ModuleNotFoundError` 或 `Cannot find module`
   - 解决方法：确保已安装所有依赖

3. **数据库未初始化**
   - 错误信息：`no such table` 或数据库相关错误
   - 解决方法：运行 `python init_db.py`

## 📝 开发说明

- 后端代码修改后会自动重载（热重载）
- 前端代码修改后会自动刷新（热更新）
- 日志文件保存在项目根目录

## 🛡️ 安全提示

- 这是开发环境配置，不要在生产环境使用
- 测试账号仅供开发使用
- 生产环境需要配置适当的安全措施