#!/usr/bin/env python3
"""
简化的后端启动脚本 - 避免复杂依赖
"""
import uvicorn
import sys
import os

# 设置环境变量以避免下载模型
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# 确保项目目录在 Python 路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动简化版 AI 安全系统后端服务...")
    print("=" * 60)
    print("📌 服务地址: http://localhost:8000")
    print("📚 API 文档: http://localhost:8000/docs")
    print("🔄 已启用 CORS，允许前端访问")
    print("=" * 60)
    
    try:
        # 使用字符串方式导入 app
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,  # 禁用reload避免问题
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查依赖是否正确安装")