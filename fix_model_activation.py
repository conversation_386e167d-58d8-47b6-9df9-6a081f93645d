#!/usr/bin/env python3
"""
修复模型激活问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

def fix_model_activation():
    """修复模型激活问题"""
    print("🔧 修复模型激活问题...")
    
    db = SyncSessionLocal()
    
    try:
        # 1. 停用所有模型
        print("1️⃣ 停用所有模型...")
        db.query(LLMModel).update({"is_active": False})
        
        # 2. 只激活本地qwen:1.8b模型
        print("2️⃣ 激活本地qwen:1.8b模型...")
        local_model = db.query(LLMModel).filter(
            LLMModel.name == "本地 qwen:1.8b (增强版)"
        ).first()
        
        if local_model:
            local_model.is_active = True
            # 优化配置
            local_model.config_params = {
                'model': 'qwen:1.8b',
                'api_format': 'ollama',
                'temperature': 0.7,
                'top_p': 0.9,
                'max_tokens': 800  # 增加token限制，提供更完整的回答
            }
            print(f"✅ 激活模型: {local_model.name}")
            print(f"   配置: {local_model.config_params}")
        else:
            print("❌ 没有找到本地qwen:1.8b模型")
        
        # 3. 提交更改
        db.commit()
        
        # 4. 验证结果
        print("\n3️⃣ 验证激活状态...")
        active_models = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).all()
        
        print(f"激活的模型数: {len(active_models)}")
        for model in active_models:
            print(f"  ✅ {model.name} (ID: {model.id})")
        
        if len(active_models) == 1:
            print("✅ 模型激活状态已修复")
        else:
            print("❌ 模型激活状态仍有问题")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        db.rollback()
        
    finally:
        db.close()

if __name__ == "__main__":
    fix_model_activation()
