
# AI安全防护系统需求说明

## 背景

AI安全防护系统用于对用户与大模型直接的对话进行安全防护，起到数据脱敏、权限控制、内容过滤等功能。

---

## 功能说明

### 会话管理

- 用户通过会话管理功能与大模型进行对话；
- 支持发送文本和图片消息；
- 支持永久保存历史会话记录；
- 支持新建会话与删除历史会话；
- 会话记录应保存在 **MySQL** 中；
- 消息在发送前须通过 **安全策略审核**（关键词与恶意意图识别）：
  - 未通过审查将提示用户并禁止发送；
- 支持不同用户 **只能看到自己的对话记录**；
- 支持选择已对接的不同大模型进行对话。

---

### 安全策略管理

用于对用户与大模型交互会话的内容审查，包括：

- **关键词过滤**
- **恶意意图识别**
- **数据脱敏**

数据应保存在 **MySQL** 中。

#### 关键词管理

- 用户可自定义关键词并进行分组；
- 输入内容匹配关键词时提示“禁止发送敏感内容”，并拦截消息；
- 可与用户权限管理模块联动，不同角色使用不同关键词组。

#### 恶意意图识别

- 支持 **正则表达式** 的分类管理（录入、修改、删除）；
  - 如：权限提升类、隐私套取类、数据投毒类等；
- 判断用户是否存在恶意提问（如套取密码、身份证号）；
- 支持两种方式识别：
  - 正则表达式匹配
  - 轻量级模型语义识别（预留）；
- 判断为恶意意图时提示“系统识别存在恶意意图，拦截消息”；
- 可与权限管理联动，不同角色使用不同策略。
- 支持识别上下文类的恶意意图；

---

### 输出数据脱敏

- 自动检测大模型输出内容中是否存在敏感信息（如身份证号、姓名、地址、电话）；
- 若存在敏感信息则执行脱敏；
- 支持自定义敏感信息分类；
- 支持设定 **脱敏级别**：
  - 高级：全部星号脱敏；
  - 中级：部分脱敏（如身份证前3后3保留）；
  - 初级：小部分脱敏；
- 可自定义星号位数；
- 支持与权限管理联动，不同用户角色使用不同脱敏策略。

---

### SDK支持

- 安全策略模块支持作为 SDK 单独部署；
- 应支持 Windows 和主流 Linux 系统。

---

### 数据分级分类管理

- 对大模型后端数据库或文档进行 **分类** 和 **分级**：
  - 分类下设分级；
  - 数据分为：
    - 高级敏感
    - 中级敏感
    - 初级敏感
    - 完全开放
- 支持与权限管理联动，控制不同用户对不同分类/级别数据的访问；
- 需考虑法规政策在系统内的落地实现。

---

### 接口管理

- 支持对接不同大模型；
- 可配置大模型参数（地址、秘钥等）；
- 支持配置多个模型接口；
- 会话管理可选择对接目标模型；
- 支持 SDK 单独配置。

---

### 用户管理

- 支持创建、修改、删除用户；
- 支持设置用户角色与级别；
- 支持自定义角色与级别：
  - 例如角色：医生；
  - 级别：主任医师（1级）、医师（2级）、实习（3级）等。

---

### 权限管理

- **功能权限**：限制不同用户访问不同模块；
- **数据权限**：限制访问不同分类分级的数据；
- **脱敏权限**：控制不同用户所生效的脱敏策略；
- **安全审查权限**：控制关键词与恶意意图识别策略的应用。

---

### 访问控制

- 对用户登录AI防护系统进行验证。

---

### 日志管理

- 记录以下行为日志：
  - 登录行为；
  - 操作行为；
  - 数据访问记录等。

### RAG知识库管理
- 支持RAG知识库的能力（通过接口管理模块对接大模型）
- 支持对知识库进行管理；
- 支持对知识库进行分类；
- 支持对知识库进行分级；
- 支持对知识库进行权限控制，如分角色、分用户级别访问不同知识库；
- 支持对知识库进行内容审核；
- 支持对知识库进行内容脱敏；
- 支持对知识库进行内容搜索；
- 支持对知识库进行内容更新；
- 支持word、pdf、txt、excel、PPT等格式的文档上传到支持库；