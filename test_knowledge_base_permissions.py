#!/usr/bin/env python3
"""
测试知识库权限配置的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_knowledge_base_permissions():
    """测试知识库权限配置"""
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 获取可用模块列表
    print("\n📋 获取可用模块列表...")
    response = requests.get(f"{BASE_URL}/admin/permissions/available-modules", headers=headers)
    if response.status_code == 200:
        modules = response.json()["modules"]
        print("✅ 可用模块列表:")
        for module in modules:
            print(f"  - {module['name']}: {module['description']}")
        
        # 检查是否包含知识库模块
        kb_module = next((m for m in modules if m["name"] == "KNOWLEDGE_BASE"), None)
        if kb_module:
            print(f"\n✅ 找到知识库模块: {kb_module['description']}")
        else:
            print("\n❌ 未找到知识库模块")
    else:
        print(f"❌ 获取模块列表失败: {response.status_code}")
        print(response.text)
        return

    # 3. 获取当前用户信息
    print("\n👤 获取当前用户信息...")
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if response.status_code == 200:
        user = response.json()
        print(f"✅ 当前用户: {user['username']} (角色ID: {user['role_id']})")
        
        # 4. 获取角色权限配置
        print(f"\n🔑 获取角色 {user['role_id']} 的权限配置...")
        response = requests.get(f"{BASE_URL}/admin/permissions/roles/{user['role_id']}/permissions", headers=headers)
        if response.status_code == 200:
            permissions = response.json()
            print("✅ 角色权限配置:")
            print(f"  角色名称: {permissions['role_name']}")
            print("  模块权限:")
            for perm in permissions["module_permissions"]:
                status = "✓" if perm["can_access"] else "✗"
                print(f"    {status} {perm['module_name']}")
            
            # 检查知识库权限
            kb_perm = next((p for p in permissions["module_permissions"] 
                           if p["module_name"] == "KNOWLEDGE_BASE"), None)
            if kb_perm:
                if kb_perm["can_access"]:
                    print(f"\n✅ 角色 {permissions['role_name']} 有知识库管理权限")
                else:
                    print(f"\n❌ 角色 {permissions['role_name']} 没有知识库管理权限")
            else:
                print(f"\n⚠️ 角色 {permissions['role_name']} 未配置知识库权限")
        else:
            print(f"❌ 获取角色权限失败: {response.status_code}")
            print(response.text)
    else:
        print(f"❌ 获取用户信息失败: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_knowledge_base_permissions()
