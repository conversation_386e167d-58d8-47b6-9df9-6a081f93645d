#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "========================================"
echo "   AI Security System - Status Check"
echo "========================================"
echo

# 检查后端服务
echo -e "${BLUE}Backend Service Status:${NC}"
if lsof -ti:8000 >/dev/null 2>&1; then
    echo -e "${GREEN}  ✓ Backend service is running on port 8000${NC}"
    BACKEND_PID=$(lsof -ti:8000)
    echo -e "    Process ID: $BACKEND_PID"
else
    echo -e "${RED}  ✗ Backend service is not running${NC}"
fi

# 检查前端服务
echo -e "${BLUE}Frontend Service Status:${NC}"
FRONTEND_FOUND=false
for port in 5173 5174 5175; do
    if lsof -ti:$port >/dev/null 2>&1; then
        echo -e "${GREEN}  ✓ Frontend service is running on port $port${NC}"
        FRONTEND_PID=$(lsof -ti:$port)
        echo -e "    Process ID: $FRONTEND_PID"
        FRONTEND_FOUND=true
        break
    fi
done

if [ "$FRONTEND_FOUND" = false ]; then
    echo -e "${RED}  ✗ Frontend service is not running${NC}"
fi

echo
echo "========================================"
echo -e "${BLUE}Service URLs:${NC}"
echo "  Backend: http://localhost:8000"
echo "  API Docs: http://localhost:8000/docs"
if [ "$FRONTEND_FOUND" = true ]; then
    echo "  Frontend: http://localhost:$port"
else
    echo "  Frontend: Not available"
fi

echo
echo "========================================"
echo -e "${BLUE}Available Commands:${NC}"
echo "  ./start_all.sh  - Start all services"
echo "  ./stop_all.sh   - Stop all services"
echo "  tail -f backend.log   - View backend logs"
echo "  tail -f frontend.log  - View frontend logs"
echo

echo
echo -e "${BLUE}[Python Process]${NC}"
PYTHON_PIDS=$(pgrep -f "python.*start_backend.py\|uvicorn.*app.main:app")
if [ ! -z "$PYTHON_PIDS" ]; then
    echo -e "  ${GREEN}[OK] Python process is running (PID: $PYTHON_PIDS)${NC}"
else
    echo -e "  ${RED}[X] No Python process found${NC}"
fi

echo
echo -e "${BLUE}[Node.js Process]${NC}"
NODE_PIDS=$(pgrep -f "node.*vite\|npm.*run.*dev")
if [ ! -z "$NODE_PIDS" ]; then
    echo -e "  ${GREEN}[OK] Node.js process is running (PID: $NODE_PIDS)${NC}"
else
    echo -e "  ${RED}[X] No Node.js process found${NC}"
fi

echo
echo -e "${BLUE}[Port Usage]${NC}"
echo "  Port 8000 (Backend):"
PORT_8000=$(lsof -ti:8000 2>/dev/null)
if [ ! -z "$PORT_8000" ]; then
    echo -e "    ${GREEN}In use by PID: $PORT_8000${NC}"
else
    echo -e "    ${YELLOW}Not in use${NC}"
fi

echo "  Port 5173 (Frontend):"
PORT_5173=$(lsof -ti:5173 2>/dev/null)
if [ ! -z "$PORT_5173" ]; then
    echo -e "    ${GREEN}In use by PID: $PORT_5173${NC}"
else
    echo -e "    ${YELLOW}Not in use${NC}"
fi

echo
echo "========================================"
echo 