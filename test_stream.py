#!/usr/bin/env python3
"""
测试流式显示功能
"""

import requests
import json
import time

def test_streaming():
    """测试流式响应"""
    
    # 1. 登录获取token
    print("🔑 登录获取token...")
    login_response = requests.post(
        "http://localhost:8000/api/v1/login/access-token",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        data="username=admin&password=admin123"
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return
    
    token = login_response.json()["access_token"]
    print("✅ 登录成功")
    
    # 2. 创建会话
    print("📝 创建测试会话...")
    session_response = requests.post(
        "http://localhost:8000/api/v1/sessions/",
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        },
        json={"title": "流式测试", "llm_model_id": 9}
    )
    
    if session_response.status_code not in [200, 201]:
        print(f"❌ 会话创建失败: {session_response.status_code}")
        print(f"响应内容: {session_response.text}")
        return
    
    session_id = session_response.json()["id"]
    print(f"✅ 会话创建成功，ID: {session_id}")
    
    # 3. 测试流式响应
    print("🌊 测试流式响应...")
    print("=" * 60)
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    data = {
        "session_id": session_id,
        "user_message": "请简单介绍一下你自己",
        "llm_model_id": 9,
        "stream": True
    }
    
    start_time = time.time()
    chunk_count = 0
    total_content = ""
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/messages/ai-generate-stream",
            headers=headers,
            json=data,
            stream=True
        )
        
        print(f"📡 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("🔄 接收流式数据:")
            print("-" * 50)
            
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith('data: '):
                        try:
                            data_content = line_text[6:]
                            chunk_data = json.loads(data_content)
                            
                            if chunk_data.get('type') == 'user_message':
                                print(f"👤 用户消息已保存")
                                
                            elif chunk_data.get('type') == 'ai_chunk':
                                chunk_count += 1
                                chunk_text = chunk_data.get('chunk', '')
                                total_content += chunk_text
                                elapsed = time.time() - start_time
                                
                                # 显示chunk信息
                                print(f"[{elapsed:.1f}s] Chunk {chunk_count}: \"{chunk_text}\"")
                                
                                # 显示累积内容的前100字符
                                if len(total_content) <= 100:
                                    print(f"    累积内容: {total_content}")
                                else:
                                    print(f"    累积内容: {total_content[:100]}...")
                                print()
                                
                            elif chunk_data.get('type') == 'completed':
                                elapsed = time.time() - start_time
                                print("-" * 50)
                                print(f"✅ 流式响应完成!")
                                print(f"⏰ 总时间: {elapsed:.1f}秒")
                                print(f"📊 总chunks: {chunk_count}")
                                print(f"📝 总字符数: {len(total_content)}")
                                print(f"🔄 平均每秒chunks: {chunk_count/elapsed:.1f}")
                                
                                if chunk_count > 1:
                                    print("✅ 流式显示正常工作!")
                                else:
                                    print("⚠️ 流式显示可能有问题，只收到1个chunk")
                                break
                                
                            elif chunk_data.get('type') == 'error':
                                print(f"❌ 错误: {chunk_data.get('error')}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"⚠️ JSON解析错误: {e}")
                            continue
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_streaming() 