#!/usr/bin/env python3
"""
运行数据库迁移脚本
"""

import sys
import os
import subprocess

def run_migration():
    """运行Alembic数据库迁移"""
    try:
        # 确保在正确的目录中
        os.chdir('/Users/<USER>/Desktop/project/ai-security-system-gemini')
        
        # 运行迁移
        result = subprocess.run([
            sys.executable, '-c',
            'import alembic.command; import alembic.config; '
            'cfg = alembic.config.Config("alembic.ini"); '
            'alembic.command.upgrade(cfg, "head")'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据库迁移成功完成")
            print(result.stdout)
        else:
            print("❌ 数据库迁移失败")
            print("错误信息:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 运行迁移时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)