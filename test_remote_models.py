#!/usr/bin/env python3
"""
测试远程模型连接
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel
from app.services.llm_service import LLMService

async def test_remote_models():
    """测试远程模型连接"""
    print("🔧 测试远程模型连接...")
    
    db = SyncSessionLocal()
    
    try:
        # 查询远程模型
        remote_models = db.query(LLMModel).filter(
            LLMModel.is_active == True,
            LLMModel.api_url.like('%39.99.156.233%')
        ).all()
        
        print(f"找到 {len(remote_models)} 个激活的远程模型")
        
        for model in remote_models:
            print(f"\n🌐 测试模型: {model.name} (ID: {model.id})")
            print(f"   API URL: {model.api_url}")
            print(f"   配置: {model.config_params}")
            
            # 测试连接
            try:
                result = await LLMService.test_model_connection(model)
                
                if result["success"]:
                    print(f"   ✅ 连接成功")
                    print(f"   响应: {result['response'][:100]}...")
                else:
                    print(f"   ❌ 连接失败")
                    print(f"   错误: {result['error']}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {str(e)}")
        
        # 测试本地模型作为对比
        print(f"\n🏠 测试本地模型作为对比...")
        local_model = db.query(LLMModel).filter(
            LLMModel.is_active == True,
            LLMModel.api_url.like('%localhost%')
        ).first()
        
        if local_model:
            print(f"   测试模型: {local_model.name} (ID: {local_model.id})")
            try:
                result = await LLMService.test_model_connection(local_model)
                
                if result["success"]:
                    print(f"   ✅ 本地模型连接成功")
                    print(f"   响应: {result['response'][:100]}...")
                else:
                    print(f"   ❌ 本地模型连接失败")
                    print(f"   错误: {result['error']}")
                    
            except Exception as e:
                print(f"   ❌ 本地模型测试异常: {str(e)}")
        else:
            print(f"   没有找到激活的本地模型")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_remote_models())
