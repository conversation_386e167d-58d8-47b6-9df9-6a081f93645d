# start_backend_with_permissions.py
"""
启动后端服务脚本 - 包含权限控制系统
"""

import uvicorn
from main import app

if __name__ == "__main__":
    print("🚀 启动AI安全系统后端服务...")
    print("📋 功能列表:")
    print("  ✅ 基于角色的权限控制 (RBAC)")
    print("  ✅ 管理员可访问所有数据")
    print("  ✅ 普通用户只能访问自己的数据")
    print("  ✅ 完整的安全检查系统")
    print("  ✅ AI对话生成功能")
    print("")
    print("🌐 API文档地址: http://localhost:8000/docs")
    print("🔗 前端地址: http://localhost:5173")
    print("")
    
    # 启动服务
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 