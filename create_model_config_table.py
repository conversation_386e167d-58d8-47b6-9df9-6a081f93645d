#!/usr/bin/env python3
"""
手动创建知识库模型配置表
"""

import sys
import os
sys.path.append('.')

from sqlalchemy import create_engine, text
from app.core.config import settings

def create_knowledge_base_model_config_table():
    """创建知识库模型配置表"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    # 创建表的SQL
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_base_model_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        config_type VARCHAR(20) NOT NULL,
        model_id INTEGER NOT NULL,
        user_id INTEGER NULL,
        category_id INTEGER NULL,
        grade_id INTEGER NULL,
        config_name VARCHAR(100) NOT NULL,
        description TEXT NULL,
        model_params JSON NULL,
        is_active BOOLEAN NOT NULL DEFAULT 1,
        priority INTEGER NOT NULL DEFAULT 100,
        created_by INTEGER NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_id) REFERENCES llm_models (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
    );
    """
    
    # 创建索引的SQL
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS ix_knowledge_base_model_configs_id ON knowledge_base_model_configs (id);",
        "CREATE INDEX IF NOT EXISTS ix_knowledge_base_model_configs_config_type ON knowledge_base_model_configs (config_type);"
    ]
    
    try:
        with engine.connect() as conn:
            # 创建表
            print("创建知识库模型配置表...")
            conn.execute(text(create_table_sql))
            
            # 创建索引
            print("创建索引...")
            for index_sql in create_indexes_sql:
                conn.execute(text(index_sql))
            
            # 提交事务
            conn.commit()
            
            # 创建默认全局配置
            print("创建默认全局配置...")
            default_config_sql = """
            INSERT OR IGNORE INTO knowledge_base_model_configs 
            (config_type, model_id, config_name, description, is_active, priority, created_by, model_params)
            SELECT 
                'global' as config_type,
                (SELECT id FROM llm_models WHERE is_active = 1 LIMIT 1) as model_id,
                '默认知识库模型' as config_name,
                '知识库问答功能的默认模型配置' as description,
                1 as is_active,
                100 as priority,
                (SELECT id FROM users WHERE username = 'admin' LIMIT 1) as created_by,
                '{"temperature": 0.7, "max_tokens": 1000}' as model_params
            WHERE (SELECT COUNT(*) FROM llm_models WHERE is_active = 1) > 0
              AND (SELECT COUNT(*) FROM users WHERE username = 'admin') > 0
              AND (SELECT COUNT(*) FROM knowledge_base_model_configs WHERE config_type = 'global') = 0;
            """
            
            conn.execute(text(default_config_sql))
            conn.commit()
            
            print("知识库模型配置表创建成功！")
            
    except Exception as e:
        print(f"创建表失败: {str(e)}")
        raise e

if __name__ == "__main__":
    create_knowledge_base_model_config_table()