#!/usr/bin/env python3
"""
测试本地模型路径查询
"""

import sys
import os
import asyncio
import requests
import json
sys.path.append('.')

async def test_model_path_query():
    """测试本地模型路径查询"""
    
    print("🔍 测试本地模型路径查询")
    print("=" * 60)
    
    try:
        # 1. 登录获取token
        print("\n1. 登录获取token...")
        login_url = "http://localhost:8000/api/v1/login/access-token"
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = requests.post(login_url, data=login_data)
        if login_response.status_code != 200:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            return
        
        token_data = login_response.json()
        access_token = token_data.get('access_token')
        print(f"   ✅ 登录成功")
        
        # 2. 测试本地模型路径查询
        print("\n2. 测试本地模型路径查询...")
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        rag_url = "http://localhost:8000/api/v1/rag/query"
        
        query = "本地模型路径是什么？"
        print(f"   查询: {query}")
        
        rag_data = {
            "query": query,
            "top_k": 10,  # 增加到10个结果
            "temperature": 0.7,
            "max_tokens": 1000,
            "include_sources": True
        }
        
        try:
            rag_response = requests.post(rag_url, json=rag_data, headers=headers)
            
            print(f"   响应状态码: {rag_response.status_code}")
            
            if rag_response.status_code == 200:
                result = rag_response.json()
                
                print(f"   响应数据结构:")
                for key in result.keys():
                    print(f"     - {key}: {type(result[key])}")
                
                # 详细分析sources
                sources = result.get('sources', [])
                print(f"\n   📊 Sources详情 ({len(sources)}个):")
                
                for i, source in enumerate(sources, 1):
                    print(f"     Source {i}:")
                    print(f"       document_id: {source.get('document_id')}")
                    print(f"       document_name: {source.get('document_name')}")
                    print(f"       similarity: {source.get('similarity')}")
                    print(f"       chunk_id: {source.get('chunk_id')}")
                    content = source.get('content', '')
                    print(f"       content: {content[:150]}...")
                    print()
                
                # 检查是否有文档2（模型路径文档）
                doc2_sources = [s for s in sources if s.get('document_id') == 2]
                print(f"   文档2 sources数量: {len(doc2_sources)}")
                
                if doc2_sources:
                    print("   ✅ 找到文档2的sources:")
                    for i, source in enumerate(doc2_sources, 1):
                        similarity = source.get('similarity', 0)
                        print(f"     {i}. 相似度: {similarity}")
                        print(f"        内容: {source.get('content', '')[:200]}...")
                else:
                    print("   ❌ 未找到文档2的sources")
                
                # 分析答案
                answer = result.get('answer', '')
                context_used = result.get('context_used', False)
                error_msg = result.get('error')
                
                print(f"\n   💬 答案详情:")
                print(f"     context_used: {context_used}")
                print(f"     success: {result.get('success')}")
                print(f"     documents_retrieved: {result.get('documents_retrieved')}")
                print(f"     model_id: {result.get('model_id')}")
                
                if error_msg:
                    print(f"     error: {error_msg}")
                
                if answer is None:
                    print(f"     answer: None (LLM可能未返回结果)")
                elif answer == '':
                    print(f"     answer: 空字符串")
                elif "抱歉" in answer or "无法" in answer:
                    print(f"     answer: 标准拒绝回答")
                    print(f"     answer内容: {answer}")
                else:
                    print(f"     answer长度: {len(answer)}")
                    print(f"     answer内容: {answer[:300]}...")
                
            else:
                print(f"   ❌ RAG请求失败: {rag_response.status_code}")
                print(f"      错误信息: {rag_response.text}")
        
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_model_path_query())
