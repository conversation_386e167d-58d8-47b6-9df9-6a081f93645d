-- 为sessions表添加session_type字段的SQL迁移脚本

-- 添加session_type字段
ALTER TABLE sessions 
ADD COLUMN session_type VARCHAR(50) DEFAULT 'chat' 
COMMENT '会话类型：chat(普通对话)、rag(知识库问答)';

-- 更新现有会话的类型（可选，根据需要调整）
-- 如果有现有的RAG相关会话，可以根据标题或其他特征来识别并更新
UPDATE sessions 
SET session_type = 'rag' 
WHERE title LIKE '%知识库%' OR title LIKE '%RAG%' OR title LIKE '%问答%';

-- 创建索引以提高查询性能
CREATE INDEX idx_sessions_type_user ON sessions(session_type, user_id, is_deleted);

-- 验证迁移结果
SELECT 
    session_type,
    COUNT(*) as count,
    MIN(created_at) as earliest,
    MAX(created_at) as latest
FROM sessions 
WHERE is_deleted = FALSE
GROUP BY session_type;
