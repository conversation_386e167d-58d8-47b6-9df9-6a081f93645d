#!/bin/bash

echo "🚀 AI安全系统 - 本地中文模型安装脚本"
echo "====================================="
echo ""

# 检查是否已安装Ollama
if command -v ollama &> /dev/null; then
    echo "✅ Ollama已安装"
else
    echo "📦 正在安装Ollama..."
    if command -v brew &> /dev/null; then
        brew install ollama
    else
        echo "❌ 需要先安装Homebrew"
        echo "访问: https://brew.sh"
        exit 1
    fi
fi

# 检查Ollama服务
echo ""
echo "🔍 检查Ollama服务..."
if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "✅ Ollama服务正在运行"
else
    echo "🚀 启动Ollama服务..."
    echo "请在新终端窗口运行: ollama serve"
    echo "然后按Enter继续..."
    read
fi

# 下载中文模型
echo ""
echo "📥 下载中文模型 qwen:0.5b (约350MB)..."
ollama pull qwen:0.5b

# 配置系统使用本地模型
echo ""
echo "🔧 配置系统使用本地模型..."
python3 setup_chinese_model.py

echo ""
echo "✨ 安装完成！"
echo "现在RAG功能将使用本地中文模型，响应速度大幅提升！" 

echo "🚀 AI安全系统 - 本地中文模型安装脚本"
echo "====================================="
echo ""

# 检查是否已安装Ollama
if command -v ollama &> /dev/null; then
    echo "✅ Ollama已安装"
else
    echo "📦 正在安装Ollama..."
    if command -v brew &> /dev/null; then
        brew install ollama
    else
        echo "❌ 需要先安装Homebrew"
        echo "访问: https://brew.sh"
        exit 1
    fi
fi

# 检查Ollama服务
echo ""
echo "🔍 检查Ollama服务..."
if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "✅ Ollama服务正在运行"
else
    echo "🚀 启动Ollama服务..."
    echo "请在新终端窗口运行: ollama serve"
    echo "然后按Enter继续..."
    read
fi

# 下载中文模型
echo ""
echo "📥 下载中文模型 qwen:0.5b (约350MB)..."
ollama pull qwen:0.5b

# 配置系统使用本地模型
echo ""
echo "🔧 配置系统使用本地模型..."
python3 setup_chinese_model.py

echo ""
echo "✨ 安装完成！"
echo "现在RAG功能将使用本地中文模型，响应速度大幅提升！" 