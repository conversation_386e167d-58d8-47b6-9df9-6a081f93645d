<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>调试前端认证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI安全系统 - 前端认证调试</h1>
        
        <h2>1. 检查LocalStorage中的认证数据</h2>
        <button onclick="checkAuth()">检查认证数据</button>
        <pre id="authData"></pre>
        
        <h2>2. 模拟登录</h2>
        <div>
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="admin123">
            <button onclick="doLogin()">登录</button>
        </div>
        <pre id="loginResult"></pre>
        
        <h2>3. 测试API调用</h2>
        <button onclick="testModelAPI()">测试模型管理API</button>
        <pre id="apiResult"></pre>
        
        <h2>4. 修复建议</h2>
        <div id="suggestions"></div>
    </div>

    <script>
        // 检查认证数据
        function checkAuth() {
            const authData = localStorage.getItem('auth-storage');
            const display = document.getElementById('authData');
            
            if (!authData) {
                display.innerHTML = '<span class="error">❌ 未找到认证数据</span>';
                return;
            }
            
            try {
                const parsed = JSON.parse(authData);
                display.innerHTML = `<span class="success">✅ 找到认证数据：</span>\n${JSON.stringify(parsed, null, 2)}`;
                
                // 检查token
                const token = parsed?.state?.token;
                if (token) {
                    display.innerHTML += `\n\n<span class="success">Token: ${token.substring(0, 20)}...</span>`;
                } else {
                    display.innerHTML += '\n\n<span class="error">❌ 未找到Token</span>';
                }
            } catch (e) {
                display.innerHTML = `<span class="error">❌ 解析错误: ${e.message}</span>`;
            }
        }
        
        // 模拟登录
        async function doLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const display = document.getElementById('loginResult');
            
            try {
                display.innerHTML = '正在登录...';
                
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('http://localhost:8000/api/v1/login/access-token', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const data = await response.json();
                    display.innerHTML = `<span class="success">✅ 登录成功！</span>\n`;
                    display.innerHTML += `Token: ${data.access_token.substring(0, 20)}...\n`;
                    display.innerHTML += `User: ${JSON.stringify(data.user_info, null, 2)}`;
                    
                    // 模拟前端存储
                    const authStore = {
                        state: {
                            user: data.user_info,
                            token: data.access_token,
                            isAuthenticated: true
                        }
                    };
                    localStorage.setItem('auth-storage', JSON.stringify(authStore));
                    display.innerHTML += '\n\n<span class="success">✅ 已保存到LocalStorage</span>';
                } else {
                    const error = await response.text();
                    display.innerHTML = `<span class="error">❌ 登录失败: ${response.status}\n${error}</span>`;
                }
            } catch (e) {
                display.innerHTML = `<span class="error">❌ 网络错误: ${e.message}</span>`;
            }
        }
        
        // 测试API
        async function testModelAPI() {
            const display = document.getElementById('apiResult');
            const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}');
            const token = authData?.state?.token;
            
            if (!token) {
                display.innerHTML = '<span class="error">❌ 请先登录</span>';
                return;
            }
            
            try {
                display.innerHTML = '正在调用API...';
                
                const response = await fetch('http://localhost:8000/api/v1/admin/llm-models/', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    display.innerHTML = `<span class="success">✅ API调用成功！</span>\n`;
                    display.innerHTML += `找到 ${data.length} 个模型：\n${JSON.stringify(data, null, 2)}`;
                } else {
                    const error = await response.text();
                    display.innerHTML = `<span class="error">❌ API调用失败: ${response.status}\n${error}</span>`;
                }
            } catch (e) {
                display.innerHTML = `<span class="error">❌ 网络错误: ${e.message}</span>`;
            }
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkAuth();
            
            // 显示建议
            const suggestions = document.getElementById('suggestions');
            suggestions.innerHTML = `
                <ul>
                    <li>确保后端服务在 http://localhost:8000 运行</li>
                    <li>使用管理员账号 admin/admin123 登录</li>
                    <li>如果出现CORS错误，请检查后端CORS配置</li>
                    <li>登录成功后，认证数据会自动保存到LocalStorage</li>
                </ul>
            `;
        };
    </script>
</body>
</html> 