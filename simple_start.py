#!/usr/bin/env python3
"""
简化的后端启动脚本
跳过可能有问题的模块，专注于基本功能
"""

import os
import sys
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量，避免网络连接问题
os.environ["TRANSFORMERS_OFFLINE"] = "1"
os.environ["HF_HUB_OFFLINE"] = "1"

try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from dotenv import load_dotenv
    import uvicorn
    
    # 加载 .env 文件中的环境变量
    load_dotenv()
    
    # 创建简化的FastAPI应用
    app = FastAPI(
        title="AI Security System API",
        description="API for AI Security System",
        version="0.1.0"
    )
    
    # 配置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",
            "http://127.0.0.1:3000", 
            "http://localhost:5173",
            "http://127.0.0.1:5173",
            "http://localhost:5174",
            "http://127.0.0.1:5174",
            "*"
        ],
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],
        expose_headers=["*"],
        max_age=86400,
    )
    
    @app.get("/")
    async def read_root():
        return {"message": "AI Security System API is running!", "status": "ok"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "version": app.version}
    
    # 尝试导入完整的API路由（如果可能的话）
    try:
        from app.api.v1.api import api_router
        app.include_router(api_router, prefix="/api/v1")
        print("✅ 完整API路由加载成功")
    except Exception as e:
        print(f"⚠️  完整API路由加载失败，使用基础功能: {e}")
        
        # 添加基础的登录端点
        @app.post("/api/v1/auth/login")
        async def basic_login():
            return {"message": "基础登录功能，请检查完整API"}
    
    if __name__ == "__main__":
        print("🚀 启动简化版AI安全系统后端...")
        print("📍 访问地址: http://localhost:8000")
        print("📋 API文档: http://localhost:8000/docs")
        
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            log_level="info",
            reload=False  # 禁用自动重载避免问题
        )
        
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print("请检查依赖是否正确安装")
    sys.exit(1) 