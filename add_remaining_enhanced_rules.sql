-- 添加剩余的增强规则 (使用正确的枚举值)

-- 医疗健康隐私
INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES (
    '医疗健康隐私-增强版',
    '(?i)(请|帮我|告诉我|透露|说出|获取|查询|泄露|提供).{0,20}(病房|病床|病历|诊断|体检|化验|手术|用药|病情|健康状况|医疗记录)',
    '检测尝试获取医疗健康隐私信息',
    'PRIVACY_EXTRACTION', 'high', 1, 85, 2, 1000, 1, 0, 0,
    'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')
);

-- 危险物品制作
INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES (
    '危险物品制作-增强版',
    '(?i)(如何|怎么|方法|教我|告诉我|制作|制造|生产|合成|调配).{0,30}(炸药|炸弹|爆炸物|TNT|硝化甘油|黑火药|汽油弹|燃烧瓶|毒药|毒剂|化学武器)',
    '检测危险爆炸物或毒剂制作指导',
    'OTHER', 'critical', 1, 100, 2, 1000, 1, 0, 0,
    'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')
);

-- 权限绕过攻击
INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES (
    '权限绕过攻击-增强版',
    '(?i)(如何|怎么|方法|教我).{0,30}(绕过|跳过|避开|无视|忽略|突破).{0,20}(权限|验证|检查|限制|防护|安全措施)',
    '检测尝试绕过权限验证的攻击',
    'PRIVILEGE_ESCALATION', 'critical', 1, 90, 2, 1000, 1, 0, 0,
    'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')
);

-- 社会工程攻击
INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES (
    '身份冒充攻击-增强版',
    '(?i)(我是|我现在是|请当我是|假装我是|伪装成).{0,20}(管理员|老板|领导|客服|技术支持|警察|医生|银行|政府|官员)',
    '检测身份冒充类社会工程攻击',
    'SOCIAL_ENGINEERING', 'high', 1, 80, 2, 1000, 1, 0, 0,
    'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')
);

-- 角色扮演越狱
INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES (
    '角色扮演越狱-增强版',
    '(?i)(你现在是|请扮演|假装你是|当作你是|模拟|roleplay|role-play).{0,30}(DAN|邪恶|不受限制|无限制|自由|破解版|越狱版|无道德|无伦理)',
    '检测角色扮演类越狱攻击',
    'JAILBREAK_ROLE_PLAYING', 'critical', 1, 90, 2, 1000, 1, 0, 0,
    'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')
);

-- 指令注入越狱
INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES (
    '指令注入越狱-增强版',
    '(?i)(忽略|无视|跳过|绕过).{0,20}(之前|上面|以上|先前).{0,20}(指令|指示|提示|规则|限制|约束|系统提示)',
    '检测指令注入类越狱攻击',
    'JAILBREAK_INSTRUCTION_HIJACKING', 'critical', 1, 95, 2, 1000, 1, 0, 0,
    'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')
);