#!/usr/bin/env python3
"""
直接测试向量化器搜索功能
"""

import sys
import os
import asyncio
sys.path.append('.')

async def test_vectorizer_search():
    """直接测试向量化器搜索"""
    
    print("=== 直接测试向量化器搜索 ===\n")
    
    try:
        # 1. 检查是否使用生产级服务
        from app.services.knowledge_base_service import PRODUCTION_SERVICES, production_document_vectorizer
        print(f"1. 生产级服务状态: {PRODUCTION_SERVICES}")
        print(f"   向量化器类型: {type(production_document_vectorizer)}")
        
        # 2. 直接测试向量化器搜索
        print("\n2. 直接测试向量化器搜索...")
        
        query = "本地模型路径"
        print(f"   查询: {query}")
        
        # 调用搜索方法（生产级向量化器需要vector_db参数）
        from app.services.vector_db_simple import SimpleVectorDBService
        vector_db = SimpleVectorDBService()
        
        results = await production_document_vectorizer.search_similar_chunks(
            query=query,
            vector_db=vector_db,
            top_k=10,
            threshold=0.1  # 使用低阈值
        )
        
        print(f"   搜索结果数量: {len(results)}")
        
        # 显示结果
        for i, result in enumerate(results):
            # 尝试从多个地方获取document_id
            doc_id = result.get('metadata', {}).get('document_id')
            if doc_id is None:
                # 从chunk_id解析document_id
                chunk_id = result.get('chunk_id', result.get('id', ''))
                if chunk_id.startswith('doc_'):
                    try:
                        parts = chunk_id.split('_')
                        doc_id = int(parts[1])
                    except (IndexError, ValueError):
                        doc_id = 'unknown'
                else:
                    doc_id = 'unknown'
            
            similarity = result.get('similarity', 0)
            content = result.get('content', '')
            
            print(f"   结果{i+1}: 文档{doc_id}, 相似度: {similarity:.3f}")
            print(f"     chunk_id: {result.get('chunk_id', result.get('id', 'unknown'))}")
            print(f"     metadata: {result.get('metadata', {})}")
            print(f"     内容: {content[:100]}...")
            print()
        
        # 检查文档2的结果（使用修正后的逻辑）
        doc2_results = []
        for r in results:
            doc_id = r.get('metadata', {}).get('document_id')
            if doc_id is None:
                # 从chunk_id解析document_id
                chunk_id = r.get('chunk_id', r.get('id', ''))
                if chunk_id.startswith('doc_'):
                    try:
                        parts = chunk_id.split('_')
                        doc_id = int(parts[1])
                    except (IndexError, ValueError):
                        continue
            if doc_id == 2:
                doc2_results.append(r)
        print(f"   文档2的结果数量: {len(doc2_results)}")
        
        if doc2_results:
            print("   ✅ 找到文档2的结果！")
            for i, result in enumerate(doc2_results):
                print(f"     文档2结果{i+1}: 相似度 {result['similarity']:.3f}")
                print(f"       内容: {result['content'][:150]}...")
        else:
            print("   ❌ 没有找到文档2的结果")
        
        return len(doc2_results) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_vectorizer_search())
    if success:
        print("\n🎉 向量化器搜索测试成功！")
    else:
        print("\n⚠️ 向量化器搜索测试失败")