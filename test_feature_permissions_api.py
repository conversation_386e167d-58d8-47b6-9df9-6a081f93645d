#!/usr/bin/env python3
"""
测试功能权限API是否正常工作
"""

import requests
import json

# API配置
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api/v1"

def test_feature_permissions_api():
    """测试功能权限API"""
    print("=== 测试功能权限API ===")
    
    # 测试登录获取token
    print("\n1. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{API_URL}/login/access-token", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功，获取到token: {token[:20]}...")
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试获取角色列表
    print("\n2. 测试获取角色列表...")
    try:
        response = requests.get(f"{API_URL}/admin/roles/", headers=headers)
        if response.status_code == 200:
            roles = response.json()
            print(f"✅ 获取到 {len(roles)} 个角色")
            if roles:
                role_id = roles[0]["id"]
                role_name = roles[0]["name"]
                print(f"   使用角色: {role_name} (ID: {role_id})")
            else:
                print("❌ 没有找到角色")
                return
        else:
            print(f"❌ 获取角色失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ 获取角色请求失败: {e}")
        return
    
    # 测试获取角色的功能权限
    print(f"\n3. 测试获取角色 {role_name} 的知识库功能权限...")
    try:
        response = requests.get(
            f"{API_URL}/admin/feature-permissions/roles/{role_id}/feature-permissions/KNOWLEDGE_BASE",
            headers=headers
        )
        if response.status_code == 200:
            feature_permissions = response.json()
            print("✅ 获取功能权限成功:")
            print(json.dumps(feature_permissions, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 获取功能权限失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 获取功能权限请求失败: {e}")
    
    # 测试获取所有功能权限
    print(f"\n4. 测试获取角色 {role_name} 的所有功能权限...")
    try:
        response = requests.get(
            f"{API_URL}/admin/feature-permissions/roles/{role_id}/all-feature-permissions",
            headers=headers
        )
        if response.status_code == 200:
            all_permissions = response.json()
            print("✅ 获取所有功能权限成功:")
            print(json.dumps(all_permissions, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 获取所有功能权限失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 获取所有功能权限请求失败: {e}")
    
    # 测试更新功能权限
    print(f"\n5. 测试更新角色 {role_name} 的知识库功能权限...")
    update_data = {
        "feature_permissions": [
            {
                "feature_name": "CHAT",
                "operations": ["read", "create"]
            },
            {
                "feature_name": "DOCUMENTS", 
                "operations": ["read", "create", "update", "delete"]
            },
            {
                "feature_name": "MODELS",
                "operations": ["read"]
            },
            {
                "feature_name": "USER_PERMISSIONS",
                "operations": ["read", "create", "update"]
            }
        ]
    }
    
    try:
        response = requests.put(
            f"{API_URL}/admin/feature-permissions/roles/{role_id}/feature-permissions/KNOWLEDGE_BASE",
            headers=headers,
            json=update_data
        )
        if response.status_code == 200:
            print("✅ 更新功能权限成功")
            
            # 验证更新结果
            print("\n6. 验证更新结果...")
            response = requests.get(
                f"{API_URL}/admin/feature-permissions/roles/{role_id}/feature-permissions/KNOWLEDGE_BASE",
                headers=headers
            )
            if response.status_code == 200:
                updated_permissions = response.json()
                print("✅ 验证成功，更新后的权限:")
                print(json.dumps(updated_permissions, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 验证失败: {response.status_code} - {response.text}")
        else:
            print(f"❌ 更新功能权限失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 更新功能权限请求失败: {e}")

if __name__ == "__main__":
    test_feature_permissions_api()