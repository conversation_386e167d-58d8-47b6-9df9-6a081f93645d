#!/usr/bin/env python3
"""
调试RAG提示词构建
"""

import asyncio
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.llm_service import LLMService
from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

async def debug_rag_prompt():
    """调试RAG提示词构建"""
    print("🔍 调试RAG提示词构建...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 获取激活的模型
        llm_model = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).first()
        
        if not llm_model:
            print("❌ 没有找到激活的LLM模型")
            return
        
        print(f"✅ 找到激活模型: {llm_model.name}")
        
        # 模拟RAG服务中的prompt构建
        query = "什么是人工智能安全？"
        context = """人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。

AI安全是指确保人工智能系统安全、可靠和有益的研究领域。它涉及防止AI系统造成意外伤害，确保AI系统按照预期目标运行，以及防止恶意使用AI技术。

AI安全的主要关注点包括：
1. 对齐问题：确保AI系统的目标与人类价值观一致
2. 鲁棒性：AI系统在面对意外输入或环境变化时的稳定性
3. 可解释性：理解AI系统的决策过程
4. 隐私保护：保护用户数据不被滥用
5. 公平性：避免AI系统产生偏见或歧视"""
        
        # 构建增强提示
        enhanced_prompt = f"""基于以下知识库内容回答用户的问题。如果知识库中没有相关信息，请明确说明。

知识库内容：
{context}

用户问题：{query}

请基于上述知识库内容，准确、详细地回答用户的问题。如果知识库中的信息不足以完全回答问题，请说明哪些部分可以回答，哪些部分缺少信息。
"""
        
        print(f"\n📝 增强提示词长度: {len(enhanced_prompt)} 字符")
        print(f"📝 增强提示词内容:")
        print("-" * 50)
        print(enhanced_prompt)
        print("-" * 50)
        
        # 构建消息
        messages = [
            {"role": "system", "content": "你是一个基于知识库的问答助手。请根据提供的上下文准确回答问题。"},
            {"role": "user", "content": enhanced_prompt}
        ]
        
        print(f"\n📨 消息数组:")
        for i, msg in enumerate(messages):
            print(f"  {i+1}. {msg['role']}: {len(msg['content'])} 字符")
        
        # 创建模型副本
        import copy
        test_model = copy.deepcopy(llm_model)
        if test_model.config_params:
            test_model.config_params['max_tokens'] = 200
        else:
            test_model.config_params = {'max_tokens': 200}
        
        print(f"\n🔧 模型配置: {test_model.config_params}")
        
        # 测试简化版本
        print(f"\n🧪 测试简化版本...")
        simple_prompt = f"问题：{query}\n\n请简单回答这个问题。"
        simple_messages = [
            {"role": "user", "content": simple_prompt}
        ]
        
        print(f"📝 简化提示词长度: {len(simple_prompt)} 字符")
        
        try:
            print("🚀 调用LLM API（简化版本）...")
            response = await LLMService._call_llm_api(
                llm_model=test_model,
                messages=simple_messages,
                stream=False
            )
            
            print("✅ 简化版本调用成功")
            print(f"   响应: {response}")
            
        except Exception as e:
            print("❌ 简化版本调用失败")
            print(f"   错误: {str(e)}")
        
        # 测试完整版本
        print(f"\n🧪 测试完整版本...")
        try:
            print("🚀 调用LLM API（完整版本）...")
            response = await LLMService._call_llm_api(
                llm_model=test_model,
                messages=messages,
                stream=False
            )
            
            print("✅ 完整版本调用成功")
            print(f"   响应: {response}")
            
        except Exception as e:
            print("❌ 完整版本调用失败")
            print(f"   错误: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_rag_prompt())
