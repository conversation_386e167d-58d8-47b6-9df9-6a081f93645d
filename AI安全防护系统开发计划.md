# AI安全防护系统分步开发计划

## 阶段一：核心基础架构与用户管理

1.  **项目初始化与环境搭建**：
    *   选择后端技术栈（例如：Java Spring Boot, Python Django/Flask, Node.js Express 等）。
    *   创建项目结构，配置版本控制 (Git)。
    *   配置开发、测试、生产环境。
2.  **数据库初始化**：
    *   执行数据库 `CREATE TABLE` 语句，生成数据库表结构。
3.  **用户认证与授权模块**：
    *   **API实现**：
        *   用户注册 (`POST /api/users/register`)
        *   用户登录 (`POST /api/users/login`)，返回JWT或其他形式的token。
        *   用户登出 (`POST /api/users/logout`)
        *   获取当前用户信息 (`GET /api/users/me`)
    *   **密码安全**：使用强哈希算法（如 bcrypt, scrypt）存储密码。
    *   **基础角色管理**：
        *   API 实现 `roles` 表的 CRUD 操作 (管理员功能)。
        *   API 实现 `levels` 表的 CRUD 操作 (管理员功能)。
        *   用户注册时或管理员分配用户角色和级别。
4.  **访问控制**：
    *   实现中间件或拦截器，对需要认证的API进行token验证。
    *   初步实现基于角色的访问控制（例如，某些API只有管理员能访问）。
5.  **日志系统集成**：
    *   集成基础的日志框架 (如 Log4j, Logback, Winston)，记录应用运行日志和错误。

## 阶段二：大模型接口与基础会话管理

1.  **大模型接口管理模块 (`llm_models`)**：
    *   **API实现** (管理员功能)：
        *   创建、读取、更新、删除大模型配置 (`CRUD /api/admin/llm-models`)。
        *   安全存储和处理API密钥（例如，数据库加密存储，配置中心管理）。
2.  **基础会话管理模块 (`sessions`, `messages`)**：
    *   **API实现**：
        *   创建新会话 (`POST /api/sessions`)：用户选择一个已配置的 `llm_model`。
        *   获取用户历史会话列表 (`GET /api/sessions`)：分页、排序。
        *   删除会话 (`DELETE /api/sessions/{sessionId}`) (软删除)。
        *   发送消息 (`POST /api/sessions/{sessionId}/messages`)：
            *   用户发送消息，记录到 `messages` 表 (此时暂不经过安全策略)。
            *   调用对应 `llm_model` 的API获取回复。
            *   将AI回复记录到 `messages` 表。
        *   获取会话消息历史 (`GET /api/sessions/{sessionId}/messages`)：分页。
3.  **（可选）初步前端界面**：
    *   如果需要Web界面，可以开始搭建一个简单的聊天界面，用于测试会话功能。

## 阶段三：核心安全策略 - 关键词与恶意意图识别

1.  **关键词管理模块 (`keyword_groups`, `keywords`)**：
    *   **API实现** (管理员功能)：
        *   `keyword_groups` 的 CRUD 操作。
        *   `keywords` 的 CRUD 操作，并关联到 `keyword_groups`。
2.  **关键词过滤实现**：
    *   在用户发送消息的逻辑中，增加关键词匹配检查。
    *   如果匹配到关键词，则拦截消息，设置 `messages.is_blocked = TRUE` 和 `messages.block_reason`。
3.  **恶意意图分类与规则管理模块 (`malicious_intent_categories`, `malicious_intent_rules`)**：
    *   **API实现** (管理员功能)：
        *   `malicious_intent_categories` 的 CRUD 操作。
        *   `malicious_intent_rules` (基于正则表达式) 的 CRUD 操作，并关联到分类。
4.  **恶意意图识别实现**：
    *   在用户发送消息的逻辑中（关键词过滤之后或并行），增加恶意意图正则表达式匹配检查。
    *   如果匹配到规则，则拦截消息，设置 `messages.is_blocked = TRUE` 和 `messages.block_reason`。
5.  **安全策略与角色联动 (初步)**：
    *   实现 `role_keyword_group_assignments` 和 `role_malicious_intent_category_assignments` 的API管理。
    *   在关键词和恶意意图检查时，根据用户角色加载对应的策略。

## 阶段四：输出数据脱敏与权限细化

1.  **敏感信息类型与脱敏规则管理 (`sensitive_info_types`, `desensitization_rules`)**：
    *   **API实现** (管理员功能)：
        *   `sensitive_info_types` 的 CRUD 操作。
        *   `desensitization_rules` 的 CRUD 操作，包括 `desensitize_config` 的JSON配置。
2.  **输出数据脱敏实现**：
    *   在接收到大模型回复后、返回给用户前，应用脱敏规则。
    *   根据 `desensitization_rules` 中的 `detection_pattern` 检测敏感信息，并根据 `desensitize_config` 进行脱敏处理。
3.  **权限细化**：
    *   实现 `role_module_permissions` 的API管理和后端逻辑，控制用户对不同功能模块的访问。
    *   实现 `role_desensitization_rule_assignments` 的API管理，并根据用户角色应用不同的脱敏策略。

## 阶段五：数据分级、SDK支持与日志完善

1.  **数据分级分类管理 (`data_source_categories`, `data_source_grades`)**：
    *   **API实现** (管理员功能)：相关表的CRUD。
    *   实现 `role_data_grade_access` 的API管理和后端逻辑。
    *   （此功能在AI防护系统中如何具体落地需进一步分析，可能更多是概念层面或与特定知识库集成）。
2.  **SDK支持**：
    *   将核心的安全策略模块（关键词、恶意意图、脱敏）封装成可独立部署的SDK。
    *   设计SDK的配置和调用接口。
3.  **审计日志 (`audit_logs`)**：
    *   **API实现**：提供日志查询接口 (管理员功能)。
    *   在所有关键操作（登录、配置修改、重要数据访问等）处，记录审计日志到 `audit_logs` 表。

## 阶段六：测试、优化与部署

1.  **全面测试**：
    *   单元测试、集成测试、端到端测试。
    *   安全测试（渗透测试、漏洞扫描）。
2.  **性能优化**：
    *   数据库查询优化、API响应时间优化。
3.  **文档编写**：
    *   API文档、用户手册、部署文档。
4.  **部署**：
    *   准备生产环境，进行部署。
