# app/db/initial_data.py
import logging

from app.db.session import init_db, engine, SessionLocal
# Import models to ensure they are registered with Base metadata
from app.db import models # This will import app.db.models.__init__ which imports User, Role
from app.core.config import settings
from app.services import user_service as user_service_module
from app.services import role_service as role_service_module
from app.schemas.user import UserCreate
from app.schemas.role import RoleCreate

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init() -> None:
    if not engine:
        logger.error("Database engine not initialized. Check DATABASE_URL.")
        return

    # Check if tables already exist to avoid trying to recreate them
    # This is a simple check; for complex scenarios, migrations (Alembic) are better.
    # For example, check for the 'users' table
    from sqlalchemy import inspect
    inspector = inspect(engine)
    
    logger.info("Initializing database...")
    init_db() # This creates tables if they don't exist
    logger.info("Database initialization finished.")

    db = SessionLocal()

    try:
        # 1. Create Admin Role if it doesn't exist
        admin_role_name = settings.ADMIN_USER_ROLE_NAME
        admin_role = role_service_module.get_role_by_name(db, name=admin_role_name)
        if not admin_role:
            logger.info(f"Creating role: {admin_role_name}")
            role_in = RoleCreate(name=admin_role_name, description="Administrator role with full permissions")
            admin_role = role_service_module.create_role(db, role_in=role_in)
            logger.info(f"Role '{admin_role_name}' created with id {admin_role.id}")
        else:
            logger.info(f"Role '{admin_role_name}' already exists with id {admin_role.id}")

        # 2. Create First Superuser if it doesn't exist
        superuser_username = settings.FIRST_SUPERUSER_USERNAME
        user = user_service_module.get_user_by_username(db, username=superuser_username)
        if not user:
            logger.info(f"Creating superuser: {superuser_username}")
            user_in = UserCreate(
                username=superuser_username,
                password=settings.FIRST_SUPERUSER_PASSWORD,
                email=settings.FIRST_SUPERUSER_EMAIL, # Assuming you have this in settings or provide a default
                full_name="Admin User",
                role_id=admin_role.id  # Associate with the admin role
            )
            user = user_service_module.create_user(db, user_in=user_in)
            logger.info(f"Superuser '{superuser_username}' created and assigned role '{admin_role_name}'.")
        else:
            logger.info(f"Superuser '{superuser_username}' already exists.")
            # Optionally, ensure existing superuser has the admin role
            if user.role_id != admin_role.id:
                logger.info(f"Updating superuser '{superuser_username}' to have role '{admin_role_name}'.")
                user.role_id = admin_role.id
                db.commit()
                db.refresh(user)
                logger.info(f"Superuser '{superuser_username}' role updated.")

    except Exception as e:
        logger.error(f"Error during initial data creation: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("Starting database initialization script.")
    init()
    logger.info("Database initialization script finished.")
