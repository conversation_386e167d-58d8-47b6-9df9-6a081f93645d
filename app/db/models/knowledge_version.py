from __future__ import annotations
from sqlalchemy import Column, String, Text, Integer, ForeignKey, DateTime, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Optional, TYPE_CHECKING, Dict, Any
from datetime import datetime

from app.db.base_class import Base

if TYPE_CHECKING:
    from .knowledge_document import KnowledgeDocument
    from .user import User


class KnowledgeVersion(Base):
    """知识库文档版本模型"""
    __tablename__ = "knowledge_versions"

    # 基本信息
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, index=True, comment="版本ID")
    document_id: Mapped[int] = mapped_column(Integer, ForeignKey("knowledge_documents.id"), nullable=False, comment="所属文档ID")
    version_number: Mapped[int] = mapped_column(Integer, nullable=False, comment="版本号")
    
    # 版本信息
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="版本标题")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="版本描述")
    change_summary: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="变更摘要")
    
    # 文件信息
    file_path: Mapped[str] = mapped_column(String(500), nullable=False, comment="版本文件路径")
    file_size: Mapped[int] = mapped_column(Integer, nullable=False, comment="文件大小（字节）")
    file_hash: Mapped[str] = mapped_column(String(64), nullable=False, comment="文件哈希值")
    
    # 变更信息
    changes: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="详细变更记录")
    
    # 用户关联
    created_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    
    # 关系
    document: Mapped[KnowledgeDocument] = relationship(
        "KnowledgeDocument",
        back_populates="versions",
        lazy="select"
    )
    creator: Mapped[User] = relationship(
        "User",
        back_populates="created_knowledge_versions",
        lazy="select"
    )
    
    def __repr__(self):
        return f"<KnowledgeVersion(id={self.id}, document_id={self.document_id}, version={self.version_number})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "version_number": self.version_number,
            "title": self.title,
            "description": self.description,
            "change_summary": self.change_summary,
            "file_size": self.file_size,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "changes": self.changes
        } 