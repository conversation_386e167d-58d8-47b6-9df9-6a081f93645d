# app/db/models/__init__.py

# 导入所有模型以确保它们被SQLAlchemy识别
from .user import User
from .role import Role
from .level import Level
from .llm_model import LLMModel
from .session import Session
from .message import Message
from .keyword_group import KeywordGroup
from .keyword import Keyword
from .regex_rule import RegexRule
from .desensitization_rule import DesensitizationRule
from .security_policy import SecurityPolicy
from .user_model_assignment import UserModelAssignment
from .level_data_access import LevelDataAccess
from .operation_log import OperationLog

# 新增权限管理模型
from .role_module_permission import RoleModulePermission
from .role_keyword_assignment import RoleKeywordGroupAssignment
from .role_feature_permission import RoleFeaturePermission
from .data_source_category import DataSourceCategory
from .data_source_grade import DataSourceGrade
from .role_data_grade_access import RoleDataGradeAccess
from .audit_log import AuditLog

# 新增知识库管理模型
from .knowledge_document import KnowledgeDocument
from .knowledge_chunk import KnowledgeChunk
from .knowledge_version import KnowledgeVersion
from .knowledge_base_model_config import KnowledgeBaseModelConfig
from .user_document_permission import UserDocumentPermission

__all__ = [
    "User",
    "Role", 
    "Level",
    "LLMModel",
    "Session",
    "Message",
    "KeywordGroup",
    "Keyword",
    "RegexRule",
    "DesensitizationRule",
    "SecurityPolicy",
    "UserModelAssignment",
    "OperationLog",
    # 新增权限管理模型
    "RoleModulePermission",
    "RoleKeywordGroupAssignment",
    "RoleFeaturePermission", 
    "DataSourceCategory",
    "DataSourceGrade",
    "RoleDataGradeAccess",
    "AuditLog",
    # 新增知识库管理模型
    "KnowledgeDocument",
    "KnowledgeChunk",
    "KnowledgeVersion",
    "KnowledgeBaseModelConfig",
    "UserDocumentPermission"
]