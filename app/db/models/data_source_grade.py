from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
import enum

from app.db.base_class import Base

if TYPE_CHECKING:
    from .data_source_category import DataSourceCategory
    from .role_data_grade_access import RoleDataGradeAccess
    from .knowledge_document import KnowledgeDocument

class DataGradeName(str, enum.Enum):
    """数据分级名称枚举"""
    HIGH_SENSITIVE = "高级敏感"
    MEDIUM_SENSITIVE = "中级敏感"
    LOW_SENSITIVE = "初级敏感"
    FULLY_OPEN = "完全开放"

class DataSourceGrade(Base):
    """数据源分级表"""
    __tablename__ = "data_source_grades"

    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="数据源分级ID"
    )
    category_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("data_source_categories.id", ondelete="CASCADE"), 
        nullable=False,
        comment="所属分类ID (外键)"
    )
    name: Mapped[DataGradeName] = mapped_column(
        Enum(DataGradeName), 
        nullable=False,
        comment="分级名称"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="描述"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    category: Mapped["DataSourceCategory"] = relationship(
        "DataSourceCategory",
        back_populates="grades",
        lazy="select"
    )
    role_accesses: Mapped[List["RoleDataGradeAccess"]] = relationship(
        "RoleDataGradeAccess",
        back_populates="data_source_grade",
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    knowledge_documents: Mapped[List["KnowledgeDocument"]] = relationship(
        "KnowledgeDocument",
        back_populates="grade",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )

    def __repr__(self):
        return f"<DataSourceGrade(id={self.id}, name='{self.name.value}', category_id={self.category_id})>"
    
    @classmethod
    def get_by_category_and_name(cls, db_session, category_id: int, name: DataGradeName):
        """根据分类和名称获取分级"""
        return db_session.query(cls).filter(
            cls.category_id == category_id,
            cls.name == name
        ).first()
    
    @classmethod
    def get_by_category(cls, db_session, category_id: int):
        """获取指定分类下的所有分级"""
        return db_session.query(cls).filter(
            cls.category_id == category_id
        ).order_by(cls.name).all()
    
    def get_sensitivity_level(self) -> int:
        """获取敏感度等级（数字越小越敏感）"""
        level_map = {
            DataGradeName.HIGH_SENSITIVE: 1,
            DataGradeName.MEDIUM_SENSITIVE: 2,
            DataGradeName.LOW_SENSITIVE: 3,
            DataGradeName.FULLY_OPEN: 4
        }
        return level_map.get(self.name, 4) 