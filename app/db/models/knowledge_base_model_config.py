"""
知识库模型配置表
"""
from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .llm_model import LLMModel
    from .user import User

class KnowledgeBaseModelConfig(Base):
    """知识库模型配置表
    
    用于配置知识库问答功能使用的大模型
    支持配置默认模型、用户自定义模型等
    """
    __tablename__ = "knowledge_base_model_configs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="配置ID，主键")
    
    # 配置类型：global(全局默认), user(用户自定义), category(按分类), grade(按分级)
    config_type: Mapped[str] = mapped_column(String(20), nullable=False, index=True, comment="配置类型")
    
    # 关联的模型ID
    model_id: Mapped[int] = mapped_column(Integer, ForeignKey("llm_models.id"), nullable=False, comment="关联的LLM模型ID")
    
    # 可选的用户ID（用户自定义配置时使用）
    user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="用户ID（用户自定义配置）")
    
    # 可选的分类ID（按分类配置时使用）
    category_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="数据分类ID（按分类配置）")
    
    # 可选的分级ID（按分级配置时使用）  
    grade_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="数据分级ID（按分级配置）")
    
    # 配置名称
    config_name: Mapped[str] = mapped_column(String(100), nullable=False, comment="配置名称")
    
    # 配置描述
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="配置描述")
    
    # 模型特定参数（JSON格式）
    model_params: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="模型特定参数")
    
    # 是否启用
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 优先级（数字越小优先级越高）
    priority: Mapped[int] = mapped_column(Integer, default=100, nullable=False, comment="优先级")
    
    # 创建者
    created_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    model: Mapped[LLMModel] = relationship(
        "LLMModel",
        back_populates="kb_model_configs",
        lazy="select"
    )
    
    user: Mapped[Optional[User]] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="kb_model_configs",
        lazy="select"
    )
    
    creator: Mapped[User] = relationship(
        "User",
        foreign_keys=[created_by],
        lazy="select"
    )

    def __repr__(self):
        return f"<KnowledgeBaseModelConfig(id={self.id}, type='{self.config_type}', model_id={self.model_id})>"
        
    def get_model_param(self, key: str, default: Any = None) -> Any:
        """获取模型参数"""
        if not self.model_params:
            return default
        return self.model_params.get(key, default)
        
    def set_model_param(self, key: str, value: Any) -> None:
        """设置模型参数"""
        if self.model_params is None:
            self.model_params = {}
        self.model_params[key] = value
        
    def is_applicable_for_user(self, user_id: int, category_id: Optional[int] = None, grade_id: Optional[int] = None) -> bool:
        """检查配置是否适用于特定用户和场景"""
        if self.config_type == "global":
            return True
        elif self.config_type == "user":
            return self.user_id == user_id
        elif self.config_type == "category":
            return self.category_id == category_id
        elif self.config_type == "grade":
            return self.grade_id == grade_id
        return False
        
    @classmethod
    async def get_applicable_config(cls, db_session, user_id: int, category_id: Optional[int] = None, grade_id: Optional[int] = None):
        """获取适用的配置（按优先级排序）"""
        from sqlalchemy import select
        
        # 依次检查用户自定义、分级、分类、全局配置
        configs = []
        
        # 用户自定义配置（优先级最高）
        user_result = await db_session.execute(select(cls).filter(
            cls.is_active == True,
            cls.config_type == "user",
            cls.user_id == user_id
        ).order_by(cls.priority.asc()))
        user_config = user_result.scalar_one_or_none()
        if user_config:
            configs.append(user_config)
        
        # 分级配置
        if grade_id:
            grade_result = await db_session.execute(select(cls).filter(
                cls.is_active == True,
                cls.config_type == "grade",
                cls.grade_id == grade_id
            ).order_by(cls.priority.asc()))
            grade_config = grade_result.scalar_one_or_none()
            if grade_config:
                configs.append(grade_config)
        
        # 分类配置
        if category_id:
            category_result = await db_session.execute(select(cls).filter(
                cls.is_active == True,
                cls.config_type == "category",
                cls.category_id == category_id
            ).order_by(cls.priority.asc()))
            category_config = category_result.scalar_one_or_none()
            if category_config:
                configs.append(category_config)
        
        # 全局配置（优先级最低）
        global_result = await db_session.execute(select(cls).filter(
            cls.is_active == True,
            cls.config_type == "global"
        ).order_by(cls.priority.asc()))
        global_config = global_result.scalar_one_or_none()
        if global_config:
            configs.append(global_config)
        
        return configs[0] if configs else None