from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Enum, Text, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import TYPE_CHECKING, Optional
from datetime import datetime
import enum
import re

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .role import Role

class DataType(str, enum.Enum):
    """敏感数据类型枚举"""
    IDENTITY_CARD = "identity_card"      # 身份证号
    CREDIT_CARD = "credit_card"          # 信用卡号
    PHONE = "phone"                      # 手机号
    EMAIL = "email"                      # 邮箱地址
    IP_ADDRESS = "ip_address"            # IP地址
    URL = "url"                          # 网址
    ADDRESS = "address"                  # 地址
    BANK_ACCOUNT = "bank_account"        # 银行账号
    PASSWORD = "password"                # 密码
    USERNAME = "username"                # 用户名
    SOCIAL_SECURITY = "social_security"  # 社会保障号
    CUSTOM = "custom"                    # 自定义类型

class MaskingLevel(str, enum.Enum):
    """脱敏级别枚举"""
    LOW = "low"        # 低级别脱敏 - 保留部分信息
    MEDIUM = "medium"  # 中级别脱敏 - 保留少量信息
    HIGH = "high"      # 高级别脱敏 - 几乎完全隐藏
    COMPLETE = "complete"  # 完全脱敏 - 完全替换

class MaskingStrategy(str, enum.Enum):
    """脱敏策略枚举"""
    ASTERISK = "asterisk"        # 使用*替换
    X_MARK = "x_mark"           # 使用X替换
    HASH = "hash"               # 使用#替换
    REPLACEMENT = "replacement"  # 使用指定字符替换
    PARTIAL_SHOW = "partial_show"  # 部分显示
    FORMAT_PRESERVE = "format_preserve"  # 保留格式

class DesensitizationRule(Base):
    __tablename__ = "desensitization_rules"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="脱敏规则ID，主键")
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True, comment="规则名称")
    description: Mapped[str] = mapped_column(String(500), nullable=True, comment="规则描述")
    
    # 数据类型和检测
    data_type: Mapped[DataType] = mapped_column(
        Enum(DataType, native_enum=False, length=50, values_callable=lambda x: [e.value for e in x]),
        nullable=False, 
        index=True, 
        comment="敏感数据类型"
    )
    pattern: Mapped[str] = mapped_column(Text, nullable=False, comment="检测正则表达式模式")
    
    # 脱敏配置
    masking_level: Mapped[MaskingLevel] = mapped_column(
        Enum(MaskingLevel, native_enum=False, length=20, values_callable=lambda x: [e.value for e in x]),
        nullable=False, 
        comment="脱敏级别"
    )
    masking_strategy: Mapped[MaskingStrategy] = mapped_column(
        Enum(MaskingStrategy, native_enum=False, length=30, values_callable=lambda x: [e.value for e in x]),
        nullable=False, 
        comment="脱敏策略"
    )
    mask_char: Mapped[str] = mapped_column(String(5), default="*", nullable=False, comment="脱敏字符")
    replacement_text: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="替换文本（用于replacement策略）")
    
    # 部分显示配置（用于partial_show策略）
    show_first: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="显示前几位")
    show_last: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="显示后几位")
    min_mask_length: Mapped[int] = mapped_column(Integer, default=3, nullable=False, comment="最小脱敏长度")
    
    # 状态控制
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否激活")
    priority: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="优先级，数字越大优先级越高")
    
    # 技术配置
    flags: Mapped[int] = mapped_column(Integer, default=re.IGNORECASE, nullable=False, comment="正则表达式标志")
    confidence_threshold: Mapped[float] = mapped_column(Float, default=0.8, nullable=False, comment="置信度阈值")
    
    # 角色关联（哪些角色使用此规则）
    role_id: Mapped[Optional[int]] = mapped_column(
        Integer, 
        ForeignKey("roles.id", ondelete="SET NULL"), 
        nullable=True, 
        index=True,
        comment="关联角色ID，为空表示全局规则"
    )
    
    # 统计信息
    match_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="匹配次数统计")
    mask_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="脱敏执行次数统计")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=datetime.utcnow,
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        comment="更新时间"
    )
    created_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="创建者")
    updated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="最后更新者")

    # 关系映射
    role: Mapped[Optional["Role"]] = relationship(
        "Role",
        back_populates="desensitization_rules",
        lazy="select"
    )

    def __repr__(self):
        return f"<DesensitizationRule(id={self.id}, name='{self.name}', data_type={self.data_type}, is_active={self.is_active})>"
    
    def compile_pattern(self) -> re.Pattern:
        """编译正则表达式模式"""
        try:
            return re.compile(self.pattern, self.flags)
        except re.error as e:
            raise ValueError(f"Invalid regex pattern '{self.pattern}': {str(e)}")
    
    def detect_matches(self, text: str) -> list:
        """检测文本中的敏感数据匹配"""
        if not self.is_active:
            return []
        
        try:
            compiled_pattern = self.compile_pattern()
            matches = []
            
            for match in compiled_pattern.finditer(text):
                matches.append({
                    "start": match.start(),
                    "end": match.end(),
                    "matched_text": match.group(),
                    "data_type": self.data_type,
                    "rule_name": self.name,
                    "confidence": self._calculate_confidence(match.group())
                })
                
            return matches
            
        except (re.error, ValueError):
            return []
    
    def apply_masking(self, text: str, start: int, end: int) -> str:
        """对指定位置的文本应用脱敏"""
        if start < 0 or end > len(text) or start >= end:
            return text
        
        matched_text = text[start:end]
        
        if self.masking_strategy == MaskingStrategy.ASTERISK:
            masked_text = self._mask_with_char(matched_text, "*")
        elif self.masking_strategy == MaskingStrategy.X_MARK:
            masked_text = self._mask_with_char(matched_text, "X")
        elif self.masking_strategy == MaskingStrategy.HASH:
            masked_text = self._mask_with_char(matched_text, "#")
        elif self.masking_strategy == MaskingStrategy.REPLACEMENT:
            masked_text = self.replacement_text or "[MASKED]"
        elif self.masking_strategy == MaskingStrategy.PARTIAL_SHOW:
            masked_text = self._partial_show_mask(matched_text)
        elif self.masking_strategy == MaskingStrategy.FORMAT_PRESERVE:
            masked_text = self._format_preserve_mask(matched_text)
        else:
            masked_text = self._mask_with_char(matched_text, self.mask_char)
        
        return text[:start] + masked_text + text[end:]
    
    def _mask_with_char(self, text: str, char: str = None) -> str:
        """使用指定字符进行脱敏"""
        mask_char = char or self.mask_char or "*"
        
        if self.masking_level == MaskingLevel.LOW:
            # 低级别：只脱敏中间部分
            if len(text) <= 4:
                return text[0] + mask_char * (len(text) - 2) + text[-1] if len(text) >= 2 else text
            return text[:2] + mask_char * (len(text) - 4) + text[-2:]
        elif self.masking_level == MaskingLevel.MEDIUM:
            # 中级别：保留首尾少量字符
            if len(text) <= 2:
                return mask_char * len(text)
            return text[0] + mask_char * (len(text) - 2) + text[-1]
        elif self.masking_level == MaskingLevel.HIGH:
            # 高级别：几乎全部脱敏
            if len(text) <= 1:
                return mask_char * len(text)
            return text[0] + mask_char * (len(text) - 1)
        else:  # COMPLETE
            # 完全脱敏
            return mask_char * max(len(text), self.min_mask_length)
    
    def _partial_show_mask(self, text: str) -> str:
        """部分显示脱敏"""
        text_len = len(text)
        show_first = min(self.show_first, text_len)
        show_last = min(self.show_last, text_len)
        
        if show_first + show_last >= text_len:
            return text  # 如果显示长度超过文本长度，不脱敏
        
        mask_length = max(text_len - show_first - show_last, self.min_mask_length)
        mask_char = self.mask_char or "*"
        
        return text[:show_first] + mask_char * mask_length + text[-show_last:] if show_last > 0 else text[:show_first] + mask_char * mask_length
    
    def _format_preserve_mask(self, text: str) -> str:
        """保留格式的脱敏"""
        mask_char = self.mask_char or "*"
        result = ""
        
        for char in text:
            if char.isalnum():  # 字母数字替换为脱敏字符
                result += mask_char
            else:  # 保留特殊字符（如-、空格等）
                result += char
        
        return result
    
    def _calculate_confidence(self, matched_text: str) -> float:
        """计算匹配置信度"""
        # 基础置信度基于文本长度和模式复杂度
        base_confidence = 0.8
        
        # 根据匹配文本长度调整
        if len(matched_text) < 3:
            base_confidence *= 0.7
        elif len(matched_text) > 10:
            base_confidence *= 1.1
        
        # 根据数据类型调整
        type_confidence_map = {
            DataType.IDENTITY_CARD: 0.95,
            DataType.CREDIT_CARD: 0.9,
            DataType.PHONE: 0.85,
            DataType.EMAIL: 0.9,
            DataType.IP_ADDRESS: 0.8,
            DataType.CUSTOM: 0.7
        }
        
        base_confidence *= type_confidence_map.get(self.data_type, 0.8)
        
        return min(1.0, max(0.1, base_confidence))
    
    def increment_match_count(self):
        """增加匹配计数"""
        self.match_count += 1
    
    def increment_mask_count(self):
        """增加脱敏执行计数"""
        self.mask_count += 1
    
    def get_effectiveness_rate(self) -> float:
        """计算有效性率（脱敏执行率）"""
        if self.match_count == 0:
            return 1.0  # 没有匹配记录时返回100%
        
        return self.mask_count / self.match_count
    
    def is_pattern_valid(self) -> bool:
        """验证正则表达式是否有效"""
        try:
            re.compile(self.pattern, self.flags)
            return True
        except re.error:
            return False 