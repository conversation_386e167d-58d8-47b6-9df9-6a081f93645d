from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import List, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .keyword import Keyword
    from .role_keyword_assignment import RoleKeywordGroupAssignment

class KeywordGroup(Base):
    __tablename__ = "keyword_groups"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="关键词分组ID，主键")
    name: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True, comment="分组名称，唯一")
    description: Mapped[str] = mapped_column(String(500), nullable=True, comment="分组描述")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否激活")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射 - 与关键词的关系
    keywords: Mapped[List[Keyword]] = relationship(
        "Keyword",
        back_populates="keyword_group",
        cascade="save-update, merge, refresh-expire, delete",
        passive_deletes=True,
        lazy="select"
    )
    
    # 角色分配关系
    role_assignments: Mapped[List[RoleKeywordGroupAssignment]] = relationship(
        "RoleKeywordGroupAssignment",
        back_populates="keyword_group",
        cascade="all, delete-orphan",
        lazy="select"
    )

    def __repr__(self):
        return f"<KeywordGroup(id={self.id}, name='{self.name}', is_active={self.is_active})>"
        
    @property
    def keyword_count(self) -> int:
        """获取该分组下的关键词数量"""
        return len(self.keywords) if self.keywords else 0
        
    def can_be_deleted(self) -> bool:
        """检查分组是否可以被删除"""
        # 如果还有关联的关键词，不能删除
        if self.keyword_count > 0:
            return False
        return True
        
    def get_active_keywords(self) -> List[Keyword]:
        """获取该分组下所有激活的关键词"""
        if not self.keywords:
            return []
        return [keyword for keyword in self.keywords if keyword.is_active] 