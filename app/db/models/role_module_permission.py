from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .role import Role

class RoleModulePermission(Base):
    """角色模块权限表"""
    __tablename__ = "role_module_permissions"

    role_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        primary_key=True,
        comment="角色ID (外键)"
    )
    module_name: Mapped[str] = mapped_column(
        String(100), 
        primary_key=True,
        comment="模块名称 (例如: USER_MANAGEMENT, SESSION_HISTORY)"
    )
    can_access: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否可访问"
    )

    # 关系映射
    role: Mapped["Role"] = relationship(
        "Role",
        back_populates="module_permissions",
        lazy="select"
    )

    def __repr__(self):
        return f"<RoleModulePermission(role_id={self.role_id}, module={self.module_name}, access={self.can_access})>"

    @classmethod
    def get_user_permissions(cls, db_session, user_role_id: int) -> set:
        """获取用户的模块权限集合"""
        permissions = db_session.query(cls).filter(
            cls.role_id == user_role_id,
            cls.can_access == True
        ).all()
        return {perm.module_name for perm in permissions}
    
    @classmethod
    def has_permission(cls, db_session, user_role_id: int, module_name: str) -> bool:
        """检查用户是否有特定模块的权限"""
        permission = db_session.query(cls).filter(
            cls.role_id == user_role_id,
            cls.module_name == module_name,
            cls.can_access == True
        ).first()
        return permission is not None 