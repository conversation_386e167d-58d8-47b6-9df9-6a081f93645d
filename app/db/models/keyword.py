from __future__ import annotations
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, Bo<PERSON><PERSON>, DateTime, ForeignKey, Enum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import TYPE_CHECKING
from datetime import datetime
import enum

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .keyword_group import KeywordGroup

class MatchType(str, enum.Enum):
    """匹配类型枚举"""
    EXACT = "exact"        # 精确匹配
    PARTIAL = "partial"    # 部分匹配
    REGEX = "regex"        # 正则表达式匹配

class Keyword(Base):
    __tablename__ = "keywords"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="关键词ID，主键")
    word: Mapped[str] = mapped_column(String(200), nullable=False, index=True, comment="关键词内容")
    match_type: Mapped[MatchType] = mapped_column(Enum(MatchType), default=MatchType.PARTIAL, nullable=False, comment="匹配类型")
    is_active: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=True, nullable=False, comment="是否激活")
    priority: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="优先级，数字越大优先级越高")
    description: Mapped[str] = mapped_column(String(300), nullable=True, comment="关键词描述")
    
    # 外键关联
    keyword_group_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("keyword_groups.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="关键词分组ID"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射 - 与关键词分组的关系
    keyword_group: Mapped[KeywordGroup] = relationship(
        "KeywordGroup",
        back_populates="keywords",
        lazy="select"
    )

    def __repr__(self):
        return f"<Keyword(id={self.id}, word='{self.word}', match_type={self.match_type}, is_active={self.is_active})>"
        
    def matches_text(self, text: str) -> bool:
        """检查给定文本是否匹配当前关键词"""
        if not self.is_active:
            return False
            
        text_lower = text.lower()
        word_lower = self.word.lower()
        
        if self.match_type == MatchType.EXACT:
            # 精确匹配：完全相等
            return text_lower == word_lower
        elif self.match_type == MatchType.PARTIAL:
            # 部分匹配：包含关键词
            return word_lower in text_lower
        elif self.match_type == MatchType.REGEX:
            # 正则表达式匹配
            import re
            try:
                return bool(re.search(self.word, text, re.IGNORECASE))
            except re.error:
                # 如果正则表达式有错误，降级为部分匹配
                return word_lower in text_lower
        
        return False 