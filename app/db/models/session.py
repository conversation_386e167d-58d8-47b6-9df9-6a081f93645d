from __future__ import annotations
from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import List, Optional, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .user import User
    from .llm_model import LLMModel
    from .message import Message

class Session(Base):
    __tablename__ = "sessions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="会话ID，主键")
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    llm_model_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("llm_models.id"), nullable=True, comment="使用的大模型ID")
    title: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="会话标题")
    session_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, default="chat", comment="会话类型：chat(普通对话)、rag(知识库问答)")
    
    # 软删除标记
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否已删除")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    last_activity_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="最后活跃时间"
    )

    # 关系映射
    user: Mapped[User] = relationship(
        "User", 
        back_populates="sessions",
        lazy="select"
    )
    
    llm_model: Mapped[Optional[LLMModel]] = relationship(
        "LLMModel", 
        back_populates="sessions",
        lazy="select"
    )
    
    messages: Mapped[List[Message]] = relationship(
        "Message", 
        back_populates="session",
        cascade="all, delete-orphan",
        order_by="Message.created_at",
        lazy="select"
    )

    def __repr__(self):
        return f"<Session(id={self.id}, user_id={self.user_id}, title='{self.title}')>"
        
    @property
    def message_count(self) -> int:
        """获取会话中的消息数量"""
        return len(self.messages) if self.messages else 0
        
    @property
    def is_active(self) -> bool:
        """会话是否活跃（未删除）"""
        return not self.is_deleted
        
    def soft_delete(self) -> None:
        """软删除会话"""
        self.is_deleted = True
        
    def restore(self) -> None:
        """恢复已删除的会话"""
        self.is_deleted = False
        
    def update_activity(self) -> None:
        """更新最后活跃时间"""
        self.last_activity_at = datetime.utcnow()
        
    def get_title_or_default(self) -> str:
        """获取会话标题，如果没有则返回默认标题"""
        if self.title:
            return self.title
        # 如果没有设置标题，使用创建时间作为默认标题
        return f"会话 {self.created_at.strftime('%Y-%m-%d %H:%M')}"
        
    def get_last_user_message(self) -> Optional[Message]:
        """获取最后一条用户消息"""
        if not self.messages:
            return None
        for message in reversed(self.messages):
            if message.sender_type == "USER":
                return message
        return None
        
    def get_last_ai_message(self) -> Optional[Message]:
        """获取最后一条AI消息"""
        if not self.messages:
            return None
        for message in reversed(self.messages):
            if message.sender_type == "AI":
                return message
        return None 