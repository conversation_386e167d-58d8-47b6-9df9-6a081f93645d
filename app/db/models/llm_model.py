from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .session import Session
    from .user_model_assignment import UserModelAssignment
    from .knowledge_base_model_config import KnowledgeBaseModelConfig

class LLMModel(Base):
    __tablename__ = "llm_models"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="模型ID，主键")
    name: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True, comment="模型名称，唯一")
    api_url: Mapped[str] = mapped_column(String(512), nullable=False, comment="API接口地址")
    api_key_encrypted: Mapped[Optional[str]] = mapped_column(String(512), nullable=True, comment="加密后的API密钥")
    
    # 模型配置参数 (JSON格式存储temperature, max_tokens等)
    config_params: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="模型特定配置参数")
    
    # 状态控制
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否激活")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射 - 与会话的关系
    sessions: Mapped[List[Session]] = relationship(
        "Session",
        back_populates="llm_model",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 关系映射 - 与用户模型分配的关系
    user_assignments: Mapped[List[UserModelAssignment]] = relationship(
        "UserModelAssignment",
        back_populates="model",
        cascade="all, delete-orphan",
        passive_deletes=False,
        lazy="select"
    )
    
    # 关系映射 - 与知识库模型配置的关系
    kb_model_configs: Mapped[List[KnowledgeBaseModelConfig]] = relationship(
        "KnowledgeBaseModelConfig",
        back_populates="model",
        cascade="all, delete-orphan",
        passive_deletes=False,
        lazy="select"
    )

    def __repr__(self):
        return f"<LLMModel(id={self.id}, name='{self.name}', is_active={self.is_active})>"
        
    @property
    def session_count(self) -> int:
        """获取使用该模型的会话数量"""
        return len(self.sessions) if self.sessions else 0
        
    def can_be_deleted(self) -> bool:
        """检查模型是否可以被删除"""
        # 如果还有关联会话，不能删除
        if self.session_count > 0:
            return False
        return True
        
    def get_config_param(self, key: str, default: Any = None) -> Any:
        """获取配置参数"""
        if not self.config_params:
            return default
        return self.config_params.get(key, default)
        
    def set_config_param(self, key: str, value: Any) -> None:
        """设置配置参数"""
        if self.config_params is None:
            self.config_params = {}
        self.config_params[key] = value
        
    def validate_config(self) -> bool:
        """验证配置参数是否完整"""
        if not self.api_url:
            return False
        # 可以添加更多验证逻辑
        return True 