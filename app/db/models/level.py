# app/db/models/level.py

from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import List, Optional, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .user import User
    from .role import Role
    from .security_policy import SecurityPolicy
    from .level_data_access import LevelDataAccess

class Level(Base):
    __tablename__ = "levels"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="级别ID，主键")
    role_id: Mapped[int] = mapped_column(Integer, ForeignKey("roles.id"), nullable=False, comment="所属角色ID")
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True, comment="级别名称")
    rank_value: Mapped[int] = mapped_column(Integer, nullable=False, comment="级别排序值")
    description: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="级别描述")
    
    # 添加状态控制
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="级别是否激活")
    
    # 添加时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    role: Mapped[Role] = relationship(
        "Role", 
        back_populates="levels",
        lazy="select"
    )
    
    users: Mapped[List[User]] = relationship(
        "User", 
        back_populates="level",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与security_policies的关系
    security_policies: Mapped[List[SecurityPolicy]] = relationship(
        "SecurityPolicy",
        back_populates="level",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与数据访问权限的关系
    data_access_permissions: Mapped[List[LevelDataAccess]] = relationship(
        "LevelDataAccess",
        back_populates="level",
        cascade="all, delete-orphan",
        lazy="select"
    )

    def __repr__(self):
        return f"<Level(id={self.id}, name='{self.name}', rank_value={self.rank_value})>"
        
    @property
    def user_count(self) -> int:
        """获取该级别下的用户数量"""
        return len(self.users) if self.users else 0
        
    def can_be_deleted(self) -> bool:
        """检查级别是否可以被删除"""
        # 如果还有关联用户，不能删除
        if self.user_count > 0:
            return False
        return True
