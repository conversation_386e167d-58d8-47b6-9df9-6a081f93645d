from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boole<PERSON>, DateTime, ForeignKey, Enum, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import TYPE_CHECKING, Optional
from datetime import datetime
import enum
import re

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .role import Role

class IntentCategory(str, enum.Enum):
    """恶意意图分类枚举"""
    PRIVILEGE_ESCALATION = "privilege_escalation"  # 权限提升类
    PRIVACY_EXTRACTION = "privacy_extraction"      # 隐私套取类
    DATA_POISONING = "data_poisoning"              # 数据投毒类
    SYSTEM_BYPASS = "system_bypass"                # 系统绕过类
    SOCIAL_ENGINEERING = "social_engineering"      # 社会工程学类
    MALICIOUS_CODE = "malicious_code"              # 恶意代码类
    INFORMATION_DISCLOSURE = "information_disclosure"  # 信息泄露类
    # 越狱攻击专门分类
    JAILBREAK_GOAL_HIJACKING = "jailbreak_goal_hijacking"        # 越狱-目标竞争攻击
    JAILBREAK_ROLE_PLAYING = "jailbreak_role_playing"            # 越狱-角色扮演攻击
    JAILBREAK_ENCODING_TRANSLATION = "jailbreak_encoding_translation"  # 越狱-编码翻译攻击
    JAILBREAK_INSTRUCTION_HIJACKING = "jailbreak_instruction_hijacking"  # 越狱-指令劫持攻击
    JAILBREAK_CONTEXT_LEARNING = "jailbreak_context_learning"    # 越狱-上下文学习攻击
    JAILBREAK_PROMPT_INJECTION = "jailbreak_prompt_injection"    # 越狱-提示注入攻击
    JAILBREAK_CHAIN_PROMPT = "jailbreak_chain_prompt"            # 越狱-链式提示攻击
    OTHER = "other"                                # 其他类型

class RuleSeverity(str, enum.Enum):
    """规则严重性等级"""
    LOW = "low"        # 低风险
    MEDIUM = "medium"  # 中风险
    HIGH = "high"      # 高风险
    CRITICAL = "critical"  # 严重

class RegexRule(Base):
    __tablename__ = "regex_rules"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="正则规则ID，主键")
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True, comment="规则名称")
    pattern: Mapped[str] = mapped_column(Text, nullable=False, comment="正则表达式模式")
    description: Mapped[str] = mapped_column(String(500), nullable=True, comment="规则描述")
    
    # 分类信息
    category: Mapped[IntentCategory] = mapped_column(Enum(IntentCategory), nullable=False, comment="恶意意图分类")
    severity: Mapped[str] = mapped_column(String(8), default="medium", nullable=False, comment="严重性等级")
    
    # 状态控制
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否激活")
    priority: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="优先级，数字越大优先级越高")
    
    # 技术配置
    flags: Mapped[int] = mapped_column(Integer, default=re.IGNORECASE, nullable=False, comment="正则表达式标志")
    timeout_ms: Mapped[int] = mapped_column(Integer, default=1000, nullable=False, comment="匹配超时时间（毫秒）")
    
    # 版本控制
    version: Mapped[int] = mapped_column(Integer, default=1, nullable=False, comment="规则版本号")
    parent_rule_id: Mapped[Optional[int]] = mapped_column(
        Integer, 
        ForeignKey("regex_rules.id", ondelete="SET NULL"), 
        nullable=True,
        comment="父规则ID，用于版本追踪"
    )
    
    # 角色关联（哪些角色使用此规则）
    role_id: Mapped[Optional[int]] = mapped_column(
        Integer, 
        ForeignKey("roles.id", ondelete="SET NULL"), 
        nullable=True, 
        index=True,
        comment="关联角色ID，为空表示全局规则"
    )
    
    # 统计信息
    match_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="匹配次数统计")
    false_positive_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="误报次数统计")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    created_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="创建者")
    updated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="最后更新者")

    # 关系映射
    role: Mapped[Optional["Role"]] = relationship(
        "Role",
        back_populates="regex_rules",
        lazy="select"
    )
    
    # 自引用关系（父子规则版本）
    parent_rule: Mapped[Optional["RegexRule"]] = relationship(
        "RegexRule",
        remote_side=[id],
        back_populates="child_rules",
        lazy="select"
    )
    child_rules: Mapped[list["RegexRule"]] = relationship(
        "RegexRule",
        back_populates="parent_rule",
        lazy="select"
    )

    def __repr__(self):
        return f"<RegexRule(id={self.id}, name='{self.name}', category={self.category}, is_active={self.is_active})>"
    
    @property
    def severity_enum(self) -> RuleSeverity:
        """获取严重性等级的enum值"""
        try:
            return RuleSeverity(self.severity)
        except ValueError:
            return RuleSeverity.MEDIUM
    
    @severity_enum.setter
    def severity_enum(self, value: RuleSeverity):
        """设置严重性等级的enum值"""
        self.severity = value.value
    
    def compile_pattern(self) -> re.Pattern:
        """编译正则表达式模式"""
        try:
            return re.compile(self.pattern, self.flags)
        except re.error as e:
            raise ValueError(f"Invalid regex pattern '{self.pattern}': {str(e)}")
    
    def test_pattern(self, text: str) -> bool:
        """测试正则表达式是否匹配给定文本"""
        if not self.is_active:
            return False
        
        try:
            compiled_pattern = self.compile_pattern()
            # 直接进行匹配，不使用signal（signal在某些环境下可能不工作）
            result = bool(compiled_pattern.search(text))
            return result
                
        except (re.error, ValueError):
            # 正则表达式错误
            return False
    
    def increment_match_count(self):
        """增加匹配计数"""
        self.match_count += 1
    
    def increment_false_positive_count(self):
        """增加误报计数"""
        self.false_positive_count += 1
    
    def get_accuracy_rate(self) -> float:
        """计算准确率（非误报率）"""
        total_matches = self.match_count
        if total_matches == 0:
            return 1.0  # 没有匹配记录时返回100%
        
        return 1.0 - (self.false_positive_count / total_matches)
    
    def is_pattern_valid(self) -> bool:
        """验证正则表达式是否有效"""
        try:
            re.compile(self.pattern, self.flags)
            return True
        except re.error:
            return False 