# app/db/models/user.py

from __future__ import annotations  # 启用前向引用
from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Optional, List, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base
# 移除直接导入，避免潜在的循环导入问题
# from .role import Role
# from .level import Level

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .role import Role
    from .level import Level
    from .session import Session
    from .message import Message
    from .security_policy import SecurityPolicy
    from .audit_log import AuditLog
    from .knowledge_document import KnowledgeDocument
    from .knowledge_version import KnowledgeVersion
    from .knowledge_base_model_config import KnowledgeBaseModelConfig

class User(Base):
    __tablename__ = "users"

    # Columns as defined in database_schema.sql
    # Using Mapped and mapped_column for SQLAlchemy 2.0 style
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, index=True, comment="用户ID，主键")
    username: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True, comment="用户名，唯一")
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False, comment="哈希后的密码")
    email: Mapped[Optional[str]] = mapped_column(String(255), unique=True, index=True, nullable=True, comment="邮箱，唯一")
    
    role_id: Mapped[int] = mapped_column(Integer, ForeignKey("roles.id"), nullable=False, comment="角色ID，外键关联roles表")
    level_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("levels.id"), nullable=True, comment="级别ID，外键关联levels表")
    manager_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="直接上级用户ID")

    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="账户是否激活")
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 修复关系映射配置 - 解决类型检查问题
    role: Mapped[Role] = relationship(
        "Role",  # 字符串引用避免循环导入
        back_populates="users",
        lazy="select"
    )
    level: Mapped[Optional[Level]] = relationship(
        "Level",  # 字符串引用避免循环导入
        back_populates="users",
        lazy="select"
    )
    
    # 添加与sessions和messages的关系
    sessions: Mapped[List[Session]] = relationship(
        "Session", 
        back_populates="user",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        order_by="Session.last_activity_at.desc()",
        lazy="select"
    )
    
    messages: Mapped[List[Message]] = relationship(
        "Message", 
        back_populates="user",
        cascade="save-update, merge, refresh-expire", 
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与security_policies的关系
    security_policies: Mapped[List[SecurityPolicy]] = relationship(
        "SecurityPolicy",
        back_populates="user",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 层级管理关系
    manager: Mapped[Optional[User]] = relationship(
        "User",
        remote_side="User.id",
        back_populates="subordinates",
        lazy="select"
    )
    subordinates: Mapped[List[User]] = relationship(
        "User",
        back_populates="manager",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 审计日志关系
    audit_logs: Mapped[List[AuditLog]] = relationship(
        "AuditLog",
        back_populates="user",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 知识库相关关系
    uploaded_documents: Mapped[List["KnowledgeDocument"]] = relationship(
        "KnowledgeDocument",
        foreign_keys="KnowledgeDocument.uploaded_by",
        back_populates="uploader",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    reviewed_documents: Mapped[List["KnowledgeDocument"]] = relationship(
        "KnowledgeDocument",
        foreign_keys="KnowledgeDocument.reviewed_by",
        back_populates="reviewer",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    created_knowledge_versions: Mapped[List["KnowledgeVersion"]] = relationship(
        "KnowledgeVersion",
        back_populates="creator",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 知识库模型配置关系
    kb_model_configs: Mapped[List["KnowledgeBaseModelConfig"]] = relationship(
        "KnowledgeBaseModelConfig",
        foreign_keys="KnowledgeBaseModelConfig.user_id",
        back_populates="user",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
        
    @property
    def session_count(self) -> int:
        """获取用户的会话数量"""
        return len(self.sessions) if self.sessions else 0
        
    @property
    def active_session_count(self) -> int:
        """获取用户的活跃会话数量"""
        if not self.sessions:
            return 0
        return len([s for s in self.sessions if not s.is_deleted])
        
    @property
    def message_count(self) -> int:
        """获取用户发送的消息数量"""
        return len(self.messages) if self.messages else 0
        
    def get_active_sessions(self) -> List[Session]:
        """获取用户的活跃会话列表"""
        if not self.sessions:
            return []
        return [s for s in self.sessions if not s.is_deleted]
        
    def get_latest_session(self) -> Optional[Session]:
        """获取用户最新的活跃会话"""
        active_sessions = self.get_active_sessions()
        if not active_sessions:
            return None
        return max(active_sessions, key=lambda s: s.last_activity_at)
    
    @property
    def is_superuser(self) -> bool:
        """检查用户是否为超级用户/管理员"""
        if not self.role:
            return False
        # 检查角色名称是否为管理员角色
        admin_role_names = ["管理员", "admin", "administrator", "super_admin"]
        return self.role.name.lower() in [name.lower() for name in admin_role_names]


