# app/db/models/knowledge_document.py

from __future__ import annotations
from sqlalchemy import Column, String, Text, Boolean, DateTime, Foreign<PERSON>ey, Integer, JSON, Float, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Optional, List, TYPE_CHECKING, Dict, Any
from datetime import datetime
import enum

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User
    from .data_source_category import DataSourceCategory
    from .data_source_grade import DataSourceGrade
    from .knowledge_chunk import KnowledgeChunk
    from .knowledge_version import KnowledgeVersion
    from .user_document_permission import UserDocumentPermission


class DocumentStatus(str, enum.Enum):
    """文档状态枚举"""
    PENDING = "pending"          # 待处理
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 处理失败
    ARCHIVED = "archived"       # 已归档


class DocumentType(str, enum.Enum):
    """文档类型枚举"""
    PDF = "PDF"
    WORD = "WORD"
    EXCEL = "EXCEL"
    PPT = "PPT"
    TXT = "TXT"
    MARKDOWN = "MARKDOWN"
    HTML = "HTML"
    OTHER = "OTHER"


class KnowledgeDocument(Base):
    """知识库文档模型"""
    __tablename__ = "knowledge_documents"

    # 基本信息
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, index=True, comment="文档ID")
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="文档标题")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="文档描述")
    
    # 文件信息
    file_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="原始文件名")
    file_path: Mapped[str] = mapped_column(String(500), nullable=False, comment="文件存储路径")
    file_size: Mapped[int] = mapped_column(Integer, nullable=False, comment="文件大小（字节）")
    file_hash: Mapped[str] = mapped_column(String(64), nullable=False, unique=True, comment="文件哈希值")
    document_type: Mapped[DocumentType] = mapped_column(SQLEnum(DocumentType), nullable=False, comment="文档类型")
    
    # 分类分级
    category_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("data_source_categories.id"), nullable=True, comment="数据分类ID")
    grade_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("data_source_grades.id"), nullable=True, comment="数据分级ID")
    
    # 处理状态
    status: Mapped[DocumentStatus] = mapped_column(
        SQLEnum(DocumentStatus), 
        default=DocumentStatus.PENDING, 
        nullable=False, 
        comment="文档状态"
    )
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    
    # 向量化信息
    is_vectorized: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否已向量化")
    chunk_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="文档块数量")
    embedding_model: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="使用的嵌入模型")
    
    # 元数据
    doc_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="文档元数据")
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True, comment="文档标签")
    
    # 审核信息
    is_reviewed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否已审核")
    reviewed_by: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="审核人ID")
    reviewed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True, comment="审核时间")
    
    # 用户关联
    uploaded_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False, comment="上传用户ID")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    processed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), 
        nullable=True,
        comment="处理完成时间"
    )
    
    # 关系
    uploader: Mapped[User] = relationship(
        "User", 
        foreign_keys=[uploaded_by],
        back_populates="uploaded_documents",
        lazy="select"
    )
    reviewer: Mapped[Optional[User]] = relationship(
        "User", 
        foreign_keys=[reviewed_by],
        back_populates="reviewed_documents",
        lazy="select"
    )
    category: Mapped[Optional[DataSourceCategory]] = relationship(
        "DataSourceCategory",
        back_populates="knowledge_documents",
        lazy="select"
    )
    grade: Mapped[Optional[DataSourceGrade]] = relationship(
        "DataSourceGrade",
        back_populates="knowledge_documents",
        lazy="select"
    )
    chunks: Mapped[List[KnowledgeChunk]] = relationship(
        "KnowledgeChunk",
        back_populates="document",
        cascade="all, delete-orphan",
        lazy="select"
    )
    versions: Mapped[List[KnowledgeVersion]] = relationship(
        "KnowledgeVersion",
        back_populates="document",
        cascade="all, delete-orphan",
        order_by="KnowledgeVersion.version_number.desc()",
        lazy="select"
    )
    user_permissions: Mapped[List["UserDocumentPermission"]] = relationship(
        "UserDocumentPermission",
        back_populates="document",
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    def __repr__(self):
        return f"<KnowledgeDocument(id={self.id}, title='{self.title}', status='{self.status.value}')>"
    
    @property
    def is_accessible_by_role(self, role_id: int, db) -> bool:
        """检查角色是否可以访问此文档"""
        if not self.grade_id:
            return True  # 没有分级限制
            
        # 检查角色是否有权限访问此分级
        from .role_data_grade_access import RoleDataGradeAccess
        access = db.query(RoleDataGradeAccess).filter(
            RoleDataGradeAccess.role_id == role_id,
            RoleDataGradeAccess.grade_id == self.grade_id
        ).first()
        
        return access is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "document_type": self.document_type.value,
            "status": self.status.value,
            "category_id": self.category_id,
            "grade_id": self.grade_id,
            "is_vectorized": self.is_vectorized,
            "chunk_count": self.chunk_count,
            "tags": self.tags,
            "uploaded_by": self.uploaded_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 