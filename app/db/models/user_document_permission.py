from sqlalchemy import In<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String, DateTime, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import TYPE_CHECKING, Optional
import enum

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User
    from .knowledge_document import KnowledgeDocument

class PermissionType(str, enum.Enum):
    """权限类型枚举"""
    READ = "read"           # 读取权限
    DOWNLOAD = "download"   # 下载权限
    SEARCH = "search"       # 搜索权限
    SHARE = "share"         # 分享权限

class UserDocumentPermission(Base):
    """用户文档权限表 - 控制特定用户对特定文档的访问权限"""
    __tablename__ = "user_document_permissions"

    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="权限记录ID"
    )
    user_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False,
        index=True,
        comment="用户ID (外键)"
    )
    document_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("knowledge_documents.id", ondelete="CASCADE"), 
        nullable=False,
        index=True,
        comment="文档ID (外键)"
    )
    permission_type: Mapped[PermissionType] = mapped_column(
        String(20), 
        nullable=False,
        comment="权限类型"
    )
    granted: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否授予权限"
    )
    granted_by: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("users.id"), 
        nullable=False,
        comment="授权人ID (外键)"
    )
    granted_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="授权时间"
    )
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), 
        nullable=True,
        comment="权限过期时间 (NULL表示永不过期)"
    )
    reason: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="授权原因或备注"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[user_id],
        lazy="select"
    )
    document: Mapped["KnowledgeDocument"] = relationship(
        "KnowledgeDocument",
        back_populates="user_permissions",
        lazy="select"
    )
    granter: Mapped["User"] = relationship(
        "User",
        foreign_keys=[granted_by],
        lazy="select"
    )

    def __repr__(self):
        return f"<UserDocumentPermission(user_id={self.user_id}, document_id={self.document_id}, type={self.permission_type}, granted={self.granted})>"
    
    @classmethod
    def has_permission(cls, db_session, user_id: int, document_id: int, permission_type: PermissionType) -> bool:
        """检查用户是否有特定文档的特定权限"""
        permission = db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.document_id == document_id,
            cls.permission_type == permission_type,
            cls.granted == True
        ).first()
        
        if not permission:
            return False
        
        # 检查权限是否过期
        if permission.expires_at and permission.expires_at < datetime.now():
            return False
        
        return True
    
    @classmethod
    def get_user_permissions(cls, db_session, user_id: int, document_id: int) -> list:
        """获取用户对特定文档的所有权限"""
        permissions = db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.document_id == document_id,
            cls.granted == True
        ).all()
        
        # 过滤掉过期的权限
        valid_permissions = []
        for perm in permissions:
            if not perm.expires_at or perm.expires_at > datetime.now():
                valid_permissions.append(perm)
        
        return valid_permissions
    
    @classmethod
    def get_accessible_documents(cls, db_session, user_id: int, permission_type: PermissionType = PermissionType.READ) -> list:
        """获取用户可访问的文档ID列表"""
        permissions = db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.permission_type == permission_type,
            cls.granted == True
        ).all()
        
        # 过滤掉过期的权限
        valid_document_ids = []
        for perm in permissions:
            if not perm.expires_at or perm.expires_at > datetime.now():
                valid_document_ids.append(perm.document_id)
        
        return valid_document_ids
    
    @classmethod
    def grant_permission(
        cls, 
        db_session, 
        user_id: int, 
        document_id: int, 
        permission_type: PermissionType,
        granted_by: int,
        expires_at: Optional[datetime] = None,
        reason: Optional[str] = None
    ):
        """授予用户文档权限"""
        # 检查是否已存在相同权限
        existing = db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.document_id == document_id,
            cls.permission_type == permission_type
        ).first()
        
        if existing:
            # 更新现有权限
            existing.granted = True
            existing.granted_by = granted_by
            existing.granted_at = datetime.now()
            existing.expires_at = expires_at
            existing.reason = reason
            existing.updated_at = datetime.now()
            return existing
        else:
            # 创建新权限
            new_permission = cls(
                user_id=user_id,
                document_id=document_id,
                permission_type=permission_type,
                granted=True,
                granted_by=granted_by,
                expires_at=expires_at,
                reason=reason
            )
            db_session.add(new_permission)
            return new_permission
    
    @classmethod
    def revoke_permission(cls, db_session, user_id: int, document_id: int, permission_type: PermissionType):
        """撤销用户文档权限"""
        permission = db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.document_id == document_id,
            cls.permission_type == permission_type
        ).first()
        
        if permission:
            permission.granted = False
            permission.updated_at = datetime.now()
            return permission
        
        return None
    
    def is_expired(self) -> bool:
        """检查权限是否已过期"""
        if not self.expires_at:
            return False
        return self.expires_at < datetime.now()
    
    def days_until_expiry(self) -> Optional[int]:
        """计算权限还有多少天过期"""
        if not self.expires_at:
            return None
        
        delta = self.expires_at - datetime.now()
        return max(0, delta.days)
