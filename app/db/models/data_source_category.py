from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import List, Optional, TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .data_source_grade import DataSourceGrade
    from .knowledge_document import KnowledgeDocument

class DataSourceCategory(Base):
    """数据源分类表"""
    __tablename__ = "data_source_categories"

    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="数据源分类ID"
    )
    name: Mapped[str] = mapped_column(
        String(100), 
        nullable=False, 
        unique=True, 
        index=True,
        comment="分类名称"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="描述"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    grades: Mapped[List["DataSourceGrade"]] = relationship(
        "DataSourceGrade",
        back_populates="category",
        cascade="all, delete-orphan",
        lazy="select"
    )
    knowledge_documents: Mapped[List["KnowledgeDocument"]] = relationship(
        "KnowledgeDocument",
        back_populates="category",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )

    def __repr__(self):
        return f"<DataSourceCategory(id={self.id}, name='{self.name}')>"
    
    @classmethod
    def get_by_name(cls, db_session, name: str):
        """根据名称获取分类"""
        return db_session.query(cls).filter(cls.name == name).first()
    
    @classmethod
    def get_all_active(cls, db_session):
        """获取所有活跃的分类"""
        return db_session.query(cls).order_by(cls.name).all()
    
    def get_grades_count(self) -> int:
        """获取该分类下的分级数量"""
        return len(self.grades) 