from __future__ import annotations
from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime
from enum import Enum

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .session import Session
    from .user import User

class SenderType(str, Enum):
    """消息发送者类型枚举"""
    USER = "user"
    AI = "ai"
    SYSTEM = "system"

class Message(Base):
    """消息模型 - 存储对话消息和安全检查结果"""
    __tablename__ = "messages"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="消息ID，主键")
    
    # 关联字段
    session_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("sessions.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="所属会话ID"
    )
    user_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="消息发送用户ID"
    )
    
    # 消息内容
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="消息内容")
    sender_type: Mapped[SenderType] = mapped_column(
        String(10), 
        nullable=False, 
        default=SenderType.USER,
        comment="发送者类型: user/ai/system"
    )
    
    # 安全检查相关字段
    is_blocked: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否被安全系统拦截")
    block_reason: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="拦截原因")
    security_check_passed: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="安全检查是否通过")
    
    # 安全检查详情 - 存储JSON格式的检查结果
    security_check_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, 
        nullable=True,
        comment="安全检查详细结果，包含各项检查的具体信息"
    )
    
    # AI相关字段
    ai_model_used: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="使用的AI模型名称")
    ai_response_time: Mapped[Optional[float]] = mapped_column(nullable=True, comment="AI响应时间（秒）")
    ai_token_count: Mapped[Optional[int]] = mapped_column(nullable=True, comment="AI响应使用的token数量")
    
    # 状态字段
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否已删除（软删除）")
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True, comment="删除时间")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    session: Mapped[Session] = relationship(
        "Session",
        back_populates="messages",
        lazy="select"
    )
    
    user: Mapped[User] = relationship(
        "User",
        back_populates="messages",
        lazy="select"
    )

    def __repr__(self):
        return f"<Message(id={self.id}, session_id={self.session_id}, sender_type='{self.sender_type}', content='{self.content[:50]}...')>"
    
    @property
    def content_preview(self) -> str:
        """获取消息内容预览（限制长度）"""
        if len(self.content) <= 100:
            return self.content
        return self.content[:100] + "..."
    
    @property
    def is_secure(self) -> bool:
        """判断消息是否安全"""
        return self.security_check_passed and not self.is_blocked
    
    @property
    def security_score(self) -> int:
        """获取安全评分"""
        if not self.security_check_details:
            return 100 if self.is_secure else 0
        return self.security_check_details.get('score', 100 if self.is_secure else 0)
    
    def mark_as_deleted(self) -> None:
        """标记消息为已删除（软删除）"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """恢复已删除的消息"""
        self.is_deleted = False
        self.deleted_at = None
    
    def update_security_check(self, passed: bool, details: Optional[Dict[str, Any]] = None, 
                            is_blocked: bool = False, block_reason: Optional[str] = None) -> None:
        """更新安全检查结果"""
        self.security_check_passed = passed
        self.is_blocked = is_blocked
        self.block_reason = block_reason
        if details:
            self.security_check_details = details
    
    def set_ai_metadata(self, model_name: str, response_time: float, token_count: Optional[int] = None) -> None:
        """设置AI相关元数据"""
        self.ai_model_used = model_name
        self.ai_response_time = response_time
        self.ai_token_count = token_count 