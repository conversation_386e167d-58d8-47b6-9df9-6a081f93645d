from sqlalchemy import Integer, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .role import Role
    from .keyword_group import KeywordGroup

class RoleKeywordGroupAssignment(Base):
    """角色关键词组分配表"""
    __tablename__ = "role_keyword_group_assignments"

    role_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        primary_key=True,
        comment="角色ID (外键)"
    )
    keyword_group_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("keyword_groups.id", ondelete="CASCADE"), 
        primary_key=True,
        comment="关键词组ID (外键)"
    )

    # 关系映射
    role: Mapped["Role"] = relationship(
        "Role",
        back_populates="keyword_group_assignments",
        lazy="select"
    )
    keyword_group: Mapped["KeywordGroup"] = relationship(
        "KeywordGroup",
        back_populates="role_assignments",
        lazy="select"
    )

    def __repr__(self):
        return f"<RoleKeywordGroupAssignment(role_id={self.role_id}, keyword_group_id={self.keyword_group_id})>"

    @classmethod
    def get_role_keyword_groups(cls, db_session, role_id: int) -> list:
        """获取角色分配的关键词组列表"""
        assignments = db_session.query(cls).filter(
            cls.role_id == role_id
        ).all()
        return [assignment.keyword_group for assignment in assignments]
    
    @classmethod
    def is_keyword_group_assigned(cls, db_session, role_id: int, keyword_group_id: int) -> bool:
        """检查关键词组是否分配给指定角色"""
        assignment = db_session.query(cls).filter(
            cls.role_id == role_id,
            cls.keyword_group_id == keyword_group_id
        ).first()
        return assignment is not None 