# app/db/models/role.py

from __future__ import annotations  # 启用前向引用
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import List, Optional, TYPE_CHECKING
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .user import User
    from .level import Level
    from .regex_rule import RegexRule
    from .desensitization_rule import DesensitizationRule
    from .security_policy import SecurityPolicy
    from .role_module_permission import RoleModulePermission
    from .role_keyword_assignment import RoleKeywordGroupAssignment
    from .role_data_grade_access import RoleDataGradeAccess
    from .role_feature_permission import RoleFeaturePermission
    from .audit_log import AuditLog

class Role(Base):
    __tablename__ = "roles"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="角色ID，主键")
    name: Mapped[str] = mapped_column(String(50), nullable=False, unique=True, index=True, comment="角色名称，唯一")
    description: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="角色描述")
    
    # 添加角色状态控制
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="角色是否激活")
    
    # 添加是否为系统内置角色标识（防止误删系统关键角色）
    is_system: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否为系统内置角色")
    
    # 添加时间戳 - 修复TIMESTAMP类型问题
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 修复关系映射配置 - 解决类型检查问题
    users: Mapped[List[User]] = relationship(
        "User",  # 字符串引用避免循环导入
        back_populates="role", 
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与levels的关系
    levels: Mapped[List[Level]] = relationship(
        "Level",
        back_populates="role",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与regex_rules的关系
    regex_rules: Mapped[List[RegexRule]] = relationship(
        "RegexRule",
        back_populates="role",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与desensitization_rules的关系
    desensitization_rules: Mapped[List[DesensitizationRule]] = relationship(
        "DesensitizationRule",
        back_populates="role",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 添加与security_policies的关系
    security_policies: Mapped[List[SecurityPolicy]] = relationship(
        "SecurityPolicy",
        back_populates="role",
        cascade="save-update, merge, refresh-expire",
        passive_deletes=False,
        lazy="select"
    )
    
    # 新增权限管理关系
    module_permissions: Mapped[List[RoleModulePermission]] = relationship(
        "RoleModulePermission",
        back_populates="role",
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    keyword_group_assignments: Mapped[List[RoleKeywordGroupAssignment]] = relationship(
        "RoleKeywordGroupAssignment",
        back_populates="role",
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    data_grade_accesses: Mapped[List[RoleDataGradeAccess]] = relationship(
        "RoleDataGradeAccess",
        back_populates="role",
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    # 功能级权限关系
    feature_permissions: Mapped[List[RoleFeaturePermission]] = relationship(
        "RoleFeaturePermission",
        back_populates="role",
        cascade="all, delete-orphan",
        lazy="select"
    )

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}', is_active={self.is_active})>"
        
    @property
    def user_count(self) -> int:
        """获取该角色下的用户数量"""
        return len(self.users) if self.users else 0
        
    @property
    def level_count(self) -> int:
        """获取该角色下的级别数量"""
        return len(self.levels) if self.levels else 0
        
    def can_be_deleted(self) -> bool:
        """检查角色是否可以被删除"""
        # 系统内置角色不能删除
        if self.is_system:
            return False
        # 如果还有关联用户，不能删除
        if self.user_count > 0:
            return False
        # 如果还有关联级别，不能删除
        if self.level_count > 0:
            return False
        return True
