from __future__ import annotations
from sqlalchemy import <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from datetime import datetime
from typing import TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User
    from .llm_model import LLMModel

class UserModelAssignment(Base):
    __tablename__ = "user_model_assignments"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, index=True, comment="分配记录ID，主键")
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    model_id: Mapped[int] = mapped_column(Integer, ForeignKey("llm_models.id", ondelete="CASCADE"), nullable=False, index=True, comment="模型ID")
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否为默认模型")
    assigned_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="分配者用户ID")
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="分配时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    user: Mapped[User] = relationship(
        "User",
        foreign_keys=[user_id],
        lazy="select"
    )
    
    model: Mapped[LLMModel] = relationship(
        "LLMModel",
        back_populates="user_assignments",
        lazy="select"
    )
    
    assigner: Mapped[User] = relationship(
        "User",
        foreign_keys=[assigned_by],
        lazy="select"
    )

    def __repr__(self):
        return f"<UserModelAssignment(user_id={self.user_id}, model_id={self.model_id}, is_default={self.is_default})>"
    
    @property
    def model_name(self) -> str:
        """获取分配的模型名称"""
        return self.model.name if self.model else "Unknown"
    
    @property
    def user_name(self) -> str:
        """获取用户名称"""
        return self.user.username if self.user else "Unknown"
    
    @property 
    def assigner_name(self) -> str:
        """获取分配者名称"""
        return self.assigner.username if self.assigner else "Unknown" 