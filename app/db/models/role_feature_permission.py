from sqlalchemy import <PERSON>umn, Integer, String, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .role import Role

class RoleFeaturePermission(Base):
    """角色功能权限表 - 支持细粒度的功能级权限控制"""
    __tablename__ = "role_feature_permissions"

    role_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        primary_key=True,
        comment="角色ID (外键)"
    )
    module_name: Mapped[str] = mapped_column(
        String(100), 
        primary_key=True,
        comment="模块名称 (例如: KNOWLEDGE_BASE)"
    )
    feature_name: Mapped[str] = mapped_column(
        String(100), 
        primary_key=True,
        comment="功能名称 (例如: CHAT, DOCUMENTS, MODELS, USER_PERMISSIONS)"
    )
    can_access: Mapped[bool] = mapped_column(
        <PERSON>olean, 
        default=False, 
        nullable=False,
        comment="是否可访问"
    )
    can_create: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否可创建"
    )
    can_read: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否可读取"
    )
    can_update: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否可更新"
    )
    can_delete: Mapped[bool] = mapped_column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否可删除"
    )
    description: Mapped[str] = mapped_column(
        Text,
        nullable=True,
        comment="权限描述"
    )

    # 关系映射
    role: Mapped["Role"] = relationship(
        "Role",
        back_populates="feature_permissions",
        lazy="select"
    )

    def __repr__(self):
        return f"<RoleFeaturePermission(role_id={self.role_id}, module={self.module_name}, feature={self.feature_name})>"

    @classmethod
    def get_user_feature_permissions(cls, db_session, user_role_id: int, module_name: str = None) -> dict:
        """获取用户的功能权限字典"""
        query = db_session.query(cls).filter(
            cls.role_id == user_role_id,
            cls.can_access == True
        )
        
        if module_name:
            query = query.filter(cls.module_name == module_name)
            
        permissions = query.all()
        
        result = {}
        for perm in permissions:
            if perm.module_name not in result:
                result[perm.module_name] = {}
            
            result[perm.module_name][perm.feature_name] = {
                'can_access': perm.can_access,
                'can_create': perm.can_create,
                'can_read': perm.can_read,
                'can_update': perm.can_update,
                'can_delete': perm.can_delete,
                'description': perm.description
            }
        
        return result
    
    @classmethod
    def has_feature_permission(cls, db_session, user_role_id: int, module_name: str, 
                              feature_name: str, action: str = 'access') -> bool:
        """检查用户是否有特定功能的特定操作权限"""
        permission = db_session.query(cls).filter(
            cls.role_id == user_role_id,
            cls.module_name == module_name,
            cls.feature_name == feature_name,
            cls.can_access == True
        ).first()
        
        if not permission:
            return False
            
        action_map = {
            'access': permission.can_access,
            'create': permission.can_create,
            'read': permission.can_read,
            'update': permission.can_update,
            'delete': permission.can_delete
        }
        
        return action_map.get(action, False)

    @classmethod
    def get_knowledge_base_permissions(cls, db_session, user_role_id: int) -> dict:
        """获取知识库模块的详细权限"""
        permissions = cls.get_user_feature_permissions(db_session, user_role_id, 'KNOWLEDGE_BASE')
        return permissions.get('KNOWLEDGE_BASE', {})