from sqlalchemy import <PERSON>umn, Integer, <PERSON>olean, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .level import Level

class LevelDataAccess(Base):
    """级别数据访问权限表 - 管理不同级别用户可访问的数据敏感度"""
    __tablename__ = "level_data_access"

    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="权限ID"
    )
    level_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("levels.id", ondelete="CASCADE"), 
        nullable=False,
        comment="级别ID"
    )
    sensitivity_level: Mapped[int] = mapped_column(
        Integer, 
        nullable=False,
        comment="数据敏感度级别 (1=高级敏感, 2=中级敏感, 3=初级敏感, 4=完全开放)"
    )
    can_access: Mapped[bool] = mapped_column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否允许访问"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

    # 关系映射
    level: Mapped["Level"] = relationship(
        "Level",
        back_populates="data_access_permissions",
        lazy="select"
    )

    # 唯一约束：同一级别对同一敏感度级别只能有一条记录
    __table_args__ = (
        UniqueConstraint('level_id', 'sensitivity_level', name='uk_level_sensitivity'),
    )

    def __repr__(self):
        return f"<LevelDataAccess(level_id={self.level_id}, sensitivity_level={self.sensitivity_level}, can_access={self.can_access})>"

    @classmethod
    def get_accessible_levels_for_user_level(cls, db_session, level_id: int):
        """获取指定级别可访问的所有敏感度级别"""
        return db_session.query(cls).filter(
            cls.level_id == level_id,
            cls.can_access == True
        ).all()

    @classmethod
    def set_level_access(cls, db_session, level_id: int, sensitivity_level: int, can_access: bool):
        """设置级别对某个敏感度的访问权限"""
        existing = db_session.query(cls).filter(
            cls.level_id == level_id,
            cls.sensitivity_level == sensitivity_level
        ).first()
        
        if existing:
            existing.can_access = can_access
        else:
            new_access = cls(
                level_id=level_id,
                sensitivity_level=sensitivity_level,
                can_access=can_access
            )
            db_session.add(new_access)
        
        return existing or new_access 