from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User

class AuditLog(Base):
    """审计日志表"""
    __tablename__ = "audit_logs"

    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="日志ID"
    )
    user_id: Mapped[Optional[int]] = mapped_column(
        Integer, 
        ForeignKey("users.id", ondelete="SET NULL"), 
        nullable=True,
        comment="操作用户ID (系统操作可为空, 外键)"
    )
    action_type: Mapped[str] = mapped_column(
        String(100), 
        nullable=False, 
        index=True,
        comment="行为类型 (例如: LOGIN, CREATE_USER, SEND_MESSAGE_ATTEMPT)"
    )
    target_entity: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="目标实体 (例如: users, sessions, keywords)"
    )
    target_id: Mapped[Optional[int]] = mapped_column(
        Integer, 
        nullable=True,
        comment="目标实体ID"
    )
    details: Mapped[Optional[str]] = mapped_column(
        Text, 
        nullable=True,
        comment="行为详情 (JSON或文本)"
    )
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45), 
        nullable=True,
        comment="IP地址"
    )
    user_agent: Mapped[Optional[str]] = mapped_column(
        String(500), 
        nullable=True,
        comment="用户代理"
    )
    session_id: Mapped[Optional[str]] = mapped_column(
        String(100), 
        nullable=True,
        comment="会话ID"
    )
    result: Mapped[Optional[str]] = mapped_column(
        String(20), 
        nullable=True,
        comment="操作结果 (SUCCESS, FAILED, BLOCKED)"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        index=True,
        comment="创建时间"
    )

    # 关系映射
    user: Mapped[Optional["User"]] = relationship(
        "User",
        back_populates="audit_logs",
        lazy="select"
    )

    # 索引
    __table_args__ = (
        Index('idx_audit_user_action', 'user_id', 'action_type'),
        Index('idx_audit_entity_target', 'target_entity', 'target_id'),
        Index('idx_audit_created_at_desc', 'created_at'),
    )

    def __repr__(self):
        return f"<AuditLog(id={self.id}, action='{self.action_type}', user_id={self.user_id}, target='{self.target_entity}:{self.target_id}')>"
    
    @classmethod
    def create_log(cls, db_session, action_type: str, user_id: Optional[int] = None, 
                   target_entity: Optional[str] = None, target_id: Optional[int] = None,
                   details: Optional[str] = None, ip_address: Optional[str] = None,
                   user_agent: Optional[str] = None, session_id: Optional[str] = None,
                   result: Optional[str] = None):
        """创建审计日志记录"""
        log = cls(
            user_id=user_id,
            action_type=action_type,
            target_entity=target_entity,
            target_id=target_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            result=result
        )
        db_session.add(log)
        return log
    
    @classmethod
    def get_user_logs(cls, db_session, user_id: int, limit: int = 100):
        """获取用户的审计日志"""
        return db_session.query(cls).filter(
            cls.user_id == user_id
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_by_action_type(cls, db_session, action_type: str, limit: int = 100):
        """根据操作类型获取日志"""
        return db_session.query(cls).filter(
            cls.action_type == action_type
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_recent_logs(cls, db_session, limit: int = 100):
        """获取最近的审计日志"""
        return db_session.query(cls).order_by(
            cls.created_at.desc()
        ).limit(limit).all()
    
    @classmethod
    def count_by_action_and_result(cls, db_session, action_type: str, result: str, 
                                   start_time: Optional[datetime] = None, 
                                   end_time: Optional[datetime] = None) -> int:
        """统计指定条件下的日志数量"""
        query = db_session.query(cls).filter(
            cls.action_type == action_type,
            cls.result == result
        )
        
        if start_time:
            query = query.filter(cls.created_at >= start_time)
        if end_time:
            query = query.filter(cls.created_at <= end_time)
            
        return query.count() 