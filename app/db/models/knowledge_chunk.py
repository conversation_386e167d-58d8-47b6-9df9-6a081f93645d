# app/db/models/knowledge_chunk.py

from __future__ import annotations
from sqlalchemy import Column, String, Text, Integer, ForeignKey, JSON, Float, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Optional, List, TYPE_CHECKING, Dict, Any
from datetime import datetime

from app.db.base_class import Base

if TYPE_CHECKING:
    from .knowledge_document import KnowledgeDocument


class KnowledgeChunk(Base):
    """知识库文档块模型 - 存储向量化后的文档片段"""
    __tablename__ = "knowledge_chunks"

    # 基本信息
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, index=True, comment="文档块ID")
    document_id: Mapped[int] = mapped_column(Integer, ForeignKey("knowledge_documents.id"), nullable=False, comment="所属文档ID")
    
    # 内容信息
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="文档块内容")
    chunk_index: Mapped[int] = mapped_column(Integer, nullable=False, comment="块在文档中的索引")
    start_char: Mapped[int] = mapped_column(Integer, nullable=False, comment="在原文档中的起始字符位置")
    end_char: Mapped[int] = mapped_column(Integer, nullable=False, comment="在原文档中的结束字符位置")
    
    # 向量信息
    vector_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, unique=True, comment="向量数据库中的ID")
    embedding_dimension: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="嵌入向量维度")
    
    # 元数据
    chunk_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="块元数据")
    page_number: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="所在页码（如果适用）")
    section_title: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="所在章节标题")
    
    # 搜索相关
    relevance_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="搜索相关性分数（临时）")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    document: Mapped[KnowledgeDocument] = relationship(
        "KnowledgeDocument",
        back_populates="chunks",
        lazy="select"
    )
    
    def __repr__(self):
        return f"<KnowledgeChunk(id={self.id}, document_id={self.document_id}, index={self.chunk_index})>"
    
    def to_dict(self, include_content: bool = True) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "id": self.id,
            "document_id": self.document_id,
            "chunk_index": self.chunk_index,
            "start_char": self.start_char,
            "end_char": self.end_char,
            "vector_id": self.vector_id,
            "page_number": self.page_number,
            "section_title": self.section_title,
            "relevance_score": self.relevance_score,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
        
        if include_content:
            result["content"] = self.content
            
        return result
    
    def get_context(self, db, context_size: int = 1) -> List[KnowledgeChunk]:
        """获取上下文块（前后相邻的块）"""
        chunks = []
        
        # 获取前面的块
        for i in range(1, context_size + 1):
            prev_chunk = db.query(KnowledgeChunk).filter(
                KnowledgeChunk.document_id == self.document_id,
                KnowledgeChunk.chunk_index == self.chunk_index - i
            ).first()
            if prev_chunk:
                chunks.insert(0, prev_chunk)
        
        # 添加当前块
        chunks.append(self)
        
        # 获取后面的块
        for i in range(1, context_size + 1):
            next_chunk = db.query(KnowledgeChunk).filter(
                KnowledgeChunk.document_id == self.document_id,
                KnowledgeChunk.chunk_index == self.chunk_index + i
            ).first()
            if next_chunk:
                chunks.append(next_chunk)
        
        return chunks 