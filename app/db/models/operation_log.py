from __future__ import annotations
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User

class OperationLog(Base):
    __tablename__ = "operation_logs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, index=True, comment="日志ID，主键")
    operator_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="操作者用户ID")
    target_user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True, comment="被操作的用户ID")
    operation_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True, comment="操作类型")
    operation_details: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="操作详情")
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True, comment="操作IP地址")
    user_agent: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="用户代理")
    success: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="操作是否成功")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        index=True,
        comment="操作时间"
    )

    # 关系映射
    operator: Mapped[User] = relationship(
        "User",
        foreign_keys=[operator_id],
        lazy="select"
    )
    
    target_user: Mapped[Optional[User]] = relationship(
        "User",
        foreign_keys=[target_user_id],
        lazy="select"
    )

    def __repr__(self):
        return f"<OperationLog(id={self.id}, operator_id={self.operator_id}, operation_type='{self.operation_type}', success={self.success})>"
    
    @property
    def operator_name(self) -> str:
        """获取操作者名称"""
        return self.operator.username if self.operator else "Unknown"
    
    @property
    def target_user_name(self) -> Optional[str]:
        """获取被操作用户名称"""
        return self.target_user.username if self.target_user else None
    
    @property
    def status_text(self) -> str:
        """获取操作状态文本"""
        return "成功" if self.success else "失败"
    
    def to_dict(self) -> dict:
        """转换为字典格式，便于序列化"""
        return {
            "id": self.id,
            "operator_id": self.operator_id,
            "operator_name": self.operator_name,
            "target_user_id": self.target_user_id,
            "target_user_name": self.target_user_name,
            "operation_type": self.operation_type,
            "operation_details": self.operation_details,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "success": self.success,
            "status_text": self.status_text,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None
        } 