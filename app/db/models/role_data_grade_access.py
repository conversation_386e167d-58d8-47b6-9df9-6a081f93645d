from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import TYPE_CHECKING

from app.db.base_class import Base

if TYPE_CHECKING:
    from .role import Role
    from .data_source_grade import DataSourceGrade

class RoleDataGradeAccess(Base):
    """角色数据分级访问表"""
    __tablename__ = "role_data_grade_access"

    role_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        primary_key=True,
        comment="角色ID (外键)"
    )
    data_source_grade_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("data_source_grades.id", ondelete="CASCADE"), 
        primary_key=True,
        comment="数据源分级ID (外键)"
    )
    access_allowed: Mapped[bool] = mapped_column(
        <PERSON><PERSON>an, 
        default=False, 
        nullable=False,
        comment="是否允许访问"
    )

    # 关系映射
    role: Mapped["Role"] = relationship(
        "Role",
        back_populates="data_grade_accesses",
        lazy="select"
    )
    data_source_grade: Mapped["DataSourceGrade"] = relationship(
        "DataSourceGrade",
        back_populates="role_accesses",
        lazy="select"
    )

    def __repr__(self):
        return f"<RoleDataGradeAccess(role_id={self.role_id}, grade_id={self.data_source_grade_id}, allowed={self.access_allowed})>"

    @classmethod
    def get_role_accessible_grades(cls, db_session, role_id: int) -> list:
        """获取角色可访问的数据分级列表"""
        accesses = db_session.query(cls).filter(
            cls.role_id == role_id,
            cls.access_allowed == True
        ).all()
        return [access.data_source_grade for access in accesses]
    
    @classmethod
    def has_grade_access(cls, db_session, role_id: int, grade_id: int) -> bool:
        """检查角色是否有特定数据分级的访问权限"""
        access = db_session.query(cls).filter(
            cls.role_id == role_id,
            cls.data_source_grade_id == grade_id,
            cls.access_allowed == True
        ).first()
        return access is not None
    
    @classmethod
    def can_access_sensitivity_level(cls, db_session, role_id: int, required_level: int) -> bool:
        """
        检查角色是否可以访问指定敏感度等级的数据
        required_level: 1=高级敏感, 2=中级敏感, 3=初级敏感, 4=完全开放
        """
        accessible_grades = cls.get_role_accessible_grades(db_session, role_id)
        
        # 检查是否有任何可访问的分级满足要求的敏感度等级
        for grade in accessible_grades:
            if grade.get_sensitivity_level() <= required_level:
                return True
        
        return False 