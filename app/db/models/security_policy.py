from __future__ import annotations
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import TYPE_CHECKING, Optional, Dict, Any
from datetime import datetime

from app.db.base_class import Base

# 仅在类型检查时导入，避免运行时循环导入
if TYPE_CHECKING:
    from .role import Role
    from .level import Level
    from .user import User

class SecurityPolicy(Base):
    __tablename__ = "security_policies"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True, comment="策略ID，主键")
    
    # 策略基本信息
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True, comment="策略名称")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="策略描述")
    
    # 关联目标
    role_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("roles.id"), nullable=True, index=True, comment="关联角色ID")
    level_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("levels.id"), nullable=True, index=True, comment="关联等级ID")
    user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, index=True, comment="关联用户ID")
    
    # 基础配置
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="策略是否启用")
    max_severity_threshold: Mapped[str] = mapped_column(String(20), default="medium", nullable=False, comment="最大严重性阈值")
    enable_early_termination: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否启用早期终止")
    timeout_seconds: Mapped[int] = mapped_column(Integer, default=30, nullable=False, comment="超时时间(秒)")
    
    # 关键词过滤配置
    keyword_enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否启用关键词过滤")
    keyword_priority_threshold: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="关键词优先级阈值")
    allowed_keyword_groups: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="允许的关键词分组ID列表(JSON)")
    blocked_keyword_groups: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="禁止的关键词分组ID列表(JSON)")
    
    # 绕过配置
    bypass_enabled: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, comment="是否允许绕过")
    bypassable_modules: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="可绕过的模块列表(JSON)")
    
    # 模块配置
    enabled_modules: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="启用的模块列表(JSON)")
    disabled_modules: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="禁用的模块列表(JSON)")
    module_configs: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="模块特定配置(JSON)")
    
    # 行为配置
    auto_desensitize: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否自动脱敏")
    block_on_critical: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="严重威胁时是否阻止")
    log_all_requests: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否记录所有请求")
    
    # 优先级（数字越小优先级越高）
    priority: Mapped[int] = mapped_column(Integer, default=100, nullable=False, comment="策略优先级")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="创建者")

    # 关系映射
    role: Mapped[Optional[Role]] = relationship(
        "Role", 
        back_populates="security_policies",
        lazy="select"
    )
    
    level: Mapped[Optional[Level]] = relationship(
        "Level", 
        back_populates="security_policies",
        lazy="select"
    )
    
    user: Mapped[Optional[User]] = relationship(
        "User", 
        back_populates="security_policies",
        lazy="select"
    )

    def __repr__(self):
        return f"<SecurityPolicy(id={self.id}, name='{self.name}', target={self.get_target_description()})>"
        
    def get_target_description(self) -> str:
        """获取策略目标描述"""
        if self.user_id:
            return f"User-{self.user_id}"
        elif self.level_id:
            return f"Level-{self.level_id}"
        elif self.role_id:
            return f"Role-{self.role_id}"
        else:
            return "Global"
    
    def to_policy_dict(self) -> Dict[str, Any]:
        """转换为策略字典格式，供策略管理器使用"""
        import json
        
        try:
            enabled_modules = json.loads(self.enabled_modules) if self.enabled_modules else []
        except:
            enabled_modules = ["keyword_filter", "regex_pattern", "malicious_intent", "data_desensitization"]
            
        try:
            disabled_modules = json.loads(self.disabled_modules) if self.disabled_modules else []
        except:
            disabled_modules = []
            
        try:
            bypassable_modules = json.loads(self.bypassable_modules) if self.bypassable_modules else []
        except:
            bypassable_modules = []
            
        try:
            module_configs = json.loads(self.module_configs) if self.module_configs else {}
        except:
            module_configs = {}
            
        try:
            allowed_keyword_groups = json.loads(self.allowed_keyword_groups) if self.allowed_keyword_groups else []
        except:
            allowed_keyword_groups = []
            
        try:
            blocked_keyword_groups = json.loads(self.blocked_keyword_groups) if self.blocked_keyword_groups else []
        except:
            blocked_keyword_groups = []
        
        return {
            "enabled": self.enabled,
            "max_severity_threshold": self.max_severity_threshold,
            "enable_early_termination": self.enable_early_termination,
            "timeout_seconds": self.timeout_seconds,
            "enabled_modules": enabled_modules,
            "disabled_modules": disabled_modules,
            "module_configs": module_configs,
            "bypass_enabled": self.bypass_enabled,
            "bypassable_modules": bypassable_modules,
            "auto_desensitize": self.auto_desensitize,
            "block_on_critical": self.block_on_critical,
            "log_all_requests": self.log_all_requests,
            "keyword_enabled": self.keyword_enabled,
            "keyword_priority_threshold": self.keyword_priority_threshold,
            "allowed_keyword_groups": allowed_keyword_groups,
            "blocked_keyword_groups": blocked_keyword_groups
        } 