# app/db/base.py

# Import base class
from app.db.base_class import Base

# Import all models to ensure they are registered with SQLAlchemy
# This must be done to avoid "failed to locate a name" errors when using relationships

# 导入所有模型以确保它们被注册到SQLAlchemy中
from app.db.models.user import User
from app.db.models.role import Role  
from app.db.models.level import Level
from app.db.models.session import Session
from app.db.models.message import Message
from app.db.models.llm_model import LLMModel
from app.db.models.keyword_group import KeywordGroup
from app.db.models.keyword import Keyword
from app.db.models.regex_rule import RegexRule
from app.db.models.desensitization_rule import DesensitizationRule
from app.db.models.security_policy import SecurityPolicy  # 新添加的安全策略模型

# Export Base for other modules
__all__ = ["Base"] 