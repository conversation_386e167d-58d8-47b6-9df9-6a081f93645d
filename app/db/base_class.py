# app/db/base_class.py

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import <PERSON>I<PERSON>ger # For id to match BIGINT UNSIGNED
from typing import Annotated

# Recommended to use a type annotation for the primary key
# For SQLAlchemy 2.0 style with type annotations
# See: https://docs.sqlalchemy.org/en/20/orm/declarative_tables.html#mapped-column-derives-the-datatype-and-nullability-from-the-type-annotation
# and https://docs.sqlalchemy.org/en/20/orm/declarative_config.html#adding-a-declarative-base-class-with-type-annotations

# Create the base class for declarative models
Base = declarative_base()

# You can still define a class that all your models inherit from if you want to add common methods or properties
# that are not directly related to SQLAlchemy's declarative system, but your models should inherit from the
# 'Base' created by declarative_base() for SQLAlchemy features.
# Example:
# class ModelBase:
#     def to_dict(self):
#         # a common method
#         pass
#
# And then in your models:
# from .base_class import Base, ModelBase
# class User(Base, ModelBase):
#     __tablename__ = "users"
#     ...

# Example of a common primary key type annotation for BIGINT
# This can be imported and used in your models.
# id_pk = Annotated[int, mapped_column(BigInteger, primary_key=True, autoincrement=True, index=True)]
