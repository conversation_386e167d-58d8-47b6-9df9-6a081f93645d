# app/db/session.py

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from typing import AsyncGenerator, Generator

from app.core.config import settings
# Correctly import Base from base_class
from app.db.base import Base 

if not settings.DATABASE_URL:
    raise RuntimeError("DATABASE_URL not set in environment or .env file")

# Create an asynchronous SQLAlchemy engine
# For SQLite, the URL should be prefixed with 'sqlite+aiosqlite://'
async_database_url = settings.DATABASE_URL
if async_database_url.startswith("sqlite:///"):
    async_database_url = async_database_url.replace("sqlite:///", "sqlite+aiosqlite:///")
elif async_database_url.startswith("sqlite://"):
    # For absolute paths like sqlite:////path/to/db.sqlite
    async_database_url = async_database_url.replace("sqlite://", "sqlite+aiosqlite://")

engine = create_async_engine(async_database_url, echo=False) # Set echo=True for SQL logging

# Create an asynchronous session factory
SessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,  # Good default for async context
    autocommit=False,
    autoflush=False,
)

# 创建同步数据库引擎和会话（用于登录等同步操作）
sync_engine = create_engine(settings.DATABASE_URL, echo=False)
SyncSessionLocal = sessionmaker(
    bind=sync_engine,
    class_=Session,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get an asynchronous database session.
    Ensures the session is closed after the request.
    """
    async with SessionLocal() as session:
        try:
            yield session
            await session.commit() # Commit changes if no exceptions occurred
        except Exception:
            await session.rollback() # Rollback on error
            raise
        finally:
            await session.close()

def get_sync_db() -> Generator[Session, None, None]:
    """
    Dependency to get a synchronous database session.
    Ensures the session is closed after the request.
    """
    db = SyncSessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()

# The init_db function (Base.metadata.create_all) is no longer needed here
# as Alembic handles database schema creation and migrations.
