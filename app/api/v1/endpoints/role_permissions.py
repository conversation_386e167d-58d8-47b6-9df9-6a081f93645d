from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Dict, Any
from pydantic import BaseModel

from app.db.session import get_db
from app.db.models.role import Role
from app.db.models.role_module_permission import RoleModulePermission
from app.db.models.role_keyword_assignment import RoleKeywordGroupAssignment
from app.db.models.role_data_grade_access import RoleDataGradeAccess
from app.db.models.keyword_group import KeywordGroup
from app.db.models.data_source_grade import DataSourceGrade
from app.api.async_deps import get_current_user
from app.db.models.user import User

router = APIRouter()

# Pydantic模型
class ModulePermissionUpdate(BaseModel):
    module_name: str
    can_access: bool

class ModulePermissionResponse(BaseModel):
    module_name: str
    can_access: bool

class KeywordGroupAssignment(BaseModel):
    keyword_group_id: int
    assigned: bool

class DataGradeAccess(BaseModel):
    data_source_grade_id: int
    access_allowed: bool

class RolePermissionsResponse(BaseModel):
    role_id: int
    role_name: str
    module_permissions: List[ModulePermissionResponse]
    keyword_group_assignments: List[int]  # 分配的关键词组ID列表
    data_grade_accesses: List[int]  # 可访问的数据分级ID列表

@router.get("/roles/{role_id}/permissions", response_model=RolePermissionsResponse)
async def get_role_permissions(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定角色的所有权限配置"""

    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )

    # 获取模块权限
    module_permissions_query = await db.execute(
        select(RoleModulePermission).where(RoleModulePermission.role_id == role_id)
    )
    module_permissions = module_permissions_query.scalars().all()

    # 系统所有可用模块列表
    # 注意：知识库相关权限已迁移到功能权限系统，不再使用模块权限
    available_modules = [
        "USER_MANAGEMENT", "ROLE_MANAGEMENT", "KEYWORD_MANAGEMENT",
        "SECURITY_POLICY", "SESSION_MANAGEMENT", "AUDIT_LOGS",
        "SYSTEM_CONFIG", "MESSAGE_MONITORING", "DESENSITIZATION",
        "LLM_MODEL_MANAGEMENT"
    ]

    # 创建权限字典，方便查找
    permission_dict = {perm.module_name: perm.can_access for perm in module_permissions}

    # 确保所有模块都有权限记录，没有的默认为False
    complete_module_permissions = []
    for module_name in available_modules:
        complete_module_permissions.append(
            ModulePermissionResponse(
                module_name=module_name,
                can_access=permission_dict.get(module_name, False)
            )
        )

    # 获取关键词组分配
    keyword_assignments_query = await db.execute(
        select(RoleKeywordGroupAssignment).where(RoleKeywordGroupAssignment.role_id == role_id)
    )
    keyword_assignments = keyword_assignments_query.scalars().all()

    # 获取数据分级访问权限
    data_accesses_query = await db.execute(
        select(RoleDataGradeAccess).where(
            RoleDataGradeAccess.role_id == role_id,
            RoleDataGradeAccess.access_allowed == True
        )
    )
    data_accesses = data_accesses_query.scalars().all()

    return RolePermissionsResponse(
        role_id=role.id,
        role_name=role.name,
        module_permissions=complete_module_permissions,
        keyword_group_assignments=[assignment.keyword_group_id for assignment in keyword_assignments],
        data_grade_accesses=[access.data_source_grade_id for access in data_accesses]
    )

@router.put("/roles/{role_id}/module-permissions")
async def update_role_module_permissions(
    role_id: int,
    permissions: List[ModulePermissionUpdate],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新角色的模块权限"""
    
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # 批量更新权限
    for perm_update in permissions:
        # 查找现有权限记录
        existing_perm_query = await db.execute(
            select(RoleModulePermission).where(
                RoleModulePermission.role_id == role_id,
                RoleModulePermission.module_name == perm_update.module_name
            )
        )
        existing_perm = existing_perm_query.scalar_one_or_none()
        
        if existing_perm:
            # 更新现有权限
            existing_perm.can_access = perm_update.can_access
        else:
            # 创建新权限记录
            new_perm = RoleModulePermission(
                role_id=role_id,
                module_name=perm_update.module_name,
                can_access=perm_update.can_access
            )
            db.add(new_perm)
    
    await db.commit()
    
    return {"message": "Role module permissions updated successfully"}

@router.put("/roles/{role_id}/keyword-groups")
async def update_role_keyword_groups(
    role_id: int,
    assignments: List[KeywordGroupAssignment],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新角色的关键词组分配"""
    
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    for assignment in assignments:
        # 检查关键词组是否存在
        keyword_group = await db.get(KeywordGroup, assignment.keyword_group_id)
        if not keyword_group:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Keyword group {assignment.keyword_group_id} not found"
            )
        
        # 查找现有分配
        existing_assignment_query = await db.execute(
            select(RoleKeywordGroupAssignment).where(
                RoleKeywordGroupAssignment.role_id == role_id,
                RoleKeywordGroupAssignment.keyword_group_id == assignment.keyword_group_id
            )
        )
        existing_assignment = existing_assignment_query.scalar_one_or_none()
        
        if assignment.assigned:
            # 需要分配
            if not existing_assignment:
                new_assignment = RoleKeywordGroupAssignment(
                    role_id=role_id,
                    keyword_group_id=assignment.keyword_group_id
                )
                db.add(new_assignment)
        else:
            # 需要取消分配
            if existing_assignment:
                await db.delete(existing_assignment)
    
    await db.commit()
    
    return {"message": "Role keyword group assignments updated successfully"}

@router.put("/roles/{role_id}/data-grade-access")
async def update_role_data_grade_access(
    role_id: int,
    accesses: List[DataGradeAccess],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新角色的数据分级访问权限"""
    
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    for access in accesses:
        # 检查数据分级是否存在
        data_grade = await db.get(DataSourceGrade, access.data_source_grade_id)
        if not data_grade:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Data source grade {access.data_source_grade_id} not found"
            )
        
        # 查找现有访问权限
        existing_access_query = await db.execute(
            select(RoleDataGradeAccess).where(
                RoleDataGradeAccess.role_id == role_id,
                RoleDataGradeAccess.data_source_grade_id == access.data_source_grade_id
            )
        )
        existing_access = existing_access_query.scalar_one_or_none()
        
        if existing_access:
            # 更新现有权限
            existing_access.access_allowed = access.access_allowed
        else:
            # 创建新权限记录
            new_access = RoleDataGradeAccess(
                role_id=role_id,
                data_source_grade_id=access.data_source_grade_id,
                access_allowed=access.access_allowed
            )
            db.add(new_access)
    
    await db.commit()
    
    return {"message": "Role data grade access updated successfully"}

@router.get("/available-modules")
async def get_available_modules(
    current_user: User = Depends(get_current_user)
):
    """获取所有可用的系统模块列表"""
    
    # 系统模块定义
    # 注意：知识库相关权限已迁移到功能权限系统，不再使用模块权限
    available_modules = [
        {"name": "USER_MANAGEMENT", "description": "用户管理"},
        {"name": "ROLE_MANAGEMENT", "description": "角色管理"},
        {"name": "KEYWORD_MANAGEMENT", "description": "关键词管理"},
        {"name": "SECURITY_POLICY", "description": "安全策略管理"},
        {"name": "SESSION_MANAGEMENT", "description": "会话管理"},
        {"name": "AUDIT_LOGS", "description": "审计日志"},
        {"name": "SYSTEM_CONFIG", "description": "系统配置"},
        {"name": "MESSAGE_MONITORING", "description": "消息监控"},
        {"name": "DESENSITIZATION", "description": "数据脱敏管理"},
        {"name": "LLM_MODEL_MANAGEMENT", "description": "LLM模型管理"}
    ]
    
    return {"modules": available_modules}

@router.get("/roles/{role_id}/module-permissions")
async def get_role_module_permissions(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取角色的模块权限"""
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # 获取模块权限
    module_permissions_query = await db.execute(
        select(RoleModulePermission).where(RoleModulePermission.role_id == role_id)
    )
    module_permissions = module_permissions_query.scalars().all()
    
    # 获取有权限的模块列表
    granted_modules = [perm.module_name for perm in module_permissions if perm.can_access]
    
    return {"modules": granted_modules}

@router.get("/roles/{role_id}/keyword-groups")
async def get_role_keyword_groups(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取角色的关键词组分配"""
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # 获取关键词组分配
    try:
        assignments_query = await db.execute(
            select(RoleKeywordGroupAssignment)
            .join(KeywordGroup, RoleKeywordGroupAssignment.keyword_group_id == KeywordGroup.id)
            .where(RoleKeywordGroupAssignment.role_id == role_id)
            .add_columns(KeywordGroup.name, KeywordGroup.description)
        )
        assignments = assignments_query.all()
        
        keyword_groups = [
            {
                "id": assignment.RoleKeywordGroupAssignment.keyword_group_id,
                "name": assignment.name,
                "description": assignment.description
            }
            for assignment in assignments
        ]
    except Exception as e:
        # 如果查询失败，返回空数据
        keyword_groups = []
    
    return {"keyword_groups": keyword_groups}

@router.get("/roles/{role_id}/data-grade-access")
async def get_role_data_grade_access(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取角色的数据分级访问权限"""
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # 暂时返回空数据，避免数据库表不存在的问题
    try:
        # 获取数据分级访问权限
        accesses_query = await db.execute(
            select(RoleDataGradeAccess).where(
                RoleDataGradeAccess.role_id == role_id,
                RoleDataGradeAccess.access_allowed == True
            )
        )
        accesses = accesses_query.scalars().all()
        
        data_grade_access = [
            {
                "data_category": f"数据分级{access.data_source_grade_id}",
                "max_grade": access.data_source_grade_id
            }
            for access in accesses
        ]
        
        return {"data_grade_access": data_grade_access}
    except Exception as e:
        # 如果数据库查询失败，返回空数据
        return {"data_grade_access": []}

# 添加缺失的导入
from sqlalchemy import select 