"""
用户文档权限管理API端点
"""

from typing import Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app import models
from app.db.models.knowledge_document import KnowledgeDocument
from app.api.deps import get_db, get_current_user
from app.db.models.user_document_permission import UserDocumentPermission, PermissionType
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.core.permissions import is_admin, is_super_admin

router = APIRouter()

# Pydantic模型
class PermissionGrantRequest(BaseModel):
    """权限授予请求"""
    user_id: int = Field(..., description="目标用户ID")
    document_id: int = Field(..., description="文档ID")
    permission_type: PermissionType = Field(..., description="权限类型")
    expires_days: Optional[int] = Field(None, description="权限有效期（天数），None表示永不过期")
    reason: Optional[str] = Field(None, description="授权原因")

class PermissionRevokeRequest(BaseModel):
    """权限撤销请求"""
    user_id: int = Field(..., description="目标用户ID")
    document_id: int = Field(..., description="文档ID")
    permission_type: PermissionType = Field(..., description="权限类型")

class BatchPermissionRequest(BaseModel):
    """批量权限操作请求"""
    user_ids: List[int] = Field(..., description="用户ID列表")
    document_ids: List[int] = Field(..., description="文档ID列表")
    permission_types: List[PermissionType] = Field(..., description="权限类型列表")
    expires_days: Optional[int] = Field(None, description="权限有效期（天数）")
    reason: Optional[str] = Field(None, description="操作原因")

class PermissionResponse(BaseModel):
    """权限响应"""
    id: int
    user_id: int
    user_name: Optional[str] = None  # 添加用户名字段
    document_id: int
    document_name: Optional[str] = None  # 添加文档名字段
    permission_type: str
    granted: bool
    granted_by: int
    granted_by_name: Optional[str] = None  # 添加授权者名字段
    granted_at: datetime
    expires_at: Optional[datetime]
    reason: Optional[str]
    is_expired: bool
    days_until_expiry: Optional[int]
    
    class Config:
        from_attributes = True

@router.post("/grant", response_model=PermissionResponse)
def grant_document_permission(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    request: PermissionGrantRequest
) -> Any:
    """
    授予用户文档权限
    """
    # 检查权限
    permission_service = KnowledgeBasePermissionService(db=db)
    if not (is_admin(current_user) or is_super_admin(current_user)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：只有管理员可以授予文档权限"
        )
    
    # 验证目标用户存在
    target_user = db.query(models.User).filter(models.User.id == request.user_id).first()
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="目标用户不存在"
        )
    
    # 验证文档存在
    document = db.query(KnowledgeDocument).filter(
        KnowledgeDocument.id == request.document_id
    ).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )
    
    # 计算过期时间
    expires_at = None
    if request.expires_days:
        expires_at = datetime.now() + timedelta(days=request.expires_days)
    
    # 授予权限
    permission = UserDocumentPermission.grant_permission(
        db_session=db,
        user_id=request.user_id,
        document_id=request.document_id,
        permission_type=request.permission_type,
        granted_by=current_user.id,
        expires_at=expires_at,
        reason=request.reason
    )
    
    db.commit()
    
    # 构造响应
    permission_type_str = permission.permission_type
    if hasattr(permission.permission_type, 'value'):
        permission_type_str = permission.permission_type.value

    response = PermissionResponse(
        id=permission.id,
        user_id=permission.user_id,
        user_name=target_user.username if target_user else None,
        document_id=permission.document_id,
        document_name=document.file_name if document else None,
        permission_type=permission_type_str,
        granted=permission.granted,
        granted_by=permission.granted_by,
        granted_by_name=current_user.username,  # 使用当前用户作为授权者
        granted_at=permission.granted_at,
        expires_at=permission.expires_at,
        reason=permission.reason,
        is_expired=permission.is_expired(),
        days_until_expiry=permission.days_until_expiry()
    )
    
    return response

@router.post("/revoke")
def revoke_document_permission(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    request: PermissionRevokeRequest
) -> Any:
    """
    撤销用户文档权限
    """
    # 检查权限
    if not (is_admin(current_user) or is_super_admin(current_user)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：只有管理员可以撤销文档权限"
        )
    
    # 撤销权限
    permission = UserDocumentPermission.revoke_permission(
        db_session=db,
        user_id=request.user_id,
        document_id=request.document_id,
        permission_type=request.permission_type
    )
    
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限记录不存在"
        )
    
    db.commit()
    
    return {"message": "权限已撤销"}

@router.get("/user/{user_id}/document/{document_id}", response_model=List[PermissionResponse])
def get_user_document_permissions(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    user_id: int,
    document_id: int
) -> Any:
    """
    获取用户对特定文档的权限列表
    """
    # 检查权限
    if not (is_admin(current_user) or is_super_admin(current_user) or current_user.id == user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：只能查看自己的权限或管理员权限"
        )
    
    # 获取权限记录，预加载关联信息
    from sqlalchemy.orm import joinedload
    permissions = db.query(UserDocumentPermission).options(
        joinedload(UserDocumentPermission.user),
        joinedload(UserDocumentPermission.document),
        joinedload(UserDocumentPermission.granter)
    ).filter(
        UserDocumentPermission.user_id == user_id,
        UserDocumentPermission.document_id == document_id,
        UserDocumentPermission.granted == True
    ).all()
    
    # 过滤掉过期的权限
    valid_permissions = []
    for perm in permissions:
        if not perm.expires_at or perm.expires_at > datetime.now():
            valid_permissions.append(perm)
    
    responses = []
    for perm in valid_permissions:
        # 处理权限类型，可能是枚举或字符串
        permission_type_str = perm.permission_type
        if hasattr(perm.permission_type, 'value'):
            permission_type_str = perm.permission_type.value

        responses.append(PermissionResponse(
            id=perm.id,
            user_id=perm.user_id,
            user_name=perm.user.username if perm.user else None,
            document_id=perm.document_id,
            document_name=perm.document.file_name if perm.document else None,
            permission_type=permission_type_str,
            granted=perm.granted,
            granted_by=perm.granted_by,
            granted_by_name=perm.granter.username if perm.granter else None,
            granted_at=perm.granted_at,
            expires_at=perm.expires_at,
            reason=perm.reason,
            is_expired=perm.is_expired(),
            days_until_expiry=perm.days_until_expiry()
        ))
    
    return responses

@router.get("/user/{user_id}/accessible-documents")
def get_user_accessible_documents(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    user_id: int,
    permission_type: PermissionType = Query(PermissionType.READ, description="权限类型")
) -> Any:
    """
    获取用户可访问的文档列表
    """
    # 检查权限
    if not (is_admin(current_user) or is_super_admin(current_user) or current_user.id == user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：只能查看自己的可访问文档或管理员权限"
        )
    
    document_ids = UserDocumentPermission.get_accessible_documents(
        db_session=db,
        user_id=user_id,
        permission_type=permission_type
    )
    
    # 获取文档详情
    documents = db.query(KnowledgeDocument).filter(
        KnowledgeDocument.id.in_(document_ids)
    ).all()
    
    # 处理权限类型和文档状态
    permission_type_str = permission_type.value if hasattr(permission_type, 'value') else permission_type

    return {
        "user_id": user_id,
        "permission_type": permission_type_str,
        "accessible_document_count": len(document_ids),
        "documents": [
            {
                "id": doc.id,
                "title": doc.title,
                "file_name": doc.file_name,
                "status": doc.status.value if hasattr(doc.status, 'value') else doc.status,
                "created_at": doc.created_at
            }
            for doc in documents
        ]
    }

@router.get("/list", response_model=List[PermissionResponse])
def list_all_permissions(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数")
) -> Any:
    """
    获取所有用户文档权限列表
    """
    # 检查权限
    if not (is_admin(current_user) or is_super_admin(current_user)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：只有管理员可以查看所有权限"
        )

    # 获取所有权限记录，预加载用户和文档信息
    from sqlalchemy.orm import joinedload
    permissions = db.query(UserDocumentPermission).options(
        joinedload(UserDocumentPermission.user),
        joinedload(UserDocumentPermission.document),
        joinedload(UserDocumentPermission.granter)
    ).offset(skip).limit(limit).all()

    responses = []
    for perm in permissions:
        # 处理权限类型，可能是枚举或字符串
        permission_type_str = perm.permission_type
        if hasattr(perm.permission_type, 'value'):
            permission_type_str = perm.permission_type.value

        responses.append(PermissionResponse(
            id=perm.id,
            user_id=perm.user_id,
            user_name=perm.user.username if perm.user else None,
            document_id=perm.document_id,
            document_name=perm.document.file_name if perm.document else None,
            permission_type=permission_type_str,
            granted=perm.granted,
            granted_by=perm.granted_by,
            granted_by_name=perm.granter.username if perm.granter else None,
            granted_at=perm.granted_at,
            expires_at=perm.expires_at,
            reason=perm.reason,
            is_expired=perm.is_expired(),
            days_until_expiry=perm.days_until_expiry()
        ))

    return responses

@router.post("/batch-grant")
def batch_grant_permissions(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    request: BatchPermissionRequest
) -> Any:
    """
    批量授予权限
    """
    # 检查权限
    if not (is_admin(current_user) or is_super_admin(current_user)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：只有管理员可以批量授予权限"
        )

    # 计算过期时间
    expires_at = None
    if request.expires_days:
        expires_at = datetime.now() + timedelta(days=request.expires_days)

    granted_count = 0
    errors = []

    # 批量授予权限
    for user_id in request.user_ids:
        for document_id in request.document_ids:
            for permission_type in request.permission_types:
                try:
                    UserDocumentPermission.grant_permission(
                        db_session=db,
                        user_id=user_id,
                        document_id=document_id,
                        permission_type=permission_type,
                        granted_by=current_user.id,
                        expires_at=expires_at,
                        reason=request.reason
                    )
                    granted_count += 1
                except Exception as e:
                    errors.append(f"用户{user_id}-文档{document_id}-权限{permission_type.value}: {str(e)}")

    db.commit()

    return {
        "message": f"批量权限授予完成",
        "granted_count": granted_count,
        "total_operations": len(request.user_ids) * len(request.document_ids) * len(request.permission_types),
        "errors": errors
    }
