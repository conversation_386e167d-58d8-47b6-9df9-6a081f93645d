from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app import models, schemas
from app.api import deps
from app.core.security import require_module_permission
from app.db.models.keyword_group import KeywordGroup

router = APIRouter()

@router.get("/", response_model=schemas.KeywordGroupListResponse)
def get_keyword_groups(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT")),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    active_only: bool = Query(False, description="是否只返回激活的分组")
) -> Any:
    """
    获取关键词分组列表（管理员功能）
    """
    
    # 构建查询
    query = db.query(models.KeywordGroup)
    if active_only:
        query = query.filter(models.KeywordGroup.is_active == True)
    
    # 获取总数
    total = query.count()
    
    # 分页获取数据
    groups = query.offset(skip).limit(limit).all()
    
    # 计算额外信息
    items = []
    for group in groups:
        group_dict = {
            "id": group.id,
            "name": group.name,
            "description": group.description,
            "is_active": group.is_active,
            "created_at": group.created_at,
            "updated_at": group.updated_at,
            "keyword_count": group.keyword_count,
            "can_be_deleted": group.can_be_deleted()
        }
        group_data = schemas.KeywordGroupRead(**group_dict)
        items.append(group_data)
    
    return schemas.KeywordGroupListResponse(
        items=items,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )

@router.post("/", response_model=schemas.KeywordGroupSchema)
def create_keyword_group(
    group_in: schemas.KeywordGroupCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    创建新的关键词分组（管理员功能）
    """
    
    # 检查分组名称是否已存在
    existing_group = db.query(models.KeywordGroup).filter(
        models.KeywordGroup.name == group_in.name
    ).first()
    if existing_group:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="分组名称已存在"
        )
    
    # 创建新分组
    db_group = models.KeywordGroup(**group_in.dict())
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    
    # 构建返回数据，包含计算属性
    group_dict = {
        "id": db_group.id,
        "name": db_group.name,
        "description": db_group.description,
        "is_active": db_group.is_active,
        "created_at": db_group.created_at,
        "updated_at": db_group.updated_at,
        "keyword_count": db_group.keyword_count,
        "can_be_deleted": db_group.can_be_deleted()
    }
    
    return schemas.KeywordGroupSchema(**group_dict)

@router.get("/{group_id}", response_model=schemas.KeywordGroupSchema)
def get_keyword_group(
    group_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    获取指定关键词分组的信息（管理员功能）
    """
    
    group = db.query(models.KeywordGroup).filter(
        models.KeywordGroup.id == group_id
    ).first()
    
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词分组不存在"
        )
    
    # 构建返回数据，包含计算属性
    group_dict = {
        "id": group.id,
        "name": group.name,
        "description": group.description,
        "is_active": group.is_active,
        "created_at": group.created_at,
        "updated_at": group.updated_at,
        "keyword_count": group.keyword_count,
        "can_be_deleted": group.can_be_deleted()
    }
    
    return schemas.KeywordGroupSchema(**group_dict)

@router.put("/{group_id}", response_model=schemas.KeywordGroupSchema)
def update_keyword_group(
    group_id: int,
    group_in: schemas.KeywordGroupUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    更新关键词分组（管理员功能）
    """
    
    # 查找要更新的分组
    group = db.query(models.KeywordGroup).filter(
        models.KeywordGroup.id == group_id
    ).first()
    
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词分组不存在"
        )
    
    # 如果要更新名称，检查是否与其他分组重复
    if group_in.name and group_in.name != group.name:
        existing_group = db.query(models.KeywordGroup).filter(
            models.KeywordGroup.name == group_in.name,
            models.KeywordGroup.id != group_id
        ).first()
        if existing_group:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分组名称已存在"
            )
    
    # 更新分组信息
    update_data = group_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(group, field, value)
    
    db.add(group)
    db.commit()
    db.refresh(group)
    
    # 构建返回数据，包含计算属性
    group_dict = {
        "id": group.id,
        "name": group.name,
        "description": group.description,
        "is_active": group.is_active,
        "created_at": group.created_at,
        "updated_at": group.updated_at,
        "keyword_count": group.keyword_count,
        "can_be_deleted": group.can_be_deleted()
    }
    
    return schemas.KeywordGroupSchema(**group_dict)

@router.delete("/{group_id}")
def delete_keyword_group(
    group_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    删除关键词分组（管理员功能）
    """
    
    # 查找要删除的分组
    group = db.query(models.KeywordGroup).filter(
        models.KeywordGroup.id == group_id
    ).first()
    
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词分组不存在"
        )
    
    # 检查是否可以删除
    if not group.can_be_deleted():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该分组下还有关键词，无法删除"
        )
    
    # 删除分组
    db.delete(group)
    db.commit()
    
    return {"message": "关键词分组删除成功"}

@router.get("/{group_id}/keywords", response_model=List[schemas.KeywordSchema])
def get_group_keywords(
    group_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT")),
    active_only: bool = Query(False, description="是否只返回激活的关键词")
) -> Any:
    """
    获取指定分组下的所有关键词（管理员功能）
    """
    
    # 检查分组是否存在
    group = db.query(models.KeywordGroup).filter(
        models.KeywordGroup.id == group_id
    ).first()
    
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词分组不存在"
        )
    
    # 获取关键词
    query = db.query(models.Keyword).filter(
        models.Keyword.keyword_group_id == group_id
    )
    
    if active_only:
        query = query.filter(models.Keyword.is_active == True)
    
    keywords = query.order_by(models.Keyword.priority.desc(), models.Keyword.created_at.asc()).all()
    
    return keywords 