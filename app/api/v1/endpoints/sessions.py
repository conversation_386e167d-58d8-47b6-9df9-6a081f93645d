# app/api/v1/endpoints/sessions.py

from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import and_, or_

from app import crud, models, schemas
from app.api import deps
from app.core.permissions import can_access_session, is_admin
from app.db.session import get_sync_db

router = APIRouter()

@router.post("/", response_model=schemas.SessionRead, status_code=status.HTTP_201_CREATED)
def create_session(
    *,
    db: Session = Depends(get_sync_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    session_in: schemas.SessionCreate
) -> Any:
    """
    Create a new session.
    """
    if not can_access_session(current_user, None):  # 检查创建权限
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough permissions"
        )
    
    # 创建会话对象
    session_data = session_in.model_dump()
    session_data.update({
        "user_id": current_user.id,
        "is_deleted": False
    })
    
    db_session = models.Session(**session_data)
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    
    return db_session

@router.get("/", response_model=List[schemas.SessionRead])
def get_user_sessions(
    db: Session = Depends(get_sync_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0, description="Number of sessions to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of sessions to return"),
    include_deleted: bool = Query(False, description="Include deleted sessions")
) -> Any:
    """
    Retrieve sessions for the current user.
    """
    # 确保current_user不是None
    if current_user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not authenticated"
        )
    
    # 构建查询条件
    if is_admin(current_user):
        # 管理员可以看到所有会话
        query_conditions = []
    else:
        # 普通用户只能看到自己的会话
        query_conditions = [models.Session.user_id == current_user.id]
    
    # 只显示普通聊天会话，排除知识库问答会话(rag类型)
    query_conditions.append(
        or_(
            models.Session.session_type == "chat",
            models.Session.session_type.is_(None)  # 兼容旧数据（没有设置session_type的）
        )
    )
    
    # 是否包含已删除的会话
    if not include_deleted:
        query_conditions.append(models.Session.is_deleted == False)
    
    # 构建查询
    query = db.query(models.Session)
    if query_conditions:
        query = query.filter(and_(*query_conditions))
    
    # 添加排序和分页
    sessions = query.order_by(models.Session.last_activity_at.desc()).offset(skip).limit(limit).all()
    
    return sessions

@router.get("/{session_id}", response_model=schemas.SessionRead)  
def get_session(
    session_id: int,
    db: Session = Depends(get_sync_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Get a specific session by ID.
    """
    session = db.query(models.Session).filter(models.Session.id == session_id).first()
    if not session:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough permissions"
        )
    
    return session

@router.put("/{session_id}", response_model=schemas.SessionRead)
def update_session(
    session_id: int,
    session_update: schemas.SessionUpdate,
    db: Session = Depends(get_sync_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Update a session.
    """
    session = db.query(models.Session).filter(models.Session.id == session_id).first()
    if not session:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough permissions"
        )
    
    # 更新会话数据
    update_data = session_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(session, field):
            setattr(session, field, value)
    
    db.commit()
    db.refresh(session)
    return session

@router.delete("/{session_id}")
def delete_session(
    session_id: int,
    db: Session = Depends(get_sync_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Delete a session (soft delete).
    """
    session = db.query(models.Session).filter(models.Session.id == session_id).first()
    if not session:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough permissions"
        )
    
    # 软删除
    session.is_deleted = True
    db.commit()
    
    return {"message": "Session deleted successfully"}

# 管理员专用端点
@router.get("/admin/all", response_model=List[schemas.SessionRead])
def get_all_sessions(
    db: Session = Depends(get_sync_db),
    current_user: models.User = Depends(deps.get_current_active_admin_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    include_deleted: bool = Query(False)
) -> Any:
    """
    Get all sessions (admin only).
    """
    query = db.query(models.Session)
    if not include_deleted:
        query = query.filter(models.Session.is_deleted == False)
    
    sessions = query.order_by(models.Session.last_activity_at.desc()).offset(skip).limit(limit).all()
    return sessions 