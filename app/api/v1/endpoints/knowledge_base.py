"""
知识库管理API端点
"""
import os
import hashlib
import logging
import json
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, BackgroundTasks, Request
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, text

from app.api.deps import get_db, get_current_active_user
from app.db.models import User, KnowledgeDocument, DataSourceCategory, DataSourceGrade, KnowledgeChunk, KnowledgeBaseModelConfig, LLMModel
from app.db.models.knowledge_document import DocumentStatus
from app.schemas.knowledge_base import (
    KnowledgeDocumentCreate,
    KnowledgeDocumentUpdate,
    KnowledgeDocumentResponse,
    KnowledgeDocumentListResponse,
    DocumentUploadResponse,
    DocumentSearchRequest,
    DocumentSearchResponse,
    SearchResultChunk,
    ProcessingStatus,
    DocumentType,
    DocumentStatistics,
    AdvancedSearchRequest,
    AdvancedSearchResponse,
    SearchResultDocument
)
from app.schemas.knowledge_base_model_config import (
    KnowledgeBaseModelConfigCreate,
    KnowledgeBaseModelConfigUpdate,
    KnowledgeBaseModelConfigResponse,
    KnowledgeBaseModelConfigListResponse,
    KnowledgeBaseModelConfigQuery,
    KnowledgeBaseModelSelection,
    ModelPermissionConfig,
    KnowledgeBaseModelConfigSummary,
    ConfigType
)
from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.knowledge_base_audit import KnowledgeBaseAuditService
from app.services.pipeline.factory import SecurityPipelineFactory
from app.core.permissions import is_admin

# 设置日志记录器
logger = logging.getLogger(__name__)

router = APIRouter()

# 初始化服务
knowledge_service = KnowledgeBaseService()
audit_service = KnowledgeBaseAuditService()  # 初始化审计服务


def get_document_type(filename: str) -> DocumentType:
    """根据文件扩展名获取文档类型"""
    ext = filename.lower().split('.')[-1]
    mapping = {
        'pdf': DocumentType.PDF,
        'doc': DocumentType.WORD,
        'docx': DocumentType.WORD,
        'xls': DocumentType.EXCEL,
        'xlsx': DocumentType.EXCEL,
        'ppt': DocumentType.PPT,
        'pptx': DocumentType.PPT,
        'txt': DocumentType.TXT,
    }
    return mapping.get(ext, DocumentType.OTHER)


def calculate_file_hash(content: bytes) -> str:
    """计算文件哈希值"""
    return hashlib.sha256(content).hexdigest()


async def process_document_async(document_id: int, file_path: str, db: Session):
    """异步处理文档（向量化）"""
    try:
        # 使用原生SQL查询文档信息，避免枚举转换问题
        result = db.execute(
            text("""
                SELECT id, file_name, uploaded_by, file_size, document_type
                FROM knowledge_documents 
                WHERE id = :document_id
            """),
            {"document_id": document_id}
        ).fetchone()
        
        if not result:
            return
            
        # 调用知识库服务处理文档（service层会管理状态）
        await knowledge_service.process_document(db, document_id)

        # 记录上传成功的审计日志
        await audit_service.log_document_upload(
            db=db,
            user_id=result.uploaded_by,
            document_id=document_id,
            filename=result.file_name,
            content_type=result.document_type,
            file_size=result.file_size,
            request=None,
            success=True
        )
        
    except Exception as e:
        logger.error(f"处理文档失败 {document_id}: {str(e)}")
        
        # 记录失败的审计日志
        if result:
            await audit_service.log_document_upload(
                db=db,
                user_id=result.uploaded_by,
                document_id=document_id,
                filename=result.file_name,
                content_type=result.document_type,
                file_size=result.file_size,
                request=None,
                success=False,
                error_message=str(e)
            )
        
        # 手动更新文档状态为失败
        try:
            db.execute(
                text("UPDATE knowledge_documents SET status = 'failed', error_message = :error WHERE id = :doc_id"),
                {"error": str(e), "doc_id": document_id}
            )
            db.commit()
        except Exception as update_error:
            logger.error(f"更新文档状态失败: {update_error}")
            db.rollback()


@router.post("/documents", response_model=DocumentUploadResponse)
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = None,
    description: Optional[str] = None,
    category_id: Optional[int] = None,
    grade_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    上传知识库文档
    
    权限要求：knowledge_base.create
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查创建权限
    if not perm_service.check_create_permission(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法创建文档")
    
    # 读取文件内容
    content = await file.read()
    file_size = len(content)
    file_hash = calculate_file_hash(content)
    
    # 检查文件是否已存在
    existing_doc = db.query(KnowledgeDocument).filter(
        KnowledgeDocument.file_hash == file_hash
    ).first()
    if existing_doc:
        raise HTTPException(status_code=400, detail="文档已存在")
    
    # 保存文件到磁盘
    upload_dir = "uploads/knowledge_base"
    os.makedirs(upload_dir, exist_ok=True)
    file_path = os.path.join(upload_dir, f"{file_hash}_{file.filename}")
    
    with open(file_path, "wb") as f:
        f.write(content)
    
    # 创建文档记录
    document = KnowledgeDocument(
        title=title or file.filename,
        description=description,
        file_name=file.filename,
        file_path=file_path,
        file_size=file_size,
        file_hash=file_hash,
        document_type=get_document_type(file.filename),
        category_id=category_id,
        grade_id=grade_id,
        status=DocumentStatus.PENDING,
        uploaded_by=current_user.id,
        doc_metadata={}
    )
    
    db.add(document)
    db.commit()
    db.refresh(document)
    
    # 添加后台任务处理文档
    background_tasks.add_task(process_document_async, document.id, file_path, db)
    
    return DocumentUploadResponse(
        document_id=document.id,
        message="文档上传成功，正在处理中",
        file_name=file.filename,
        file_size=file_size,
        status=ProcessingStatus.PENDING
    )


@router.get("/documents", response_model=KnowledgeDocumentListResponse)
def get_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=1000),
    category_id: Optional[int] = None,
    grade_id: Optional[int] = None,
    status: Optional[ProcessingStatus] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库文档列表
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 构建查询
    query = db.query(KnowledgeDocument).options(
        joinedload(KnowledgeDocument.category),
        joinedload(KnowledgeDocument.grade),
        joinedload(KnowledgeDocument.uploader)
    )
    
    # 应用权限过滤
    query = perm_service.filter_documents_by_permission(
        current_user, 
        query, 
        KnowledgeBasePermissionService.PERMISSION_READ
    )
    
    # 应用过滤条件
    if category_id:
        query = query.filter(KnowledgeDocument.category_id == category_id)
    if grade_id:
        query = query.filter(KnowledgeDocument.grade_id == grade_id)
    if status:
        query = query.filter(KnowledgeDocument.status == status.value)
    if search:
        query = query.filter(
            or_(
                KnowledgeDocument.title.contains(search),
                KnowledgeDocument.description.contains(search),
                KnowledgeDocument.file_name.contains(search)
            )
        )
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    try:
        documents = query.order_by(KnowledgeDocument.created_at.desc()).offset(skip).limit(limit).all()
    except Exception as e:
        logger.error(f"查询知识库文档时出错: {e}")
        # 如果标准查询失败，尝试使用原生SQL查询绕过枚举值问题
        try:
            sql_query = text("""
                SELECT kd.*, dc.name as category_name, dg.name as grade_name, u.username as creator_name
                FROM knowledge_documents kd
                LEFT JOIN data_source_categories dc ON kd.category_id = dc.id
                LEFT JOIN data_source_grades dg ON kd.grade_id = dg.id
                LEFT JOIN users u ON kd.uploaded_by = u.id
                ORDER BY kd.created_at DESC
                LIMIT :limit OFFSET :offset
            """)
            
            result = db.execute(sql_query, {"limit": limit, "offset": skip})
            raw_documents = result.fetchall()
            
            # 手动构建文档对象
            documents = []
            for row in raw_documents:
                # 处理文档类型枚举值
                doc_type_str = row.document_type
                try:
                    # 尝试直接转换
                    doc_type = DocumentType(doc_type_str)
                except (ValueError, AttributeError):
                    try:
                        # 尝试小写转换
                        doc_type = DocumentType(doc_type_str.lower())
                    except (ValueError, AttributeError):
                        try:
                            # 尝试大写转换
                            doc_type = DocumentType(doc_type_str.upper())
                        except (ValueError, AttributeError):
                            doc_type = DocumentType.OTHER
                
                # 处理状态枚举值
                status_str = row.status
                try:
                    # 直接使用字符串值创建ProcessingStatus枚举
                    status = ProcessingStatus(status_str)
                except (ValueError, AttributeError):
                    try:
                        # 尝试转换为小写
                        status = ProcessingStatus(status_str.lower())
                    except (ValueError, AttributeError):
                        # 如果数据库中的状态值无效，默认为PENDING
                        logger.warning(f"无效的文档状态值: {status_str}, 文档ID: {row.id}")
                        status = ProcessingStatus.PENDING
                
                # 创建文档响应对象
                doc_obj = type('Document', (), {
                    'id': row.id,
                    'title': row.title,
                    'description': row.description,
                    'file_name': row.file_name,
                    'file_path': row.file_path,
                    'file_size': row.file_size,
                    'file_hash': row.file_hash,
                    'document_type': doc_type,
                    'status': status,
                    'error_message': row.error_message,
                    'chunk_count': row.chunk_count or 0,
                    'category_id': row.category_id,
                    'grade_id': row.grade_id,
                    'doc_metadata': json.loads(row.doc_metadata) if isinstance(row.doc_metadata, str) else (row.doc_metadata or {}),
                    'uploaded_by': row.uploaded_by,
                    'created_at': row.created_at,
                    'updated_at': row.updated_at,
                    'category_name': row.category_name,
                    'grade_name': row.grade_name,
                    'creator_name': row.creator_name
                })()
                documents.append(doc_obj)
                
            # 重新计算总数
            count_sql = text("SELECT COUNT(*) FROM knowledge_documents")
            total = db.execute(count_sql).scalar()
            
        except Exception as e2:
            logger.error(f"原生SQL查询也失败: {e2}")
            documents = []
            total = 0
    
    # 转换为响应模型
    items = []
    for doc in documents:
        try:
            # 如果是标准ORM对象，需要处理枚举值
            if hasattr(doc, '__dict__') and hasattr(doc, '__class__') and 'sqlalchemy' in str(type(doc)):
                # 处理文档类型枚举值
                doc_type = doc.document_type
                if isinstance(doc_type, str):
                    try:
                        # 尝试直接转换
                        doc_type = DocumentType(doc_type)
                    except ValueError:
                        try:
                            # 尝试小写转换
                            doc_type = DocumentType(doc_type.lower())
                        except ValueError:
                            try:
                                # 尝试大写转换
                                doc_type = DocumentType(doc_type.upper())
                            except ValueError:
                                doc_type = DocumentType.OTHER
                else:
                    # 如果已经是枚举类型，直接使用其值
                    doc_type = DocumentType(doc_type.value if hasattr(doc_type, 'value') else str(doc_type))
                
                # 处理状态枚举值 - 数据库存储的是大写值，API Schema需要小写值
                status = doc.status
                if isinstance(status, str):
                    # 直接从字符串转换，数据库存储的是大写，需要转换为小写
                    status_mapping = {
                        "PENDING": ProcessingStatus.PENDING,
                        "PROCESSING": ProcessingStatus.PROCESSING, 
                        "COMPLETED": ProcessingStatus.COMPLETED,
                        "FAILED": ProcessingStatus.FAILED,
                        "ARCHIVED": ProcessingStatus.ARCHIVED
                    }
                    status = status_mapping.get(status, ProcessingStatus.PENDING)
                else:
                    # 如果已经是枚举类型，获取其字符串值并转换
                    status_value = status.value if hasattr(status, 'value') else str(status)
                    status_mapping = {
                        "PENDING": ProcessingStatus.PENDING,
                        "PROCESSING": ProcessingStatus.PROCESSING, 
                        "COMPLETED": ProcessingStatus.COMPLETED,
                        "FAILED": ProcessingStatus.FAILED,
                        "ARCHIVED": ProcessingStatus.ARCHIVED
                    }
                    status = status_mapping.get(status_value, ProcessingStatus.PENDING)
                
                item = KnowledgeDocumentResponse(
                    id=doc.id,
                    title=doc.title,
                    description=doc.description,
                    file_name=doc.file_name,
                    file_path=doc.file_path,
                    file_size=doc.file_size,
                    file_hash=doc.file_hash,
                    document_type=doc_type,
                    status=status,
                    error_message=doc.error_message,
                    chunk_count=doc.chunk_count,
                    version=1,  # 默认版本号
                    category_id=doc.category_id,
                    grade_id=doc.grade_id,
                    doc_metadata=json.loads(doc.doc_metadata) if isinstance(doc.doc_metadata, str) else (doc.doc_metadata or {}),
                    created_by=doc.uploaded_by,
                    created_at=doc.created_at,
                    updated_at=doc.updated_at,
                    category_name=doc.category.name if doc.category else None,
                    grade_name=doc.grade.name if doc.grade else None,
                    creator_name=doc.uploader.username if doc.uploader else None
                )
            else:
                # 如果是手动构建的对象，直接使用
                item = KnowledgeDocumentResponse(
                    id=doc.id,
                    title=doc.title,
                    description=doc.description,
                    file_name=doc.file_name,
                    file_path=doc.file_path,
                    file_size=doc.file_size,
                    file_hash=doc.file_hash,
                    document_type=doc.document_type,
                    status=doc.status,
                    error_message=doc.error_message,
                    chunk_count=doc.chunk_count,
                    version=1,  # 默认版本号
                    category_id=doc.category_id,
                    grade_id=doc.grade_id,
                    doc_metadata=json.loads(doc.doc_metadata) if isinstance(doc.doc_metadata, str) else (doc.doc_metadata or {}),
                    created_by=doc.uploaded_by,
                    created_at=doc.created_at,
                    updated_at=doc.updated_at,
                    category_name=getattr(doc, 'category_name', None),
                    grade_name=getattr(doc, 'grade_name', None),
                    creator_name=getattr(doc, 'creator_name', None)
                )
            items.append(item)
        except Exception as e:
            logger.error(f"处理文档时出错: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            continue
    
    return KnowledgeDocumentListResponse(total=total, items=items)


@router.get("/documents/{document_id}", response_model=KnowledgeDocumentResponse)
def get_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库文档详情
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 查询文档
    document = db.query(KnowledgeDocument).options(
        joinedload(KnowledgeDocument.category),
        joinedload(KnowledgeDocument.grade),
        joinedload(KnowledgeDocument.uploader)
    ).filter(KnowledgeDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    # 检查文档访问权限
    if not perm_service.check_document_access(
        current_user, 
        document, 
        KnowledgeBasePermissionService.PERMISSION_READ
    ):
        raise HTTPException(status_code=403, detail="权限不足：无法访问此文档")
    
    # 转换为响应模型
    return KnowledgeDocumentResponse(
        id=document.id,
        title=document.title,
        description=document.description,
        file_name=document.file_name,
        file_path=document.file_path,
        file_size=document.file_size,
        file_hash=document.file_hash,
        document_type=document.document_type,
        status=ProcessingStatus(document.status.value),
        error_message=document.error_message,
        chunk_count=document.chunk_count,
        version=1,  # 默认版本号
        category_id=document.category_id,
        grade_id=document.grade_id,
        doc_metadata=json.loads(document.doc_metadata) if isinstance(document.doc_metadata, str) else (document.doc_metadata or {}),
        created_by=document.uploaded_by,
        created_at=document.created_at,
        updated_at=document.updated_at,
        category_name=document.category.name if document.category else None,
        grade_name=document.grade.name if document.grade else None,
        creator_name=document.uploader.username if document.uploader else None
    )


@router.put("/documents/{document_id}", response_model=KnowledgeDocumentResponse)
def update_document(
    document_id: int,
    update_data: KnowledgeDocumentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新知识库文档信息
    
    权限要求：knowledge_base.update
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 查询文档
    document = db.query(KnowledgeDocument).filter(KnowledgeDocument.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    # 检查文档更新权限
    if not perm_service.check_document_access(
        current_user, 
        document, 
        KnowledgeBasePermissionService.PERMISSION_UPDATE
    ):
        raise HTTPException(status_code=403, detail="权限不足：无法更新此文档")
    
    # 更新字段
    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(document, field, value)
    
    document.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(document)
    
    # 重新加载关联数据
    document = db.query(KnowledgeDocument).options(
        joinedload(KnowledgeDocument.category),
        joinedload(KnowledgeDocument.grade),
        joinedload(KnowledgeDocument.uploader)
    ).filter(KnowledgeDocument.id == document_id).first()
    
    # 转换为响应模型
    return KnowledgeDocumentResponse(
        id=document.id,
        title=document.title,
        description=document.description,
        file_name=document.file_name,
        file_path=document.file_path,
        file_size=document.file_size,
        file_hash=document.file_hash,
        document_type=document.document_type,
        status=ProcessingStatus(document.status.value),
        error_message=document.error_message,
        chunk_count=document.chunk_count,
        version=1,  # 默认版本号
        category_id=document.category_id,
        grade_id=document.grade_id,
        doc_metadata=json.loads(document.doc_metadata) if isinstance(document.doc_metadata, str) else (document.doc_metadata or {}),
        created_by=document.uploaded_by,
        created_at=document.created_at,
        updated_at=document.updated_at,
        category_name=document.category.name if document.category else None,
        grade_name=document.grade.name if document.grade else None,
        creator_name=document.uploader.username if document.uploader else None
    )


@router.delete("/documents/{document_id}")
def delete_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    删除知识库文档
    
    权限要求：knowledge_base.delete
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 使用原生SQL查询文档，避免枚举转换问题
    result = db.execute(
        text("""
            SELECT id, file_path, category_id, grade_id
            FROM knowledge_documents 
            WHERE id = :document_id
        """),
        {"document_id": document_id}
    ).fetchone()
    
    if not result:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    # 创建一个简单的文档对象用于权限检查
    class SimpleDocument:
        def __init__(self, id, category_id, grade_id):
            self.id = id
            self.category_id = category_id
            self.grade_id = grade_id
            self.created_by = None  # 设置为None，因为数据库中没有这个字段
    
    document = SimpleDocument(
        id=result.id,
        category_id=result.category_id,
        grade_id=result.grade_id
    )
    
    # 检查文档删除权限
    if not perm_service.check_document_access(
        current_user, 
        document, 
        KnowledgeBasePermissionService.PERMISSION_DELETE
    ):
        raise HTTPException(status_code=403, detail="权限不足：无法删除此文档")
    
    # 删除文件（如果存在）
    try:
        if result.file_path and os.path.exists(result.file_path):
            os.remove(result.file_path)
    except Exception as e:
        logger.warning(f"删除文件失败: {result.file_path}, 错误: {str(e)}")
    
    # 删除向量数据（如果服务支持）
    try:
        if hasattr(knowledge_service, 'delete_document'):
            # 使用异步方式调用
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(
                    knowledge_service.delete_document(db, document_id, current_user.id, hard_delete=True)
                )
            finally:
                loop.close()
    except Exception as e:
        logger.warning(f"删除向量数据失败: {str(e)}")
    
    # 删除数据库记录
    try:
        db.execute(
            text("DELETE FROM knowledge_documents WHERE id = :document_id"),
            {"document_id": document_id}
        )
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除数据库记录失败: {str(e)}")
    
    return {"message": "文档删除成功"}


@router.post("/search", response_model=DocumentSearchResponse)
async def search_documents(
    search_request: DocumentSearchRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    搜索知识库文档
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查搜索权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法搜索知识库")
    
    # 记录开始时间
    start_time = datetime.utcnow()
    
    # 调用知识库服务进行搜索
    results = await knowledge_service.search_knowledge_base(
        db=db,
        query=search_request.query,
        user_id=current_user.id,
        category_ids=search_request.category_ids,
        grade_ids=search_request.grade_ids,
        top_k=search_request.top_k,
        threshold=search_request.threshold
    )
    
    # 计算搜索耗时
    search_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
    
    # 记录搜索审计日志
    await audit_service.log_document_search(
        db=db,
        user_id=current_user.id,
        query=search_request.query,
        result_count=len(results),
        search_params={
            "category_ids": search_request.category_ids,
            "grade_ids": search_request.grade_ids,
            "top_k": search_request.top_k,
            "threshold": search_request.threshold
        },
        request=request
    )
    
    # 构建响应
    chunks = []
    for result in results:
        chunk = SearchResultChunk(
            chunk_id=result["chunk_id"],
            document_id=result["document_id"],
            document_title=result.get("document_name", ""),  # 修改字段名
            content=result.get("content", result.get("content_preview", "")),  # 兼容不同字段
            similarity_score=result.get("similarity", 0),  # 修改字段名
            chunk_metadata=result.get("metadata", {}),
            highlight=result.get("highlight")
        )
        chunks.append(chunk)
    
    return DocumentSearchResponse(
        query=search_request.query,
        total_results=len(chunks),
        chunks=chunks,
        search_time_ms=search_time_ms
    )


@router.post("/search/advanced", response_model=AdvancedSearchResponse)
async def advanced_search_documents(
    search_request: AdvancedSearchRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    高级搜索知识库文档
    
    支持多种搜索模式：
    - semantic: 语义搜索（基于向量相似度）
    - keyword: 关键词搜索（基于文本匹配）
    - hybrid: 混合搜索（结合语义和关键词）
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查搜索权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法搜索知识库")
    
    # 记录开始时间
    start_time = datetime.utcnow()
    
    # 转换文档类型枚举值
    document_types = None
    if search_request.document_types:
        document_types = [dt.value for dt in search_request.document_types]
    
    # 执行高级搜索
    results = await knowledge_service.advanced_search(
        db=db,
        query=search_request.query,
        user_id=current_user.id,
        search_mode=search_request.search_mode,
        category_ids=search_request.category_ids,
        grade_ids=search_request.grade_ids,
        document_types=document_types,
        date_from=search_request.date_from,
        date_to=search_request.date_to,
        uploaded_by=search_request.uploaded_by,
        top_k=search_request.top_k,
        threshold=search_request.threshold,
        include_content=search_request.include_content,
        include_metadata=search_request.include_metadata,
        highlight_keywords=search_request.highlight_keywords,
        group_by_document=search_request.group_by_document
    )
    
    # 计算搜索耗时
    search_time_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
    
    # 记录搜索审计日志
    await audit_service.log_document_search(
        db=db,
        user_id=current_user.id,
        query=search_request.query,
        result_count=results.get('total_chunks', 0),
        search_params={
            "search_mode": search_request.search_mode,
            "category_ids": search_request.category_ids,
            "grade_ids": search_request.grade_ids,
            "document_types": search_request.document_types,
            "date_range": {
                "from": search_request.date_from.isoformat() if search_request.date_from else None,
                "to": search_request.date_to.isoformat() if search_request.date_to else None
            },
            "top_k": search_request.top_k,
            "threshold": search_request.threshold,
            "group_by_document": search_request.group_by_document
        },
        request=request
    )
    
    # 构建响应
    if search_request.group_by_document:
        # 按文档分组的响应
        documents = []
        for doc in results.get('documents', []):
            # 转换片段格式
            matched_chunks = []
            for chunk in doc.get('chunks', []):
                matched_chunks.append(SearchResultChunk(
                    chunk_id=chunk['chunk_id'],
                    document_id=doc['document_id'],
                    document_title=doc['document_title'],
                    content=chunk['content'],
                    similarity_score=chunk['similarity_score'],
                    chunk_metadata=chunk.get('metadata', {}),
                    highlight=chunk.get('highlight')
                ))
            
            documents.append(SearchResultDocument(
                document_id=doc['document_id'],
                document_title=doc['document_title'],
                file_name=doc['file_name'],
                document_type=doc['document_type'],
                category_name=doc.get('category_name'),
                grade_name=doc.get('grade_name'),
                total_chunks=doc['total_chunks'],
                matched_chunks=matched_chunks,
                max_similarity_score=doc['max_similarity_score'],
                created_at=doc['created_at'],
                uploaded_by_name=doc.get('uploaded_by_name')
            ))
        
        return AdvancedSearchResponse(
            query=search_request.query,
            search_mode=search_request.search_mode,
            total_documents=results['total_documents'],
            total_chunks=results['total_chunks'],
            documents=documents,
            search_time_ms=search_time_ms,
            facets=results.get('facets')
        )
    else:
        # 不分组的响应（兼容原有格式）
        chunks = []
        for result in results.get('chunks', []):
            chunk = SearchResultChunk(
                chunk_id=result['chunk_id'],
                document_id=result['document_id'],
                document_title=result.get('document_info', {}).get('document').file_name if result.get('document_info', {}).get('document') else '',
                content=result['content'],
                similarity_score=result.get('similarity', 0),
                chunk_metadata=result.get('metadata', {}),
                highlight=result.get('highlight')
            )
            chunks.append(chunk)
        
        # 返回标准的响应格式，但 documents 字段为空
        return AdvancedSearchResponse(
            query=search_request.query,
            search_mode=search_request.search_mode,
            total_documents=results['total_documents'],
            total_chunks=results['total_chunks'],
            documents=[],  # 不分组时不返回文档列表
            search_time_ms=search_time_ms,
            facets=None
        )


@router.get("/statistics", response_model=DocumentStatistics)
def get_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库统计信息
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库统计")
    
    # 查询基础统计数据
    total_documents = db.query(KnowledgeDocument).count()
    total_chunks = db.query(KnowledgeChunk).count()
    total_size_bytes = db.query(func.sum(KnowledgeDocument.file_size)).scalar() or 0
    
    # 按状态统计
    status_stats = db.query(
        KnowledgeDocument.status,
        func.count(KnowledgeDocument.id)
    ).group_by(KnowledgeDocument.status).all()
    
    by_status = {}
    for status, count in status_stats:
        by_status[status] = count
    
    # 按文档类型统计
    try:
        type_stats = db.query(
            KnowledgeDocument.document_type,
            func.count(KnowledgeDocument.id)
        ).group_by(KnowledgeDocument.document_type).all()
        
        by_type = {}
        for doc_type, count in type_stats:
            try:
                # 处理枚举值，确保兼容性
                if isinstance(doc_type, str):
                    # 如果是字符串，尝试转换为对应的枚举
                    try:
                        # 尝试直接转换
                        enum_value = DocumentType(doc_type)
                    except ValueError:
                        try:
                            # 尝试小写转换
                            enum_value = DocumentType(doc_type.lower())
                        except ValueError:
                            try:
                                # 尝试大写转换
                                enum_value = DocumentType(doc_type.upper())
                            except ValueError:
                                enum_value = DocumentType.OTHER
                    by_type[enum_value] = count
                else:
                    # 如果已经是枚举类型，直接使用
                    by_type[doc_type] = count
            except Exception as e:
                logger.warning(f"处理文档类型统计时出错: {e}, doc_type: {doc_type}")
                # 出错时归类到OTHER
                by_type[DocumentType.OTHER] = by_type.get(DocumentType.OTHER, 0) + count
    except Exception as e:
        logger.error(f"查询文档类型统计时出错: {e}")
        by_type = {}
    
    # 按分类统计
    category_stats = db.query(
        DataSourceCategory.name,
        func.count(KnowledgeDocument.id)
    ).join(
        KnowledgeDocument, DataSourceCategory.id == KnowledgeDocument.category_id
    ).group_by(DataSourceCategory.name).all()
    
    by_category = {}
    for category_name, count in category_stats:
        by_category[category_name] = count
    
    # 按分级统计
    grade_stats = db.query(
        DataSourceGrade.name,
        func.count(KnowledgeDocument.id)
    ).join(
        KnowledgeDocument, DataSourceGrade.id == KnowledgeDocument.grade_id
    ).group_by(DataSourceGrade.name).all()
    
    by_grade = {}
    for grade_name, count in grade_stats:
        grade_name_str = grade_name.value if hasattr(grade_name, 'value') else str(grade_name)
        by_grade[grade_name_str] = count
    
    stats = {
        'total_documents': total_documents,
        'total_chunks': total_chunks,
        'total_size_bytes': total_size_bytes,
        'by_status': by_status,
        'by_type': by_type,
        'by_category': by_category,
        'by_grade': by_grade,
    }
    
    return DocumentStatistics(**stats)


@router.get("/permissions")
def get_user_permissions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户的知识库权限信息
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 获取权限摘要
    permissions = perm_service.get_permission_summary(current_user)
    
    return permissions


@router.get("/audit/trail")
async def get_audit_trail(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    operation_types: Optional[str] = Query(None, description="操作类型，逗号分隔"),
    limit: int = Query(100, ge=1, le=500, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户的知识库操作审计记录
    
    权限要求：knowledge_base.read
    """
    # 检查权限
    perm_service = KnowledgeBasePermissionService(db)
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问审计记录")
    
    # 解析操作类型
    op_types = None
    if operation_types:
        op_types = [op.strip() for op in operation_types.split(",")]
    
    # 获取审计记录
    audit_trail = await audit_service.get_user_audit_trail(
        db=db,
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        operation_types=op_types,
        limit=limit
    )
    
    return {"audit_trail": audit_trail}


@router.get("/audit/document/{document_id}")
async def get_document_audit_trail(
    document_id: int,
    limit: int = Query(50, ge=1, le=200, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取特定文档的审计记录
    
    权限要求：对该文档有read权限
    """
    # 检查文档权限
    perm_service = KnowledgeBasePermissionService(db)
    document = db.query(KnowledgeDocument).filter(KnowledgeDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    if not perm_service.check_document_access(
        current_user,
        document,
        KnowledgeBasePermissionService.PERMISSION_READ
    ):
        raise HTTPException(status_code=403, detail="权限不足：无法访问此文档的审计记录")
    
    # 获取文档审计记录
    audit_trail = await audit_service.get_document_audit_trail(
        db=db,
        document_id=document_id,
        limit=limit
    )
    
    return {
        "document_id": document_id,
        "document_title": document.title,
        "audit_trail": audit_trail
    }


@router.get("/audit/statistics")
async def get_security_statistics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库安全统计信息
    
    权限要求：管理员权限
    """
    # 检查管理员权限
    if not is_admin(current_user):
        raise HTTPException(status_code=403, detail="权限不足：需要管理员权限")
    
    # 获取安全统计
    statistics = await audit_service.get_security_statistics(
        db=db,
        start_date=start_date,
        end_date=end_date
    )
    
    return statistics


@router.post("/audit/compliance/{document_id}")
async def check_document_compliance(
    document_id: int,
    compliance_rules: Optional[dict] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    检查文档的合规性
    
    权限要求：对该文档有read权限
    """
    # 检查文档权限
    perm_service = KnowledgeBasePermissionService(db)
    document = db.query(KnowledgeDocument).filter(KnowledgeDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    if not perm_service.check_document_access(
        current_user,
        document,
        KnowledgeBasePermissionService.PERMISSION_READ
    ):
        raise HTTPException(status_code=403, detail="权限不足：无法检查此文档的合规性")
    
    # 检查合规性
    compliance_result = await audit_service.check_compliance(
        db=db,
        document_id=document_id,
        compliance_rules=compliance_rules
    )
    
    return compliance_result


@router.get("/data-categories")
def get_knowledge_base_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库可用的数据分类
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 查询所有数据分类
    categories = db.query(DataSourceCategory).all()
    
    result = []
    for category in categories:
        # 获取该分类下的分级
        grades = db.query(DataSourceGrade).filter_by(category_id=category.id).all()
        
        category_data = {
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "grades": [
                {
                    "id": grade.id,
                    "name": grade.name.value if hasattr(grade.name, 'value') else str(grade.name),
                    "description": grade.description,
                    "sensitivity_level": grade.get_sensitivity_level() if hasattr(grade, 'get_sensitivity_level') else 1
                }
                for grade in grades
            ]
        }
        result.append(category_data)
    
    return result


@router.get("/data-grades")
def get_knowledge_base_grades(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库可用的数据分级
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 查询所有数据分级
    grades = db.query(DataSourceGrade).join(DataSourceCategory).all()
    
    result = []
    for grade in grades:
        grade_data = {
            "id": grade.id,
            "name": grade.name.value if hasattr(grade.name, 'value') else str(grade.name),
            "description": grade.description,
            "category_id": grade.category_id,
            "category_name": grade.category.name if grade.category else "未知分类",
            "sensitivity_level": grade.get_sensitivity_level() if hasattr(grade, 'get_sensitivity_level') else 1
        }
        result.append(grade_data)
    
    return result 


@router.post("/reprocess-pending")
def reprocess_pending_documents(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    重新处理所有待处理的文档
    
    权限要求：管理员权限
    """
    # 权限检查：只有管理员可以执行此操作
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足：需要管理员权限")
    
    try:
        # 使用原生SQL查询所有pending状态的文档
        pending_docs = db.execute(
            text("""
                SELECT id, file_path, file_name
                FROM knowledge_documents 
                WHERE status = 'pending' AND file_path IS NOT NULL
            """)
        ).fetchall()
        
        if not pending_docs:
            return {"message": "没有待处理的文档", "count": 0}
        
        # 为每个pending文档添加后台处理任务
        for doc in pending_docs:
            background_tasks.add_task(process_document_async, doc.id, doc.file_path, db)
        
        return {
            "message": f"已启动 {len(pending_docs)} 个文档的重新处理任务",
            "count": len(pending_docs),
            "documents": [{"id": doc.id, "name": doc.file_name} for doc in pending_docs]
        }
        
    except Exception as e:
        logger.error(f"重新处理pending文档失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重新处理失败: {str(e)}")


@router.get("/llm-models", response_model=List[dict])
def get_knowledge_base_llm_models(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库可用的LLM模型列表
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 查询激活的LLM模型
    try:
        from app.db.models.llm_model import LLMModel
        models = db.query(LLMModel).filter(LLMModel.is_active == True).all()
        
        result = []
        for model in models:
            model_data = {
                "id": model.id,
                "name": model.name,
                "api_url": model.api_url,
                "config_params": model.config_params or {},
                "is_active": model.is_active,
                "created_at": model.created_at,
                "updated_at": model.updated_at,
                # 不返回加密的API密钥
                "has_api_key": bool(model.api_key_encrypted)
            }
            result.append(model_data)
        
        return result
        
    except Exception as e:
        logger.error(f"查询LLM模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询模型失败: {str(e)}")


@router.get("/llm-models/{model_id}")
def get_knowledge_base_llm_model(
    model_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库特定LLM模型详情
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    try:
        from app.db.models.llm_model import LLMModel
        model = db.query(LLMModel).filter(
            LLMModel.id == model_id,
            LLMModel.is_active == True
        ).first()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在或未激活")
        
        model_data = {
            "id": model.id,
            "name": model.name,
            "api_url": model.api_url,
            "config_params": model.config_params or {},
            "is_active": model.is_active,
            "created_at": model.created_at,
            "updated_at": model.updated_at,
            "has_api_key": bool(model.api_key_encrypted),
            "session_count": model.session_count if hasattr(model, 'session_count') else 0
        }
        
        return model_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询LLM模型详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询模型详情失败: {str(e)}")


@router.put("/llm-models/{model_id}")
def update_knowledge_base_llm_model(
    model_id: int,
    model_update: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新知识库LLM模型配置
    
    权限要求：knowledge_base.update 或 管理员权限
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查更新权限（需要管理员权限或特殊权限）
    if not (is_admin(current_user) or perm_service.check_update_permission(current_user)):
        raise HTTPException(status_code=403, detail="权限不足：无法更新模型配置")
    
    try:
        from app.db.models.llm_model import LLMModel
        from app.schemas.llm_model import LLMModelUpdate
        
        model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 使用CRUD服务更新模型
        from app.crud.crud_llm_model import llm_model_crud
        
        # 创建更新schema对象
        update_schema = LLMModelUpdate(**model_update)
        updated_model = llm_model_crud.update(db=db, db_obj=model, obj_in=update_schema)
        
        return {
            "id": updated_model.id,
            "name": updated_model.name,
            "api_url": updated_model.api_url,
            "config_params": updated_model.config_params or {},
            "is_active": updated_model.is_active,
            "updated_at": updated_model.updated_at,
            "has_api_key": bool(updated_model.api_key_encrypted)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新LLM模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新模型失败: {str(e)}")


@router.post("/llm-models")
def create_knowledge_base_llm_model(
    model_create: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    创建知识库LLM模型配置
    
    权限要求：管理员权限
    """
    # 检查管理员权限
    if not is_admin(current_user):
        raise HTTPException(status_code=403, detail="权限不足：需要管理员权限")
    
    try:
        from app.schemas.llm_model import LLMModelCreate
        from app.crud.crud_llm_model import llm_model_crud
        
        # 创建模型schema对象
        create_schema = LLMModelCreate(**model_create)
        new_model = llm_model_crud.create(db=db, obj_in=create_schema)
        
        return {
            "id": new_model.id,
            "name": new_model.name,
            "api_url": new_model.api_url,
            "config_params": new_model.config_params or {},
            "is_active": new_model.is_active,
            "created_at": new_model.created_at,
            "has_api_key": bool(new_model.api_key_encrypted)
        }
        
    except Exception as e:
        logger.error(f"创建LLM模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建模型失败: {str(e)}")


@router.get("/permissions/config")
def get_knowledge_base_permissions_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库权限配置
    
    权限要求：knowledge_base.read 或 管理员权限
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查权限
    if not (is_admin(current_user) or perm_service.check_module_access(current_user)):
        raise HTTPException(status_code=403, detail="权限不足：无法访问权限配置")
    
    try:
        # 获取当前用户的权限配置
        permissions_config = perm_service.get_permission_summary(current_user)
        
        # 如果是管理员，返回完整的权限配置
        if is_admin(current_user):
            # 获取所有角色的权限配置
            from app.db.models.role import Role
            from app.db.models.role_module_permission import RoleModulePermission
            
            roles = db.query(Role).all()
            permissions = db.query(RoleModulePermission).filter(
                RoleModulePermission.module_name == "knowledge_base"
            ).all()
            
            roles_config = []
            for role in roles:
                role_permissions = []
                for perm in role.module_permissions:
                    if perm.module_name == "knowledge_base":
                        role_permissions.append({
                            "id": f"{perm.role_id}_{perm.module_name}",
                            "name": perm.module_name,
                            "description": f"访问{perm.module_name}模块",
                            "module_name": perm.module_name,
                            "can_access": perm.can_access
                        })
                
                roles_config.append({
                    "id": role.id,
                    "name": role.name,
                    "description": role.description,
                    "permissions": role_permissions
                })
            
            permissions_config["all_roles"] = roles_config
            permissions_config["available_permissions"] = [
                {
                    "id": f"{perm.role_id}_{perm.module_name}",
                    "name": perm.module_name,
                    "description": f"访问{perm.module_name}模块",
                    "module_name": perm.module_name,
                    "can_access": perm.can_access
                }
                for perm in permissions
            ]
        
        return permissions_config
        
    except Exception as e:
        logger.error(f"获取权限配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取权限配置失败: {str(e)}")


@router.put("/permissions/config")
def update_knowledge_base_permissions_config(
    permissions_update: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新知识库权限配置
    
    权限要求：管理员权限
    """
    # 检查管理员权限
    if not is_admin(current_user):
        raise HTTPException(status_code=403, detail="权限不足：需要管理员权限")
    
    try:
        # 更新角色权限配置
        if "role_permissions" in permissions_update:
            from app.db.models.role import Role
            from app.db.models.role_module_permission import RoleModulePermission
            
            for role_config in permissions_update["role_permissions"]:
                role_id = role_config.get("role_id")
                permission_ids = role_config.get("permission_ids", [])
                
                role = db.query(Role).filter(Role.id == role_id).first()
                if not role:
                    continue
                
                # 清除现有的知识库权限
                current_kb_permissions = db.query(RoleModulePermission).filter(
                    RoleModulePermission.role_id == role_id,
                    RoleModulePermission.module_name == "knowledge_base"
                ).all()
                for perm in current_kb_permissions:
                    db.delete(perm)
                
                # 添加新的权限
                for perm_id in permission_ids:
                    # 解析权限ID格式 "role_id_module_name"
                    if "_" in str(perm_id):
                        parts = str(perm_id).split("_", 1)
                        if len(parts) == 2 and parts[1] == "knowledge_base":
                            new_permission = RoleModulePermission(
                                role_id=role_id,
                                module_name="knowledge_base",
                                can_access=True
                            )
                            db.add(new_permission)
            
            db.commit()
        
        return {"message": "权限配置更新成功"}
        
    except Exception as e:
        logger.error(f"更新权限配置失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新权限配置失败: {str(e)}")


# 知识库模型配置相关API
@router.get("/model-configs", response_model=KnowledgeBaseModelConfigListResponse)
def get_knowledge_base_model_configs(
    config_type: Optional[ConfigType] = Query(None, description="配置类型筛选"),
    model_id: Optional[int] = Query(None, description="模型ID筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(10, ge=1, le=100, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库模型配置列表
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 构建查询
    query = db.query(KnowledgeBaseModelConfig).options(
        joinedload(KnowledgeBaseModelConfig.model),
        joinedload(KnowledgeBaseModelConfig.user),
        joinedload(KnowledgeBaseModelConfig.creator)
    )
    
    # 应用过滤条件
    if config_type:
        query = query.filter(KnowledgeBaseModelConfig.config_type == config_type.value)
    if model_id:
        query = query.filter(KnowledgeBaseModelConfig.model_id == model_id)
    if is_active is not None:
        query = query.filter(KnowledgeBaseModelConfig.is_active == is_active)
    
    # 如果不是管理员，只能查看自己创建的或全局的配置
    if not is_admin(current_user):
        query = query.filter(
            or_(
                KnowledgeBaseModelConfig.config_type == "global",
                KnowledgeBaseModelConfig.created_by == current_user.id,
                KnowledgeBaseModelConfig.user_id == current_user.id
            )
        )
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    configs = query.order_by(
        KnowledgeBaseModelConfig.priority.asc(),
        KnowledgeBaseModelConfig.created_at.desc()
    ).offset(skip).limit(limit).all()
    
    # 转换为响应模型
    items = []
    for config in configs:
        item = KnowledgeBaseModelConfigResponse(
            id=config.id,
            config_type=ConfigType(config.config_type),
            model_id=config.model_id,
            user_id=config.user_id,
            category_id=config.category_id,
            grade_id=config.grade_id,
            config_name=config.config_name,
            description=config.description,
            model_params=config.model_params or {},
            is_active=config.is_active,
            priority=config.priority,
            created_by=config.created_by,
            created_at=config.created_at,
            updated_at=config.updated_at,
            model_name=config.model.name if config.model else None,
            model_api_url=config.model.api_url if config.model else None,
            creator_name=config.creator.username if config.creator else None,
            user_name=config.user.username if config.user else None
        )
        items.append(item)
    
    return KnowledgeBaseModelConfigListResponse(total=total, items=items)


@router.post("/model-configs", response_model=KnowledgeBaseModelConfigResponse)
def create_knowledge_base_model_config(
    config_create: KnowledgeBaseModelConfigCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    创建知识库模型配置
    
    权限要求：knowledge_base.create 或 管理员权限
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查创建权限
    can_create_global = is_admin(current_user)
    can_create_user = perm_service.check_create_permission(current_user)
    
    if not (can_create_global or can_create_user):
        raise HTTPException(status_code=403, detail="权限不足：无法创建模型配置")
    
    # 检查配置类型权限
    if config_create.config_type in [ConfigType.GLOBAL, ConfigType.CATEGORY, ConfigType.GRADE]:
        if not can_create_global:
            raise HTTPException(status_code=403, detail="权限不足：只有管理员可以创建全局/分类/分级配置")
    
    # 验证模型是否存在且可用
    model = db.query(LLMModel).filter(
        LLMModel.id == config_create.model_id,
        LLMModel.is_active == True
    ).first()
    if not model:
        raise HTTPException(status_code=404, detail="模型不存在或未激活")
    
    # 检查配置唯一性（同一类型下避免重复配置）
    existing_config = None
    if config_create.config_type == ConfigType.GLOBAL:
        existing_config = db.query(KnowledgeBaseModelConfig).filter(
            KnowledgeBaseModelConfig.config_type == "global",
            KnowledgeBaseModelConfig.is_active == True
        ).first()
    elif config_create.config_type == ConfigType.USER:
        user_id = config_create.user_id or current_user.id
        existing_config = db.query(KnowledgeBaseModelConfig).filter(
            KnowledgeBaseModelConfig.config_type == "user",
            KnowledgeBaseModelConfig.user_id == user_id,
            KnowledgeBaseModelConfig.is_active == True
        ).first()
    elif config_create.config_type == ConfigType.CATEGORY:
        existing_config = db.query(KnowledgeBaseModelConfig).filter(
            KnowledgeBaseModelConfig.config_type == "category",
            KnowledgeBaseModelConfig.category_id == config_create.category_id,
            KnowledgeBaseModelConfig.is_active == True
        ).first()
    elif config_create.config_type == ConfigType.GRADE:
        existing_config = db.query(KnowledgeBaseModelConfig).filter(
            KnowledgeBaseModelConfig.config_type == "grade",
            KnowledgeBaseModelConfig.grade_id == config_create.grade_id,
            KnowledgeBaseModelConfig.is_active == True
        ).first()
    
    if existing_config:
        raise HTTPException(status_code=400, detail="该类型的配置已存在，请先删除或禁用现有配置")
    
    # 设置用户ID（用户配置时）
    user_id = None
    if config_create.config_type == ConfigType.USER:
        user_id = config_create.user_id or current_user.id
    
    # 创建配置
    config = KnowledgeBaseModelConfig(
        config_type=config_create.config_type.value,
        model_id=config_create.model_id,
        user_id=user_id,
        category_id=config_create.category_id,
        grade_id=config_create.grade_id,
        config_name=config_create.config_name,
        description=config_create.description,
        model_params=config_create.model_params,
        is_active=config_create.is_active,
        priority=config_create.priority,
        created_by=current_user.id
    )
    
    db.add(config)
    db.commit()
    db.refresh(config)
    
    # 重新加载关联数据
    config = db.query(KnowledgeBaseModelConfig).options(
        joinedload(KnowledgeBaseModelConfig.model),
        joinedload(KnowledgeBaseModelConfig.user),
        joinedload(KnowledgeBaseModelConfig.creator)
    ).filter(KnowledgeBaseModelConfig.id == config.id).first()
    
    # 转换为响应模型
    return KnowledgeBaseModelConfigResponse(
        id=config.id,
        config_type=ConfigType(config.config_type),
        model_id=config.model_id,
        user_id=config.user_id,
        category_id=config.category_id,
        grade_id=config.grade_id,
        config_name=config.config_name,
        description=config.description,
        model_params=config.model_params or {},
        is_active=config.is_active,
        priority=config.priority,
        created_by=config.created_by,
        created_at=config.created_at,
        updated_at=config.updated_at,
        model_name=config.model.name if config.model else None,
        model_api_url=config.model.api_url if config.model else None,
        creator_name=config.creator.username if config.creator else None,
        user_name=config.user.username if config.user else None
    )


@router.get("/model-configs/selection/current", response_model=KnowledgeBaseModelSelection)
def get_current_model_selection(
    category_id: Optional[int] = Query(None, description="文档分类ID"),
    grade_id: Optional[int] = Query(None, description="文档分级ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前场景下的模型选择结果
    
    根据用户、分类、分级等条件选择最合适的模型配置
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 获取适用的配置（按优先级排序）
    config = KnowledgeBaseModelConfig.get_applicable_config(
        db, current_user.id, category_id, grade_id
    )
    
    if not config:
        raise HTTPException(status_code=404, detail="未找到适用的模型配置")
    
    # 加载关联的模型信息
    model = db.query(LLMModel).filter(LLMModel.id == config.model_id).first()
    if not model:
        raise HTTPException(status_code=500, detail="配置关联的模型不存在")
    
    # 确定选择原因
    reason = ""
    if config.config_type == "user":
        reason = "用户自定义配置"
    elif config.config_type == "grade":
        reason = f"按分级配置（分级ID: {config.grade_id}）"
    elif config.config_type == "category":
        reason = f"按分类配置（分类ID: {config.category_id}）"
    elif config.config_type == "global":
        reason = "全局默认配置"
    
    return KnowledgeBaseModelSelection(
        config_id=config.id,
        model_id=model.id,
        model_name=model.name,
        model_api_url=model.api_url,
        model_params=config.model_params or {},
        config_type=ConfigType(config.config_type),
        priority=config.priority,
        selection_reason=reason
    )


@router.get("/model-configs/permissions/current", response_model=ModelPermissionConfig)
def get_current_user_model_permissions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户的模型配置权限
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查基础权限
    can_access = perm_service.check_module_access(current_user)
    if not can_access:
        return ModelPermissionConfig(
            can_view=False,
            can_create=False,
            can_update=False,
            can_delete=False,
            can_config_global=False,
            can_config_category=False,
            can_config_grade=False
        )
    
    # 检查各种操作权限
    is_user_admin = is_admin(current_user)
    can_create = perm_service.check_create_permission(current_user)
    can_update = perm_service.check_update_permission(current_user)
    can_delete = perm_service.check_delete_permission(current_user)
    
    # 获取可访问的模型列表
    accessible_models = []
    models = db.query(LLMModel).filter(LLMModel.is_active == True).all()
    for model in models:
        accessible_models.append(model.id)
    
    # 获取可配置的分类和分级（管理员权限）
    accessible_categories = []
    accessible_grades = []
    if is_user_admin:
        categories = db.query(DataSourceCategory).all()
        grades = db.query(DataSourceGrade).all()
        accessible_categories = [cat.id for cat in categories]
        accessible_grades = [grade.id for grade in grades]
    
    return ModelPermissionConfig(
        can_view=True,
        can_create=can_create,
        can_update=can_update,
        can_delete=can_delete,
        can_config_global=is_user_admin,
        can_config_category=is_user_admin,
        can_config_grade=is_user_admin,
        accessible_models=accessible_models,
        accessible_categories=accessible_categories,
        accessible_grades=accessible_grades
    )


@router.get("/model-configs/summary", response_model=KnowledgeBaseModelConfigSummary)
def get_knowledge_base_model_config_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库模型配置摘要信息
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 统计配置数量
    total_configs = db.query(KnowledgeBaseModelConfig).count()
    active_configs = db.query(KnowledgeBaseModelConfig).filter(
        KnowledgeBaseModelConfig.is_active == True
    ).count()
    global_configs = db.query(KnowledgeBaseModelConfig).filter(
        KnowledgeBaseModelConfig.config_type == "global"
    ).count()
    user_configs = db.query(KnowledgeBaseModelConfig).filter(
        KnowledgeBaseModelConfig.config_type == "user"
    ).count()
    category_configs = db.query(KnowledgeBaseModelConfig).filter(
        KnowledgeBaseModelConfig.config_type == "category"
    ).count()
    grade_configs = db.query(KnowledgeBaseModelConfig).filter(
        KnowledgeBaseModelConfig.config_type == "grade"
    ).count()
    
    # 获取使用中的模型列表
    models_in_use = db.query(
        LLMModel.id,
        LLMModel.name,
        func.count(KnowledgeBaseModelConfig.id).label('config_count')
    ).join(
        KnowledgeBaseModelConfig, LLMModel.id == KnowledgeBaseModelConfig.model_id
    ).filter(
        KnowledgeBaseModelConfig.is_active == True
    ).group_by(LLMModel.id, LLMModel.name).all()
    
    models_list = []
    for model_id, model_name, config_count in models_in_use:
        models_list.append({
            "model_id": model_id,
            "model_name": model_name,
            "config_count": config_count
        })
    
    # 获取最近更新的配置
    recent_configs = db.query(KnowledgeBaseModelConfig).options(
        joinedload(KnowledgeBaseModelConfig.model),
        joinedload(KnowledgeBaseModelConfig.creator)
    ).order_by(
        KnowledgeBaseModelConfig.updated_at.desc()
    ).limit(5).all()
    
    recent_updates = []
    for config in recent_configs:
        recent_updates.append({
            "config_id": config.id,
            "config_name": config.config_name,
            "config_type": config.config_type,
            "model_name": config.model.name if config.model else "未知",
            "updated_at": config.updated_at.isoformat(),
            "updated_by": config.creator.username if config.creator else "未知"
        })
    
    return KnowledgeBaseModelConfigSummary(
        total_configs=total_configs,
        active_configs=active_configs,
        global_configs=global_configs,
        user_configs=user_configs,
        category_configs=category_configs,
        grade_configs=grade_configs,
        models_in_use=models_list,
        recent_updates=recent_updates
    )


@router.get("/model-configs/{config_id}", response_model=KnowledgeBaseModelConfigResponse)
def get_knowledge_base_model_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取知识库模型配置详情
    
    权限要求：knowledge_base.read
    """
    # 初始化权限服务
    perm_service = KnowledgeBasePermissionService(db)
    
    # 检查模块访问权限
    if not perm_service.check_module_access(current_user):
        raise HTTPException(status_code=403, detail="权限不足：无法访问知识库")
    
    # 查询配置
    config = db.query(KnowledgeBaseModelConfig).options(
        joinedload(KnowledgeBaseModelConfig.model),
        joinedload(KnowledgeBaseModelConfig.user),
        joinedload(KnowledgeBaseModelConfig.creator)
    ).filter(KnowledgeBaseModelConfig.id == config_id).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 检查访问权限
    if not is_admin(current_user):
        if config.config_type != "global" and config.created_by != current_user.id and config.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="权限不足：无法访问此配置")
    
    # 转换为响应模型
    return KnowledgeBaseModelConfigResponse(
        id=config.id,
        config_type=ConfigType(config.config_type),
        model_id=config.model_id,
        user_id=config.user_id,
        category_id=config.category_id,
        grade_id=config.grade_id,
        config_name=config.config_name,
        description=config.description,
        model_params=config.model_params or {},
        is_active=config.is_active,
        priority=config.priority,
        created_by=config.created_by,
        created_at=config.created_at,
        updated_at=config.updated_at,
        model_name=config.model.name if config.model else None,
        model_api_url=config.model.api_url if config.model else None,
        creator_name=config.creator.username if config.creator else None,
        user_name=config.user.username if config.user else None
    )


@router.put("/model-configs/{config_id}", response_model=KnowledgeBaseModelConfigResponse)
def update_knowledge_base_model_config(
    config_id: int,
    config_update: KnowledgeBaseModelConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新知识库模型配置
    
    权限要求：knowledge_base.update 或 管理员权限
    """
    # 查询配置
    config = db.query(KnowledgeBaseModelConfig).filter(KnowledgeBaseModelConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 检查权限
    can_update = is_admin(current_user) or config.created_by == current_user.id
    if not can_update:
        raise HTTPException(status_code=403, detail="权限不足：无法更新此配置")
    
    # 验证模型（如果要更新）
    if config_update.model_id:
        model = db.query(LLMModel).filter(
            LLMModel.id == config_update.model_id,
            LLMModel.is_active == True
        ).first()
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在或未激活")
    
    # 更新字段
    update_dict = config_update.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(config, field, value)
    
    config.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(config)
    
    # 重新加载关联数据
    config = db.query(KnowledgeBaseModelConfig).options(
        joinedload(KnowledgeBaseModelConfig.model),
        joinedload(KnowledgeBaseModelConfig.user),
        joinedload(KnowledgeBaseModelConfig.creator)
    ).filter(KnowledgeBaseModelConfig.id == config_id).first()
    
    # 转换为响应模型
    return KnowledgeBaseModelConfigResponse(
        id=config.id,
        config_type=ConfigType(config.config_type),
        model_id=config.model_id,
        user_id=config.user_id,
        category_id=config.category_id,
        grade_id=config.grade_id,
        config_name=config.config_name,
        description=config.description,
        model_params=config.model_params or {},
        is_active=config.is_active,
        priority=config.priority,
        created_by=config.created_by,
        created_at=config.created_at,
        updated_at=config.updated_at,
        model_name=config.model.name if config.model else None,
        model_api_url=config.model.api_url if config.model else None,
        creator_name=config.creator.username if config.creator else None,
        user_name=config.user.username if config.user else None
    )


@router.delete("/model-configs/{config_id}")
def delete_knowledge_base_model_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    删除知识库模型配置
    
    权限要求：knowledge_base.delete 或 管理员权限
    """
    # 查询配置
    config = db.query(KnowledgeBaseModelConfig).filter(KnowledgeBaseModelConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 检查权限
    can_delete = is_admin(current_user) or config.created_by == current_user.id
    if not can_delete:
        raise HTTPException(status_code=403, detail="权限不足：无法删除此配置")
    
    # 防止删除最后一个全局配置
    if config.config_type == "global":
        other_global_configs = db.query(KnowledgeBaseModelConfig).filter(
            KnowledgeBaseModelConfig.config_type == "global",
            KnowledgeBaseModelConfig.id != config_id,
            KnowledgeBaseModelConfig.is_active == True
        ).count()
        
        if other_global_configs == 0:
            raise HTTPException(
                status_code=400, 
                detail="无法删除最后一个全局配置，请先创建其他全局配置"
            )
    
    # 删除配置
    db.delete(config)
    db.commit()
    
    return {"message": "配置删除成功"}

