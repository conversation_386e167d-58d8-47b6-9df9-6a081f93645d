"""
RAG会话管理API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import models, schemas
from app.api.deps import get_db, get_current_user
from app.services.rag_permissions import RAGPermissionService
from app.core.permissions import can_access_session

router = APIRouter()


@router.post("/", response_model=schemas.SessionRead, status_code=status.HTTP_201_CREATED)
def create_rag_session(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    session_in: schemas.SessionCreate
) -> Any:
    """
    创建新的RAG会话
    """
    # 检查RAG权限
    rag_permission_service = RAGPermissionService(db=db)
    if not rag_permission_service.check_rag_access(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：无法创建知识库问答会话"
        )
    
    # 创建会话对象，标记为RAG会话
    session_data = session_in.model_dump()
    session_data.update({
        "user_id": current_user.id,
        "is_deleted": False,
        "session_type": "rag"  # 标记为RAG会话类型
    })
    
    db_session = models.Session(**session_data)
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    
    return db_session


@router.get("/", response_model=List[schemas.SessionRead])
def get_rag_sessions(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    获取用户的RAG会话列表
    """
    # 检查RAG权限
    rag_permission_service = RAGPermissionService(db=db)
    if not rag_permission_service.check_rag_access(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：无法访问知识库问答会话"
        )
    
    # 查询用户的RAG会话
    sessions = db.query(models.Session).filter(
        models.Session.user_id == current_user.id,
        models.Session.is_deleted == False,
        models.Session.session_type == "rag"
    ).order_by(models.Session.updated_at.desc()).offset(skip).limit(limit).all()
    
    return sessions


@router.get("/{session_id}", response_model=schemas.SessionRead)
def get_rag_session(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    session_id: int
) -> Any:
    """
    获取特定的RAG会话
    """
    session = db.query(models.Session).filter(
        models.Session.id == session_id,
        models.Session.session_type == "rag"
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：无法访问此会话"
        )
    
    return session


@router.put("/{session_id}", response_model=schemas.SessionRead)
def update_rag_session(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    session_id: int,
    session_in: schemas.SessionUpdate
) -> Any:
    """
    更新RAG会话
    """
    session = db.query(models.Session).filter(
        models.Session.id == session_id,
        models.Session.session_type == "rag"
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：无法修改此会话"
        )
    
    # 更新会话
    update_data = session_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(session, field, value)
    
    db.commit()
    db.refresh(session)
    
    return session


@router.delete("/{session_id}")
def delete_rag_session(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    session_id: int
) -> Any:
    """
    删除RAG会话
    """
    session = db.query(models.Session).filter(
        models.Session.id == session_id,
        models.Session.session_type == "rag"
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：无法删除此会话"
        )
    
    # 软删除
    session.is_deleted = True
    db.commit()
    
    return {"message": "会话已删除"}


@router.get("/{session_id}/messages", response_model=List[schemas.MessageResponse])
def get_rag_session_messages(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    session_id: int,
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    获取RAG会话的消息历史
    """
    session = db.query(models.Session).filter(
        models.Session.id == session_id,
        models.Session.session_type == "rag"
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：无法访问此会话消息"
        )
    
    # 获取会话消息
    messages = db.query(models.Message).filter(
        models.Message.session_id == session_id
    ).order_by(models.Message.created_at.asc()).offset(skip).limit(limit).all()
    
    return messages
