from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List
from pydantic import BaseModel
from sqlalchemy.orm import selectinload

from app.db.session import get_db
from app.db.models.data_source_category import DataSourceCategory
from app.db.models.data_source_grade import DataSourceGrade, DataGradeName
from app.db.models.role_data_grade_access import RoleDataGradeAccess
from app.db.models.role import Role
from app.api.async_deps import get_current_user
from app.db.models.user import User
from app.core.security import get_accessible_data_sensitivity_levels

router = APIRouter()

# Pydantic模型
class DataSourceCategoryCreate(BaseModel):
    name: str
    description: str = None

class DataSourceCategoryUpdate(BaseModel):
    name: str = None
    description: str = None

class DataSourceGradeResponse(BaseModel):
    id: int
    name: str
    description: str = None
    sensitivity_level: int

class DataSourceCategoryResponse(BaseModel):
    id: int
    name: str
    description: str = None
    grades: List[DataSourceGradeResponse]
    created_at: str
    updated_at: str

class DataGradeAccessSummary(BaseModel):
    grade_id: int
    grade_name: str
    category_name: str
    sensitivity_level: int
    roles_with_access: List[str]

@router.get("/categories", response_model=List[DataSourceCategoryResponse])
async def get_data_source_categories(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有数据源分类及其分级"""
    
    categories_query = await db.execute(
        select(DataSourceCategory).order_by(DataSourceCategory.name)
    )
    categories = categories_query.scalars().all()
    
    result = []
    for category in categories:
        grades_query = await db.execute(
            select(DataSourceGrade).where(
                DataSourceGrade.category_id == category.id
            ).order_by(DataSourceGrade.name)
        )
        grades = grades_query.scalars().all()
        
        category_response = DataSourceCategoryResponse(
            id=category.id,
            name=category.name,
            description=category.description,
            grades=[
                DataSourceGradeResponse(
                    id=grade.id,
                    name=grade.name.value,
                    description=grade.description,
                    sensitivity_level=grade.get_sensitivity_level()
                ) for grade in grades
            ],
            created_at=category.created_at.isoformat(),
            updated_at=category.updated_at.isoformat()
        )
        result.append(category_response)
    
    return result

@router.post("/categories", response_model=DataSourceCategoryResponse)
async def create_data_source_category(
    category_data: DataSourceCategoryCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新的数据源分类"""
    
    # 检查名称是否已存在
    existing_query = await db.execute(
        select(DataSourceCategory).where(DataSourceCategory.name == category_data.name)
    )
    existing = existing_query.scalar_one_or_none()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Data source category with name '{category_data.name}' already exists"
        )
    
    # 创建新分类
    new_category = DataSourceCategory(
        name=category_data.name,
        description=category_data.description
    )
    db.add(new_category)
    await db.commit()
    await db.refresh(new_category)
    
    # 自动为新分类创建所有敏感度级别的分级
    for grade_name in DataGradeName:
        new_grade = DataSourceGrade(
            category_id=new_category.id,
            name=grade_name,
            description=f"{new_category.name} - {grade_name.value}级别"
        )
        db.add(new_grade)
    
    await db.commit()
    
    # 重新查询包含分级的完整数据
    category_with_grades_query = await db.execute(
        select(DataSourceCategory).where(DataSourceCategory.id == new_category.id)
    )
    category_with_grades = category_with_grades_query.scalar_one()
    
    grades_query = await db.execute(
        select(DataSourceGrade).where(DataSourceGrade.category_id == new_category.id)
    )
    grades = grades_query.scalars().all()
    
    return DataSourceCategoryResponse(
        id=category_with_grades.id,
        name=category_with_grades.name,
        description=category_with_grades.description,
        grades=[
            DataSourceGradeResponse(
                id=grade.id,
                name=grade.name.value,
                description=grade.description,
                sensitivity_level=grade.get_sensitivity_level()
            ) for grade in grades
        ],
        created_at=category_with_grades.created_at.isoformat(),
        updated_at=category_with_grades.updated_at.isoformat()
    )

@router.put("/categories/{category_id}", response_model=DataSourceCategoryResponse)
async def update_data_source_category(
    category_id: int,
    category_data: DataSourceCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新数据源分类"""
    
    # 检查分类是否存在
    category = await db.get(DataSourceCategory, category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source category not found"
        )
    
    # 检查名称是否与其他分类冲突
    if category_data.name and category_data.name != category.name:
        existing_query = await db.execute(
            select(DataSourceCategory).where(
                DataSourceCategory.name == category_data.name,
                DataSourceCategory.id != category_id
            )
        )
        existing = existing_query.scalar_one_or_none()
        
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Data source category with name '{category_data.name}' already exists"
            )
    
    # 更新分类信息
    if category_data.name is not None:
        category.name = category_data.name
    if category_data.description is not None:
        category.description = category_data.description
    
    await db.commit()
    await db.refresh(category)
    
    # 获取分级信息
    grades_query = await db.execute(
        select(DataSourceGrade).where(DataSourceGrade.category_id == category_id)
    )
    grades = grades_query.scalars().all()
    
    return DataSourceCategoryResponse(
        id=category.id,
        name=category.name,
        description=category.description,
        grades=[
            DataSourceGradeResponse(
                id=grade.id,
                name=grade.name.value,
                description=grade.description,
                sensitivity_level=grade.get_sensitivity_level()
            ) for grade in grades
        ],
        created_at=category.created_at.isoformat(),
        updated_at=category.updated_at.isoformat()
    )

@router.delete("/categories/{category_id}")
async def delete_data_source_category(
    category_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除数据源分类（级联删除分级和访问权限）"""
    
    # 检查分类是否存在
    category = await db.get(DataSourceCategory, category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source category not found"
        )
    
    # 删除分类（级联删除分级和访问权限）
    await db.delete(category)
    await db.commit()
    
    return {"message": f"Data source category '{category.name}' deleted successfully"}

@router.get("/grades/access-summary", response_model=List[DataGradeAccessSummary])
async def get_data_grade_access_summary(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取数据分级访问权限汇总"""
    
    # 查询所有分级及其访问权限
    grades_query = await db.execute(
        select(DataSourceGrade).join(DataSourceCategory).order_by(
            DataSourceCategory.name, DataSourceGrade.name
        )
    )
    grades = grades_query.scalars().all()
    
    result = []
    for grade in grades:
        # 获取有权限访问该分级的角色
        accesses_query = await db.execute(
            select(RoleDataGradeAccess).join(Role).where(
                RoleDataGradeAccess.data_source_grade_id == grade.id,
                RoleDataGradeAccess.access_allowed == True
            )
        )
        accesses = accesses_query.scalars().all()
        
        # 获取角色名称
        role_names = []
        for access in accesses:
            if access.role:
                role_names.append(access.role.name)
        
        summary = DataGradeAccessSummary(
            grade_id=grade.id,
            grade_name=grade.name.value,
            category_name=grade.category.name,
            sensitivity_level=grade.get_sensitivity_level(),
            roles_with_access=role_names
        )
        result.append(summary)
    
    return result

@router.get("/available-grades")
async def get_available_data_grades(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有可用的数据分级列表"""
    
    grades_query = await db.execute(
        select(DataSourceGrade).join(DataSourceCategory).order_by(
            DataSourceCategory.name, DataSourceGrade.name
        )
    )
    grades = grades_query.scalars().all()
    
    result = []
    for grade in grades:
        result.append({
            "id": grade.id,
            "name": grade.name.value,
            "category_name": grade.category.name,
            "sensitivity_level": grade.get_sensitivity_level(),
            "description": grade.description
        })
    
    return {"grades": result}

# 新增：基于用户级别权限的数据访问控制
@router.get("/accessible-grades")
async def get_user_accessible_grades(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户基于级别权限可访问的数据分级列表"""
    
    try:
        # 获取用户可访问的敏感度级别
        accessible_levels = get_accessible_data_sensitivity_levels(current_user)
        print(f"DEBUG: user={current_user.username}, accessible_levels={accessible_levels}")
    except Exception as e:
        print(f"DEBUG: Error in get_accessible_data_sensitivity_levels: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
    
    # 查询所有数据分级
    grades_query = await db.execute(
        select(DataSourceGrade).join(DataSourceCategory).options(
            selectinload(DataSourceGrade.category)
        ).order_by(
            DataSourceCategory.name, DataSourceGrade.name
        )
    )
    grades = grades_query.scalars().all()
    
    # 过滤用户可访问的分级
    accessible_grades = []
    for grade in grades:
        grade_sensitivity = grade.get_sensitivity_level()
        if grade_sensitivity in accessible_levels:
            accessible_grades.append({
                "id": grade.id,
                "name": grade.name.value,
                "category_name": grade.category.name,
                "sensitivity_level": grade_sensitivity,
                "description": grade.description,
                "accessible": True
            })
        else:
            # 不可访问的数据，只显示基本信息
            accessible_grades.append({
                "id": grade.id,
                "name": grade.name.value,
                "category_name": grade.category.name,
                "sensitivity_level": grade_sensitivity,
                "description": "权限不足，无法查看详细信息",
                "accessible": False
            })
    
    user_level_info = {
        "user_id": current_user.id,
        "username": current_user.username,
        "level_name": current_user.level.name if current_user.level else "未分配级别",
        "rank_value": current_user.level.rank_value if current_user.level else None,
        "accessible_sensitivity_levels": accessible_levels
    }
    
    return {
        "user_level_info": user_level_info,
        "total_grades": len(grades),
        "accessible_count": len([g for g in accessible_grades if g["accessible"]]),
        "grades": accessible_grades
    } 