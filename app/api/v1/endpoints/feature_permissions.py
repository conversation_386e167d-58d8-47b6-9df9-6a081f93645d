from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Dict, Any
from pydantic import BaseModel

from app.db.session import get_db
from app.db.models.role import Role
from app.db.models.role_feature_permission import RoleFeaturePermission
from app.api.async_deps import get_current_user
from app.db.models.user import User
from app.services.knowledge_base_feature_permissions import KnowledgeBaseFeaturePermissionService

router = APIRouter()

# Pydantic模型
class FeaturePermissionUpdate(BaseModel):
    feature_name: str
    can_access: bool
    can_create: bool = False
    can_read: bool = False
    can_update: bool = False
    can_delete: bool = False

class FeaturePermissionResponse(BaseModel):
    feature_name: str
    can_access: bool
    can_create: bool
    can_read: bool
    can_update: bool
    can_delete: bool
    description: str = ""

class RoleFeaturePermissionsResponse(BaseModel):
    role_id: int
    role_name: str
    module_name: str
    feature_permissions: List[FeaturePermissionResponse]

@router.get("/roles/{role_id}/feature-permissions/{module_name}", response_model=RoleFeaturePermissionsResponse)
async def get_role_feature_permissions(
    role_id: int,
    module_name: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定角色在特定模块的功能权限配置"""

    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )

    # 获取功能权限
    feature_permissions_query = await db.execute(
        select(RoleFeaturePermission).where(
            RoleFeaturePermission.role_id == role_id,
            RoleFeaturePermission.module_name == module_name
        )
    )
    feature_permissions = feature_permissions_query.scalars().all()

    # 如果是知识库模块，确保所有预定义功能都有权限记录
    if module_name == 'KNOWLEDGE_BASE':
        available_features = KnowledgeBaseFeaturePermissionService.get_all_features()
        
        # 创建权限字典，方便查找
        permission_dict = {perm.feature_name: perm for perm in feature_permissions}
        
        # 确保所有功能都有权限记录，没有的默认为False
        complete_feature_permissions = []
        for feature_name, feature_info in available_features.items():
            if feature_name in permission_dict:
                perm = permission_dict[feature_name]
                complete_feature_permissions.append(
                    FeaturePermissionResponse(
                        feature_name=feature_name,
                        can_access=perm.can_access,
                        can_create=perm.can_create,
                        can_read=perm.can_read,
                        can_update=perm.can_update,
                        can_delete=perm.can_delete,
                        description=perm.description or feature_info.get('description', '')
                    )
                )
            else:
                # 创建默认权限记录
                complete_feature_permissions.append(
                    FeaturePermissionResponse(
                        feature_name=feature_name,
                        can_access=False,
                        can_create=False,
                        can_read=False,
                        can_update=False,
                        can_delete=False,
                        description=feature_info.get('description', '')
                    )
                )
    else:
        # 其他模块直接返回现有权限
        complete_feature_permissions = [
            FeaturePermissionResponse(
                feature_name=perm.feature_name,
                can_access=perm.can_access,
                can_create=perm.can_create,
                can_read=perm.can_read,
                can_update=perm.can_update,
                can_delete=perm.can_delete,
                description=perm.description or ""
            )
            for perm in feature_permissions
        ]

    return RoleFeaturePermissionsResponse(
        role_id=role.id,
        role_name=role.name,
        module_name=module_name,
        feature_permissions=complete_feature_permissions
    )

@router.put("/roles/{role_id}/feature-permissions/{module_name}")
async def update_role_feature_permissions(
    role_id: int,
    module_name: str,
    permissions: List[FeaturePermissionUpdate],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新角色的功能权限"""
    
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # 批量更新权限
    for perm_update in permissions:
        # 查找现有权限记录
        existing_perm_query = await db.execute(
            select(RoleFeaturePermission).where(
                RoleFeaturePermission.role_id == role_id,
                RoleFeaturePermission.module_name == module_name,
                RoleFeaturePermission.feature_name == perm_update.feature_name
            )
        )
        existing_perm = existing_perm_query.scalar_one_or_none()
        
        if existing_perm:
            # 更新现有权限
            existing_perm.can_access = perm_update.can_access
            existing_perm.can_create = perm_update.can_create
            existing_perm.can_read = perm_update.can_read
            existing_perm.can_update = perm_update.can_update
            existing_perm.can_delete = perm_update.can_delete
        else:
            # 创建新权限记录
            # 获取功能描述
            description = ""
            if module_name == 'KNOWLEDGE_BASE':
                feature_info = KnowledgeBaseFeaturePermissionService.get_feature_definition(perm_update.feature_name)
                description = feature_info.get('description', '') if feature_info else ""
            
            new_perm = RoleFeaturePermission(
                role_id=role_id,
                module_name=module_name,
                feature_name=perm_update.feature_name,
                can_access=perm_update.can_access,
                can_create=perm_update.can_create,
                can_read=perm_update.can_read,
                can_update=perm_update.can_update,
                can_delete=perm_update.can_delete,
                description=description
            )
            db.add(new_perm)
    
    await db.commit()
    
    return {"message": "Role feature permissions updated successfully"}

@router.get("/available-features/{module_name}")
async def get_available_features(
    module_name: str,
    current_user: User = Depends(get_current_user)
):
    """获取指定模块的所有可用功能列表"""
    
    if module_name == 'KNOWLEDGE_BASE':
        features = KnowledgeBaseFeaturePermissionService.get_all_features()
        return {
            "module_name": module_name,
            "features": [
                {
                    "feature_name": feature_name,
                    "name": feature_info.get('name', feature_name),
                    "description": feature_info.get('description', ''),
                    "route": feature_info.get('route', '')
                }
                for feature_name, feature_info in features.items()
            ]
        }
    else:
        # 其他模块暂时返回空列表
        return {
            "module_name": module_name,
            "features": []
        }

@router.get("/users/{user_id}/accessible-features/{module_name}")
async def get_user_accessible_features(
    user_id: int,
    module_name: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户在指定模块的可访问功能列表"""
    
    # 获取用户信息
    user = await db.get(User, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if module_name == 'KNOWLEDGE_BASE':
        accessible_features = await KnowledgeBaseFeaturePermissionService.get_accessible_features(db, user)
        return {
            "user_id": user_id,
            "module_name": module_name,
            "accessible_features": accessible_features
        }
    else:
        # 其他模块暂时返回空列表
        return {
            "user_id": user_id,
            "module_name": module_name,
            "accessible_features": []
        }

@router.get("/current-user/accessible-features/{module_name}")
async def get_current_user_accessible_features(
    module_name: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户在指定模块的可访问功能列表"""
    
    if module_name == 'KNOWLEDGE_BASE':
        accessible_features = await KnowledgeBaseFeaturePermissionService.get_accessible_features(db, current_user)
        return {
            "user_id": current_user.id,
            "module_name": module_name,
            "accessible_features": accessible_features
        }
    else:
        # 其他模块暂时返回空列表
        return {
            "user_id": current_user.id,
            "module_name": module_name,
            "accessible_features": []
        }

@router.get("/roles/{role_id}/all-feature-permissions")
async def get_role_all_feature_permissions(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定角色的所有功能权限配置"""
    
    # 检查角色是否存在
    role = await db.get(Role, role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # 获取所有功能权限
    feature_permissions_query = await db.execute(
        select(RoleFeaturePermission).where(
            RoleFeaturePermission.role_id == role_id
        )
    )
    feature_permissions = feature_permissions_query.scalars().all()
    
    # 按模块分组
    permissions_by_module = {}
    for perm in feature_permissions:
        if perm.module_name not in permissions_by_module:
            permissions_by_module[perm.module_name] = []
        
        permissions_by_module[perm.module_name].append(
            FeaturePermissionResponse(
                feature_name=perm.feature_name,
                can_access=perm.can_access,
                can_create=perm.can_create,
                can_read=perm.can_read,
                can_update=perm.can_update,
                can_delete=perm.can_delete,
                description=perm.description or ""
            )
        )
    
    return {
        "role_id": role.id,
        "role_name": role.name,
        "feature_permissions_by_module": permissions_by_module
    }
