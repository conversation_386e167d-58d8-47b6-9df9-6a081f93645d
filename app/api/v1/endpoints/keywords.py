from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app import models, schemas
from app.api import deps
from app.core.security import require_module_permission
from app.db.models.keyword import Keyword

router = APIRouter()

@router.get("/", response_model=schemas.KeywordListResponse)
def get_keywords(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT")),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
    group_id: int = Query(None, description="按分组ID过滤"),
    active_only: bool = Query(False, description="是否只返回激活的关键词"),
    search: str = Query(None, description="搜索关键词内容")
) -> Any:
    """
    获取关键词列表（管理员功能）
    """
    # 构建查询
    query = db.query(models.Keyword).join(models.KeywordGroup)
    
    if group_id:
        query = query.filter(models.Keyword.keyword_group_id == group_id)
    
    if active_only:
        query = query.filter(models.Keyword.is_active == True)
    
    if search:
        query = query.filter(models.Keyword.word.contains(search))
    
    # 获取总数
    total = query.count()
    
    # 分页获取数据，按优先级降序，创建时间升序排列
    keywords = query.order_by(
        models.Keyword.priority.desc(), 
        models.Keyword.created_at.asc()
    ).offset(skip).limit(limit).all()
    
    # 构建返回数据，包含分组名称
    items = []
    for keyword in keywords:
        keyword_dict = {
            "id": keyword.id,
            "word": keyword.word,
            "match_type": keyword.match_type,
            "is_active": keyword.is_active,
            "priority": keyword.priority,
            "description": keyword.description,
            "keyword_group_id": keyword.keyword_group_id,
            "created_at": keyword.created_at,
            "updated_at": keyword.updated_at,
            "keyword_group_name": keyword.keyword_group.name
        }
        keyword_data = schemas.KeywordRead(**keyword_dict)
        items.append(keyword_data)
    
    return schemas.KeywordListResponse(
        items=items,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )

@router.post("/", response_model=schemas.KeywordSchema)
def create_keyword(
    keyword_in: schemas.KeywordCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    创建新的关键词（管理员功能）
    """
    # 检查关键词分组是否存在
    group = db.query(models.KeywordGroup).filter(
        models.KeywordGroup.id == keyword_in.keyword_group_id
    ).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指定的关键词分组不存在"
        )
    
    # 检查同一分组内是否已存在相同的关键词
    existing_keyword = db.query(models.Keyword).filter(
        models.Keyword.word == keyword_in.word,
        models.Keyword.keyword_group_id == keyword_in.keyword_group_id
    ).first()
    if existing_keyword:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该分组内已存在相同的关键词"
        )
    
    # 创建新关键词
    db_keyword = models.Keyword(**keyword_in.dict())
    db.add(db_keyword)
    db.commit()
    db.refresh(db_keyword)
    
    return db_keyword

@router.get("/{keyword_id}", response_model=schemas.KeywordSchema)
def get_keyword(
    keyword_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    获取指定关键词的信息（管理员功能）
    """
    keyword = db.query(models.Keyword).filter(
        models.Keyword.id == keyword_id
    ).first()
    
    if not keyword:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词不存在"
        )
    
    return keyword

@router.put("/{keyword_id}", response_model=schemas.KeywordSchema)
def update_keyword(
    keyword_id: int,
    keyword_in: schemas.KeywordUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    更新关键词（管理员功能）
    """
    # 查找要更新的关键词
    keyword = db.query(models.Keyword).filter(
        models.Keyword.id == keyword_id
    ).first()
    
    if not keyword:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词不存在"
        )
    
    # 如果要更新分组，检查分组是否存在
    if keyword_in.keyword_group_id and keyword_in.keyword_group_id != keyword.keyword_group_id:
        group = db.query(models.KeywordGroup).filter(
            models.KeywordGroup.id == keyword_in.keyword_group_id
        ).first()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的关键词分组不存在"
            )
    
    # 如果要更新关键词内容或分组，检查是否与其他关键词重复
    target_group_id = keyword_in.keyword_group_id or keyword.keyword_group_id
    target_word = keyword_in.word or keyword.word
    
    if (keyword_in.word and keyword_in.word != keyword.word) or \
       (keyword_in.keyword_group_id and keyword_in.keyword_group_id != keyword.keyword_group_id):
        existing_keyword = db.query(models.Keyword).filter(
            models.Keyword.word == target_word,
            models.Keyword.keyword_group_id == target_group_id,
            models.Keyword.id != keyword_id
        ).first()
        if existing_keyword:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该分组内已存在相同的关键词"
            )
    
    # 更新关键词信息
    update_data = keyword_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(keyword, field, value)
    
    db.add(keyword)
    db.commit()
    db.refresh(keyword)
    
    return keyword

@router.delete("/{keyword_id}")
def delete_keyword(
    keyword_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("KEYWORD_MANAGEMENT"))
) -> Any:
    """
    删除关键词（管理员功能）
    """
    # 查找要删除的关键词
    keyword = db.query(models.Keyword).filter(
        models.Keyword.id == keyword_id
    ).first()
    
    if not keyword:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关键词不存在"
        )
    
    # 删除关键词
    db.delete(keyword)
    db.commit()
    
    return {"message": "关键词删除成功"}

@router.post("/check", response_model=schemas.KeywordMatchResult)
def check_keyword_match(
    request_data: dict,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    user_role_id: int = Query(None, description="用户角色ID（用于角色相关的关键词策略）")
) -> Any:
    """
    检查文本是否匹配关键词（安全检查功能）
    所有用户都可以调用，但会根据用户角色应用不同的关键词策略
    """
    # 从请求数据中提取文本
    text = request_data.get("text", "")
    if not text:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少文本参数"
        )
    
    # 获取激活的关键词（后续可根据用户角色过滤）
    query = db.query(models.Keyword).join(models.KeywordGroup).filter(
        models.Keyword.is_active == True,
        models.KeywordGroup.is_active == True
    )
    
    # TODO: 这里可以根据用户角色过滤关键词分组
    # 目前先返回所有激活的关键词
    
    keywords = query.order_by(models.Keyword.priority.desc()).all()
    
    # 检查是否匹配任何关键词
    for keyword in keywords:
        if keyword.matches_text(text):
            return schemas.KeywordMatchResult(
                matched=True,
                keyword_id=keyword.id,
                keyword_word=keyword.word,
                keyword_group_name=keyword.keyword_group.name,
                match_type=keyword.match_type,
                message=f"内容包含敏感关键词：{keyword.word}"
            )
    
    # 未匹配到任何关键词
    return schemas.KeywordMatchResult(
        matched=False,
        message="内容未匹配到敏感关键词"
    )

@router.post("/batch-check", response_model=List[schemas.KeywordMatchResult])
def batch_check_keyword_match(
    texts: List[str],
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    user_role_id: int = Query(None, description="用户角色ID")
) -> Any:
    """
    批量检查多个文本是否匹配关键词（安全检查功能）
    """
    if len(texts) > 10:  # 限制批量检查的数量
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="批量检查的文本数量不能超过10个"
        )
    
    results = []
    for text in texts:
        # 调用单个检查接口
        result = check_keyword_match({"text": text}, db, current_user, user_role_id)
        results.append(result)
    
    return results 