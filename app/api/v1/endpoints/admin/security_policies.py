from typing import List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.core.security import require_module_permission
from app.db.models.security_policy import SecurityPolicy
from app.db.models.role import Role
from app.db.models.level import Level  
from app.db.models.user import User
from app.schemas.security_policy import (
    SecurityPolicyCreate,
    SecurityPolicyUpdate,
    SecurityPolicyResponse,
    SecurityPolicyTargetInfo
)
import logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=SecurityPolicyResponse)
def create_security_policy(
    *,
    db: Session = Depends(get_db),
    policy_in: SecurityPolicyCreate,
    current_user = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    创建新的安全策略
    """
    # 验证目标对象存在且只指定了一个
    target_count = sum([
        policy_in.role_id is not None,
        policy_in.level_id is not None,
        policy_in.user_id is not None
    ])
    
    if target_count != 1:
        raise HTTPException(
            status_code=400,
            detail="必须且只能指定一个目标对象（role_id、level_id 或 user_id）"
        )
    
    # 验证目标对象存在
    if policy_in.role_id:
        role = db.query(Role).filter(Role.id == policy_in.role_id).first()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
    
    if policy_in.level_id:
        level = db.query(Level).filter(Level.id == policy_in.level_id).first()
        if not level:
            raise HTTPException(status_code=404, detail="等级不存在")
    
    if policy_in.user_id:
        user = db.query(User).filter(User.id == policy_in.user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
    
    # 检查是否已存在相同目标的策略
    existing_policy = db.query(SecurityPolicy).filter(
        SecurityPolicy.role_id == policy_in.role_id,
        SecurityPolicy.level_id == policy_in.level_id, 
        SecurityPolicy.user_id == policy_in.user_id
    ).first()
    
    if existing_policy:
        raise HTTPException(status_code=400, detail="该目标已存在安全策略")
    
    # 创建策略
    policy_data = policy_in.model_dump()
    db_policy = SecurityPolicy(**policy_data)
    
    db.add(db_policy)
    db.commit()
    db.refresh(db_policy)
    
    logger.info(f"管理员 {current_user.username} 创建了安全策略: {db_policy.name}")
    
    return db_policy

@router.get("/")
def list_security_policies(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    获取安全策略列表
    """
    try:
        policies = db.query(SecurityPolicy).offset(skip).limit(limit).all()
        
        # 手动构建响应数据以避免序列化问题
        result = []
        for policy in policies:
            policy_data = {
                "id": policy.id,
                "name": policy.name,
                "description": policy.description,
                "enabled": policy.enabled,
                "role_id": policy.role_id,
                "level_id": policy.level_id,
                "user_id": policy.user_id,
                "keyword_enabled": policy.keyword_enabled,
                "keyword_priority_threshold": policy.keyword_priority_threshold,
                "max_severity_threshold": policy.max_severity_threshold,
                "bypass_enabled": policy.bypass_enabled,
                "created_at": policy.created_at.isoformat() if policy.created_at else None,
                "updated_at": policy.updated_at.isoformat() if policy.updated_at else None
            }
            
            # 添加目标信息
            if policy.role_id and policy.role:
                policy_data["target_name"] = policy.role.name
                policy_data["target_type"] = "role"
            elif policy.level_id and policy.level:
                policy_data["target_name"] = policy.level.name
                policy_data["target_type"] = "level"
            elif policy.user_id and policy.user:
                policy_data["target_name"] = policy.user.username
                policy_data["target_type"] = "user"
                
            result.append(policy_data)
            
        return result
        
    except Exception as e:
        logger.error(f"Error listing security policies: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取安全策略列表失败: {str(e)}")

@router.get("/{policy_id}")
def get_security_policy(
    *,
    db: Session = Depends(get_db),
    policy_id: int,
    current_user = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    获取指定的安全策略详情
    """
    try:
        policy = db.query(SecurityPolicy).filter(SecurityPolicy.id == policy_id).first()
        if not policy:
            raise HTTPException(status_code=404, detail="安全策略不存在")
        
        # 手动构建响应数据
        policy_data = {
            "id": policy.id,
            "name": policy.name,
            "description": policy.description,
            "enabled": policy.enabled,
            "role_id": policy.role_id,
            "level_id": policy.level_id,
            "user_id": policy.user_id,
            "keyword_enabled": policy.keyword_enabled,
            "keyword_priority_threshold": policy.keyword_priority_threshold,
            "allowed_keyword_groups": policy.allowed_keyword_groups,
            "blocked_keyword_groups": policy.blocked_keyword_groups,
            "max_severity_threshold": policy.max_severity_threshold,
            "timeout_seconds": policy.timeout_seconds,
            "bypass_enabled": policy.bypass_enabled,
            "bypassable_modules": policy.bypassable_modules,
            "enabled_modules": policy.enabled_modules,
            "disabled_modules": policy.disabled_modules,
            "module_configs": policy.module_configs,
            "enable_early_termination": policy.enable_early_termination,
            "auto_desensitize": policy.auto_desensitize,
            "block_on_critical": policy.block_on_critical,
            "log_all_requests": policy.log_all_requests,
            "priority": policy.priority,
            "created_by": policy.created_by,
            "created_at": policy.created_at.isoformat() if policy.created_at else None,
            "updated_at": policy.updated_at.isoformat() if policy.updated_at else None
        }
        
        # 添加目标信息
        if policy.role_id and policy.role:
            policy_data["target_name"] = policy.role.name
            policy_data["target_type"] = "role"
        elif policy.level_id and policy.level:
            policy_data["target_name"] = policy.level.name
            policy_data["target_type"] = "level"
        elif policy.user_id and policy.user:
            policy_data["target_name"] = policy.user.username
            policy_data["target_type"] = "user"
            
        return policy_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting security policy {policy_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取安全策略失败: {str(e)}")

@router.put("/{policy_id}", response_model=SecurityPolicyResponse)
def update_security_policy(
    *,
    db: Session = Depends(get_db),
    policy_id: int,
    policy_update: SecurityPolicyUpdate,
    current_user = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    更新安全策略
    """
    policy = db.query(SecurityPolicy).filter(SecurityPolicy.id == policy_id).first()
    if not policy:
        raise HTTPException(status_code=404, detail="安全策略不存在")
    
    # 更新字段
    update_data = policy_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(policy, field, value)
    
    db.commit()
    db.refresh(policy)
    
    logger.info(f"管理员 {current_user.username} 更新了安全策略: {policy.name}")
    
    return policy

@router.delete("/{policy_id}")
def delete_security_policy(
    *,
    db: Session = Depends(get_db),
    policy_id: int,
    current_user = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    删除安全策略
    """
    policy = db.query(SecurityPolicy).filter(SecurityPolicy.id == policy_id).first()
    if not policy:
        raise HTTPException(status_code=404, detail="安全策略不存在")
    
    policy_name = policy.name
    db.delete(policy)
    db.commit()
    
    logger.info(f"管理员 {current_user.username} 删除了安全策略: {policy_name}")
    
    return {"message": "安全策略删除成功"}

@router.get("/target-info/{policy_id}", response_model=SecurityPolicyTargetInfo)
def get_policy_target_info(
    *,
    db: Session = Depends(get_db),
    policy_id: int,
    current_user = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    获取安全策略的目标对象信息
    """
    policy = db.query(SecurityPolicy).filter(SecurityPolicy.id == policy_id).first()
    if not policy:
        raise HTTPException(status_code=404, detail="安全策略不存在")
    
    target_info = {
        "target_type": None,
        "target_name": None,
        "target_description": None
    }
    
    if policy.role_id:
        role = db.query(Role).filter(Role.id == policy.role_id).first()
        if role:
            target_info.update({
                "target_type": "role",
                "target_name": role.name,
                "target_description": role.description
            })
    
    elif policy.level_id:
        level = db.query(Level).filter(Level.id == policy.level_id).first()
        if level:
            target_info.update({
                "target_type": "level",
                "target_name": level.name,
                "target_description": level.description
            })
    
    elif policy.user_id:
        user = db.query(User).filter(User.id == policy.user_id).first()
        if user:
            target_info.update({
                "target_type": "user",
                "target_name": user.username,
                "target_description": f"用户 {user.username} ({user.email})"
            })
    
    return target_info 