from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from typing import List
from pydantic import BaseModel

from app.db.session import get_db
from app.db.models.level_data_access import LevelDataAccess
from app.db.models.level import Level
from app.api.async_deps import get_current_user
from app.db.models.user import User
from app.core.permissions import is_admin

router = APIRouter()

# Pydantic模型
class LevelDataAccessResponse(BaseModel):
    id: int
    level_id: int
    level_name: str
    sensitivity_level: int
    sensitivity_name: str
    can_access: bool

class LevelDataAccessUpdate(BaseModel):
    sensitivity_level: int
    can_access: bool

class LevelDataAccessBatchUpdate(BaseModel):
    level_id: int
    access_rules: List[LevelDataAccessUpdate]

# 敏感度级别映射
SENSITIVITY_LEVEL_NAMES = {
    1: "高级敏感",
    2: "中级敏感", 
    3: "初级敏感",
    4: "完全开放"
}

@router.get("/levels/{level_id}/data-access", response_model=List[LevelDataAccessResponse])
async def get_level_data_access(
    level_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定级别的数据访问权限配置"""
    
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以查看级别数据访问权限"
        )
    
    # 检查级别是否存在
    level_result = await db.execute(select(Level).where(Level.id == level_id))
    level = level_result.scalar_one_or_none()
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="级别不存在"
        )
    
    # 查询该级别的数据访问权限
    access_result = await db.execute(
        select(LevelDataAccess).where(LevelDataAccess.level_id == level_id)
    )
    access_permissions = access_result.scalars().all()
    
    # 构建完整的权限列表（包括未配置的）
    configured_levels = {perm.sensitivity_level: perm.can_access for perm in access_permissions}
    
    result = []
    for sensitivity_level in [1, 2, 3, 4]:
        can_access = configured_levels.get(sensitivity_level, False)  # 默认不允许访问
        result.append(LevelDataAccessResponse(
            id=0,  # 如果没有配置记录，ID为0
            level_id=level_id,
            level_name=level.name,
            sensitivity_level=sensitivity_level,
            sensitivity_name=SENSITIVITY_LEVEL_NAMES[sensitivity_level],
            can_access=can_access
        ))
    
    return result

@router.put("/levels/{level_id}/data-access")
async def update_level_data_access(
    level_id: int,
    access_rules: List[LevelDataAccessUpdate],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量更新级别的数据访问权限"""
    
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以修改级别数据访问权限"
        )
    
    # 检查级别是否存在
    level_result = await db.execute(select(Level).where(Level.id == level_id))
    level = level_result.scalar_one_or_none()
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="级别不存在"
        )
    
    # 验证敏感度级别
    for rule in access_rules:
        if rule.sensitivity_level not in [1, 2, 3, 4]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的敏感度级别: {rule.sensitivity_level}"
            )
    
    # 删除现有配置
    await db.execute(
        delete(LevelDataAccess).where(LevelDataAccess.level_id == level_id)
    )
    
    # 添加新配置 - 存储所有权限记录（包括 True 和 False）
    for rule in access_rules:
        new_access = LevelDataAccess(
            level_id=level_id,
            sensitivity_level=rule.sensitivity_level,
            can_access=rule.can_access
        )
        db.add(new_access)
    
    await db.commit()
    
    return {"message": f"级别 '{level.name}' 的数据访问权限已更新"}

@router.get("/levels/data-access/summary")
async def get_all_levels_data_access_summary(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有级别的数据访问权限汇总"""
    
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以查看数据访问权限汇总"
        )
    
    # 查询所有级别
    levels_result = await db.execute(select(Level).order_by(Level.rank_value))
    levels = levels_result.scalars().all()
    
    # 查询所有数据访问权限
    access_result = await db.execute(select(LevelDataAccess))
    all_access = access_result.scalars().all()
    
    # 按级别组织权限数据
    access_by_level = {}
    for access in all_access:
        if access.level_id not in access_by_level:
            access_by_level[access.level_id] = {}
        access_by_level[access.level_id][access.sensitivity_level] = access.can_access
    
    # 构建汇总数据
    summary = []
    for level in levels:
        level_access = access_by_level.get(level.id, {})
        
        # 如果没有配置权限，使用默认规则
        if not level_access:
            from app.core.security import _get_default_accessibility_by_rank
            default_levels = _get_default_accessibility_by_rank(level.rank_value)
            for sensitivity_level in default_levels:
                level_access[sensitivity_level] = True
        
        # 构建权限矩阵
        permissions = {}
        actual_accessible_count = 0
        for sensitivity_level in [1, 2, 3, 4]:
            can_access = level_access.get(sensitivity_level, False)
            permissions[SENSITIVITY_LEVEL_NAMES[sensitivity_level]] = can_access
            if can_access:
                actual_accessible_count += 1
        
        summary.append({
            "level_id": level.id,
            "level_name": level.name,
            "rank_value": level.rank_value,
            "permissions": permissions,
            "accessible_count": actual_accessible_count
        })
    
    return {
        "total_levels": len(levels),
        "sensitivity_levels": SENSITIVITY_LEVEL_NAMES,
        "summary": summary
    }

@router.post("/levels/{level_id}/data-access/reset-default")
async def reset_level_data_access_to_default(
    level_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """将级别的数据访问权限重置为默认规则"""
    
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以重置级别数据访问权限"
        )
    
    # 检查级别是否存在
    level_result = await db.execute(select(Level).where(Level.id == level_id))
    level = level_result.scalar_one_or_none()
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="级别不存在"
        )
    
    # 删除现有配置
    await db.execute(
        delete(LevelDataAccess).where(LevelDataAccess.level_id == level_id)
    )
    
    # 根据rank_value设置默认权限
    from app.core.security import _get_default_accessibility_by_rank
    default_levels = _get_default_accessibility_by_rank(level.rank_value)
    
    # 添加默认权限配置
    for sensitivity_level in default_levels:
        new_access = LevelDataAccess(
            level_id=level_id,
            sensitivity_level=sensitivity_level,
            can_access=True
        )
        db.add(new_access)
    
    await db.commit()
    
    return {
        "message": f"级别 '{level.name}' 的数据访问权限已重置为默认规则",
        "default_accessible_levels": default_levels
    } 