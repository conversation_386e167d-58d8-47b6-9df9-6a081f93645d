"""
用户模型分配管理API
支持为用户分配模型、设置默认模型、批量操作等功能
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_
from sqlalchemy.orm import selectinload

from app.db.session import get_db
from app.api.async_deps import get_current_active_user
from app.core.permissions import can_assign_models_to_user, is_admin, can_view_user_permissions
from app.core.audit import OperationType, format_operation_details, get_client_info, AuditLogger
from app.db.models.user import User
from app.db.models.llm_model import LLMModel
from app.db.models.user_model_assignment import UserModelAssignment
from app.schemas.user_model_assignment import (
    UserMultiModelAssignmentRequest,
    SetDefaultModelRequest,
    ModelAssignmentResponse,
    UserAvailableModel,
    UserModelStats
)

router = APIRouter()

@router.get("/all", response_model=List[UserModelStats])
async def get_all_user_model_assignments(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有用户的模型分配统计"""
    # 检查管理员权限
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    # 获取所有用户
    users_result = await db.execute(
        select(User)
        .options(selectinload(User.role))
        .options(selectinload(User.level))
    )
    users = users_result.scalars().all()
    
    # 获取所有模型分配
    assignments_result = await db.execute(
        select(UserModelAssignment)
        .options(selectinload(UserModelAssignment.model))
    )
    assignments = assignments_result.scalars().all()
    
    # 按用户分组统计
    user_assignments = {}
    for assignment in assignments:
        user_id = assignment.user_id
        if user_id not in user_assignments:
            user_assignments[user_id] = []
        user_assignments[user_id].append(assignment)
    
    # 构建响应
    result = []
    for user in users:
        user_model_assignments = user_assignments.get(user.id, [])
        default_model = next((a.model.name for a in user_model_assignments if a.is_default), None)
        
        result.append(UserModelStats(
            user_id=user.id,
            username=user.username,
            email=user.email,
            role_name=user.role.name if user.role else "未知角色",
            level_name=user.level.name if user.level else "未知等级",
            is_active=user.is_active,
            assigned_models_count=len(user_model_assignments),
            default_model=default_model,
            has_assignments=len(user_model_assignments) > 0
        ))
    
    return result

@router.get("/users/{user_id}/models", response_model=List[UserAvailableModel])
async def get_user_available_models(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户可用的模型列表"""
    # 检查权限
    all_users_result = await db.execute(select(User))
    all_users = all_users_result.scalars().all()
    target_user = next((u for u in all_users if u.id == user_id), None)
    
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not can_assign_models_to_user(current_user, target_user, all_users):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该用户的模型分配"
        )
    
    # 获取所有模型
    models_result = await db.execute(select(LLMModel))
    all_models = models_result.scalars().all()
    
    # 获取用户的模型分配
    assignments_result = await db.execute(
        select(UserModelAssignment)
        .options(selectinload(UserModelAssignment.assigner))
        .where(UserModelAssignment.user_id == user_id)
    )
    assignments = assignments_result.scalars().all()
    assignment_dict = {a.model_id: a for a in assignments}
    
    # 构建响应
    result = []
    for model in all_models:
        assignment = assignment_dict.get(model.id)
        result.append(UserAvailableModel(
            id=model.id,
            name=model.name,
            api_url=model.api_url,
            is_active=model.is_active,
            is_assigned=assignment is not None,
            is_default=assignment.is_default if assignment else False,
            assigned_at=assignment.created_at if assignment else None,
            assigned_by_name=assignment.assigner.username if assignment and assignment.assigner else None
        ))
    
    return result

@router.post("/users/{user_id}/assign-models", response_model=ModelAssignmentResponse)
async def assign_models_to_user(
    user_id: int,
    request_data: UserMultiModelAssignmentRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """为用户分配多个模型"""
    # 检查权限
    all_users_result = await db.execute(select(User))
    all_users = all_users_result.scalars().all()
    target_user = next((u for u in all_users if u.id == user_id), None)
    
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not can_assign_models_to_user(current_user, target_user, all_users):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限为该用户分配模型"
        )
    
    # 验证模型存在
    models_result = await db.execute(
        select(LLMModel).where(LLMModel.id.in_(request_data.model_ids))
    )
    models = models_result.scalars().all()
    model_dict = {m.id: m for m in models}
    
    if len(models) != len(request_data.model_ids):
        missing_ids = set(request_data.model_ids) - set(model_dict.keys())
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"模型不存在: {missing_ids}"
        )
    
    # 如果需要替换现有分配，先删除
    if request_data.replace_existing:
        await db.execute(
            delete(UserModelAssignment).where(UserModelAssignment.user_id == user_id)
        )
    
    # 获取现有分配
    existing_result = await db.execute(
        select(UserModelAssignment).where(UserModelAssignment.user_id == user_id)
    )
    existing_assignments = existing_result.scalars().all()
    existing_model_ids = {a.model_id for a in existing_assignments}
    
    # 创建新分配
    created_assignments = []
    
    for model_id in request_data.model_ids:
        if model_id not in existing_model_ids:
            assignment = UserModelAssignment(
                user_id=user_id,
                model_id=model_id,
                is_default=(model_id == request_data.default_model_id),
                assigned_by=current_user.id
            )
            db.add(assignment)
            created_assignments.append(model_id)
    
    await db.commit()
    
    # 记录操作日志
    operation_details = format_operation_details(
        OperationType.MODEL_ASSIGN,
        {
            "target_username": target_user.username,
            "model_names": [model_dict[mid].name for mid in request_data.model_ids],
            "default_model": model_dict[request_data.default_model_id].name if request_data.default_model_id else None
        }
    )
    
    client_info = get_client_info(request)
    await AuditLogger.log_operation_async(
        db=db,
        operator_id=current_user.id,
        operation_type=OperationType.MODEL_ASSIGN,
        operation_details=operation_details,
        target_user_id=user_id,
        success=True,
        **client_info
    )
    
    return ModelAssignmentResponse(
        success=True,
        message=f"成功为用户 {target_user.username} 分配 {len(created_assignments)} 个模型",
        affected_assignments=len(created_assignments),
        created_assignments=created_assignments
    )

@router.post("/users/{user_id}/set-default-model", response_model=ModelAssignmentResponse)
async def set_user_default_model(
    user_id: int,
    request_data: SetDefaultModelRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """设置用户的默认模型"""
    # 检查权限
    all_users_result = await db.execute(select(User))
    all_users = all_users_result.scalars().all()
    target_user = next((u for u in all_users if u.id == user_id), None)
    
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not can_assign_models_to_user(current_user, target_user, all_users):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限设置该用户的默认模型"
        )
    
    # 检查用户是否有该模型的分配
    assignment_result = await db.execute(
        select(UserModelAssignment).where(
            and_(
                UserModelAssignment.user_id == user_id,
                UserModelAssignment.model_id == request_data.model_id
            )
        )
    )
    assignment = assignment_result.scalar_one_or_none()
    
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户未分配该模型"
        )
    
    # 清除其他默认模型
    other_assignments_result = await db.execute(
        select(UserModelAssignment).where(UserModelAssignment.user_id == user_id)
    )
    other_assignments = other_assignments_result.scalars().all()
    for other_assignment in other_assignments:
        other_assignment.is_default = False
    
    # 设置新的默认模型
    assignment.is_default = True
    await db.commit()
    
    # 记录操作日志
    model_result = await db.execute(select(LLMModel).where(LLMModel.id == request_data.model_id))
    model = model_result.scalar_one()
    
    operation_details = format_operation_details(
        OperationType.MODEL_ASSIGN,
        {
            "target_username": target_user.username,
            "action": "set_default_model",
            "model_name": model.name
        }
    )
    
    client_info = get_client_info(request)
    await AuditLogger.log_operation_async(
        db=db,
        operator_id=current_user.id,
        operation_type=OperationType.MODEL_ASSIGN,
        operation_details=operation_details,
        target_user_id=user_id,
        success=True,
        **client_info
    )
    
    return ModelAssignmentResponse(
        success=True,
        message=f"成功设置用户 {target_user.username} 的默认模型为 {model.name}",
        affected_assignments=1
    )

@router.delete("/users/{user_id}/models/{model_id}", response_model=ModelAssignmentResponse)
async def revoke_user_model_assignment(
    user_id: int,
    model_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """撤销用户的模型分配"""
    # 检查权限
    all_users_result = await db.execute(select(User))
    all_users = all_users_result.scalars().all()
    target_user = next((u for u in all_users if u.id == user_id), None)
    
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not can_assign_models_to_user(current_user, target_user, all_users):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限撤销该用户的模型分配"
        )
    
    # 查找并删除分配
    assignment_result = await db.execute(
        select(UserModelAssignment).where(
            and_(
                UserModelAssignment.user_id == user_id,
                UserModelAssignment.model_id == model_id
            )
        )
    )
    assignment = assignment_result.scalar_one_or_none()
    
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到该模型分配"
        )
    
    await db.delete(assignment)
    await db.commit()
    
    # 记录操作日志
    model_result = await db.execute(select(LLMModel).where(LLMModel.id == model_id))
    model = model_result.scalar_one()
    
    operation_details = format_operation_details(
        OperationType.MODEL_ASSIGN,
        {
            "target_username": target_user.username,
            "action": "revoke_assignment",
            "model_name": model.name
        }
    )
    
    client_info = get_client_info(request)
    await AuditLogger.log_operation_async(
        db=db,
        operator_id=current_user.id,
        operation_type=OperationType.MODEL_ASSIGN,
        operation_details=operation_details,
        target_user_id=user_id,
        success=True,
        **client_info
    )
    
    return ModelAssignmentResponse(
        success=True,
        message=f"成功撤销用户 {target_user.username} 的模型 {model.name} 分配",
        affected_assignments=1
    )

@router.get("/assignments", response_model=List[dict])
async def get_all_model_assignments(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有模型分配记录"""
    # 检查管理员权限
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    # 获取所有模型分配
    assignments_result = await db.execute(
        select(UserModelAssignment)
        .options(selectinload(UserModelAssignment.user))
        .options(selectinload(UserModelAssignment.model))
        .options(selectinload(UserModelAssignment.assigner))
    )
    assignments = assignments_result.scalars().all()
    
    # 构建响应
    result = []
    for assignment in assignments:
        result.append({
            "id": assignment.id,
            "user_id": assignment.user_id,
            "model_id": assignment.model_id,
            "is_default": assignment.is_default,
            "assigned_by": assignment.assigned_by,
            "created_at": assignment.created_at.isoformat(),
            "updated_at": assignment.updated_at.isoformat(),
            "user": {
                "username": assignment.user.username if assignment.user else None,
                "email": assignment.user.email if assignment.user else None,
            } if assignment.user else None,
            "model": {
                "name": assignment.model.name if assignment.model else None,
                "api_url": assignment.model.api_url if assignment.model else None,
            } if assignment.model else None,
            "assigner": {
                "username": assignment.assigner.username if assignment.assigner else None,
            } if assignment.assigner else None
        })
    
    return result 