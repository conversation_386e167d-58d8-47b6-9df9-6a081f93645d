from typing import List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app import models, schemas
from app.api import deps
from app.core.security import require_module_permission
from app.db.models.regex_rule import RegexRule
from app.schemas.regex_rule import IntentCategory, RuleSeverity
import re

router = APIRouter()

@router.get("/", response_model=schemas.RegexRuleListResponse)
def get_regex_rules(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY")),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    active_only: bool = Query(False, description="是否只返回激活的规则"),
    category: Optional[IntentCategory] = Query(None, description="按分类过滤"),
    severity: Optional[RuleSeverity] = Query(None, description="按严重性过滤"),
    role_id: Optional[int] = Query(None, description="按角色ID过滤"),
    search: Optional[str] = Query(None, description="搜索关键词（匹配名称和描述）")
) -> Any:
    """
    获取恶意意图识别规则列表（管理员功能）
    """
    
    # 构建查询
    query = db.query(models.RegexRule)
    
    # 应用过滤条件
    if active_only:
        query = query.filter(models.RegexRule.is_active == True)
    
    if category:
        query = query.filter(models.RegexRule.category == category)
    
    if severity:
        query = query.filter(models.RegexRule.severity == severity)
    
    if role_id:
        query = query.filter(models.RegexRule.role_id == role_id)
    
    if search:
        search_filter = or_(
            models.RegexRule.name.ilike(f"%{search}%"),
            models.RegexRule.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # 获取总数
    total = query.count()
    
    # 分页获取数据，按优先级和创建时间排序
    rules = query.order_by(
        models.RegexRule.priority.desc(),
        models.RegexRule.created_at.desc()
    ).offset(skip).limit(limit).all()
    
    # 构建返回数据
    items = []
    for rule in rules:
        rule_dict = {
            "id": rule.id,
            "name": rule.name,
            "pattern": rule.pattern,
            "description": rule.description,
            "category": rule.category,
            "severity": rule.severity,
            "is_active": rule.is_active,
            "priority": rule.priority,
            "flags": rule.flags,
            "timeout_ms": rule.timeout_ms,
            "version": rule.version,
            "parent_rule_id": rule.parent_rule_id,
            "role_id": rule.role_id,
            "match_count": rule.match_count,
            "false_positive_count": rule.false_positive_count,
            "created_at": rule.created_at,
            "updated_at": rule.updated_at,
            "created_by": rule.created_by,
            "updated_by": rule.updated_by,
            "role_name": rule.role.name if rule.role else None,
            "accuracy_rate": rule.get_accuracy_rate(),
            "has_child_versions": len(rule.child_rules) > 0
        }
        rule_data = schemas.RegexRuleRead(**rule_dict)
        items.append(rule_data)
    
    return schemas.RegexRuleListResponse(
        items=items,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )

@router.post("/", response_model=schemas.RegexRuleSchema)
def create_regex_rule(
    rule_in: schemas.RegexRuleCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    创建新的恶意意图识别规则（管理员功能）
    """
    
    # 检查规则名称是否已存在
    existing_rule = db.query(models.RegexRule).filter(
        models.RegexRule.name == rule_in.name
    ).first()
    if existing_rule:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="规则名称已存在"
        )
    
    # 验证正则表达式
    try:
        re.compile(rule_in.pattern, rule_in.flags)
    except re.error as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的正则表达式: {str(e)}"
        )
    
    # 如果指定了角色ID，验证角色是否存在
    if rule_in.role_id:
        role = db.query(models.Role).filter(models.Role.id == rule_in.role_id).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的角色不存在"
            )
    
    # 创建新规则
    rule_data = rule_in.dict()
    if not rule_data.get('created_by'):
        rule_data['created_by'] = current_user.username
    
    db_rule = models.RegexRule(**rule_data)
    db.add(db_rule)
    db.commit()
    db.refresh(db_rule)
    
    return db_rule

@router.get("/{rule_id}", response_model=schemas.RegexRuleRead)
def get_regex_rule(
    rule_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    获取指定恶意意图识别规则的信息（管理员功能）
    """
    
    rule = db.query(models.RegexRule).filter(
        models.RegexRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="恶意意图识别规则不存在"
        )
    
    # 构建返回数据，包含计算属性
    rule_dict = {
        "id": rule.id,
        "name": rule.name,
        "pattern": rule.pattern,
        "description": rule.description,
        "category": rule.category,
        "severity": rule.severity,
        "is_active": rule.is_active,
        "priority": rule.priority,
        "flags": rule.flags,
        "timeout_ms": rule.timeout_ms,
        "version": rule.version,
        "parent_rule_id": rule.parent_rule_id,
        "role_id": rule.role_id,
        "match_count": rule.match_count,
        "false_positive_count": rule.false_positive_count,
        "created_at": rule.created_at,
        "updated_at": rule.updated_at,
        "created_by": rule.created_by,
        "updated_by": rule.updated_by,
        "role_name": rule.role.name if rule.role else None,
        "accuracy_rate": rule.get_accuracy_rate(),
        "has_child_versions": len(rule.child_rules) > 0
    }
    
    return schemas.RegexRuleRead(**rule_dict)

@router.put("/{rule_id}", response_model=schemas.RegexRuleSchema)
def update_regex_rule(
    rule_id: int,
    rule_in: schemas.RegexRuleUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    更新恶意意图识别规则（管理员功能）
    """
    
    # 查找要更新的规则
    rule = db.query(models.RegexRule).filter(
        models.RegexRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="恶意意图识别规则不存在"
        )
    
    # 如果要更新名称，检查是否与其他规则重复
    if rule_in.name and rule_in.name != rule.name:
        existing_rule = db.query(models.RegexRule).filter(
            models.RegexRule.name == rule_in.name,
            models.RegexRule.id != rule_id
        ).first()
        if existing_rule:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="规则名称已存在"
            )
    
    # 如果要更新正则表达式，验证其有效性
    if rule_in.pattern:
        try:
            flags = rule_in.flags if rule_in.flags is not None else rule.flags
            re.compile(rule_in.pattern, flags)
        except re.error as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的正则表达式: {str(e)}"
            )
    
    # 如果要更新角色ID，验证角色是否存在
    if rule_in.role_id:
        role = db.query(models.Role).filter(models.Role.id == rule_in.role_id).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的角色不存在"
            )
    
    # 更新规则信息
    update_data = rule_in.dict(exclude_unset=True)
    update_data['updated_by'] = current_user.username
    
    for field, value in update_data.items():
        setattr(rule, field, value)
    
    # 如果模式或标志发生变化，增加版本号
    if rule_in.pattern or rule_in.flags is not None:
        rule.version += 1
    
    db.add(rule)
    db.commit()
    db.refresh(rule)
    
    return rule

@router.delete("/{rule_id}")
def delete_regex_rule(
    rule_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    删除恶意意图识别规则（管理员功能）
    """
    
    # 查找要删除的规则
    rule = db.query(models.RegexRule).filter(
        models.RegexRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="恶意意图识别规则不存在"
        )
    
    # 检查是否有子版本规则，如果有，先删除或更新它们
    if rule.child_rules:
        # 将子规则的父规则设置为当前规则的父规则
        for child_rule in rule.child_rules:
            child_rule.parent_rule_id = rule.parent_rule_id
        db.commit()
    
    # 删除规则
    db.delete(rule)
    db.commit()
    
    return {"message": "恶意意图识别规则删除成功"}

@router.post("/test", response_model=schemas.RegexRuleTestResponse)
def test_regex_rule(
    test_request: schemas.RegexRuleTestRequest,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    测试正则表达式规则（管理员功能）
    """
    
    try:
        # 验证正则表达式
        compiled_pattern = re.compile(test_request.pattern, test_request.flags)
        
        # 测试每个文本
        test_results = []
        for text in test_request.test_texts:
            try:
                match = compiled_pattern.search(text)
                result = {
                    "text": text,
                    "matched": bool(match),
                    "match_groups": match.groups() if match else None,
                    "match_span": match.span() if match else None,
                    "error": None
                }
            except Exception as e:
                result = {
                    "text": text,
                    "matched": False,
                    "match_groups": None,
                    "match_span": None,
                    "error": str(e)
                }
            test_results.append(result)
        
        return schemas.RegexRuleTestResponse(
            pattern_valid=True,
            test_results=test_results,
            error_message=None
        )
        
    except re.error as e:
        return schemas.RegexRuleTestResponse(
            pattern_valid=False,
            test_results=[],
            error_message=f"正则表达式语法错误: {str(e)}"
        )

@router.post("/batch-check", response_model=schemas.RegexRuleBatchCheckResponse)
def batch_check_regex_rules(
    check_request: schemas.RegexRuleBatchCheckRequest,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    批量检查文本是否匹配恶意意图识别规则（管理员功能）
    """
    
    # 构建查询
    query = db.query(models.RegexRule)
    
    if check_request.only_active:
        query = query.filter(models.RegexRule.is_active == True)
    
    if check_request.category_filter:
        query = query.filter(models.RegexRule.category.in_(check_request.category_filter))
    
    if check_request.severity_filter:
        query = query.filter(models.RegexRule.severity.in_(check_request.severity_filter))
    
    if check_request.role_id:
        query = query.filter(or_(
            models.RegexRule.role_id == check_request.role_id,
            models.RegexRule.role_id.is_(None)  # 包括全局规则
        ))
    
    # 获取规则并按优先级排序
    rules = query.order_by(models.RegexRule.priority.desc()).all()
    
    # 检查每个文本
    all_results = []
    total_matches = 0
    
    for text in check_request.texts:
        text_results = []
        for rule in rules:
            try:
                if rule.test_pattern(text):
                    match_result = schemas.RegexRuleMatchResult(
                        matched=True,
                        rule_id=rule.id,
                        rule_name=rule.name,
                        category=rule.category,
                        severity=rule.severity,
                        message=f"匹配到恶意意图: {rule.name}",
                        confidence=0.8  # 可以根据实际情况调整
                    )
                    text_results.append(match_result)
                    total_matches += 1
                    
                    # 更新匹配统计
                    rule.increment_match_count()
                    
            except Exception as e:
                # 如果规则执行出错，记录但不中断处理
                continue
        
        all_results.append(text_results)
    
    # 提交统计更新
    db.commit()
    
    return schemas.RegexRuleBatchCheckResponse(
        results=all_results,
        total_checked=len(check_request.texts),
        total_matches=total_matches
    )

@router.get("/stats", response_model=schemas.RegexRuleStats)
def get_regex_rule_stats(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    获取恶意意图识别规则统计信息（管理员功能）
    """
    
    # 总规则数
    total_rules = db.query(models.RegexRule).count()
    
    # 激活规则数
    active_rules = db.query(models.RegexRule).filter(
        models.RegexRule.is_active == True
    ).count()
    
    # 按分类统计
    rules_by_category = {}
    for category in IntentCategory:
        count = db.query(models.RegexRule).filter(
            models.RegexRule.category == category
        ).count()
        rules_by_category[category.value] = count
    
    # 按严重性统计
    rules_by_severity = {}
    for severity in RuleSeverity:
        count = db.query(models.RegexRule).filter(
            models.RegexRule.severity == severity
        ).count()
        rules_by_severity[severity.value] = count
    
    # 计算平均准确率
    rules_with_matches = db.query(models.RegexRule).filter(
        models.RegexRule.match_count > 0
    ).all()
    
    if rules_with_matches:
        total_accuracy = sum(rule.get_accuracy_rate() for rule in rules_with_matches)
        avg_accuracy_rate = total_accuracy / len(rules_with_matches)
    else:
        avg_accuracy_rate = 1.0
    
    # 匹配次数最多的规则
    top_matched_rules = db.query(models.RegexRule).filter(
        models.RegexRule.match_count > 0
    ).order_by(
        models.RegexRule.match_count.desc()
    ).limit(10).all()
    
    top_matched_list = [
        {
            "id": rule.id,
            "name": rule.name,
            "category": rule.category.value,
            "match_count": rule.match_count,
            "accuracy_rate": rule.get_accuracy_rate()
        }
        for rule in top_matched_rules
    ]
    
    return schemas.RegexRuleStats(
        total_rules=total_rules,
        active_rules=active_rules,
        rules_by_category=rules_by_category,
        rules_by_severity=rules_by_severity,
        avg_accuracy_rate=round(avg_accuracy_rate, 4),
        top_matched_rules=top_matched_list
    )

@router.post("/{rule_id}/false-positive")
def mark_false_positive(
    rule_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    标记规则为误报（管理员功能）
    """
    
    rule = db.query(models.RegexRule).filter(
        models.RegexRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="恶意意图识别规则不存在"
        )
    
    rule.increment_false_positive_count()
    db.commit()
    
    return {"message": "已标记为误报", "accuracy_rate": rule.get_accuracy_rate()}

@router.post("/{rule_id}/activate")
def activate_regex_rule(
    rule_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    激活恶意意图识别规则（管理员功能）
    """
    
    rule = db.query(models.RegexRule).filter(
        models.RegexRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="恶意意图识别规则不存在"
        )
    
    rule.is_active = True
    rule.updated_by = current_user.username
    db.commit()
    
    return {"message": "规则已激活"}

@router.post("/{rule_id}/deactivate")
def deactivate_regex_rule(
    rule_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("SECURITY_POLICY"))
) -> Any:
    """
    停用恶意意图识别规则（管理员功能）
    """
    
    rule = db.query(models.RegexRule).filter(
        models.RegexRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="恶意意图识别规则不存在"
        )
    
    rule.is_active = False
    rule.updated_by = current_user.username
    db.commit()
    
    return {"message": "规则已停用"} 