from typing import List, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps # Assuming deps.py for dependencies like get_db and get_current_active_admin_user

router = APIRouter()

@router.post("/", response_model=schemas.LLMModelSchema, status_code=status.HTTP_201_CREATED)
def create_llm_model(
    *,
    db: Session = Depends(deps.get_db),
    llm_model_in: schemas.LLMModelCreate,
    current_user: models.User = Depends(deps.get_current_active_admin_user) # Protect endpoint
) -> models.LLMModel:
    """
    Create new LLM model configuration. (Admin only)
    """
    # TODO: Add logic to encrypt llm_model_in.encrypted_api_key before saving.
    llm_model = crud.llm_model_crud.create(db=db, obj_in=llm_model_in)
    return llm_model

@router.get("/", response_model=List[schemas.LLMModelSchema])
def read_llm_models(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_admin_user) # Protect endpoint
) -> Any:
    """
    Retrieve LLM model configurations. (Admin only)
    """
    llm_models = crud.llm_model_crud.get_multi(db, skip=skip, limit=limit)
    return llm_models

@router.get("/{llm_model_id}", response_model=schemas.LLMModelSchema)
def read_llm_model(
    *,
    db: Session = Depends(deps.get_db),
    llm_model_id: int,
    current_user: models.User = Depends(deps.get_current_active_admin_user) # Protect endpoint
) -> models.LLMModel:
    """
    Get LLM model configuration by ID. (Admin only)
    """
    llm_model = crud.llm_model_crud.get(db=db, id=llm_model_id)
    if not llm_model:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="LLM Model not found")
    return llm_model

@router.put("/{llm_model_id}", response_model=schemas.LLMModelSchema)
def update_llm_model(
    *,
    db: Session = Depends(deps.get_db),
    llm_model_id: int,
    llm_model_in: schemas.LLMModelUpdate,
    current_user: models.User = Depends(deps.get_current_active_admin_user) # Protect endpoint
) -> models.LLMModel:
    """
    Update an LLM model configuration. (Admin only)
    """
    # 添加调试日志
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 更新LLM模型 ID {llm_model_id}")
    logger.info(f"📝 收到的更新数据: {llm_model_in.model_dump()}")
    
    llm_model = crud.llm_model_crud.get(db=db, id=llm_model_id)
    if not llm_model:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="LLM Model not found")
    # TODO: Add encryption logic for llm_model_in.encrypted_api_key if it's being updated
    llm_model = crud.llm_model_crud.update(db=db, db_obj=llm_model, obj_in=llm_model_in)
    
    # 验证更新结果
    logger.info(f"✅ 更新完成，新的API Key长度: {len(llm_model.api_key_encrypted) if llm_model.api_key_encrypted else 0}")
    
    return llm_model

@router.delete("/{llm_model_id}", response_model=schemas.LLMModelSchema)
def delete_llm_model(
    *,
    db: Session = Depends(deps.get_db),
    llm_model_id: int,
    current_user: models.User = Depends(deps.get_current_active_admin_user) # Protect endpoint
) -> models.LLMModel:
    """
    Delete an LLM model configuration. (Admin only)
    """
    llm_model = crud.llm_model_crud.get(db=db, id=llm_model_id)
    if not llm_model:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="LLM Model not found")
    deleted_llm_model = crud.llm_model_crud.remove(db=db, id=llm_model_id)
    return deleted_llm_model
