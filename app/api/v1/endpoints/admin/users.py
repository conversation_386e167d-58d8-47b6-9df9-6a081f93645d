# app/api/v1/endpoints/admin/users.py

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.security import require_module_permission, require_level_permission, can_manage_level_range
from app.db.models.user import User
from app.schemas.user import UserRead, UserCreate, UserUpdate
from app.services import user_service
from app.crud import crud_user

router = APIRouter()

@router.get("/", response_model=List[UserRead])
def get_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
    is_active: bool = Query(None, description="用户状态过滤"),
    search: str = Query(None, description="搜索关键词"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取用户列表 (管理员)
    """
    # 临时简化实现，返回所有用户
    from app.db.models.user import User
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.post("/", response_model=UserRead)
def create_user(
    user_in: UserCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    创建新用户 (管理员)
    """
    user = user_service.create_user(db=db, user_in=user_in)
    return user

@router.get("/{user_id}", response_model=UserRead)
def get_user(
    user_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取用户详情 (管理员)
    """
    user = user_service.get_user_by_id(db=db, user_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user

@router.put("/{user_id}", response_model=UserRead)
def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_level_permission("USER_MANAGEMENT", min_rank_value=2))
):
    """
    更新用户信息 (管理员) - 需要中级及以上权限
    """
    
    # 先获取用户
    existing_user = user_service.get_user_by_id(db=db, user_id=user_id)
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查级别权限：是否可以管理目标用户
    if not can_manage_level_range(current_user, existing_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：不能管理同级或更高级别的用户"
        )
    
    # 更新用户信息
    user = user_service.update_user(db=db, db_user=existing_user, user_update=user_update)
    return user

@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_level_permission("USER_MANAGEMENT", min_rank_value=1))
):
    """
    删除用户 (管理员) - 需要高级权限
    """
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    # 获取目标用户信息
    target_user = user_service.get_user_by_id(db=db, user_id=user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查级别权限：是否可以管理目标用户
    if not can_manage_level_range(current_user, target_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：不能删除同级或更高级别的用户"
        )
    
    user = crud_user.delete_user(db=db, user_id=user_id)
    return {"message": "用户删除成功"} 