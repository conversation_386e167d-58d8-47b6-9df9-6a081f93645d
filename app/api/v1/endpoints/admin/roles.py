from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.security import require_module_permission
from app.db.models.user import User
from app.schemas.role import RoleRead, RoleCreate, RoleUpdate

router = APIRouter()

@router.get("/", response_model=List[RoleRead])
def get_roles(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
    is_active: bool = Query(None, description="角色状态过滤"),
    search: str = Query(None, description="搜索关键词"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取角色列表 (管理员)
    """
    
    # 临时简化实现，直接查询角色
    from app.db.models.role import Role
    query = db.query(Role)
    
    if is_active is not None:
        query = query.filter(Role.is_active == is_active)
    
    if search:
        query = query.filter(Role.name.contains(search))
    
    roles = query.offset(skip).limit(limit).all()
    
    # 转换为RoleRead格式
    role_list = []
    for role in roles:
        is_system = getattr(role, 'is_system', False)
        role_dict = {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "is_active": role.is_active,
            "is_system": is_system,
            "created_at": role.created_at,
            "updated_at": role.updated_at,
            "user_count": 0,  # 暂时设为0
            "can_be_deleted": not is_system  # 系统角色不能删除，但可以编辑
        }
        role_list.append(RoleRead(**role_dict))
    
    return role_list

@router.post("/", response_model=RoleRead, status_code=status.HTTP_201_CREATED)
def create_role(
    role_in: RoleCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    创建新角色 (管理员)
    """
    from app.db.models.role import Role
    from datetime import datetime
    
    # 检查角色名是否已存在
    existing_role = db.query(Role).filter(Role.name == role_in.name).first()
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色名称已存在"
        )
    
    # 创建新角色
    db_role = Role(
        name=role_in.name,
        description=role_in.description,
        is_active=True,
        is_system=role_in.is_system or False,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    
    return RoleRead(
        id=db_role.id,
        name=db_role.name,
        description=db_role.description,
        is_active=db_role.is_active,
        is_system=db_role.is_system,
        created_at=db_role.created_at,
        updated_at=db_role.updated_at,
        user_count=0,
        can_be_deleted=True
    )

@router.get("/{role_id}", response_model=RoleRead)
def get_role(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取指定角色详情 (管理员)
    """
    from app.db.models.role import Role
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    return RoleRead(
        id=role.id,
        name=role.name,
        description=role.description,
        is_active=role.is_active,
        is_system=role.is_system,
        created_at=role.created_at,
        updated_at=role.updated_at,
        user_count=0,
        can_be_deleted=True
    )

@router.put("/{role_id}", response_model=RoleRead)
def update_role(
    role_id: int,
    role_in: RoleUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    更新角色信息 (管理员)
    """
    from app.db.models.role import Role
    from datetime import datetime
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查角色名是否已被其他角色使用
    if role_in.name and role_in.name != role.name:
        existing_role = db.query(Role).filter(
            Role.name == role_in.name,
            Role.id != role_id
        ).first()
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色名称已存在"
            )
    
    # 更新角色信息
    if role_in.name is not None:
        role.name = role_in.name
    if role_in.description is not None:
        role.description = role_in.description
    if role_in.is_active is not None:
        role.is_active = role_in.is_active
    
    role.updated_at = datetime.now()
    
    db.commit()
    db.refresh(role)
    
    return RoleRead(
        id=role.id,
        name=role.name,
        description=role.description,
        is_active=role.is_active,
        is_system=role.is_system,
        created_at=role.created_at,
        updated_at=role.updated_at,
        user_count=0,
        can_be_deleted=True
    )

@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_role(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    删除角色 (管理员)
    """
    from app.db.models.role import Role
    
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查是否为系统角色
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="系统内置角色不能删除"
        )
    
    db.delete(role)
    db.commit()

@router.get("/active/list", response_model=List[RoleRead])
def get_active_roles(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取所有激活的角色 (用于下拉选择)
    """
    
    from app.db.models.role import Role
    roles = db.query(Role).filter(Role.is_active == True).all()
    
    return [RoleRead(
        id=role.id,
        name=role.name,
        description=role.description,
        is_active=role.is_active,
        is_system=getattr(role, 'is_system', False),
        created_at=role.created_at,
        updated_at=role.updated_at,
        user_count=0,
        can_be_deleted=True
    ) for role in roles] 