# app/api/v1/endpoints/admin/hierarchy.py

"""
用户层级管理API - 修复版
支持设置管理关系、查看组织结构、权限验证等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.api.async_deps import get_db, get_current_active_user
from app.core.permissions import (
    can_manage_user, 
    get_all_subordinates,
    get_management_chain,
    validate_hierarchy_integrity,
    is_admin
)
from app.middleware.permissions import (
    require_user_management_permission,
    log_successful_operation
)
from app.core.audit import OperationType, format_operation_details, get_client_info
from app.db.models.user import User
from app.schemas.user import (
    UserHierarchy, 
    OrganizationNode, 
    SetManagerRequest,
    BatchSetManagerRequest,
    ManagerOperationResponse
)

router = APIRouter()

@router.get("/organization-tree", response_model=List[OrganizationNode])
async def get_organization_tree(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取组织架构树
    显示当前用户有权限查看的组织结构
    """
    
    # 获取所有用户，预加载role和level关系
    result = await db.execute(
        select(User).options(selectinload(User.role), selectinload(User.level))
    )
    all_users = result.scalars().all()
    
    # 构建组织树
    def build_user_node(user: User) -> OrganizationNode:
        return OrganizationNode(
            id=user.id,
            username=user.username,
            email=user.email,
            role_name=user.role.name if user.role else None,
            level_name=user.level.name if user.level else None,
            manager_id=user.manager_id,
            is_active=user.is_active,
            subordinates=[]
        )
    
    # 找到所有顶级用户（没有上级的用户）
    user_nodes = {user.id: build_user_node(user) for user in all_users}
    root_users = []
    
    for user in all_users:
        if not user.manager_id:
            root_users.append(user_nodes[user.id])
        else:
            # 添加到上级的subordinates中
            if user.manager_id in user_nodes:
                user_nodes[user.manager_id].subordinates.append(user_nodes[user.id])
    
    return root_users

@router.get("/users/{user_id}/hierarchy", response_model=UserHierarchy)
async def get_user_hierarchy(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取指定用户的层级信息
    包括上级、下属数量等
    """
    # 检查是否有查看权限，预加载role和level关系
    result = await db.execute(
        select(User)
        .options(selectinload(User.role), selectinload(User.level))
        .where(User.id == user_id)
    )
    target_user = result.scalar_one_or_none()
    
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限（可以查看自己或下属的信息）
    all_users_result = await db.execute(
        select(User).options(selectinload(User.role), selectinload(User.level))
    )
    all_users = all_users_result.scalars().all()
    
    from app.core.permissions import can_view_user_permissions
    if not can_view_user_permissions(current_user, target_user, all_users):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该用户信息"
        )
    
    # 获取上级信息
    manager_name = None
    if target_user.manager_id:
        manager_result = await db.execute(
            select(User).where(User.id == target_user.manager_id)
        )
        manager = manager_result.scalar_one_or_none()
        manager_name = manager.username if manager else None
    
    # 计算下属数量
    subordinate_count = len(get_all_subordinates(target_user, all_users))
    
    return UserHierarchy(
        id=target_user.id,
        username=target_user.username,
        email=target_user.email,
        role_name=target_user.role.name if target_user.role else None,
        level_name=target_user.level.name if target_user.level else None,
        manager_id=target_user.manager_id,
        manager_name=manager_name,
        subordinate_count=subordinate_count
    )

@router.post("/users/{user_id}/set-manager")
async def set_user_manager(
    user_id: int,
    request_data: SetManagerRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    设置或移除用户的直接上级 - 简化版本
    """
    try:
        # 简单权限检查 - 检查当前用户是否为管理员
        if not (current_user.role and current_user.role.name in ['管理员', 'super_admin', 'admin']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限管理该用户"
            )
        
        # 获取目标用户
        result = await db.execute(select(User).where(User.id == user_id))
        target_user = result.scalar_one_or_none()
        
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 验证新上级（如果指定了）
        if request_data.manager_id:
            manager_result = await db.execute(select(User).where(User.id == request_data.manager_id))
            new_manager = manager_result.scalar_one_or_none()
            
            if not new_manager:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="指定的上级用户不存在"
                )
            
            # 检查是否会创建循环引用
            if request_data.manager_id == user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户不能成为自己的上级"
                )
        
        # 更新管理关系
        target_user.manager_id = request_data.manager_id
        db.add(target_user)  # 确保对象被标记为需要更新
        
        await db.commit()
        await db.refresh(target_user)
        
        return {
            "success": True,
            "message": f"成功{'设置' if request_data.manager_id else '移除'}管理关系",
            "affected_users": [target_user.username]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置管理关系失败: {str(e)}"
        )

@router.get("/validate-hierarchy")
async def validate_hierarchy(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    验证层级结构的完整性
    检查是否存在循环引用等问题
    """
    # 获取所有用户
    all_users_result = await db.execute(
        select(User).options(selectinload(User.role), selectinload(User.level))
    )
    all_users = all_users_result.scalars().all()
    
    # 验证层级结构
    issues = validate_hierarchy_integrity(all_users)
    
    # 统计信息
    total_users = len(all_users)
    users_with_managers = len([u for u in all_users if u.manager_id])
    root_users = total_users - users_with_managers
    
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "total_users": total_users,
        "users_with_managers": users_with_managers,
        "root_users": root_users
    } 