# app/api/v1/endpoints/admin/levels.py

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.security import require_module_permission
from app.db.models.user import User
from app.schemas.level import LevelRead, LevelCreate, LevelUpdate

router = APIRouter()

@router.get("/", response_model=List[LevelRead])
def get_levels(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
    is_active: bool = Query(None, description="等级状态过滤"),
    search: str = Query(None, description="搜索关键词"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取等级列表 (管理员)
    """
    
    # 查询等级，包含角色信息
    from app.db.models.level import Level
    from app.db.models.role import Role
    
    query = db.query(Level).join(Role)
    
    if is_active is not None:
        query = query.filter(Level.is_active == is_active)
    
    if search:
        query = query.filter(Level.name.contains(search))
    
    levels = query.offset(skip).limit(limit).all()
    
    # 转换为LevelRead格式
    level_list = []
    for level in levels:
        level_dict = {
            "id": level.id,
            "role_id": getattr(level, 'role_id', 1),  # 提供默认值
            "name": level.name,
            "rank_value": getattr(level, 'rank_value', 1),  # 提供默认值
            "description": level.description,
            "is_active": level.is_active,
            "created_at": level.created_at,
            "updated_at": level.updated_at,
            "user_count": level.user_count if hasattr(level, 'user_count') else 0,
            "can_be_deleted": level.can_be_deleted() if hasattr(level, 'can_be_deleted') else True,
            "role_name": level.role.name if level.role else None
        }
        level_list.append(LevelRead(**level_dict))
    
    return level_list

@router.get("/active/list", response_model=List[LevelRead])
def get_active_levels(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取所有激活的等级 (用于下拉选择)
    """
    
    from app.db.models.level import Level
    from app.db.models.role import Role
    
    levels = db.query(Level).join(Role).filter(Level.is_active == True).all()
    
    return [LevelRead(
        id=level.id,
        role_id=getattr(level, 'role_id', 1),  # 提供默认值
        name=level.name,
        rank_value=getattr(level, 'rank_value', 1),  # 提供默认值
        description=level.description,
        is_active=level.is_active,
        created_at=level.created_at,
        updated_at=level.updated_at,
        user_count=level.user_count if hasattr(level, 'user_count') else 0,
        can_be_deleted=level.can_be_deleted() if hasattr(level, 'can_be_deleted') else True,
        role_name=level.role.name if level.role else None
    ) for level in levels] 

@router.post("/", response_model=LevelRead, status_code=status.HTTP_201_CREATED)
def create_level(
    level_data: LevelCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    创建新等级 (管理员)
    """
    
    from app.db.models.level import Level
    from app.db.models.role import Role
    
    # 检查角色是否存在
    role = db.query(Role).filter(Role.id == level_data.role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指定的角色不存在"
        )
    
    # 检查同一角色下是否已存在相同名称的等级
    existing_level = db.query(Level).filter(
        Level.role_id == level_data.role_id,
        Level.name == level_data.name
    ).first()
    if existing_level:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该角色下已存在相同名称的等级"
        )
    
    # 检查同一角色下是否已存在相同排序值的等级
    existing_rank = db.query(Level).filter(
        Level.role_id == level_data.role_id,
        Level.rank_value == level_data.rank_value
    ).first()
    if existing_rank:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该角色下已存在相同排序值的等级"
        )
    
    # 创建新等级
    db_level = Level(
        role_id=level_data.role_id,
        name=level_data.name,
        rank_value=level_data.rank_value,
        description=level_data.description,
        is_active=True
    )
    
    db.add(db_level)
    db.commit()
    db.refresh(db_level)
    
    # 获取角色名称
    role_name = db_level.role.name if db_level.role else None
    
    return LevelRead(
        id=db_level.id,
        role_id=db_level.role_id,
        name=db_level.name,
        rank_value=db_level.rank_value,
        description=db_level.description,
        is_active=db_level.is_active,
        created_at=db_level.created_at,
        updated_at=db_level.updated_at,
        user_count=0,
        can_be_deleted=True,
        role_name=role_name
    )

@router.get("/{level_id}", response_model=LevelRead)
def get_level(
    level_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    获取单个等级详情 (管理员)
    """
    
    from app.db.models.level import Level
    
    level = db.query(Level).filter(Level.id == level_id).first()
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="等级不存在")
    
    return LevelRead(
        id=level.id,
        role_id=level.role_id,
        name=level.name,
        rank_value=level.rank_value,
        description=level.description,
        is_active=level.is_active,
        created_at=level.created_at,
        updated_at=level.updated_at,
        user_count=level.user_count if hasattr(level, 'user_count') else 0,
        can_be_deleted=level.can_be_deleted() if hasattr(level, 'can_be_deleted') else True,
        role_name=level.role.name if level.role else None
    )

@router.put("/{level_id}", response_model=LevelRead)
def update_level(
    level_id: int,
    level_data: LevelUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    更新等级 (管理员)
    """
    
    from app.db.models.level import Level
    
    level = db.query(Level).filter(Level.id == level_id).first()
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="等级不存在")
    
    # 更新字段
    if level_data.name is not None:
        # 检查同一角色下是否已存在相同名称的等级
        existing_level = db.query(Level).filter(
            Level.role_id == level.role_id,
            Level.name == level_data.name,
            Level.id != level_id
        ).first()
        if existing_level:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该角色下已存在相同名称的等级"
            )
        level.name = level_data.name
    
    if level_data.rank_value is not None:
        # 检查同一角色下是否已存在相同排序值的等级
        existing_rank = db.query(Level).filter(
            Level.role_id == level.role_id,
            Level.rank_value == level_data.rank_value,
            Level.id != level_id
        ).first()
        if existing_rank:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该角色下已存在相同排序值的等级"
            )
        level.rank_value = level_data.rank_value
    
    if level_data.description is not None:
        level.description = level_data.description
    
    if level_data.is_active is not None:
        level.is_active = level_data.is_active
    
    db.commit()
    db.refresh(level)
    
    return LevelRead(
        id=level.id,
        role_id=level.role_id,
        name=level.name,
        rank_value=level.rank_value,
        description=level.description,
        is_active=level.is_active,
        created_at=level.created_at,
        updated_at=level.updated_at,
        user_count=level.user_count if hasattr(level, 'user_count') else 0,
        can_be_deleted=level.can_be_deleted() if hasattr(level, 'can_be_deleted') else True,
        role_name=level.role.name if level.role else None
    )

@router.delete("/{level_id}", response_model=LevelRead)
def delete_level(
    level_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(require_module_permission("USER_MANAGEMENT"))
):
    """
    删除等级 (管理员)
    """
    
    from app.db.models.level import Level
    
    level = db.query(Level).filter(Level.id == level_id).first()
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="等级不存在")
    
    # 检查是否可以删除（有用户关联等）
    if hasattr(level, 'can_be_deleted') and not level.can_be_deleted():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该等级不能删除，可能有用户正在使用")
    
    # 保存删除前的等级信息
    deleted_level = LevelRead(
        id=level.id,
        role_id=level.role_id,
        name=level.name,
        rank_value=level.rank_value,
        description=level.description,
        is_active=level.is_active,
        created_at=level.created_at,
        updated_at=level.updated_at,
        user_count=level.user_count if hasattr(level, 'user_count') else 0,
        can_be_deleted=level.can_be_deleted() if hasattr(level, 'can_be_deleted') else True,
        role_name=level.role.name if level.role else None
    )
    
    db.delete(level)
    db.commit()
    
    return deleted_level