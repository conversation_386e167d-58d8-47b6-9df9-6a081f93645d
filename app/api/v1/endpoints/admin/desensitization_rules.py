"""
脱敏规则管理API端点
提供脱敏规则的完整CRUD操作和管理功能
"""

from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import models, schemas
from app.api.deps import get_db
from app.core.security import require_module_permission
from app.services.desensitization_service import desensitization_service
from app.db.models.desensitization_rule import DataType, MaskingLevel, MaskingStrategy

router = APIRouter()

@router.get("/", response_model=schemas.DesensitizationRuleListResponse)
def list_desensitization_rules(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    data_type: Optional[DataType] = Query(None, description="按数据类型过滤"),
    masking_level: Optional[MaskingLevel] = Query(None, description="按脱敏级别过滤"),
    is_active: Optional[bool] = Query(None, description="按激活状态过滤"),
    role_id: Optional[int] = Query(None, description="按角色ID过滤"),
    search: Optional[str] = Query(None, description="搜索规则名称或描述"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """获取脱敏规则列表"""
    
    # 构建查询
    query = db.query(models.DesensitizationRule)
    
    # 应用过滤条件
    if data_type:
        query = query.filter(models.DesensitizationRule.data_type == data_type)
    
    if masking_level:
        query = query.filter(models.DesensitizationRule.masking_level == masking_level)
    
    if is_active is not None:
        query = query.filter(models.DesensitizationRule.is_active == is_active)
    
    if role_id is not None:
        if role_id == 0:  # 0表示全局规则
            query = query.filter(models.DesensitizationRule.role_id.is_(None))
        else:
            query = query.filter(models.DesensitizationRule.role_id == role_id)
    
    if search:
        query = query.filter(
            (models.DesensitizationRule.name.contains(search)) |
            (models.DesensitizationRule.description.contains(search))
        )
    
    # 获取总数
    total = query.count()
    
    # 应用分页和排序
    rules = query.order_by(
        models.DesensitizationRule.priority.desc(),
        models.DesensitizationRule.created_at.desc()
    ).offset(skip).limit(limit).all()
    
    return {
        "items": rules,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.post("/", response_model=schemas.DesensitizationRuleResponse)
def create_desensitization_rule(
    rule_data: schemas.DesensitizationRuleCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """创建新的脱敏规则"""
    
    # 检查规则名称是否已存在
    existing_rule = db.query(models.DesensitizationRule).filter(
        models.DesensitizationRule.name == rule_data.name
    ).first()
    
    if existing_rule:
        raise HTTPException(
            status_code=400,
            detail=f"规则名称 '{rule_data.name}' 已存在"
        )
    
    # 验证角色ID是否存在
    if rule_data.role_id:
        role = db.query(models.Role).filter(models.Role.id == rule_data.role_id).first()
        if not role:
            raise HTTPException(
                status_code=400,
                detail=f"角色ID {rule_data.role_id} 不存在"
            )
    
    # 创建规则
    rule_dict = rule_data.dict()
    rule_dict["created_by"] = current_user.username
    rule_dict["updated_by"] = current_user.username
    
    rule = models.DesensitizationRule(**rule_dict)
    
    # 验证正则表达式
    if not rule.is_pattern_valid():
        raise HTTPException(
            status_code=400,
            detail="无效的正则表达式模式"
        )
    
    db.add(rule)
    db.commit()
    db.refresh(rule)
    
    return rule

@router.get("/{rule_id}", response_model=schemas.DesensitizationRuleResponse)
def get_desensitization_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """获取指定的脱敏规则"""
    
    rule = db.query(models.DesensitizationRule).filter(
        models.DesensitizationRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=404,
            detail=f"脱敏规则 {rule_id} 不存在"
        )
    
    return rule

@router.put("/{rule_id}", response_model=schemas.DesensitizationRuleResponse)
def update_desensitization_rule(
    rule_id: int,
    rule_data: schemas.DesensitizationRuleUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """更新脱敏规则"""
    
    rule = db.query(models.DesensitizationRule).filter(
        models.DesensitizationRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=404,
            detail=f"脱敏规则 {rule_id} 不存在"
        )
    
    # 检查名称冲突
    if rule_data.name and rule_data.name != rule.name:
        existing_rule = db.query(models.DesensitizationRule).filter(
            models.DesensitizationRule.name == rule_data.name,
            models.DesensitizationRule.id != rule_id
        ).first()
        
        if existing_rule:
            raise HTTPException(
                status_code=400,
                detail=f"规则名称 '{rule_data.name}' 已存在"
            )
    
    # 验证角色ID
    if rule_data.role_id:
        role = db.query(models.Role).filter(models.Role.id == rule_data.role_id).first()
        if not role:
            raise HTTPException(
                status_code=400,
                detail=f"角色ID {rule_data.role_id} 不存在"
            )
    
    # 更新字段
    update_data = rule_data.dict(exclude_unset=True)
    update_data["updated_by"] = current_user.username
    
    for field, value in update_data.items():
        setattr(rule, field, value)
    
    # 验证正则表达式
    if rule_data.pattern and not rule.is_pattern_valid():
        raise HTTPException(
            status_code=400,
            detail="无效的正则表达式模式"
        )
    
    db.commit()
    db.refresh(rule)
    
    return rule

@router.delete("/{rule_id}")
def delete_desensitization_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """删除脱敏规则"""
    
    rule = db.query(models.DesensitizationRule).filter(
        models.DesensitizationRule.id == rule_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=404,
            detail=f"脱敏规则 {rule_id} 不存在"
        )
    
    db.delete(rule)
    db.commit()
    
    return {"message": f"脱敏规则 {rule_id} 已删除"}

@router.post("/test", response_model=schemas.DesensitizationRuleTestResponse)
def test_desensitization_rule(
    test_request: schemas.DesensitizationRuleTestRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """测试脱敏规则"""
    
    if test_request.rule_id:
        # 测试指定规则
        result = desensitization_service.test_rule(
            test_request.rule_id,
            test_request.text,
            db
        )
        
        if "error" in result:
            raise HTTPException(
                status_code=400,
                detail=result["error"]
            )
        
        return {
            "original_text": result["original_text"],
            "processed_text": result["processed_text"],
            "matches": [
                {
                    "start": match["start"],
                    "end": match["end"],
                    "matched_text": match["matched_text"],
                    "masked_text": result["processed_text"][match["start"]:match["end"]],
                    "data_type": match["data_type"],
                    "rule_name": match["rule_name"],
                    "confidence": match["confidence"]
                }
                for match in result["matches"]
            ],
            "total_matches": result["total_matches"],
            "rules_applied": 1 if result["total_matches"] > 0 else 0
        }
    else:
        # 测试所有激活规则
        result = desensitization_service.process_text_desensitization(
            test_request.text,
            db,
            apply_masking=True
        )
        
        return {
            "original_text": result["original_text"],
            "processed_text": result["processed_text"],
            "matches": [
                {
                    "start": pattern["start"],
                    "end": pattern["end"],
                    "matched_text": pattern["matched_text"],
                    "masked_text": result["processed_text"][pattern["start"]:pattern["end"]] if result["processed_text"] != result["original_text"] else pattern["matched_text"],
                    "data_type": pattern["data_type"],
                    "rule_name": pattern["rule_name"],
                    "confidence": pattern["confidence"]
                }
                for pattern in result["detected_patterns"]
            ],
            "total_matches": result["total_matches"],
            "rules_applied": result["rules_applied"]
        }

@router.post("/create-defaults")
def create_default_desensitization_rules(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(require_module_permission("DESENSITIZATION"))
):
    """创建默认脱敏规则"""
    
    created_rules = desensitization_service.create_default_rules(db)
    
    return {
        "message": f"成功创建 {len(created_rules)} 个默认脱敏规则",
        "created_rules": [
            {
                "id": rule.id,
                "name": rule.name,
                "data_type": rule.data_type.value
            }
            for rule in created_rules
        ]
    }

@router.get("/options/all", response_model=schemas.DesensitizationOptionsResponse)
def get_desensitization_options():
    """获取脱敏规则配置选项"""
    
    data_types = [
        {
            "value": data_type.value,
            "label": {
                "identity_card": "身份证号",
                "credit_card": "信用卡号",
                "phone": "手机号",
                "email": "邮箱地址",
                "ip_address": "IP地址",
                "url": "网址",
                "address": "地址",
                "bank_account": "银行账号",
                "password": "密码",
                "username": "用户名",
                "social_security": "社会保障号",
                "custom": "自定义"
            }.get(data_type.value, data_type.value),
            "description": f"{data_type.value}类型的敏感数据"
        }
        for data_type in DataType
    ]
    
    masking_levels = [
        {
            "value": level.value,
            "label": {
                "low": "低级别",
                "medium": "中级别", 
                "high": "高级别",
                "complete": "完全脱敏"
            }.get(level.value, level.value),
            "description": {
                "low": "保留部分信息以便识别",
                "medium": "保留少量信息",
                "high": "几乎完全隐藏",
                "complete": "完全替换"
            }.get(level.value, "")
        }
        for level in MaskingLevel
    ]
    
    masking_strategies = [
        {
            "value": strategy.value,
            "label": {
                "asterisk": "星号替换",
                "x_mark": "X字符替换",
                "hash": "井号替换",
                "replacement": "指定文本替换",
                "partial_show": "部分显示",
                "format_preserve": "格式保留"
            }.get(strategy.value, strategy.value),
            "description": {
                "asterisk": "使用*字符替换敏感部分",
                "x_mark": "使用X字符替换敏感部分",
                "hash": "使用#字符替换敏感部分",
                "replacement": "使用指定文本完全替换",
                "partial_show": "显示前后指定位数，中间脱敏",
                "format_preserve": "保留原始格式，替换字母数字"
            }.get(strategy.value, "")
        }
        for strategy in MaskingStrategy
    ]
    
    return {
        "data_types": data_types,
        "masking_levels": masking_levels,
        "masking_strategies": masking_strategies
    } 