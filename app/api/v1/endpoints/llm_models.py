from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app import models, schemas
from app.api import deps
from app.db.models.llm_model import LLMModel
from app.db.models.user_model_assignment import UserModelAssignment

router = APIRouter()

@router.get("/my-available", response_model=List[schemas.LLMModelSchema])
def get_my_available_models(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取当前用户可使用的模型列表（基于模型分配权限）
    """
    # 查询用户被分配的模型
    stmt = (
        select(UserModelAssignment)
        .options(selectinload(UserModelAssignment.model))
        .where(UserModelAssignment.user_id == current_user.id)
    )
    
    assignments = db.execute(stmt).scalars().all()
    
    # 提取模型列表，只返回激活的模型
    available_models = []
    for assignment in assignments:
        if assignment.model and assignment.model.is_active:
            available_models.append(assignment.model)
    
    # 如果用户没有被分配任何模型，返回空列表而不是所有模型
    # 这确保了权限控制的严格性
    return available_models

@router.get("/active", response_model=List[schemas.LLMModelSchema])
def get_active_models(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取所有激活的模型列表（所有登录用户可访问）
    注意：此端点为兼容性保留，建议使用 /my-available 端点
    """
    # 只返回激活的模型
    active_models = db.query(models.LLMModel).filter(
        models.LLMModel.is_active == True
    ).all()
    
    return active_models

@router.get("/{model_id}", response_model=schemas.LLMModelSchema)
def get_model(
    model_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取指定模型的信息（所有登录用户可访问）
    """
    model = db.query(models.LLMModel).filter(
        models.LLMModel.id == model_id,
        models.LLMModel.is_active == True
    ).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在或未激活"
        )
    
    return model 