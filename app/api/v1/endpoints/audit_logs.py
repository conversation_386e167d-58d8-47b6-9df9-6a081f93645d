from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, and_, func
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta

from app.db.session import get_db
from app.db.models.audit_log import AuditLog
from app.db.models.user import User
from app.api.async_deps import get_current_user

router = APIRouter()

# Pydantic模型
class AuditLogResponse(BaseModel):
    id: int
    user_id: Optional[int]
    username: Optional[str]  # 用户名（如果有用户）
    action_type: str
    target_entity: Optional[str]
    target_id: Optional[int]
    details: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    session_id: Optional[str]
    result: Optional[str]
    created_at: str

class AuditLogCreate(BaseModel):
    action_type: str
    target_entity: Optional[str] = None
    target_id: Optional[int] = None
    details: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    result: Optional[str] = None

class AuditLogStats(BaseModel):
    total_logs: int
    today_logs: int
    successful_actions: int
    failed_actions: int
    blocked_actions: int
    top_actions: List[dict]
    top_users: List[dict]

@router.get("/logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    limit: int = Query(100, ge=1, le=1000, description="每页记录数"),
    offset: int = Query(0, ge=0, description="偏移量"),
    action_type: Optional[str] = Query(None, description="按操作类型筛选"),
    user_id: Optional[int] = Query(None, description="按用户ID筛选"),
    result: Optional[str] = Query(None, description="按结果筛选 (SUCCESS/FAILED/BLOCKED)"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取审计日志列表（支持分页和筛选）"""
    
    # 构建查询条件
    conditions = []
    
    if action_type:
        conditions.append(AuditLog.action_type == action_type)
    if user_id:
        conditions.append(AuditLog.user_id == user_id)
    if result:
        conditions.append(AuditLog.result == result)
    if start_date:
        conditions.append(AuditLog.created_at >= start_date)
    if end_date:
        conditions.append(AuditLog.created_at <= end_date)
    
    # 构建查询
    query = select(AuditLog).join(User, AuditLog.user_id == User.id, isouter=True)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    query = query.order_by(desc(AuditLog.created_at)).offset(offset).limit(limit)
    
    # 执行查询
    result_query = await db.execute(query)
    logs = result_query.scalars().all()
    
    # 获取用户信息
    user_ids = [log.user_id for log in logs if log.user_id]
    users_query = await db.execute(
        select(User).where(User.id.in_(user_ids))
    )
    users = {user.id: user.username for user in users_query.scalars().all()}
    
    # 格式化返回数据
    response_logs = []
    for log in logs:
        response_logs.append(AuditLogResponse(
            id=log.id,
            user_id=log.user_id,
            username=users.get(log.user_id) if log.user_id else None,
            action_type=log.action_type,
            target_entity=log.target_entity,
            target_id=log.target_id,
            details=log.details,
            ip_address=log.ip_address,
            user_agent=log.user_agent,
            session_id=log.session_id,
            result=log.result,
            created_at=log.created_at.isoformat()
        ))
    
    return response_logs

@router.post("/logs", response_model=AuditLogResponse)
async def create_audit_log(
    log_data: AuditLogCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新的审计日志记录"""
    
    # 创建审计日志
    new_log = AuditLog(
        user_id=current_user.id,
        action_type=log_data.action_type,
        target_entity=log_data.target_entity,
        target_id=log_data.target_id,
        details=log_data.details,
        ip_address=log_data.ip_address,
        user_agent=log_data.user_agent,
        session_id=log_data.session_id,
        result=log_data.result
    )
    
    db.add(new_log)
    await db.commit()
    await db.refresh(new_log)
    
    return AuditLogResponse(
        id=new_log.id,
        user_id=new_log.user_id,
        username=current_user.username,
        action_type=new_log.action_type,
        target_entity=new_log.target_entity,
        target_id=new_log.target_id,
        details=new_log.details,
        ip_address=new_log.ip_address,
        user_agent=new_log.user_agent,
        session_id=new_log.session_id,
        result=new_log.result,
        created_at=new_log.created_at.isoformat()
    )

@router.get("/stats", response_model=AuditLogStats)
async def get_audit_log_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取审计日志统计信息"""
    
    # 总日志数
    total_query = await db.execute(select(func.count(AuditLog.id)))
    total_logs = total_query.scalar()
    
    # 今日日志数
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_query = await db.execute(
        select(func.count(AuditLog.id)).where(
            AuditLog.created_at >= today_start
        )
    )
    today_logs = today_query.scalar()
    
    # 按结果统计
    success_query = await db.execute(
        select(func.count(AuditLog.id)).where(AuditLog.result == "SUCCESS")
    )
    successful_actions = success_query.scalar()
    
    failed_query = await db.execute(
        select(func.count(AuditLog.id)).where(AuditLog.result == "FAILED")
    )
    failed_actions = failed_query.scalar()
    
    blocked_query = await db.execute(
        select(func.count(AuditLog.id)).where(AuditLog.result == "BLOCKED")
    )
    blocked_actions = blocked_query.scalar()
    
    # 最常见的操作类型 (Top 5)
    top_actions_query = await db.execute(
        select(AuditLog.action_type, func.count(AuditLog.id).label('count'))
        .group_by(AuditLog.action_type)
        .order_by(desc(func.count(AuditLog.id)))
        .limit(5)
    )
    top_actions = [
        {"action_type": row[0], "count": row[1]}
        for row in top_actions_query.fetchall()
    ]
    
    # 最活跃的用户 (Top 5)
    top_users_query = await db.execute(
        select(User.username, func.count(AuditLog.id).label('count'))
        .join(User, AuditLog.user_id == User.id)
        .group_by(User.id, User.username)
        .order_by(desc(func.count(AuditLog.id)))
        .limit(5)
    )
    top_users = [
        {"username": row[0], "count": row[1]}
        for row in top_users_query.fetchall()
    ]
    
    return AuditLogStats(
        total_logs=total_logs,
        today_logs=today_logs,
        successful_actions=successful_actions,
        failed_actions=failed_actions,
        blocked_actions=blocked_actions,
        top_actions=top_actions,
        top_users=top_users
    )

@router.get("/action-types")
async def get_available_action_types(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有可用的操作类型列表"""
    
    # 查询数据库中已存在的操作类型
    existing_types_query = await db.execute(
        select(AuditLog.action_type).distinct().order_by(AuditLog.action_type)
    )
    existing_types = [row[0] for row in existing_types_query.fetchall()]
    
    # 预定义的操作类型
    predefined_types = [
        "LOGIN",
        "LOGOUT", 
        "CREATE_USER",
        "UPDATE_USER",
        "DELETE_USER",
        "CREATE_ROLE",
        "UPDATE_ROLE",
        "DELETE_ROLE",
        "SEND_MESSAGE",
        "MESSAGE_BLOCKED",
        "KEYWORD_DETECTION",
        "PERMISSION_DENIED",
        "SESSION_CREATED",
        "SESSION_TERMINATED",
        "SECURITY_POLICY_UPDATED",
        "SYSTEM_CONFIG_CHANGED"
    ]
    
    # 合并并去重
    all_types = sorted(set(existing_types + predefined_types))
    
    return {"action_types": all_types}

@router.delete("/logs/{log_id}")
async def delete_audit_log(
    log_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除审计日志记录（谨慎使用）"""
    
    # 检查日志是否存在
    log = await db.get(AuditLog, log_id)
    if not log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Audit log not found"
        )
    
    # 删除日志
    await db.delete(log)
    await db.commit()
    
    return {"message": f"Audit log {log_id} deleted successfully"} 