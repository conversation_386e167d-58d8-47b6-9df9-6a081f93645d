"""
RAG (Retrieval-Augmented Generation) API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.api.async_deps import get_db, get_current_user
from app.db.models.user import User
from app import models
from app.services.rag_service import RAGService
from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.llm_service import LLMService
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.rag_permissions import RAGPermissionService
from app.services.knowledge_base_audit import KnowledgeBaseAuditService
from app.services.pipeline.factory import SecurityPipelineFactory

router = APIRouter()


# Pydantic模型
class RAGQueryRequest(BaseModel):
    """RAG查询请求"""
    query: str = Field(..., description="用户查询", min_length=1, max_length=1000)
    session_id: Optional[int] = Field(None, description="会话ID，用于保存对话历史")
    model_id: Optional[int] = Field(None, description="AI模型ID")
    category_ids: Optional[List[int]] = Field(None, description="限制搜索的分类ID列表")
    grade_ids: Optional[List[int]] = Field(None, description="限制搜索的分级ID列表")
    top_k: int = Field(5, description="检索的文档块数量", ge=1, le=20)
    temperature: float = Field(0.7, description="生成温度", ge=0.0, le=2.0)
    max_tokens: int = Field(1000, description="最大生成token数", ge=100, le=4000)
    include_sources: bool = Field(True, description="是否包含来源信息")


class RAGSource(BaseModel):
    """RAG来源信息"""
    document_id: int
    document_title: str  # 保留用于兼容性
    chunk_index: int     # 保留用于兼容性
    relevance_score: float  # 保留用于兼容性
    
    # 添加前端期望的字段
    document_name: str   # 前端期望的文档名字段
    content: str         # 前端期望的内容字段
    similarity: float    # 前端期望的相似度字段
    chunk_id: str        # 前端期望的chunk ID字段
    metadata: Optional[dict] = None  # 前端期望的元数据字段


class RAGQueryResponse(BaseModel):
    """RAG查询响应"""
    success: bool
    answer: Optional[str]
    sources: Optional[List[RAGSource]]
    context_used: bool
    documents_retrieved: int
    model_id: Optional[int]
    error: Optional[str]


class RAGStatisticsResponse(BaseModel):
    """RAG统计响应"""
    total_queries: int
    successful_queries: int
    failed_queries: int
    average_documents_retrieved: float
    most_used_categories: List[dict]
    query_trends: List[dict]


# API端点
@router.post("/query", response_model=RAGQueryResponse)
async def query_with_rag(
    request: RAGQueryRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    使用RAG进行问答
    
    基于知识库内容生成答案，支持：
    - 语义搜索相关文档
    - 基于上下文生成答案
    - 权限控制和安全检查
    - 来源追踪
    """
    try:
        # 检查RAG问答权限
        rag_permission_service = RAGPermissionService(db=db)
        if not await rag_permission_service.check_rag_access(current_user):
            raise HTTPException(
                status_code=403,
                detail="权限不足：无法使用知识库问答功能"
            )

        # 初始化服务
        knowledge_service = KnowledgeBaseService()
        llm_service = LLMService()

        permission_service = KnowledgeBasePermissionService(db=db)
        
        from app.core.audit import AuditLogger
        audit_logger = AuditLogger()
        audit_service = KnowledgeBaseAuditService()
        
        # SecurityPipelineFactory 是静态类，不需要实例化
        security_pipeline_factory = SecurityPipelineFactory
        
        # 创建RAG服务实例
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_service,
            llm_service=llm_service,
            permission_service=permission_service,
            audit_service=audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        # 如果提供了session_id，保存用户消息到会话
        if request.session_id:
            # 验证会话存在且用户有权限访问
            from sqlalchemy import select
            session_result = await db.execute(select(models.Session).filter(
                models.Session.id == request.session_id,
                models.Session.user_id == current_user.id,
                models.Session.is_deleted == False
            ))
            session = session_result.scalar_one_or_none()

            if not session:
                raise HTTPException(
                    status_code=404,
                    detail="会话不存在或无权限访问"
                )

            # 保存用户消息
            user_message = models.Message(
                session_id=request.session_id,
                user_id=current_user.id,
                content=request.query,
                sender_type="user",
                is_blocked=False,
                security_check_passed=True
            )
            db.add(user_message)
            await db.flush()  # 获取ID但不提交

        # 执行RAG查询
        result = await rag_service.generate_answer(
            query=request.query,
            user_id=current_user.id,
            model_id=request.model_id,
            category_ids=request.category_ids,
            grade_ids=request.grade_ids,
            top_k=request.top_k,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            include_sources=request.include_sources
        )

        # 如果有会话，保存AI回复
        if request.session_id and result.get('success', False):
            ai_message = models.Message(
                session_id=request.session_id,
                user_id=current_user.id,
                content=result.get('answer', ''),
                sender_type="ai",
                is_blocked=False,
                security_check_passed=True,
                ai_model_used=f"Model_{request.model_id}" if request.model_id else "Default",
                ai_response_time=result.get('response_time', 0),
                ai_token_count=len(result.get('answer', '').split()) if result.get('answer') else 0
            )
            db.add(ai_message)

            # 更新会话活动时间
            session.update_activity()
            await db.commit()
        
        # 注意：这里的代码似乎是多余的，因为上面已经创建了rag_service
        
        return RAGQueryResponse(**result)
    except Exception as e:
        import traceback
        traceback.print_exc()  # 添加详细的错误追踪
        print(f"RAG API错误: {e}")  # 添加错误打印
        return RAGQueryResponse(
            success=False, 
            answer=None, 
            sources=[], 
            context_used=False, 
            documents_retrieved=0, 
            model_id=None, 
            error=str(e)
        )


@router.get("/statistics", response_model=RAGStatisticsResponse)
async def get_rag_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取RAG使用统计

    返回当前用户的RAG使用统计信息
    """
    # 检查RAG问答权限
    rag_permission_service = RAGPermissionService(db=db)
    if not await rag_permission_service.check_rag_access(current_user):
        raise HTTPException(
            status_code=403,
            detail="权限不足：无法访问知识库问答统计"
        )

    # 创建服务实例
    knowledge_service = KnowledgeBaseService()

    llm_service = LLMService()

    permission_service = KnowledgeBasePermissionService(db=db)
    
    from app.core.audit import AuditLogger
    audit_logger = AuditLogger()
    audit_service = KnowledgeBaseAuditService()
    
    # SecurityPipelineFactory 是静态类，不需要实例化
    security_pipeline_factory = SecurityPipelineFactory
    
    rag_service = RAGService(
        db=db,
        knowledge_service=knowledge_service,
        llm_service=llm_service,
        permission_service=permission_service,
        audit_service=audit_service,
        security_pipeline_factory=security_pipeline_factory
    )
    
    # 获取统计信息
    stats = await rag_service.get_rag_statistics(user_id=current_user.id)
    
    return RAGStatisticsResponse(**stats)


@router.post("/test-retrieval")
async def test_retrieval(
    query: str = Query(..., description="测试查询"),
    top_k: int = Query(5, description="返回结果数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    测试知识库检索功能

    仅返回检索结果，不生成答案，用于调试和测试
    """
    # 检查RAG问答权限
    rag_permission_service = RAGPermissionService(db=db)
    if not await rag_permission_service.check_rag_access(current_user):
        raise HTTPException(
            status_code=403,
            detail="权限不足：无法使用知识库检索功能"
        )
    
    # 创建知识库服务
    knowledge_service = KnowledgeBaseService()
    
    # 执行搜索 - search_knowledge_base 需要 db 参数
    search_results = await knowledge_service.search_knowledge_base(
        db=db,
        query=query,
        user_id=current_user.id,
        top_k=top_k
    )
    
    # 格式化结果
    formatted_results = []
    for result in search_results:
        formatted_results.append({
            "content": result.get("content", ""),
            "similarity": result.get("similarity", 0.0),
            "metadata": result.get("metadata", {})
        })
    
    return {
        "query": query,
        "results_count": len(formatted_results),
        "results": formatted_results
    } 