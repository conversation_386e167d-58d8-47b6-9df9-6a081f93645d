# app/api/v1/endpoints/messages.py

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, or_
import asyncio
import aiohttp
import json
import time
import logging

from app.api import deps
from app.db import models
from app.db.models.message import SenderType
from app import schemas
from app.api.deps import get_current_active_user
from app.core.permissions import can_access_session, can_access_message
from app.core.security import require_module_permission

logger = logging.getLogger(__name__)

router = APIRouter()

# 启动时的日志
print("🔥🔥🔥 messages.py 模块已加载！")
logger.info("🔥🔥🔥 messages.py 模块已加载！")

@router.post("/", response_model=schemas.MessageResponse)
def create_message(
    *,
    db: Session = Depends(deps.get_db),
    message_in: schemas.MessageCreate,
    current_user: models.User = Depends(get_current_active_user),
) -> Any:
    """
    创建新消息
    """
    # 验证会话是否存在且有权限访问
    session = db.query(models.Session).filter(
        models.Session.id == message_in.session_id,
        models.Session.is_deleted == False
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查会话访问权限
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此会话"
        )
    
    # 执行安全检查
    security_result = perform_security_check(message_in.content, db, current_user.role_id)
    
    # 创建消息
    message = models.Message(
        session_id=message_in.session_id,
        user_id=current_user.id,
        content=message_in.content,
        sender_type=message_in.sender_type,
        is_blocked=security_result["is_blocked"],
        block_reason=security_result.get("block_reason"),
        security_check_passed=security_result["security_check_passed"],
        security_check_details=security_result["security_check_details"]
    )
    
    db.add(message)
    
    # 更新会话的最后活动时间
    session.update_activity()
    
    db.commit()
    db.refresh(message)
    
    return message

@router.get("/session/{session_id}", response_model=schemas.MessageListResponse)
def get_session_messages(
    session_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(50, ge=1, le=100, description="返回的记录数"),
    include_deleted: bool = Query(False, description="是否包含已删除的消息")
) -> Any:
    """
    获取指定会话的消息列表
    """
    # 查找会话
    session = db.query(models.Session).filter(
        models.Session.id == session_id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查会话访问权限
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此会话"
        )
    
    # 构建查询条件
    query_conditions = [models.Message.session_id == session_id]
    if not include_deleted:
        query_conditions.append(models.Message.is_deleted == False)
    
    # 查询消息
    messages_query = db.query(models.Message).filter(
        and_(*query_conditions)
    ).order_by(models.Message.created_at)
    
    total = messages_query.count()
    messages = messages_query.offset(skip).limit(limit).all()
    
    return schemas.MessageListResponse(
        items=messages,
        total=total,
        skip=skip,
        limit=limit
    )

@router.get("/{message_id}", response_model=schemas.MessageResponse)
def get_message(
    message_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_active_user),
) -> Any:
    """
    获取单个消息详情
    """
    message = db.query(models.Message).filter(
        models.Message.id == message_id
    ).first()
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )
    
    # 检查消息访问权限
    if not can_access_message(current_user, message):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此消息"
        )
    
    return message

@router.put("/{message_id}", response_model=schemas.MessageResponse)
def update_message(
    *,
    db: Session = Depends(deps.get_db),
    message_id: int,
    message_in: schemas.MessageUpdate,
    current_user: models.User = Depends(get_current_active_user),
) -> Any:
    """
    更新消息
    """
    message = db.query(models.Message).filter(
        models.Message.id == message_id
    ).first()
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )
    
    # 检查消息访问权限
    if not can_access_message(current_user, message):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限修改此消息"
        )
    
    # 更新字段
    update_data = message_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(message, field, value)
    
    db.commit()
    db.refresh(message)
    
    return message

@router.delete("/{message_id}")
def delete_message(
    message_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_active_user),
    force: bool = Query(False, description="是否强制删除（物理删除）")
) -> Any:
    """
    删除消息（软删除或物理删除）
    """
    message = db.query(models.Message).filter(
        models.Message.id == message_id
    ).first()
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )
    
    # 检查消息访问权限
    if not can_access_message(current_user, message):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限删除此消息"
        )
    
    if force:
        # 物理删除
        db.delete(message)
    else:
        # 软删除
        message.mark_as_deleted()
    
    db.commit()
    
    return {"message": "消息删除成功"}

@router.post("/ai-generate", response_model=schemas.AIMessageGenerateResponse)
async def generate_ai_message(
    *,
    db: Session = Depends(deps.get_db),
    request: schemas.AIMessageGenerateRequest,
    current_user: models.User = Depends(get_current_active_user),
) -> Any:
    """
    生成AI消息（包含用户消息和AI回复）
    """
    start_time = time.time()
    
    # 查找会话
    session = db.query(models.Session).filter(
        models.Session.id == request.session_id,
        models.Session.is_deleted == False
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查会话访问权限
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此会话"
        )
    
    # 1. 创建用户消息
    user_security_result = perform_security_check(request.user_message, db, current_user.role_id)
    
    user_message = models.Message(
        session_id=request.session_id,
        user_id=current_user.id,
        content=request.user_message,
        sender_type=SenderType.USER,
        is_blocked=user_security_result["is_blocked"],
        block_reason=user_security_result.get("block_reason"),
        security_check_passed=user_security_result["security_check_passed"],
        security_check_details=user_security_result["security_check_details"]
    )
    
    db.add(user_message)
    db.flush()  # 获取ID但不提交
    
    # 如果用户消息被拦截，不生成AI响应
    if user_security_result["is_blocked"]:
        db.commit()
        db.refresh(user_message)
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户消息被安全系统拦截: {user_security_result.get('block_reason')}"
        )
    
    # 2. 生成AI响应
    try:
        ai_response_content = await generate_ai_response(request.user_message, session, db)
    except Exception as e:
        logger.error(f"生成AI响应时出错: {str(e)}")
        # 使用智能备用响应
        ai_response_content = generate_fallback_response(request.user_message)
    ai_response_time = time.time() - start_time
    
    # 3. 对AI响应进行安全检查
    ai_security_result = perform_security_check(ai_response_content, db, current_user.role_id)
    
    # 4. 创建AI消息
    ai_message = models.Message(
        session_id=request.session_id,
        user_id=current_user.id,  # AI消息也关联到用户
        content=ai_response_content,
        sender_type=SenderType.AI,
        is_blocked=ai_security_result["is_blocked"],
        block_reason=ai_security_result.get("block_reason"),
        security_check_passed=ai_security_result["security_check_passed"],
        security_check_details=ai_security_result["security_check_details"],
        ai_model_used=get_ai_model_name(request.llm_model_id),
        ai_response_time=ai_response_time,
        ai_token_count=estimate_token_count(ai_response_content)
    )
    
    db.add(ai_message)
    
    # 更新会话活动时间
    session.update_activity()
    
    db.commit()
    db.refresh(user_message)
    db.refresh(ai_message)
    
    generation_time = time.time() - start_time
    
    return schemas.AIMessageGenerateResponse(
        user_message=user_message,
        ai_message=ai_message,
        security_check_result=schemas.SecurityCheckResult(**user_security_result["security_check_details"]),
        generation_time=generation_time
    )


@router.post("/ai-generate-stream")
async def generate_ai_message_stream(
    *,
    db: Session = Depends(deps.get_db),
    request: schemas.AIMessageGenerateRequest,
    current_user: models.User = Depends(get_current_active_user),
    minimal_processing: bool = Query(False, description="启用最小化处理模式（减少内容过滤）")
) -> Any:
    """
    AI消息生成（流式响应）
    
    Args:
        minimal_processing: 如果为True，将减少内容过滤和处理，更接近原始模型输出
    """
    print(f"🚀🚀🚀 AI流式生成接口被调用 - 用户: {current_user.id}, 消息: {request.user_message[:30]}...")
    logger.info(f"🚀 AI流式生成接口被调用 - 用户: {current_user.id}, 消息: {request.user_message[:30]}...")
    start_time = time.time()

    print(f"🔍 查找会话 ID: {request.session_id}")
    # 验证会话是否存在且有权限访问
    session = db.query(models.Session).filter(
        models.Session.id == request.session_id,
        models.Session.is_deleted == False
    ).first()

    print(f"🔍 会话查找结果: {session is not None}")
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    if not can_access_session(current_user, session):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此会话"
        )
    
    async def generate_stream():
        """生成流式响应"""
        print(f"🎬🎬🎬 开始生成流式响应 - 用户消息: {request.user_message[:50]}...")
        logger.info(f"🎬 开始生成流式响应 - 用户消息: {request.user_message[:50]}...")
        try:
            # 1. 创建并保存用户消息
            logger.info(f"🔒 执行安全检查...")
            user_security_result = perform_security_check(request.user_message, db, current_user.role_id)
            logger.info(f"🔒 安全检查结果: {user_security_result.get('security_check_passed', False)}, 拦截: {user_security_result.get('is_blocked', False)}")
            
            user_message = models.Message(
                session_id=request.session_id,
                user_id=current_user.id,
                content=request.user_message,
                sender_type=SenderType.USER,
                is_blocked=user_security_result["is_blocked"],
                block_reason=user_security_result.get("block_reason"),
                security_check_passed=user_security_result["security_check_passed"],
                security_check_details=user_security_result["security_check_details"]
            )
            
            db.add(user_message)
            db.commit()
            db.refresh(user_message)
            
            # 发送用户消息事件
            user_message_data = {
                'id': user_message.id,
                'session_id': user_message.session_id,
                'user_id': user_message.user_id,
                'content': user_message.content,
                'sender_type': user_message.sender_type,
                'is_blocked': user_message.is_blocked,
                'block_reason': user_message.block_reason,
                'security_check_passed': user_message.security_check_passed,
                'created_at': user_message.created_at.isoformat() if user_message.created_at else None
            }
            yield f"data: {json.dumps({'type': 'user_message', 'data': user_message_data}, ensure_ascii=False)}\n\n"
            
            if user_message.is_blocked:
                # 如果用户消息被拦截，发送拦截事件
                yield f"data: {json.dumps({'type': 'blocked', 'reason': user_message.block_reason}, ensure_ascii=False)}\n\n"
                return
            
            # 2. 流式生成AI响应
            ai_response_content = ""
            ai_message_id = None
            
            try:
                # 获取LLM模型
                logger.info(f"🔍 查找LLM模型 - 请求模型ID: {request.llm_model_id}, 会话模型ID: {session.llm_model_id}")
                llm_model = db.query(models.LLMModel).filter(
                    models.LLMModel.id == (request.llm_model_id or session.llm_model_id),
                    models.LLMModel.is_active == True
                ).first()

                if not llm_model:
                    logger.error(f"❌ LLM模型不存在或未激活 - 模型ID: {request.llm_model_id or session.llm_model_id}")
                    raise Exception("指定的LLM模型不存在或未激活")

                logger.info(f"✅ 找到LLM模型: {llm_model.name} (ID: {llm_model.id})")
                logger.info(f"📡 模型API URL: {llm_model.api_url}")
                logger.info(f"⚙️ 模型配置: {llm_model.config_params}")

                # 调用流式生成 - 实时转发每个chunk
                logger.info(f"🚀 开始流式生成AI响应...")
                async for chunk in stream_ai_response(request.user_message, session, llm_model, db):
                    # 发送chunk给前端
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
            
            except Exception as e:
                logger.error(f"流式生成过程中出错: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
        
        except Exception as e:
            logger.error(f"生成流式响应时出错: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@router.post("/{message_id}/security-recheck", response_model=schemas.MessageResponse)
def recheck_message_security(
    message_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_active_user),
) -> Any:
    """
    重新检查消息的安全性
    """
    message = db.query(models.Message).filter(
        models.Message.id == message_id
    ).first()
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )
    
    # 检查消息访问权限
    if not can_access_message(current_user, message):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限操作此消息"
        )
    
    # 重新执行安全检查
    security_result = perform_security_check(message.content, db)
    
    # 更新安全检查结果
    message.update_security_check(
        passed=security_result["security_check_passed"],
        details=security_result["security_check_details"],
        is_blocked=security_result["is_blocked"],
        block_reason=security_result.get("block_reason")
    )
    
    db.commit()
    db.refresh(message)
    
    return message

# 新增：管理员专用的获取所有消息接口
@router.get("/admin/all", response_model=schemas.MessageListResponse)
def get_all_messages_admin(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(require_module_permission("MESSAGE_MONITORING")),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(50, ge=1, le=100, description="返回的记录数"),
    include_deleted: bool = Query(False, description="是否包含已删除的消息"),
    user_id: Optional[int] = Query(None, description="筛选特定用户的消息"),
    session_id: Optional[int] = Query(None, description="筛选特定会话的消息")
) -> Any:
    """
    管理员获取所有消息列表（带筛选功能）
    """

    
    # 构建查询条件
    query_conditions = []
    
    if not include_deleted:
        query_conditions.append(models.Message.is_deleted == False)
    
    if user_id:
        query_conditions.append(models.Message.user_id == user_id)
        
    if session_id:
        query_conditions.append(models.Message.session_id == session_id)
    
    # 查询消息
    messages_query = db.query(models.Message)
    if query_conditions:
        messages_query = messages_query.filter(and_(*query_conditions))
    
    messages_query = messages_query.order_by(desc(models.Message.created_at))
    
    total = messages_query.count()
    messages = messages_query.offset(skip).limit(limit).all()
    
    return schemas.MessageListResponse(
        items=messages,
        total=total,
        skip=skip,
        limit=limit
    )

def perform_security_check(content: str, db: Session = None, user_role_id: Optional[int] = None) -> dict:
    """
    执行安全检查（增强版，集成正则表达式恶意意图检测）
    """
    from app.services.security_service import security_service
    
    # 获取数据库会话
    if db is None:
        from app.db.session import SessionLocal
        db = SessionLocal()
        close_db = True
    else:
        close_db = False
    
    try:
        # 使用新的安全检测服务
        result = security_service.perform_security_check(content, db, user_role_id)
        return result
        
    except Exception as e:
        logger.error(f"安全检查服务失败，使用备用检查: {str(e)}")
        # 如果新服务失败，回退到原始实现
        return _perform_legacy_security_check(content, db)
        
    finally:
        if close_db:
            db.close()

def _perform_legacy_security_check(content: str, db: Session) -> dict:
    """
    传统安全检查实现（备用方案）
    """
    from app import models
    
    try:
        # 从数据库获取激活的关键词
        active_keywords = db.query(models.Keyword).join(models.KeywordGroup).filter(
            models.Keyword.is_active == True,
            models.KeywordGroup.is_active == True
        ).order_by(models.Keyword.priority.desc()).all()
        
        # 检查关键词匹配
        found_keywords = []
        matched_keywords_info = []
        
        for keyword in active_keywords:
            if keyword.matches_text(content):
                found_keywords.append(keyword.word)
                matched_keywords_info.append({
                    "word": keyword.word,
                    "group": keyword.keyword_group.name,
                    "match_type": keyword.match_type.value
                })
        
        keyword_check = len(found_keywords) == 0
        
    except Exception as e:
        # 如果数据库查询失败，使用备用关键词检查
        logger.error(f"数据库关键词查询失败: {e}")
        backup_keywords = ["密码", "账号", "银行卡", "身份证"]
        found_keywords = [keyword for keyword in backup_keywords if keyword in content]
        matched_keywords_info = [{"word": word, "group": "备用检查", "match_type": "exact"} for word in found_keywords]
        keyword_check = len(found_keywords) == 0
    
    # 敏感数据模式检查
    sensitive_patterns = [r'\d{15,18}', r'\d{4}-\d{4}-\d{4}-\d{4}']
    
    # 恶意意图检查（简单实现）
    malicious_keywords = ["攻击", "破坏", "黑客", "病毒", "木马"]
    malicious_intent_check = not any(keyword in content for keyword in malicious_keywords)
    
    # 数据敏感性检查（简单实现）
    import re
    has_sensitive_data = any(re.search(pattern, content) for pattern in sensitive_patterns)
    data_sensitivity_check = not has_sensitive_data
    
    # 计算安全评分
    score = 100
    if found_keywords:
        # 每个关键词根据重要性扣分更多
        score -= len(found_keywords) * 30
    if not malicious_intent_check:
        score -= 40
    if has_sensitive_data:
        score -= 35
    
    score = max(0, score)
    
    # 判断是否拦截（严格的策略）
    # 检测到关键词或者评分低于阈值都要拦截
    has_keywords = len(found_keywords) > 0
    is_blocked = has_keywords or score < 70
    
    # 确定风险等级
    if score >= 80:
        risk_level = "low"
    elif score >= 60:
        risk_level = "medium"
    else:
        risk_level = "high"
    
    return {
        "is_blocked": is_blocked,
        "block_reason": f"检测到敏感内容: {', '.join(found_keywords)}" if is_blocked else None,
        "security_check_passed": not is_blocked,
        "security_check_details": {
            "passed": not is_blocked,
            "score": score,
            "keyword_check": keyword_check,
            "malicious_intent_check": malicious_intent_check,
            "data_sensitivity_check": data_sensitivity_check,
            "blocked_keywords": found_keywords,
            "matched_keywords_info": matched_keywords_info,
            "risk_level": risk_level,
            "legacy_mode": True  # 标记这是传统模式
        }
    }

def generate_fallback_response(user_message: str) -> str:
    """
    生成智能备用响应（当AI服务不可用时）
    """
    user_msg = user_message.lower()
    
    # 根据用户消息内容生成相应的回复
    if any(keyword in user_msg for keyword in ["你好", "hello", "hi", "嗨"]):
        return "您好！我是AI安全对话助手。虽然当前AI服务暂时不可用，但我仍然可以为您提供基本的安全对话服务。请问有什么可以帮助您的吗？"
    
    elif any(keyword in user_msg for keyword in ["安全", "security", "防护", "保护"]):
        return "安全是我们系统的核心功能。我们的AI安全系统包含多层防护机制：\n1. 关键词过滤 - 识别敏感词汇\n2. 恶意意图检测 - 分析对话意图\n3. 数据脱敏处理 - 保护隐私信息\n4. 实时安全评分 - 综合风险评估\n这些功能确保每一次对话都是安全可靠的。"
    
    elif any(keyword in user_msg for keyword in ["功能", "能力", "做什么", "干什么"]):
        return "我具备以下主要功能：\n🔒 智能对话安全监控\n🛡️ 实时内容安全检查\n📊 风险等级评估\n🚫 敏感信息拦截\n💬 安全对话引导\n\n虽然当前AI模型服务暂时不可用，但安全检查功能仍在正常运行，确保对话环境的安全性。"
    
    elif any(keyword in user_msg for keyword in ["测试", "test", "试试"]):
        return "系统测试功能正常！当前状态：\n✅ 用户认证系统 - 正常\n✅ 会话管理系统 - 正常\n✅ 安全检查系统 - 正常\n⚠️ AI模型服务 - 暂时不可用\n\n基础安全功能运行良好，您可以放心使用对话系统的安全防护功能。"
    
    elif any(keyword in user_msg for keyword in ["谢谢", "感谢", "thank"]):
        return "不客气！我很高兴能为您提供安全的对话环境。如果您有任何关于系统安全功能的问题，随时可以询问我。"
    
    elif any(keyword in user_msg for keyword in ["帮助", "help", "怎么用"]):
        return "欢迎使用AI安全对话系统！您可以：\n1. 正常发送消息进行对话\n2. 查看消息的安全检查结果\n3. 了解系统的安全防护机制\n4. 测试不同类型的消息内容\n\n系统会自动对每条消息进行安全评估，确保对话内容符合安全规范。"
    
    else:
        return f"收到您的消息：「{user_message}」\n\n当前AI模型服务暂时不可用，但我已经收到并理解了您的消息。系统的安全检查功能正在正常运行，确保我们的对话环境安全可靠。\n\n您可以继续发送消息，或者询问关于系统安全功能的相关问题。"


async def stream_ai_response(user_message: str, session_obj: models.Session, llm_model: models.LLMModel, db: Session):
    """
    流式生成AI响应 - 改进版本，支持真正的逐字符流式传输
    """
    logger.info(f"🌊 开始流式AI响应 - 模型: {llm_model.name}, 消息: {user_message[:50]}...")

    # 构建消息上下文
    messages = []
    messages.append({
        "role": "user",
        "content": user_message
    })

    try:
        # 获取模型配置
        config_params = llm_model.config_params or {}
        api_format = config_params.get("api_format", "openai")
        
        logger.info(f"📡 调用模型API - 格式: {api_format}, URL: {llm_model.api_url}")

        if api_format == "ollama":
            # Ollama 流式调用
            async with aiohttp.ClientSession() as session:
                # 构建更好的提示词
                system_prompt = "你是一个友好、有用的AI助手。请用中文回答用户的问题，回答要详细、准确、有帮助。"
                full_prompt = f"{system_prompt}\n\n用户问题：{user_message}\n\n请回答："
                
                # 构建请求数据
                request_data = {
                    "model": config_params.get("model", llm_model.name),  # 优先使用配置中的model字段
                    "prompt": full_prompt,
                    "stream": True,
                    "options": {
                        "temperature": config_params.get("temperature", 0.7),
                        "top_p": config_params.get("top_p", 0.9),
                        "max_tokens": config_params.get("max_tokens", 2048),
                        "stop": ["用户问题："]  # 添加停止词避免重复
                    }
                }

                logger.info(f"🚀 发送Ollama请求: {json.dumps(request_data, ensure_ascii=False)}")

                async with session.post(
                    llm_model.api_url,  # 直接使用api_url，不再添加/api/generate
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=300)
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"❌ Ollama API错误 {response.status}: {error_text}")
                        raise Exception(f"Ollama API调用失败: {response.status} - {error_text}")

                    # 逐行读取流式响应
                    buffer = ""
                    full_response = ""
                    
                    async for line in response.content:  # 按行读取而不是逐字节
                        try:
                            line_text = line.decode('utf-8', errors='ignore').strip()
                            
                            if not line_text:
                                continue
                                
                            chunk_data = json.loads(line_text)
                            if 'response' in chunk_data:
                                # 获取新的文本片段
                                new_text = chunk_data['response']
                                full_response += new_text
                                
                                # 逐字符发送
                                for char in new_text:
                                    yield {
                                        "type": "ai_chunk",
                                        "content": char,
                                        "full_content": full_response,
                                        "is_complete": False
                                    }
                                    # 添加小延迟模拟打字机效果
                                    await asyncio.sleep(0.01)
                            
                            # 检查是否完成
                            if chunk_data.get('done', False):
                                yield {
                                    "type": "ai_complete",
                                    "full_content": full_response,
                                    "is_complete": True
                                }
                                return
                                
                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ JSON解析失败: {e}, 行内容: {line_text}")
                            continue
                        except Exception as e:
                            logger.error(f"❌ 处理响应行时出错: {e}")
                            continue

        elif api_format == "openai":
            # OpenAI 兼容格式的流式调用
            async with aiohttp.ClientSession() as session:
                request_data = {
                    "model": config_params.get("model", llm_model.name),  # 优先使用配置中的model字段
                    "messages": messages,
                    "stream": True,
                    "temperature": config_params.get("temperature", 0.7),
                    "max_tokens": config_params.get("max_tokens", 2048),
                    "top_p": config_params.get("top_p", 0.9),
                }

                headers = {
                    "Content-Type": "application/json",
                }
                
                # 添加API密钥（优先使用加密字段，然后是配置参数）
                api_key = None
                if llm_model.api_key_encrypted:
                    # TODO: 这里应该解密api_key_encrypted，暂时直接使用
                    api_key = llm_model.api_key_encrypted
                elif config_params.get("api_key"):
                    api_key = config_params["api_key"]
                
                if api_key:
                    headers["Authorization"] = f"Bearer {api_key}"

                # 检查API URL是否已经包含了chat/completions路径
                api_url = llm_model.api_url
                if not api_url.endswith('/chat/completions') and not api_url.endswith('/v1/chat/completions'):
                    api_url = f"{api_url}/v1/chat/completions"
                
                async with session.post(
                    api_url,
                    json=request_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=300)
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"❌ OpenAI API错误 {response.status}: {error_text}")
                        raise Exception(f"OpenAI API调用失败: {response.status} - {error_text}")

                    buffer = ""
                    full_response = ""
                    
                    async for line in response.content:  # 按行读取而不是逐字节
                        try:
                            line_text = line.decode('utf-8', errors='ignore').strip()
                            
                            if not line_text or not line_text.startswith('data: '):
                                continue
                                
                            if line_text == 'data: [DONE]':
                                yield {
                                    "type": "ai_complete",
                                    "full_content": full_response,
                                    "is_complete": True
                                }
                                return
                                
                            data = json.loads(line_text[6:])  # 移除 'data: ' 前缀
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    new_text = delta['content']
                                    full_response += new_text
                                    
                                    # 逐字符发送
                                    for char in new_text:
                                        yield {
                                            "type": "ai_chunk",
                                            "content": char,
                                            "full_content": full_response,
                                            "is_complete": False
                                        }
                                        await asyncio.sleep(0.01)
                                        
                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ JSON解析失败: {e}, 行内容: {line_text}")
                            continue
                        except Exception as e:
                            logger.error(f"❌ 处理响应行时出错: {e}")
                            continue

        # 如果没有匹配的API格式，返回默认响应
        default_response = "抱歉，当前模型配置不支持流式响应。"
        for char in default_response:
            yield {
                "type": "ai_chunk",
                "content": char,
                "full_content": default_response[:default_response.index(char) + 1],
                "is_complete": False
            }
            await asyncio.sleep(0.05)
            
        yield {
            "type": "ai_complete",
            "full_content": default_response,
            "is_complete": True
        }
        return
        
    except Exception as e:
        logger.error(f"❌ 流式AI响应生成失败: {str(e)}")
        error_msg = f"抱歉，AI服务暂时不可用：{str(e)}"
        yield {
            "type": "error",
            "error": error_msg,
            "is_complete": True
        }
        raise e

def get_ai_model_name(llm_model_id: Optional[int]) -> str:
    """
    获取AI模型名称
    """
    if not llm_model_id:
        return "AI-Model-Default"
    return f"AI-Model-{llm_model_id}"

def estimate_token_count(content: str) -> int:
    """
    估算token数量
    """
    # 简单估算：中文字符数 + 英文单词数
    chinese_chars = len([c for c in content if '\u4e00' <= c <= '\u9fff'])
    english_words = len(content.split()) - chinese_chars
    return chinese_chars + english_words

async def generate_ai_response(user_message: str, session_obj: models.Session, db: Session) -> str:
    """
    生成AI响应（非流式版本）
    """
    # 这里可以实现非流式的AI响应生成逻辑
    # 暂时返回一个简单的响应
    return f"收到您的消息：{user_message}。这是一个AI响应示例。"