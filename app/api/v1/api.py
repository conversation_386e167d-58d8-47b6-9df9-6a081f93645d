# app/api/v1/api.py
from fastapi import APIRouter

# Import admin endpoint routers
from app.api.v1.endpoints.admin import llm_models as admin_llm_models
from app.api.v1.endpoints.admin import users as admin_users
from app.api.v1.endpoints.admin import roles as admin_roles
from app.api.v1.endpoints.admin import levels as admin_levels
from app.api.v1.endpoints.admin import regex_rules as admin_regex_rules
from app.api.v1.endpoints.admin import desensitization_rules as admin_desensitization_rules
from app.api.v1.endpoints.admin import security_policies as admin_security_policies
from app.api.v1.endpoints.admin import user_hierarchy as admin_user_hierarchy
from app.api.v1.endpoints.admin import model_assignments as admin_model_assignments
from app.api.v1.endpoints.admin import level_data_access as admin_level_data_access

# Import other top-level endpoint routers for v1
from app.api.v1.endpoints import login
from app.api.v1.endpoints import auth
from app.api.v1.endpoints import messages
from app.api.v1.endpoints import sessions
from app.api.v1.endpoints import llm_models as public_llm_models
from app.api.v1.endpoints import keyword_groups
from app.api.v1.endpoints import keywords

# Import new permission management endpoints
from app.api.v1.endpoints import role_permissions
from app.api.v1.endpoints import data_classification
from app.api.v1.endpoints import audit_logs
from app.api.v1.endpoints import knowledge_base
from app.api.v1.endpoints import rag
from app.api.v1.endpoints import rag_sessions
from app.api.v1.endpoints import user_document_permissions
from app.api.v1.endpoints import feature_permissions
# Example: if you had a public 'items' endpoint
# from app.api.v1.endpoints import items as public_items

api_router = APIRouter()

# === Login Router ===
# This is typically at the root of /api/v1 or a similar path for token acquisition
api_router.include_router(login.router, prefix="/login", tags=["Login"])

# === Auth Router ===
# User authentication and profile endpoints
api_router.include_router(auth.router, prefix="/auth", tags=["Auth"])

# === Admin Routers ===
# Group all admin-specific endpoints under an /admin prefix
admin_api_router = APIRouter() # Router for all admin endpoints

admin_api_router.include_router(
    admin_llm_models.router, 
    prefix="/llm-models", 
    tags=["Admin - LLM Models"]
)

admin_api_router.include_router(
    admin_users.router, 
    prefix="/users", 
    tags=["Admin - Users"]
)

admin_api_router.include_router(
    admin_roles.router, 
    prefix="/roles", 
    tags=["Admin - Roles"]
)

admin_api_router.include_router(
    admin_levels.router, 
    prefix="/levels", 
    tags=["Admin - Levels"]
)

# 关键词管理端点（管理员功能）
admin_api_router.include_router(
    keyword_groups.router,
    prefix="/keyword-groups",
    tags=["Admin - Keyword Groups"]
)

admin_api_router.include_router(
    keywords.router,
    prefix="/keywords",
    tags=["Admin - Keywords"]
)

admin_api_router.include_router(
    admin_regex_rules.router,
    prefix="/regex-rules",
    tags=["Admin - Regex Rules"]
)

admin_api_router.include_router(
    admin_desensitization_rules.router,
    prefix="/desensitization-rules",
    tags=["Admin - Desensitization Rules"]
)

admin_api_router.include_router(
    admin_security_policies.router,
    prefix="/security-policies",
    tags=["Admin - Security Policies"]
)

admin_api_router.include_router(
    admin_user_hierarchy.router,
    prefix="/hierarchy",
    tags=["Admin - User Hierarchy"]
)

admin_api_router.include_router(
    admin_model_assignments.router,
    prefix="/model-assignments",
    tags=["Admin - Model Assignments"]
)

# 新增权限管理端点
admin_api_router.include_router(
    role_permissions.router,
    prefix="/permissions",
    tags=["Admin - Role Permissions"]
)

admin_api_router.include_router(
    data_classification.router,
    prefix="/data-classification",
    tags=["Admin - Data Classification"]
)

admin_api_router.include_router(
    audit_logs.router,
    prefix="/audit",
    tags=["Admin - Audit Logs"]
)

admin_api_router.include_router(
    admin_level_data_access.router,
    prefix="/level-data-access",
    tags=["Admin - Level Data Access"]
)

# 功能级权限管理端点
admin_api_router.include_router(
    feature_permissions.router,
    prefix="/feature-permissions",
    tags=["Admin - Feature Permissions"]
)

# Mount the admin_api_router under the /admin path in the main v1 api_router
api_router.include_router(admin_api_router, prefix="/admin")

# === Other V1 Routers ===
# Include other non-admin v1 routers directly under api_router if any
# Example:
# api_router.include_router(
#     public_items.router, 
#     prefix="/items", 
#     tags=["Public Items"]
# )

# === User-level Routers ===
# TODO: 添加普通用户可访问的端点
# 例如：会话管理、个人信息等

# 会话管理端点
api_router.include_router(
    sessions.router,
    prefix="/sessions",
    tags=["Sessions"]
)

# 消息管理端点
api_router.include_router(
    messages.router,
    prefix="/messages", 
    tags=["Messages"]
)

# 模型列表端点（普通用户可访问）
api_router.include_router(
    public_llm_models.router,
    prefix="/llm-models",
    tags=["LLM Models"]
)

# 关键词检查端点（普通用户可访问，用于安全检查）
# 创建单独的路由器只包含检查功能
keyword_check_router = APIRouter()
keyword_check_router.add_api_route("/check", keywords.check_keyword_match, methods=["POST"])
keyword_check_router.add_api_route("/batch-check", keywords.batch_check_keyword_match, methods=["POST"])

api_router.include_router(
    keyword_check_router,
    prefix="/security/keywords",
    tags=["Security - Keywords"]
)

# 数据分类端点（普通用户可访问，用于查看可访问的数据分级）
user_data_classification_router = APIRouter()
user_data_classification_router.add_api_route("/accessible-grades", data_classification.get_user_accessible_grades, methods=["GET"])
user_data_classification_router.add_api_route("/available-grades", data_classification.get_available_data_grades, methods=["GET"])

api_router.include_router(
    user_data_classification_router,
    prefix="/data-classification",
    tags=["Data Classification"]
)

# 知识库管理端点（普通用户可访问，需要相应权限）
api_router.include_router(
    knowledge_base.router,
    prefix="/knowledge-base",
    tags=["Knowledge Base"]
)

# RAG问答端点（普通用户可访问，需要相应权限）
api_router.include_router(
    rag.router,
    prefix="/rag",
    tags=["RAG - Retrieval Augmented Generation"]
)

# RAG会话管理端点（普通用户可访问，需要相应权限）
api_router.include_router(
    rag_sessions.router,
    prefix="/rag-sessions",
    tags=["RAG Sessions"]
)

# 用户文档权限管理端点（管理员权限）
api_router.include_router(
    user_document_permissions.router,
    prefix="/user-document-permissions",
    tags=["User Document Permissions"]
)

# The api_router will be imported by the main application file (e.g., main.py)
# and included with a prefix like /api/v1
