# app/api/deps.py
from typing import Generator, Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from app.db.session import SyncSessionLocal
from app.core.config import settings
from app.db.models import User
from app import schemas, crud # Assuming crud.user exists

# This is where your OAuth2PasswordBearer instance would be defined
# It points to the token URL (e.g., /api/v1/login/access-token)
reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)

def get_db() -> Generator:
    try:
        db = SyncSessionLocal()
        yield db
    finally:
        db.close()

# Placeholder for get_current_user, to be refined based on existing auth setup
def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(reusable_oauth2)
) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        
        # 将字符串ID转换为整数
        try:
            user_id_int = int(user_id)
        except ValueError:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    user = crud.user_crud.get(db, id=user_id_int)
    if user is None:
        raise credentials_exception
    return user

def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")
    return current_user

def get_current_active_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    # 使用我们的权限检查函数
    from app.core.permissions import is_admin
    if not is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="The user doesn't have enough privileges"
        )
    return current_user

# 别名，为了保持一致性
require_admin = get_current_active_admin_user
