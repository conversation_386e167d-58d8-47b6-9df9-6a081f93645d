# app/schemas/message.py

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class SenderType(str, Enum):
    """消息发送者类型枚举"""
    USER = "user"
    AI = "ai"
    SYSTEM = "system"

# 基础Message schema
class MessageBase(BaseModel):
    """消息基础schema"""
    content: str = Field(..., min_length=1, max_length=10000, description="消息内容")
    sender_type: SenderType = Field(default=SenderType.USER, description="发送者类型")

# 创建消息的请求schema
class MessageCreate(MessageBase):
    """创建消息请求schema"""
    session_id: int = Field(..., description="会话ID")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "content": "Hello, how can you help me?",
                "sender_type": "user",
                "session_id": 1
            }
        }
    )

# 更新消息的请求schema
class MessageUpdate(BaseModel):
    """更新消息请求schema"""
    content: Optional[str] = Field(None, min_length=1, max_length=10000, description="消息内容")
    is_blocked: Optional[bool] = Field(None, description="是否被拦截")
    block_reason: Optional[str] = Field(None, max_length=500, description="拦截原因")
    security_check_passed: Optional[bool] = Field(None, description="安全检查是否通过")
    security_check_details: Optional[Dict[str, Any]] = Field(None, description="安全检查详情")

# 消息响应schema
class MessageResponse(MessageBase):
    """消息响应schema"""
    id: int = Field(..., description="消息ID")
    session_id: int = Field(..., description="会话ID")
    user_id: int = Field(..., description="用户ID")
    
    # 安全检查相关
    is_blocked: bool = Field(..., description="是否被安全系统拦截")
    block_reason: Optional[str] = Field(None, description="拦截原因")
    security_check_passed: bool = Field(..., description="安全检查是否通过")
    security_check_details: Optional[Dict[str, Any]] = Field(None, description="安全检查详情")
    
    # AI相关字段
    ai_model_used: Optional[str] = Field(None, description="使用的AI模型名称")
    ai_response_time: Optional[float] = Field(None, description="AI响应时间（秒）")
    ai_token_count: Optional[int] = Field(None, description="AI使用的token数量")
    
    # 状态字段
    is_deleted: bool = Field(..., description="是否已删除")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    model_config = ConfigDict(from_attributes=True)

# 消息详情响应schema（包含关联信息）
class MessageDetail(MessageResponse):
    """消息详情响应schema"""
    # 可以在这里添加session和user的基本信息
    session_title: Optional[str] = Field(None, description="会话标题")
    username: Optional[str] = Field(None, description="用户名")

# 消息列表响应schema
class MessageListResponse(BaseModel):
    """消息列表响应schema"""
    items: List[MessageResponse] = Field(..., description="消息列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过的数量")
    limit: int = Field(..., description="限制数量")
    
    model_config = ConfigDict(from_attributes=True)

# 安全检查结果schema
class SecurityCheckResult(BaseModel):
    """安全检查结果schema"""
    passed: bool = Field(..., description="检查是否通过")
    score: int = Field(..., ge=0, le=100, description="安全评分（0-100）")
    keyword_check: bool = Field(..., description="关键词检查结果")
    malicious_intent_check: bool = Field(..., description="恶意意图检查结果")
    data_sensitivity_check: bool = Field(..., description="数据敏感性检查结果")
    blocked_keywords: List[str] = Field(default=[], description="被拦截的关键词")
    risk_level: str = Field(..., description="风险等级: low/medium/high")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "passed": True,
                "score": 95,
                "keyword_check": True,
                "malicious_intent_check": True,
                "data_sensitivity_check": True,
                "blocked_keywords": [],
                "risk_level": "low"
            }
        }
    )

# AI消息生成请求schema
class AIMessageGenerateRequest(BaseModel):
    """AI消息生成请求schema"""
    session_id: int = Field(..., description="会话ID")
    user_message: str = Field(..., min_length=1, max_length=10000, description="用户消息内容")
    llm_model_id: Optional[int] = Field(None, description="指定使用的LLM模型ID")
    stream: bool = Field(default=False, description="是否流式返回")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "session_id": 1,
                "user_message": "Explain quantum computing",
                "llm_model_id": 1,
                "stream": False
            }
        }
    )

# AI消息生成响应schema
class AIMessageGenerateResponse(BaseModel):
    """AI消息生成响应schema"""
    user_message: MessageResponse = Field(..., description="用户消息")
    ai_message: MessageResponse = Field(..., description="AI回复消息")
    security_check_result: SecurityCheckResult = Field(..., description="安全检查结果")
    generation_time: float = Field(..., description="生成耗时（秒）")
    
    model_config = ConfigDict(from_attributes=True)