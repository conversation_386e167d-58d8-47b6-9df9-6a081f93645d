# app/schemas/__init__.py

# Import your Pydantic schemas here to make them easily accessible.

from .role import RoleB<PERSON>, RoleCreate, RoleRead, RoleUpdate, RoleInDB, RoleListResponse
from .level import LevelBase, LevelCreate, LevelRead, LevelUpdate, LevelInDB, LevelListResponse
from .user import UserBase, UserCreate, UserRead, UserUpdate, UserInDB, UserListResponse, UserLogin
from .token import Token, TokenPayload
from .llm_model import (
    LLMModelBase, LLMModelCreate, LLMModelUpdate, LLMModelRead, 
    LLMModelInDB, LLMModelListResponse, LLMModelConfig, LLMModelSchema
)
from .session import (
    SessionBase, SessionCreate, SessionUpdate, SessionRead, 
    SessionInDB, SessionDetail, SessionListResponse, 
    SessionSoftDelete, SessionRestore
)
from .message import (
    MessageBase, MessageCreate, MessageUpdate, MessageResponse, 
    MessageDetail, MessageListResponse,
    SecurityCheckResult, AIMessageGenerateRequest, AIMessageGenerateResponse,
    SenderType
)
from .keyword_group import (
    KeywordGroupBase, KeywordGroupCreate, KeywordGroupUpdate, 
    KeywordGroupRead, KeywordGroupInDB, KeywordGroupListResponse, KeywordGroupSchema
)
from .keyword import (
    KeywordBase, KeywordCreate, KeywordUpdate, KeywordRead, 
    KeywordInDB, KeywordListResponse, KeywordSchema, 
    KeywordMatchResult, MatchType
)
from .regex_rule import (
    RegexRuleBase, RegexRuleCreate, RegexRuleUpdate, RegexRuleRead,
    RegexRuleInDB, RegexRuleListResponse, RegexRuleSchema,
    RegexRuleMatchResult, RegexRuleBatchCheckRequest, RegexRuleBatchCheckResponse,
    RegexRuleTestRequest, RegexRuleTestResponse, RegexRuleStats,
    IntentCategory, RuleSeverity
)
from .desensitization_rule import (
    DesensitizationRuleBase, DesensitizationRuleCreate, DesensitizationRuleUpdate, DesensitizationRuleResponse,
    DesensitizationRuleListResponse, DesensitizationRuleTestRequest, DesensitizationRuleTestResponse,
    DesensitizationTestMatch, DesensitizationBatchCheckRequest, DesensitizationBatchCheckResponse,
    DesensitizationBatchResult, DesensitizationRuleStats, DesensitizationStatsResponse,
    DesensitizationOptionsResponse, DataTypeOptions, MaskingLevelOptions, MaskingStrategyOptions,
    DataType, MaskingLevel, MaskingStrategy
)
from .knowledge_base import (
    ProcessingStatus, DocumentType,
    KnowledgeDocumentBase, KnowledgeDocumentCreate, KnowledgeDocumentUpdate, KnowledgeDocumentResponse,
    KnowledgeDocumentListResponse, DocumentUploadResponse,
    DocumentSearchRequest, SearchResultChunk, DocumentSearchResponse,
    ProcessingStatusUpdate, KnowledgeVersionResponse, KnowledgeVersionListResponse,
    DocumentStatistics
)

# Define __all__ to control what `from app.schemas import *` imports
__all__ = [
    # Role schemas
    "RoleBase", "RoleCreate", "RoleRead", "RoleUpdate", "RoleInDB", "RoleListResponse",
    # Level schemas
    "LevelBase", "LevelCreate", "LevelRead", "LevelUpdate", "LevelInDB", "LevelListResponse",
    # User schemas
    "UserBase", "UserCreate", "UserRead", "UserUpdate", "UserInDB", "UserListResponse", "UserLogin",
    # Token schemas
    "Token", "TokenPayload",
    # LLM Model schemas
    "LLMModelBase", "LLMModelCreate", "LLMModelUpdate", "LLMModelRead", 
    "LLMModelInDB", "LLMModelListResponse", "LLMModelConfig", "LLMModelSchema",
    # Session schemas
    "SessionBase", "SessionCreate", "SessionUpdate", "SessionRead", 
    "SessionInDB", "SessionDetail", "SessionListResponse", 
    "SessionSoftDelete", "SessionRestore",
    # Message schemas
    "MessageBase", "MessageCreate", "MessageUpdate", "MessageResponse",
    "MessageDetail", "MessageListResponse",
    "SecurityCheckResult", "AIMessageGenerateRequest", "AIMessageGenerateResponse",
    "SenderType",
    # Keyword Group schemas
    "KeywordGroupBase", "KeywordGroupCreate", "KeywordGroupUpdate", 
    "KeywordGroupRead", "KeywordGroupInDB", "KeywordGroupListResponse", "KeywordGroupSchema",
    # Keyword schemas
    "KeywordBase", "KeywordCreate", "KeywordUpdate", "KeywordRead", 
    "KeywordInDB", "KeywordListResponse", "KeywordSchema", 
    "KeywordMatchResult", "MatchType",
    # Regex Rule schemas
    "RegexRuleBase", "RegexRuleCreate", "RegexRuleUpdate", "RegexRuleRead",
    "RegexRuleInDB", "RegexRuleListResponse", "RegexRuleSchema",
    "RegexRuleMatchResult", "RegexRuleBatchCheckRequest", "RegexRuleBatchCheckResponse",
    "RegexRuleTestRequest", "RegexRuleTestResponse", "RegexRuleStats",
    "IntentCategory", "RuleSeverity",
    # Desensitization Rule schemas
    "DesensitizationRuleBase", "DesensitizationRuleCreate", "DesensitizationRuleUpdate", "DesensitizationRuleResponse",
    "DesensitizationRuleListResponse", "DesensitizationRuleTestRequest", "DesensitizationRuleTestResponse",
    "DesensitizationTestMatch", "DesensitizationBatchCheckRequest", "DesensitizationBatchCheckResponse",
    "DesensitizationBatchResult", "DesensitizationRuleStats", "DesensitizationStatsResponse",
    "DesensitizationOptionsResponse", "DataTypeOptions", "MaskingLevelOptions", "MaskingStrategyOptions",
    "DataType", "MaskingLevel", "MaskingStrategy",
    # Knowledge Base schemas
    "ProcessingStatus", "DocumentType",
    "KnowledgeDocumentBase", "KnowledgeDocumentCreate", "KnowledgeDocumentUpdate", "KnowledgeDocumentResponse",
    "KnowledgeDocumentListResponse", "DocumentUploadResponse",
    "DocumentSearchRequest", "SearchResultChunk", "DocumentSearchResponse",
    "ProcessingStatusUpdate", "KnowledgeVersionResponse", "KnowledgeVersionListResponse",
    "DocumentStatistics",
]
