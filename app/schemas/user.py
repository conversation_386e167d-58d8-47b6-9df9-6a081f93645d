# app/schemas/user.py

from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime

# Shared properties
class UserBase(BaseModel):
    username: str
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = True
    role_id: Optional[int] = None # Will be set during creation/update
    level_id: Optional[int] = None # Will be set during creation/update
    manager_id: Optional[int] = None # 直接上级用户ID

# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str
    role_id: int # role_id is mandatory for user creation

# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = None
    username: Optional[str] = None # Allow username update if desired
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    role_id: Optional[int] = None
    level_id: Optional[int] = None

# Properties shared by models stored in DB
class UserInDBBase(UserBase):
    id: int
    # password_hash: str # This should be present for DB model but not exposed
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Additional properties to return to client
class UserRead(UserInDBBase):
    # Exclude password_hash by not including it
    pass

# Additional properties stored in DB
class UserInDB(UserInDBBase):
    password_hash: str

# User login schema
class UserLogin(BaseModel):
    username: str
    password: str

# User list response schema
class UserListResponse(BaseModel):
    items: list[UserRead]
    total: int
    page: int
    size: int
    pages: int

# ====== 层级管理相关Schema ======

# 用户层级信息
class UserHierarchy(BaseModel):
    id: int
    username: str
    email: Optional[str]
    role_name: Optional[str]
    level_name: Optional[str]
    manager_id: Optional[int]
    manager_name: Optional[str]
    subordinate_count: int
    
    class Config:
        from_attributes = True

# 组织树节点
class OrganizationNode(BaseModel):
    id: int
    username: str
    email: Optional[str]
    role_name: Optional[str]
    level_name: Optional[str]
    manager_id: Optional[int]
    is_active: bool
    subordinates: List['OrganizationNode'] = []
    
    class Config:
        from_attributes = True

# 设置管理关系的请求
class SetManagerRequest(BaseModel):
    manager_id: Optional[int] = None  # None表示移除管理关系
    
# 批量设置管理关系的请求
class BatchSetManagerRequest(BaseModel):
    user_ids: List[int]
    manager_id: Optional[int] = None
    
# 管理关系操作响应
class ManagerOperationResponse(BaseModel):
    success: bool
    message: str
    affected_users: List[str] = []  # 受影响的用户名列表
