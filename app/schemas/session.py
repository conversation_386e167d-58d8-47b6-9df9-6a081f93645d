# app/schemas/session.py

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# Message schema的前向引用（避免循环导入）
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .message import MessageRead

# Shared properties
class SessionBase(BaseModel):
    title: Optional[str] = Field(None, max_length=255, description="会话标题")

# Properties to receive on session creation
class SessionCreate(SessionBase):
    llm_model_id: int = Field(..., description="使用的大模型ID")
    session_type: Optional[str] = Field("chat", description="会话类型：chat(普通对话)、rag(知识库问答)")

# Properties to receive on session update
class SessionUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=255, description="会话标题")
    llm_model_id: Optional[int] = Field(None, description="使用的大模型ID")

# Properties shared by models stored in DB
class SessionInDBBase(SessionBase):
    id: int
    user_id: int
    llm_model_id: Optional[int]  # 修复：允许为None
    session_type: Optional[str] = Field(None, description="会话类型：chat(普通对话)、rag(知识库问答)")
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    last_activity_at: datetime

    class Config:
        from_attributes = True

# Properties to return to client
class SessionRead(SessionInDBBase):
    message_count: Optional[int] = Field(None, description="会话中的消息数量")
    is_active: Optional[bool] = Field(None, description="会话是否活跃")
    display_title: Optional[str] = Field(None, description="显示标题")
    user_name: Optional[str] = Field(None, description="用户名")
    llm_model_name: Optional[str] = Field(None, description="模型名称")

# Properties stored in DB
class SessionInDB(SessionInDBBase):
    pass

# 会话详情（包含消息列表）
class SessionDetail(SessionRead):
    messages: Optional[List["MessageRead"]] = Field(None, description="会话消息列表")

# 会话列表响应Schema
class SessionListResponse(BaseModel):
    items: list[SessionRead]
    total: int
    page: int
    size: int
    pages: int

# 用于软删除操作的Schema
class SessionSoftDelete(BaseModel):
    is_deleted: bool = Field(True, description="是否删除")

# 用于恢复会话的Schema
class SessionRestore(BaseModel):
    is_deleted: bool = Field(False, description="取消删除") 