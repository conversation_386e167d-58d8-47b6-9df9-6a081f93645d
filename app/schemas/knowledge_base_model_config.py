"""
知识库模型配置相关的Pydantic Schema
"""
from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class ConfigType(str, Enum):
    """配置类型枚举"""
    GLOBAL = "global"           # 全局默认配置
    USER = "user"              # 用户自定义配置
    CATEGORY = "category"      # 按分类配置
    GRADE = "grade"           # 按分级配置


class KnowledgeBaseModelConfigBase(BaseModel):
    """知识库模型配置基础Schema"""
    config_type: ConfigType = Field(..., description="配置类型")
    model_id: int = Field(..., description="关联的LLM模型ID")
    user_id: Optional[int] = Field(None, description="用户ID（用户自定义配置时使用）")
    category_id: Optional[int] = Field(None, description="数据分类ID（按分类配置时使用）")
    grade_id: Optional[int] = Field(None, description="数据分级ID（按分级配置时使用）")
    config_name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, description="配置描述")
    model_params: Optional[Dict[str, Any]] = Field(None, description="模型特定参数")
    is_active: bool = Field(True, description="是否启用")
    priority: int = Field(100, description="优先级（数字越小优先级越高）")

    @field_validator('user_id', 'category_id', 'grade_id')
    @classmethod
    def validate_config_consistency(cls, v, info):
        """验证配置类型与相关ID的一致性"""
        # 在Pydantic V2中，我们需要在model_validator中处理跨字段验证
        return v

    @field_validator('model_params')
    @classmethod
    def validate_model_params(cls, v):
        """验证模型参数"""
        if v is not None:
            # 检查常见的模型参数
            allowed_params = {
                'temperature', 'max_tokens', 'top_p', 'frequency_penalty', 
                'presence_penalty', 'stop', 'stream', 'timeout'
            }
            for key in v.keys():
                if key not in allowed_params:
                    # 允许未知参数，但记录警告
                    pass
        return v



class KnowledgeBaseModelConfigCreate(KnowledgeBaseModelConfigBase):
    """创建知识库模型配置的Schema"""
    
    @model_validator(mode='after')
    def validate_config_type_consistency_create(self):
        """验证配置类型与相关ID的一致性（创建时）"""
        # 对于用户配置，user_id在后端自动设置，所以这里不验证
        # 只验证分类和分级配置必须有对应的ID
        if self.config_type == ConfigType.CATEGORY and self.category_id is None:
            raise ValueError("按分类配置必须指定category_id")
        elif self.config_type == ConfigType.GRADE and self.grade_id is None:
            raise ValueError("按分级配置必须指定grade_id")
        return self


class KnowledgeBaseModelConfigUpdate(BaseModel):
    """更新知识库模型配置的Schema"""
    model_id: Optional[int] = Field(None, description="关联的LLM模型ID")
    config_name: Optional[str] = Field(None, min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, description="配置描述")
    model_params: Optional[Dict[str, Any]] = Field(None, description="模型特定参数")
    is_active: Optional[bool] = Field(None, description="是否启用")
    priority: Optional[int] = Field(None, description="优先级")

    @field_validator('model_params')
    @classmethod
    def validate_model_params_update(cls, v):
        """验证模型参数"""
        if v is not None:
            allowed_params = {
                'temperature', 'max_tokens', 'top_p', 'frequency_penalty', 
                'presence_penalty', 'stop', 'stream', 'timeout'
            }
            for key in v.keys():
                if key not in allowed_params:
                    pass
        return v


class KnowledgeBaseModelConfigResponse(KnowledgeBaseModelConfigBase):
    """知识库模型配置响应Schema"""
    id: int = Field(..., description="配置ID")
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联信息
    model_name: Optional[str] = Field(None, description="模型名称")
    model_api_url: Optional[str] = Field(None, description="模型API地址")
    creator_name: Optional[str] = Field(None, description="创建者名称")
    user_name: Optional[str] = Field(None, description="关联用户名（用户配置时）")
    category_name: Optional[str] = Field(None, description="分类名称（分类配置时）")
    grade_name: Optional[str] = Field(None, description="分级名称（分级配置时）")

    @model_validator(mode='after')
    def validate_config_type_consistency_response(self):
        """验证配置类型与相关ID的一致性（响应时）"""
        if self.config_type == ConfigType.USER and self.user_id is None:
            raise ValueError("用户自定义配置必须有user_id")
        elif self.config_type == ConfigType.CATEGORY and self.category_id is None:
            raise ValueError("按分类配置必须有category_id")
        elif self.config_type == ConfigType.GRADE and self.grade_id is None:
            raise ValueError("按分级配置必须有grade_id")
        return self

    class Config:
        from_attributes = True


class KnowledgeBaseModelConfigListResponse(BaseModel):
    """知识库模型配置列表响应Schema"""
    total: int = Field(..., description="总数")
    items: List[KnowledgeBaseModelConfigResponse] = Field(..., description="配置列表")


class KnowledgeBaseModelConfigQuery(BaseModel):
    """知识库模型配置查询Schema"""
    config_type: Optional[ConfigType] = Field(None, description="配置类型筛选")
    model_id: Optional[int] = Field(None, description="模型ID筛选")
    user_id: Optional[int] = Field(None, description="用户ID筛选")
    category_id: Optional[int] = Field(None, description="分类ID筛选")
    grade_id: Optional[int] = Field(None, description="分级ID筛选")
    is_active: Optional[bool] = Field(None, description="是否启用筛选")
    skip: int = Field(0, ge=0, description="跳过数量")
    limit: int = Field(10, ge=1, le=100, description="限制数量")


class KnowledgeBaseModelSelection(BaseModel):
    """知识库模型选择结果Schema"""
    config_id: int = Field(..., description="配置ID")
    model_id: int = Field(..., description="模型ID")
    model_name: str = Field(..., description="模型名称")
    model_api_url: str = Field(..., description="模型API地址")
    model_params: Dict[str, Any] = Field(..., description="模型参数")
    config_type: ConfigType = Field(..., description="配置类型")
    priority: int = Field(..., description="优先级")
    selection_reason: str = Field(..., description="选择原因")


class ModelPermissionConfig(BaseModel):
    """模型权限配置Schema"""
    can_view: bool = Field(True, description="是否可以查看模型配置")
    can_create: bool = Field(False, description="是否可以创建模型配置")
    can_update: bool = Field(False, description="是否可以更新模型配置")
    can_delete: bool = Field(False, description="是否可以删除模型配置")
    can_config_global: bool = Field(False, description="是否可以配置全局模型")
    can_config_category: bool = Field(False, description="是否可以配置分类模型")
    can_config_grade: bool = Field(False, description="是否可以配置分级模型")
    accessible_models: List[int] = Field(default_factory=list, description="可访问的模型ID列表")
    accessible_categories: List[int] = Field(default_factory=list, description="可配置的分类ID列表")
    accessible_grades: List[int] = Field(default_factory=list, description="可配置的分级ID列表")


class KnowledgeBaseModelConfigSummary(BaseModel):
    """知识库模型配置摘要Schema"""
    total_configs: int = Field(..., description="总配置数")
    active_configs: int = Field(..., description="启用的配置数")
    global_configs: int = Field(..., description="全局配置数")
    user_configs: int = Field(..., description="用户配置数")
    category_configs: int = Field(..., description="分类配置数")
    grade_configs: int = Field(..., description="分级配置数")
    models_in_use: List[Dict[str, Any]] = Field(..., description="使用中的模型列表")
    recent_updates: List[Dict[str, Any]] = Field(..., description="最近更新列表")