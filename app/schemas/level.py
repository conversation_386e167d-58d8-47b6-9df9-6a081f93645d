# app/schemas/level.py

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

# Shared properties
class LevelBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="级别名称")
    rank_value: int = Field(..., ge=1, description="级别排序值 (数字越小级别越高)")
    description: Optional[str] = Field(None, max_length=255, description="级别描述")

# Properties to receive on level creation
class LevelCreate(LevelBase):
    role_id: int = Field(..., description="所属角色ID")

# Properties to receive on level update
class LevelUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="级别名称")
    rank_value: Optional[int] = Field(None, ge=1, description="级别排序值")
    description: Optional[str] = Field(None, max_length=255, description="级别描述")
    is_active: Optional[bool] = Field(None, description="级别是否激活")
    # role_id 通常不允许更新，如果需要可以添加

# Properties shared by models stored in DB
class LevelInDBBase(LevelBase):
    id: int
    role_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Properties to return to client
class LevelRead(LevelInDBBase):
    user_count: Optional[int] = Field(None, description="该级别下的用户数量")
    can_be_deleted: Optional[bool] = Field(None, description="是否可以删除")
    role_name: Optional[str] = Field(None, description="所属角色名称")

# Properties stored in DB
class LevelInDB(LevelInDBBase):
    pass

# 级别列表响应Schema
class LevelListResponse(BaseModel):
    items: list[LevelRead]
    total: int
    page: int
    size: int
    pages: int
