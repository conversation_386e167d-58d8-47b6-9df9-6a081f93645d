from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

from app.db.models.desensitization_rule import DataType, MaskingLevel, MaskingStrategy

# 基础模式
class DesensitizationRuleBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="规则名称")
    description: Optional[str] = Field(None, max_length=500, description="规则描述")
    data_type: DataType = Field(..., description="敏感数据类型")
    pattern: str = Field(..., min_length=1, description="检测正则表达式模式")
    masking_level: MaskingLevel = Field(..., description="脱敏级别")
    masking_strategy: MaskingStrategy = Field(..., description="脱敏策略")
    mask_char: str = Field("*", min_length=1, max_length=5, description="脱敏字符")
    replacement_text: Optional[str] = Field(None, max_length=100, description="替换文本")
    show_first: int = Field(0, ge=0, description="显示前几位")
    show_last: int = Field(0, ge=0, description="显示后几位")
    min_mask_length: int = Field(3, ge=1, description="最小脱敏长度")
    is_active: bool = Field(True, description="是否激活")
    priority: int = Field(0, description="优先级")
    flags: int = Field(2, description="正则表达式标志")  # re.IGNORECASE = 2
    confidence_threshold: float = Field(0.8, ge=0.0, le=1.0, description="置信度阈值")
    role_id: Optional[int] = Field(None, description="关联角色ID")

    @validator('pattern')
    def validate_pattern(cls, v):
        """验证正则表达式模式"""
        import re
        try:
            re.compile(v)
            return v
        except re.error as e:
            raise ValueError(f"无效的正则表达式: {str(e)}")

    @validator('show_first', 'show_last')
    def validate_show_positions(cls, v, values):
        """验证显示位置配置"""
        if v < 0:
            raise ValueError("显示位置不能为负数")
        return v

# 创建模式
class DesensitizationRuleCreate(DesensitizationRuleBase):
    created_by: Optional[str] = Field(None, max_length=100, description="创建者")

# 更新模式
class DesensitizationRuleUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="规则名称")
    description: Optional[str] = Field(None, max_length=500, description="规则描述")
    data_type: Optional[DataType] = Field(None, description="敏感数据类型")
    pattern: Optional[str] = Field(None, min_length=1, description="检测正则表达式模式")
    masking_level: Optional[MaskingLevel] = Field(None, description="脱敏级别")
    masking_strategy: Optional[MaskingStrategy] = Field(None, description="脱敏策略")
    mask_char: Optional[str] = Field(None, min_length=1, max_length=5, description="脱敏字符")
    replacement_text: Optional[str] = Field(None, max_length=100, description="替换文本")
    show_first: Optional[int] = Field(None, ge=0, description="显示前几位")
    show_last: Optional[int] = Field(None, ge=0, description="显示后几位")
    min_mask_length: Optional[int] = Field(None, ge=1, description="最小脱敏长度")
    is_active: Optional[bool] = Field(None, description="是否激活")
    priority: Optional[int] = Field(None, description="优先级")
    flags: Optional[int] = Field(None, description="正则表达式标志")
    confidence_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度阈值")
    role_id: Optional[int] = Field(None, description="关联角色ID")
    updated_by: Optional[str] = Field(None, max_length=100, description="更新者")

    @validator('pattern')
    def validate_pattern(cls, v):
        """验证正则表达式模式"""
        if v is not None:
            import re
            try:
                re.compile(v)
                return v
            except re.error as e:
                raise ValueError(f"无效的正则表达式: {str(e)}")
        return v

# 响应模式
class DesensitizationRuleResponse(DesensitizationRuleBase):
    id: int = Field(..., description="规则ID")
    match_count: int = Field(0, description="匹配次数统计")
    mask_count: int = Field(0, description="脱敏执行次数统计")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者")
    updated_by: Optional[str] = Field(None, description="最后更新者")

    class Config:
        from_attributes = True

# 列表响应模式
class DesensitizationRuleListResponse(BaseModel):
    items: List[DesensitizationRuleResponse] = Field(..., description="脱敏规则列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")

# 规则测试请求模式
class DesensitizationRuleTestRequest(BaseModel):
    text: str = Field(..., min_length=1, max_length=10000, description="测试文本")
    rule_id: Optional[int] = Field(None, description="指定规则ID，不指定则测试所有激活规则")

# 规则测试结果模式
class DesensitizationTestMatch(BaseModel):
    start: int = Field(..., description="匹配开始位置")
    end: int = Field(..., description="匹配结束位置")
    matched_text: str = Field(..., description="匹配的文本")
    masked_text: str = Field(..., description="脱敏后的文本")
    data_type: str = Field(..., description="数据类型")
    rule_name: str = Field(..., description="规则名称")
    confidence: float = Field(..., description="置信度")

class DesensitizationRuleTestResponse(BaseModel):
    original_text: str = Field(..., description="原始文本")
    processed_text: str = Field(..., description="处理后的文本")
    matches: List[DesensitizationTestMatch] = Field(..., description="匹配结果列表")
    total_matches: int = Field(..., description="总匹配数")
    rules_applied: int = Field(..., description="应用的规则数")

# 批量检测请求模式
class DesensitizationBatchCheckRequest(BaseModel):
    texts: List[str] = Field(..., min_items=1, max_items=100, description="批量检测的文本列表")
    apply_masking: bool = Field(True, description="是否应用脱敏")
    role_id: Optional[int] = Field(None, description="指定角色ID")

# 批量检测结果模式
class DesensitizationBatchResult(BaseModel):
    original_text: str = Field(..., description="原始文本")
    processed_text: str = Field(..., description="处理后的文本")
    matches: List[DesensitizationTestMatch] = Field(..., description="匹配结果")
    has_sensitive_data: bool = Field(..., description="是否包含敏感数据")

class DesensitizationBatchCheckResponse(BaseModel):
    results: List[DesensitizationBatchResult] = Field(..., description="批量检测结果")
    total_processed: int = Field(..., description="处理的文本总数")
    total_matches: int = Field(..., description="总匹配数")
    rules_applied: int = Field(..., description="应用的规则数")

# 统计信息模式
class DesensitizationRuleStats(BaseModel):
    rule_id: int = Field(..., description="规则ID")
    rule_name: str = Field(..., description="规则名称")
    data_type: str = Field(..., description="数据类型")
    match_count: int = Field(..., description="匹配次数")
    mask_count: int = Field(..., description="脱敏次数")
    effectiveness_rate: float = Field(..., description="有效性率")
    is_active: bool = Field(..., description="是否激活")

class DesensitizationStatsResponse(BaseModel):
    rules: List[DesensitizationRuleStats] = Field(..., description="规则统计列表")
    total_rules: int = Field(..., description="总规则数")
    active_rules: int = Field(..., description="激活规则数")
    total_matches: int = Field(..., description="总匹配数")
    total_maskings: int = Field(..., description="总脱敏数")
    
# 枚举类型转换为字符串供前端使用
class DataTypeOptions(BaseModel):
    value: str
    label: str
    description: str

class MaskingLevelOptions(BaseModel):
    value: str
    label: str
    description: str

class MaskingStrategyOptions(BaseModel):
    value: str
    label: str
    description: str

class DesensitizationOptionsResponse(BaseModel):
    data_types: List[DataTypeOptions] = Field(..., description="数据类型选项")
    masking_levels: List[MaskingLevelOptions] = Field(..., description="脱敏级别选项")
    masking_strategies: List[MaskingStrategyOptions] = Field(..., description="脱敏策略选项") 