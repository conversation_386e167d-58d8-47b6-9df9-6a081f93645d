# app/schemas/security_policy.py

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

# 基础属性
class SecurityPolicyBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="策略名称")
    description: Optional[str] = Field(None, max_length=500, description="策略描述")
    
    # 关联目标 (互斥，只能选择一个)
    role_id: Optional[int] = Field(None, description="关联角色ID")
    level_id: Optional[int] = Field(None, description="关联等级ID") 
    user_id: Optional[int] = Field(None, description="关联用户ID")
    
    # 基础配置
    enabled: bool = Field(True, description="策略是否启用")
    max_severity_threshold: str = Field("medium", description="最大严重性阈值")
    enable_early_termination: bool = Field(True, description="是否启用早期终止")
    timeout_seconds: int = Field(30, ge=1, le=300, description="超时时间(秒)")
    
    # 关键词过滤配置
    keyword_enabled: bool = Field(True, description="是否启用关键词过滤")
    keyword_priority_threshold: int = Field(0, ge=0, le=100, description="关键词优先级阈值")
    allowed_keyword_groups: Optional[List[int]] = Field(None, description="允许的关键词分组ID列表")
    blocked_keyword_groups: Optional[List[int]] = Field(None, description="禁止的关键词分组ID列表")
    
    # 绕过配置
    bypass_enabled: bool = Field(False, description="是否允许绕过")
    bypassable_modules: Optional[List[str]] = Field(None, description="可绕过的模块列表")
    
    # 模块配置
    enabled_modules: Optional[List[str]] = Field(None, description="启用的模块列表")
    disabled_modules: Optional[List[str]] = Field(None, description="禁用的模块列表")
    module_configs: Optional[Dict[str, Any]] = Field(None, description="模块特定配置")
    
    # 行为配置
    auto_desensitize: bool = Field(True, description="是否自动脱敏")
    block_on_critical: bool = Field(True, description="严重威胁时是否阻止")
    log_all_requests: bool = Field(True, description="是否记录所有请求")
    
    # 优先级
    priority: int = Field(100, ge=1, le=1000, description="策略优先级")
    
    @validator('max_severity_threshold')
    def validate_severity_threshold(cls, v):
        valid_thresholds = ['low', 'medium', 'high', 'critical']
        if v not in valid_thresholds:
            raise ValueError(f'严重性阈值必须是: {", ".join(valid_thresholds)}')
        return v
    
    @validator('enabled_modules')
    def validate_enabled_modules(cls, v):
        if v is not None:
            valid_modules = ['keyword_filter', 'regex_pattern', 'malicious_intent', 'data_desensitization']
            invalid_modules = [m for m in v if m not in valid_modules]
            if invalid_modules:
                raise ValueError(f'无效的模块: {", ".join(invalid_modules)}')
        return v
    
    @validator('bypassable_modules')
    def validate_bypassable_modules(cls, v):
        if v is not None:
            valid_modules = ['keyword_filter', 'regex_pattern', 'malicious_intent', 'data_desensitization']
            invalid_modules = [m for m in v if m not in valid_modules]
            if invalid_modules:
                raise ValueError(f'无效的绕过模块: {", ".join(invalid_modules)}')
        return v

# 创建策略的请求
class SecurityPolicyCreate(SecurityPolicyBase):
    pass

# 更新策略的请求
class SecurityPolicyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="策略名称")
    description: Optional[str] = Field(None, max_length=500, description="策略描述")
    
    # 基础配置
    enabled: Optional[bool] = Field(None, description="策略是否启用")
    max_severity_threshold: Optional[str] = Field(None, description="最大严重性阈值")
    enable_early_termination: Optional[bool] = Field(None, description="是否启用早期终止")
    timeout_seconds: Optional[int] = Field(None, ge=1, le=300, description="超时时间(秒)")
    
    # 关键词过滤配置
    keyword_enabled: Optional[bool] = Field(None, description="是否启用关键词过滤")
    keyword_priority_threshold: Optional[int] = Field(None, ge=0, le=100, description="关键词优先级阈值")
    allowed_keyword_groups: Optional[List[int]] = Field(None, description="允许的关键词分组ID列表")
    blocked_keyword_groups: Optional[List[int]] = Field(None, description="禁止的关键词分组ID列表")
    
    # 绕过配置
    bypass_enabled: Optional[bool] = Field(None, description="是否允许绕过")
    bypassable_modules: Optional[List[str]] = Field(None, description="可绕过的模块列表")
    
    # 模块配置
    enabled_modules: Optional[List[str]] = Field(None, description="启用的模块列表")
    disabled_modules: Optional[List[str]] = Field(None, description="禁用的模块列表")
    module_configs: Optional[Dict[str, Any]] = Field(None, description="模块特定配置")
    
    # 行为配置
    auto_desensitize: Optional[bool] = Field(None, description="是否自动脱敏")
    block_on_critical: Optional[bool] = Field(None, description="严重威胁时是否阻止")
    log_all_requests: Optional[bool] = Field(None, description="是否记录所有请求")
    
    # 优先级
    priority: Optional[int] = Field(None, ge=1, le=1000, description="策略优先级")

# 策略响应（数据库中的策略）
class SecurityPolicyInDBBase(SecurityPolicyBase):
    id: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        from_attributes = True

# 返回给客户端的策略信息
class SecurityPolicyRead(SecurityPolicyInDBBase):
    target_description: Optional[str] = Field(None, description="策略目标描述")
    role_name: Optional[str] = Field(None, description="关联角色名称")
    level_name: Optional[str] = Field(None, description="关联等级名称")
    user_name: Optional[str] = Field(None, description="关联用户名称")

# API响应中的策略信息 (别名SecurityPolicyRead)
class SecurityPolicyResponse(SecurityPolicyInDBBase):
    pass

# 目标信息类
class SecurityPolicyTargetInfo(BaseModel):
    target_type: str = Field(..., description="目标类型: role/level/user")
    target_id: int = Field(..., description="目标ID")
    target_name: str = Field(..., description="目标名称")

# 数据库中存储的策略
class SecurityPolicyInDB(SecurityPolicyInDBBase):
    pass

# 策略目标信息
class SecurityPolicyTargetInfo(BaseModel):
    target_type: Optional[str] = Field(None, description="目标类型: role, level, user")
    target_name: Optional[str] = Field(None, description="目标名称")
    target_description: Optional[str] = Field(None, description="目标描述")

# 策略列表响应
class SecurityPolicyListResponse(BaseModel):
    items: List[SecurityPolicyRead]
    total: int
    page: int
    size: int
    pages: int

# 策略配置摘要
class SecurityPolicySummary(BaseModel):
    role_policies: int = Field(..., description="角色策略数量")
    level_policies: int = Field(..., description="等级策略数量") 
    user_policies: int = Field(..., description="用户策略数量")
    total_policies: int = Field(..., description="总策略数量")
    active_policies: int = Field(..., description="激活策略数量")

# 策略验证结果
class SecurityPolicyValidation(BaseModel):
    valid: bool = Field(..., description="策略是否有效")
    errors: List[str] = Field(..., description="错误列表")
    warnings: List[str] = Field(..., description="警告列表")

# 策略目标选项
class SecurityPolicyTargetOptions(BaseModel):
    roles: List[Dict[str, Any]] = Field(..., description="可选角色列表")
    levels: List[Dict[str, Any]] = Field(..., description="可选等级列表")
    keyword_groups: List[Dict[str, Any]] = Field(..., description="可选关键词分组列表")
    available_modules: List[str] = Field(..., description="可用模块列表")
    severity_thresholds: List[str] = Field(..., description="可用严重性阈值列表") 