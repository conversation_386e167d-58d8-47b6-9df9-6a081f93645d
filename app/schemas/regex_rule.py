from datetime import datetime
from typing import Optional, List
from enum import Enum
import re

from pydantic import BaseModel, Field, validator

# 恶意意图分类枚举
class IntentCategory(str, Enum):
    PRIVILEGE_ESCALATION = "privilege_escalation"      # 权限提升类
    PRIVACY_EXTRACTION = "privacy_extraction"          # 隐私套取类
    DATA_POISONING = "data_poisoning"                  # 数据投毒类
    SYSTEM_BYPASS = "system_bypass"                    # 系统绕过类
    SOCIAL_ENGINEERING = "social_engineering"          # 社会工程学类
    MALICIOUS_CODE = "malicious_code"                  # 恶意代码类
    INFORMATION_DISCLOSURE = "information_disclosure"  # 信息泄露类
    # 越狱攻击专门分类
    JAILBREAK_GOAL_HIJACKING = "jailbreak_goal_hijacking"        # 越狱-目标竞争攻击
    JAILBREAK_ROLE_PLAYING = "jailbreak_role_playing"            # 越狱-角色扮演攻击
    JAILBREAK_ENCODING_TRANSLATION = "jailbreak_encoding_translation"  # 越狱-编码翻译攻击
    JAILBREAK_INSTRUCTION_HIJACKING = "jailbreak_instruction_hijacking"  # 越狱-指令劫持攻击
    JAILBREAK_CONTEXT_LEARNING = "jailbreak_context_learning"    # 越狱-上下文学习攻击
    JAILBREAK_PROMPT_INJECTION = "jailbreak_prompt_injection"    # 越狱-提示注入攻击
    JAILBREAK_CHAIN_PROMPT = "jailbreak_chain_prompt"            # 越狱-链式提示攻击
    OTHER = "other"                                    # 其他类型

# 规则严重性等级枚举
class RuleSeverity(str, Enum):
    LOW = "low"          # 低风险
    MEDIUM = "medium"    # 中风险
    HIGH = "high"        # 高风险
    CRITICAL = "critical"  # 严重

# Base Pydantic model for RegexRule, containing common attributes
class RegexRuleBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="规则名称")
    pattern: str = Field(..., min_length=1, description="正则表达式模式")
    description: Optional[str] = Field(None, max_length=500, description="规则描述")
    category: IntentCategory = Field(..., description="恶意意图分类")
    severity: RuleSeverity = Field(RuleSeverity.MEDIUM, description="严重性等级")
    is_active: bool = Field(True, description="是否激活")
    priority: int = Field(0, ge=0, le=999, description="优先级，数字越大优先级越高")
    flags: int = Field(re.IGNORECASE, description="正则表达式标志")
    timeout_ms: int = Field(1000, ge=100, le=10000, description="匹配超时时间（毫秒）")
    role_id: Optional[int] = Field(None, gt=0, description="关联角色ID，为空表示全局规则")

# Pydantic model for creating a RegexRule instance
class RegexRuleCreate(RegexRuleBase):
    created_by: Optional[str] = Field(None, max_length=100, description="创建者")
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('规则名称不能为空')
        return v.strip()
    
    @validator('pattern')
    def validate_pattern(cls, v):
        if not v.strip():
            raise ValueError('正则表达式模式不能为空')
        
        # 验证正则表达式语法
        try:
            re.compile(v)
        except re.error as e:
            raise ValueError(f'无效的正则表达式: {str(e)}')
        
        return v.strip()

# Pydantic model for updating a RegexRule instance
class RegexRuleUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="规则名称")
    pattern: Optional[str] = Field(None, min_length=1, description="正则表达式模式")
    description: Optional[str] = Field(None, max_length=500, description="规则描述")
    category: Optional[IntentCategory] = Field(None, description="恶意意图分类")
    severity: Optional[RuleSeverity] = Field(None, description="严重性等级")
    is_active: Optional[bool] = Field(None, description="是否激活")
    priority: Optional[int] = Field(None, ge=0, le=999, description="优先级")
    flags: Optional[int] = Field(None, description="正则表达式标志")
    timeout_ms: Optional[int] = Field(None, ge=100, le=10000, description="匹配超时时间（毫秒）")
    role_id: Optional[int] = Field(None, gt=0, description="关联角色ID")
    updated_by: Optional[str] = Field(None, max_length=100, description="更新者")
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('规则名称不能为空')
        return v.strip() if v else v
    
    @validator('pattern')
    def validate_pattern(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('正则表达式模式不能为空')
            
            # 验证正则表达式语法
            try:
                re.compile(v)
            except re.error as e:
                raise ValueError(f'无效的正则表达式: {str(e)}')
            
            return v.strip()
        return v

# Base Pydantic model for RegexRule data stored in the database
class RegexRuleInDBBase(RegexRuleBase):
    id: int
    version: int
    parent_rule_id: Optional[int]
    match_count: int
    false_positive_count: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    updated_by: Optional[str]

    class Config:
        from_attributes = True

# Pydantic model for representing a RegexRule when reading from the API
class RegexRuleSchema(RegexRuleInDBBase):
    pass

# Properties to return to client with related data
class RegexRuleRead(RegexRuleInDBBase):
    role_name: Optional[str] = Field(None, description="关联角色名称")
    accuracy_rate: Optional[float] = Field(None, description="准确率")
    has_child_versions: bool = Field(False, description="是否有子版本")
    
# Properties stored in DB
class RegexRuleInDB(RegexRuleInDBBase):
    pass

# 正则规则列表响应Schema
class RegexRuleListResponse(BaseModel):
    items: List[RegexRuleRead]
    total: int
    page: int
    size: int
    pages: int

# 正则规则匹配结果Schema
class RegexRuleMatchResult(BaseModel):
    matched: bool = Field(..., description="是否匹配到规则")
    rule_id: Optional[int] = Field(None, description="匹配到的规则ID")
    rule_name: Optional[str] = Field(None, description="匹配到的规则名称")
    category: Optional[IntentCategory] = Field(None, description="恶意意图分类")
    severity: Optional[RuleSeverity] = Field(None, description="严重性等级")
    message: Optional[str] = Field(None, description="匹配消息")
    confidence: Optional[float] = Field(None, description="匹配置信度")

# 批量匹配请求Schema
class RegexRuleBatchCheckRequest(BaseModel):
    texts: List[str] = Field(..., min_items=1, max_items=100, description="待检查的文本列表")
    category_filter: Optional[List[IntentCategory]] = Field(None, description="分类过滤器")
    severity_filter: Optional[List[RuleSeverity]] = Field(None, description="严重性过滤器")
    role_id: Optional[int] = Field(None, description="角色ID过滤器")
    only_active: bool = Field(True, description="只检查激活的规则")

# 批量匹配响应Schema
class RegexRuleBatchCheckResponse(BaseModel):
    results: List[List[RegexRuleMatchResult]] = Field(..., description="每个文本的匹配结果列表")
    total_checked: int = Field(..., description="总检查文本数")
    total_matches: int = Field(..., description="总匹配数")

# 规则测试请求Schema
class RegexRuleTestRequest(BaseModel):
    pattern: str = Field(..., description="正则表达式模式")
    flags: int = Field(re.IGNORECASE, description="正则表达式标志")
    test_texts: List[str] = Field(..., min_items=1, max_items=50, description="测试文本列表")
    timeout_ms: int = Field(1000, ge=100, le=10000, description="超时时间（毫秒）")

# 规则测试响应Schema
class RegexRuleTestResponse(BaseModel):
    pattern_valid: bool = Field(..., description="正则表达式是否有效")
    test_results: List[dict] = Field(..., description="测试结果列表")
    error_message: Optional[str] = Field(None, description="错误信息")

# 规则统计Schema
class RegexRuleStats(BaseModel):
    total_rules: int = Field(..., description="总规则数")
    active_rules: int = Field(..., description="激活规则数")
    rules_by_category: dict = Field(..., description="按分类统计")
    rules_by_severity: dict = Field(..., description="按严重性统计")
    avg_accuracy_rate: float = Field(..., description="平均准确率")
    top_matched_rules: List[dict] = Field(..., description="匹配次数最多的规则") 