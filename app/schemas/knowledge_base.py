from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class ProcessingStatus(str, Enum):
    """文档处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    ARCHIVED = "archived"  # 添加归档状态，与数据库模型保持一致


class DocumentType(str, Enum):
    """文档类型枚举"""
    PDF = "PDF"
    WORD = "WORD"
    EXCEL = "EXCEL"
    PPT = "PPT"
    TXT = "TXT"
    MARKDOWN = "MARKDOWN"
    HTML = "HTML"
    OTHER = "OTHER"


# 知识库文档基础模型
class KnowledgeDocumentBase(BaseModel):
    """知识库文档基础模型"""
    title: str = Field(..., description="文档标题")
    description: Optional[str] = Field(None, description="文档描述")
    category_id: Optional[int] = Field(None, description="分类ID")
    grade_id: Optional[int] = Field(None, description="分级ID")
    doc_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="文档元数据")


class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    """创建知识库文档请求模型"""
    pass


class KnowledgeDocumentUpdate(BaseModel):
    """更新知识库文档请求模型"""
    title: Optional[str] = Field(None, description="文档标题")
    description: Optional[str] = Field(None, description="文档描述")
    category_id: Optional[int] = Field(None, description="分类ID")
    grade_id: Optional[int] = Field(None, description="分级ID")
    doc_metadata: Optional[Dict[str, Any]] = Field(None, description="文档元数据")


class KnowledgeDocumentResponse(KnowledgeDocumentBase):
    """知识库文档响应模型"""
    id: int
    file_name: str
    file_path: str
    file_size: int
    file_hash: str
    document_type: DocumentType
    status: ProcessingStatus
    error_message: Optional[str] = None
    chunk_count: int = 0
    version: int = 1
    created_by: int
    created_at: datetime
    updated_at: datetime
    
    # 关联信息
    category_name: Optional[str] = None
    grade_name: Optional[str] = None
    creator_name: Optional[str] = None

    class Config:
        from_attributes = True


class KnowledgeDocumentListResponse(BaseModel):
    """知识库文档列表响应模型"""
    total: int
    items: List[KnowledgeDocumentResponse]


# 文档上传模型
class DocumentUploadResponse(BaseModel):
    """文档上传响应模型"""
    document_id: int
    message: str
    file_name: str
    file_size: int
    status: ProcessingStatus


# 文档搜索模型
class DocumentSearchRequest(BaseModel):
    """文档搜索请求模型"""
    query: str = Field(..., description="搜索查询文本")
    category_ids: Optional[List[int]] = Field(None, description="限定分类ID列表")
    grade_ids: Optional[List[int]] = Field(None, description="限定分级ID列表")
    top_k: int = Field(10, ge=1, le=100, description="返回结果数量")
    threshold: float = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")


class AdvancedSearchRequest(BaseModel):
    """高级搜索请求模型"""
    query: str = Field(..., description="搜索查询文本")
    category_ids: Optional[List[int]] = Field(None, description="限定分类ID列表")
    grade_ids: Optional[List[int]] = Field(None, description="限定分级ID列表")
    document_types: Optional[List[DocumentType]] = Field(None, description="限定文档类型列表")
    date_from: Optional[datetime] = Field(None, description="创建时间起始")
    date_to: Optional[datetime] = Field(None, description="创建时间结束")
    uploaded_by: Optional[List[int]] = Field(None, description="上传者ID列表")
    search_mode: str = Field("semantic", description="搜索模式：semantic（语义）, keyword（关键词）, hybrid（混合）")
    include_content: bool = Field(True, description="是否包含文档内容")
    include_metadata: bool = Field(True, description="是否包含元数据")
    top_k: int = Field(10, ge=1, le=100, description="返回结果数量")
    threshold: float = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")
    highlight_keywords: bool = Field(True, description="是否高亮关键词")
    group_by_document: bool = Field(False, description="是否按文档分组结果")


class SearchResultChunk(BaseModel):
    """搜索结果片段"""
    chunk_id: str  # 修改为str类型，兼容向量数据库的字符串ID格式（如'doc_2_chunk_0'）
    document_id: int
    document_title: str
    content: str
    similarity_score: float
    chunk_metadata: Dict[str, Any]
    highlight: Optional[str] = None


class SearchResultDocument(BaseModel):
    """搜索结果文档"""
    document_id: int
    document_title: str
    file_name: str
    document_type: DocumentType
    category_name: Optional[str] = None
    grade_name: Optional[str] = None
    total_chunks: int
    matched_chunks: List[SearchResultChunk]
    max_similarity_score: float
    created_at: datetime
    uploaded_by_name: Optional[str] = None


class DocumentSearchResponse(BaseModel):
    """文档搜索响应模型"""
    query: str
    total_results: int
    chunks: List[SearchResultChunk]
    search_time_ms: float


class AdvancedSearchResponse(BaseModel):
    """高级搜索响应模型"""
    query: str
    search_mode: str
    total_documents: int
    total_chunks: int
    documents: List[SearchResultDocument]
    search_time_ms: float
    facets: Optional[Dict[str, Any]] = Field(None, description="搜索结果分面统计")


# 文档处理状态模型
class ProcessingStatusUpdate(BaseModel):
    """处理状态更新模型"""
    status: ProcessingStatus
    message: Optional[str] = None
    progress: Optional[float] = Field(None, ge=0.0, le=100.0)


# 文档版本模型
class KnowledgeVersionResponse(BaseModel):
    """知识库版本响应模型"""
    id: int
    document_id: int
    version_number: int
    change_summary: Optional[str] = None
    created_by: int
    created_at: datetime
    creator_name: Optional[str] = None

    class Config:
        from_attributes = True


class KnowledgeVersionListResponse(BaseModel):
    """知识库版本列表响应模型"""
    total: int
    items: List[KnowledgeVersionResponse]


# 文档统计模型
class DocumentStatistics(BaseModel):
    """文档统计信息"""
    total_documents: int
    total_chunks: int
    total_size_bytes: int
    by_status: Dict[ProcessingStatus, int]
    by_type: Dict[DocumentType, int]
    by_category: Dict[str, int]
    by_grade: Dict[str, int] 