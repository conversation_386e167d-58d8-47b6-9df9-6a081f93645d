# app/schemas/role.py

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

# Shared properties
class RoleBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, max_length=255, description="角色描述")

# Properties to receive on role creation
class RoleCreate(RoleBase):
    is_system: Optional[bool] = Field(False, description="是否为系统内置角色")

# Properties to receive on role update
class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, max_length=255, description="角色描述")
    is_active: Optional[bool] = Field(None, description="角色是否激活")
    # 注意：is_system字段不允许更新，只能在创建时设置

# Properties shared by models stored in DB
class RoleInDBBase(RoleBase):
    id: int
    is_active: bool
    is_system: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # For Pydantic V2

# Properties to return to client
class RoleRead(RoleInDBBase):
    user_count: Optional[int] = Field(None, description="该角色下的用户数量")
    can_be_deleted: Optional[bool] = Field(None, description="是否可以删除")

# Properties stored in DB (might be same as RoleRead or have more fields)
class RoleInDB(RoleInDBBase):
    pass

# 角色列表响应Schema
class RoleListResponse(BaseModel):
    items: list[RoleRead]
    total: int
    page: int
    size: int
    pages: int
