from datetime import datetime
from typing import Optional, Dict, Any
import json

from pydantic import BaseModel, HttpUrl, Field, validator

# Base Pydantic model for LLMModel, containing common attributes
class LLMModelBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="模型名称")
    api_url: str = Field(..., min_length=1, max_length=512, description="API接口地址")
    config_params: Optional[Dict[str, Any]] = Field(None, description="模型特定配置参数")
    is_active: bool = True
    
    @validator('config_params', pre=True)
    def parse_config_params(cls, v):
        """解析config_params字段，支持从JSON字符串转换"""
        if v is None:
            return v
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, TypeError):
                return {}
        return v

# Pydantic model for creating an LLMModel instance
# Includes encrypted_api_key which is required on creation but not usually returned
class LLMModelCreate(LLMModelBase):
    api_key: Optional[str] = Field(None, description="API密钥（明文，将被加密存储）")
    
    @validator('api_url')
    def validate_api_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL必须以http://或https://开头')
        return v

# Pydantic model for updating an LLMModel instance
# All fields are optional for partial updates
class LLMModelUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="模型名称")
    api_url: Optional[str] = Field(None, min_length=1, max_length=512, description="API接口地址")
    api_key: Optional[str] = Field(None, description="API密钥（明文，将被加密存储）")
    config_params: Optional[Dict[str, Any]] = Field(None, description="模型特定配置参数")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    @validator('api_url')
    def validate_api_url(cls, v):
        if v is not None and not v.startswith(('http://', 'https://')):
            raise ValueError('API URL必须以http://或https://开头')
        return v

# Base Pydantic model for LLMModel data stored in the database
# Includes fields that are auto-generated or managed by the database
class LLMModelInDBBase(LLMModelBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # Formerly orm_mode = True

# Pydantic model for representing an LLMModel when reading from the API
# This is what will be returned to the client (excluding sensitive fields like encrypted_api_key by default)
class LLMModelSchema(LLMModelInDBBase):
    pass

# If you need a schema that includes the API key (e.g., for admin purposes, though generally discouraged)
# you could create another schema or modify LLMModelSchema carefully.
# For now, encrypted_api_key is not part of LLMModelSchema.

# Properties to return to client
class LLMModelRead(LLMModelInDBBase):
    session_count: Optional[int] = Field(None, description="使用该模型的会话数量")
    can_be_deleted: Optional[bool] = Field(None, description="是否可以删除")
    # 注意：不返回加密的API密钥
    
# Properties stored in DB
class LLMModelInDB(LLMModelInDBBase):
    api_key_encrypted: Optional[str] = Field(None, description="加密后的API密钥")

# LLM模型列表响应Schema
class LLMModelListResponse(BaseModel):
    items: list[LLMModelRead]
    total: int
    page: int
    size: int
    pages: int

# LLM模型配置Schema（用于特定配置参数的验证）
class LLMModelConfig(BaseModel):
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="温度参数")
    max_tokens: Optional[int] = Field(None, ge=1, le=8192, description="最大生成token数")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="Top-p采样参数")
    frequency_penalty: Optional[float] = Field(None, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(None, ge=-2.0, le=2.0, description="存在惩罚")
    
    class Config:
        extra = "allow"  # 允许额外的配置参数
