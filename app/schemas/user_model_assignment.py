# app/schemas/user_model_assignment.py

"""
用户模型分配相关的Pydantic schema
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

# 基础模型分配信息
class UserModelAssignmentBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    model_id: int = Field(..., description="模型ID")
    is_default: bool = Field(False, description="是否为默认模型")

# 创建模型分配请求
class UserModelAssignmentCreate(UserModelAssignmentBase):
    pass

# 更新模型分配请求
class UserModelAssignmentUpdate(BaseModel):
    is_default: Optional[bool] = Field(None, description="是否为默认模型")

# 模型分配响应
class UserModelAssignment(UserModelAssignmentBase):
    id: int
    assigned_by: int
    created_at: datetime
    updated_at: datetime
    
    # 关联信息
    user_name: Optional[str] = Field(None, description="用户名")
    model_name: Optional[str] = Field(None, description="模型名称")
    assigner_name: Optional[str] = Field(None, description="分配者名称")
    
    class Config:
        from_attributes = True

# 模型分配详细信息
class UserModelAssignmentDetail(UserModelAssignment):
    user: Optional[dict] = Field(None, description="用户详细信息")
    model: Optional[dict] = Field(None, description="模型详细信息")
    assigner: Optional[dict] = Field(None, description="分配者详细信息")

# 批量分配模型请求
class BatchModelAssignmentRequest(BaseModel):
    user_ids: List[int] = Field(..., description="用户ID列表")
    model_ids: List[int] = Field(..., description="模型ID列表")
    replace_existing: bool = Field(False, description="是否替换现有分配")

# 单用户分配多个模型请求
class UserMultiModelAssignmentRequest(BaseModel):
    model_ids: List[int] = Field(..., description="模型ID列表")
    default_model_id: Optional[int] = Field(None, description="默认模型ID（必须在model_ids中）")
    replace_existing: bool = Field(False, description="是否替换现有分配")

# 设置默认模型请求
class SetDefaultModelRequest(BaseModel):
    model_id: int = Field(..., description="要设置为默认的模型ID")

# 模型分配操作响应
class ModelAssignmentResponse(BaseModel):
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
    affected_assignments: int = Field(0, description="受影响的分配数量")
    created_assignments: Optional[List[int]] = Field(None, description="新创建的分配ID列表")
    updated_assignments: Optional[List[int]] = Field(None, description="更新的分配ID列表")
    failed_assignments: Optional[List[dict]] = Field(None, description="失败的分配信息")

# 用户可用模型列表
class UserAvailableModel(BaseModel):
    id: int
    name: str
    api_url: str
    is_active: bool
    is_assigned: bool = Field(False, description="是否已分配给用户")
    is_default: bool = Field(False, description="是否为用户默认模型")
    assigned_at: Optional[datetime] = Field(None, description="分配时间")
    assigned_by_name: Optional[str] = Field(None, description="分配者名称")
    
    class Config:
        from_attributes = True

# 用户模型分配统计
class UserModelStats(BaseModel):
    user_id: int
    username: str
    email: Optional[str] = Field(None, description="用户邮箱")
    role_name: Optional[str] = Field(None, description="角色名称")
    level_name: Optional[str] = Field(None, description="等级名称")
    is_active: bool = Field(True, description="是否活跃")
    assigned_models_count: int = Field(0, description="已分配模型总数")
    default_model: Optional[str] = Field(None, description="默认模型名称")
    has_assignments: bool = Field(False, description="是否有模型分配")

# 模型分配统计
class ModelAssignmentStats(BaseModel):
    model_id: int
    model_name: str
    total_assigned_users: int = Field(0, description="已分配用户总数")
    active_users: int = Field(0, description="活跃用户数")
    default_for_users: int = Field(0, description="作为默认模型的用户数")
    last_assignment_date: Optional[datetime] = Field(None, description="最后分配时间")

# 分配历史记录
class AssignmentHistory(BaseModel):
    id: int
    user_id: int
    model_id: int
    user_name: str
    model_name: str
    operation_type: str = Field(..., description="操作类型：assign, revoke, set_default")
    assigned_by: int
    assigner_name: str
    created_at: datetime
    is_current: bool = Field(..., description="是否为当前有效分配")
    
    class Config:
        from_attributes = True 