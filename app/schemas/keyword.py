from datetime import datetime
from typing import Optional, List
from enum import Enum

from pydantic import BaseModel, Field, validator

# 匹配类型枚举
class MatchType(str, Enum):
    EXACT = "exact"        # 精确匹配
    PARTIAL = "partial"    # 部分匹配
    REGEX = "regex"        # 正则表达式匹配

# Base Pydantic model for Keyword, containing common attributes
class KeywordBase(BaseModel):
    word: str = Field(..., min_length=1, max_length=200, description="关键词内容")
    match_type: MatchType = MatchType.PARTIAL
    is_active: bool = True
    priority: int = Field(0, ge=0, le=999, description="优先级，数字越大优先级越高")
    description: Optional[str] = Field(None, max_length=300, description="关键词描述")
    keyword_group_id: int = Field(..., gt=0, description="关键词分组ID")

# Pydantic model for creating a Keyword instance
class KeywordCreate(KeywordBase):
    @validator('word')
    def validate_word(cls, v):
        if not v.strip():
            raise ValueError('关键词内容不能为空')
        return v.strip()

# Pydantic model for updating a Keyword instance
# All fields are optional for partial updates
class KeywordUpdate(BaseModel):
    word: Optional[str] = Field(None, min_length=1, max_length=200, description="关键词内容")
    match_type: Optional[MatchType] = Field(None, description="匹配类型")
    is_active: Optional[bool] = Field(None, description="是否激活")
    priority: Optional[int] = Field(None, ge=0, le=999, description="优先级")
    description: Optional[str] = Field(None, max_length=300, description="关键词描述")
    keyword_group_id: Optional[int] = Field(None, gt=0, description="关键词分组ID")
    
    @validator('word')
    def validate_word(cls, v):
        if v is not None and not v.strip():
            raise ValueError('关键词内容不能为空')
        return v.strip() if v else v

# Base Pydantic model for Keyword data stored in the database
# Includes fields that are auto-generated or managed by the database
class KeywordInDBBase(KeywordBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # Formerly orm_mode = True

# Pydantic model for representing a Keyword when reading from the API
class KeywordSchema(KeywordInDBBase):
    pass

# Properties to return to client with related data
class KeywordRead(KeywordInDBBase):
    keyword_group_name: Optional[str] = Field(None, description="所属分组名称")
    
# Properties stored in DB
class KeywordInDB(KeywordInDBBase):
    pass

# 关键词列表响应Schema
class KeywordListResponse(BaseModel):
    items: List[KeywordRead]
    total: int
    page: int
    size: int
    pages: int

# 关键词匹配结果Schema
class KeywordMatchResult(BaseModel):
    matched: bool = Field(..., description="是否匹配到关键词")
    keyword_id: Optional[int] = Field(None, description="匹配到的关键词ID")
    keyword_word: Optional[str] = Field(None, description="匹配到的关键词内容")
    keyword_group_name: Optional[str] = Field(None, description="匹配到的关键词分组名称")
    match_type: Optional[MatchType] = Field(None, description="匹配类型")
    message: Optional[str] = Field(None, description="匹配消息") 