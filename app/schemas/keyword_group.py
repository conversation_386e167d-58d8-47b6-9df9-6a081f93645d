from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, validator

# Base Pydantic model for KeywordGroup, containing common attributes
class KeywordGroupBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="分组名称")
    description: Optional[str] = Field(None, max_length=500, description="分组描述")
    is_active: bool = True

# Pydantic model for creating a KeywordGroup instance
class KeywordGroupCreate(KeywordGroupBase):
    pass

# Pydantic model for updating a KeywordGroup instance
# All fields are optional for partial updates
class KeywordGroupUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="分组名称")
    description: Optional[str] = Field(None, max_length=500, description="分组描述")
    is_active: Optional[bool] = Field(None, description="是否激活")

# Base Pydantic model for KeywordGroup data stored in the database
# Includes fields that are auto-generated or managed by the database
class KeywordGroupInDBBase(KeywordGroupBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # Formerly orm_mode = True

# Pydantic model for representing a KeywordGroup when reading from the API
class KeywordGroupSchema(KeywordGroupInDBBase):
    keyword_count: Optional[int] = Field(None, description="该分组下的关键词数量")
    can_be_deleted: Optional[bool] = Field(None, description="是否可以删除")

# Properties to return to client with related data
class KeywordGroupRead(KeywordGroupInDBBase):
    keyword_count: Optional[int] = Field(None, description="该分组下的关键词数量")
    can_be_deleted: Optional[bool] = Field(None, description="是否可以删除")
    
# Properties stored in DB
class KeywordGroupInDB(KeywordGroupInDBBase):
    pass

# 关键词分组列表响应Schema
class KeywordGroupListResponse(BaseModel):
    items: List[KeywordGroupRead]
    total: int
    page: int
    size: int
    pages: int 