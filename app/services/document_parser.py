"""
文档解析器服务
支持多种文档格式的解析和文本提取
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import mimetypes
import hashlib
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class BaseParser(ABC):
    """文档解析器基类"""
    
    @abstractmethod
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析该文件"""
        pass
    
    @abstractmethod
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文档，返回结构化内容"""
        pass


class TextParser(BaseParser):
    """纯文本文件解析器"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return mime_type in ['text/plain'] or file_path.lower().endswith('.txt')
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'text',
                    'encoding': 'utf-8',
                    'char_count': len(content),
                    'line_count': content.count('\n') + 1
                },
                'sections': [{'content': content, 'page': 1}]
            }
        except Exception as e:
            logger.error(f"Failed to parse text file {file_path}: {str(e)}")
            raise


class PDFParser(BaseParser):
    """PDF文件解析器（简化实现）"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return mime_type == 'application/pdf' or file_path.lower().endswith('.pdf')
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析PDF文件（简化实现）"""
        # 实际应用中应使用 pypdf 或 pdfplumber
        # 这里仅作为示例，返回模拟数据
        try:
            # 读取文件以获取基本信息
            with open(file_path, 'rb') as f:
                file_content = f.read()
                file_size = len(file_content)
            
            # 模拟解析结果
            content = f"[PDF Content from {os.path.basename(file_path)}]"
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'pdf',
                    'file_size': file_size,
                    'page_count': 1,  # 模拟值
                    'has_images': False,
                    'has_tables': False
                },
                'sections': [{'content': content, 'page': 1}]
            }
        except Exception as e:
            logger.error(f"Failed to parse PDF file {file_path}: {str(e)}")
            raise


class WordParser(BaseParser):
    """Word文档解析器（简化实现）"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return (mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                             'application/msword'] or 
                file_path.lower().endswith(('.docx', '.doc')))
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析Word文档（简化实现）"""
        # 实际应用中应使用 python-docx
        try:
            with open(file_path, 'rb') as f:
                file_size = len(f.read())
            
            # 模拟解析结果
            content = f"[Word Content from {os.path.basename(file_path)}]"
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'word',
                    'file_size': file_size,
                    'paragraph_count': 1,  # 模拟值
                    'has_images': False,
                    'has_tables': False
                },
                'sections': [{'content': content, 'page': 1}]
            }
        except Exception as e:
            logger.error(f"Failed to parse Word file {file_path}: {str(e)}")
            raise


class ExcelParser(BaseParser):
    """Excel文件解析器（简化实现）"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return (mime_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                             'application/vnd.ms-excel'] or 
                file_path.lower().endswith(('.xlsx', '.xls')))
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析Excel文件（简化实现）"""
        # 实际应用中应使用 openpyxl 或 pandas
        try:
            with open(file_path, 'rb') as f:
                file_size = len(f.read())
            
            # 模拟解析结果
            content = f"[Excel Content from {os.path.basename(file_path)}]"
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'excel',
                    'file_size': file_size,
                    'sheet_count': 1,  # 模拟值
                    'has_formulas': False,
                    'has_charts': False
                },
                'sections': [{'content': content, 'sheet': 'Sheet1'}]
            }
        except Exception as e:
            logger.error(f"Failed to parse Excel file {file_path}: {str(e)}")
            raise


class PPTParser(BaseParser):
    """PPT文件解析器（简化实现）"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return (mime_type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation',
                             'application/vnd.ms-powerpoint'] or 
                file_path.lower().endswith(('.pptx', '.ppt')))
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析PPT文件（简化实现）"""
        # 实际应用中应使用 python-pptx
        try:
            with open(file_path, 'rb') as f:
                file_size = len(f.read())
            
            # 模拟解析结果
            content = f"[PPT Content from {os.path.basename(file_path)}]"
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'ppt',
                    'file_size': file_size,
                    'slide_count': 1,  # 模拟值
                    'has_images': False,
                    'has_animations': False
                },
                'sections': [{'content': content, 'slide': 1}]
            }
        except Exception as e:
            logger.error(f"Failed to parse PPT file {file_path}: {str(e)}")
            raise


class DocumentParser:
    """文档解析器主类"""
    
    def __init__(self):
        """初始化解析器"""
        # 注册所有解析器
        self.parsers = [
            TextParser(),
            PDFParser(),
            WordParser(),
            ExcelParser(),
            PPTParser()
        ]
        
        # 文档处理配置
        self.chunk_size = 500  # 默认块大小
        self.chunk_overlap = 50  # 块重叠大小
        
        logger.info("Document parser initialized")
    
    def get_mime_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    def get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """
        解析文档
        
        Args:
            file_path: 文档路径
            
        Returns:
            解析结果，包含content、metadata、sections等
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # 获取文件信息
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        mime_type = self.get_mime_type(file_path)
        file_hash = self.get_file_hash(file_path)
        
        # 查找合适的解析器
        parser = None
        for p in self.parsers:
            if p.can_parse(file_path, mime_type):
                parser = p
                break
        
        if not parser:
            raise ValueError(f"No parser available for file type: {mime_type}")
        
        # 解析文档
        try:
            result = parser.parse(file_path)
            
            # 添加通用元数据
            result['file_info'] = {
                'file_name': file_name,
                'file_path': file_path,
                'file_size': file_size,
                'file_hash': file_hash,
                'mime_type': mime_type,
                'parsed_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Successfully parsed document: {file_name}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to parse document {file_name}: {str(e)}")
            raise
    
    def split_text_into_chunks(
        self, 
        text: str, 
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        将文本分割成块
        
        Args:
            text: 要分割的文本
            chunk_size: 块大小（字符数）
            chunk_overlap: 块之间的重叠字符数
            
        Returns:
            文本块列表
        """
        chunk_size = chunk_size or self.chunk_size
        chunk_overlap = chunk_overlap or self.chunk_overlap
        
        if chunk_size <= 0:
            raise ValueError("Chunk size must be positive")
        
        if chunk_overlap >= chunk_size:
            raise ValueError("Chunk overlap must be less than chunk size")
        
        chunks = []
        start = 0
        text_length = len(text)
        chunk_index = 0
        
        while start < text_length:
            # 计算块的结束位置
            end = min(start + chunk_size, text_length)
            
            # 提取块内容
            chunk_content = text[start:end]
            
            # 创建块对象
            chunk = {
                'content': chunk_content,
                'chunk_index': chunk_index,
                'start_char': start,
                'end_char': end,
                'char_count': len(chunk_content)
            }
            
            chunks.append(chunk)
            
            # 更新位置
            start = end - chunk_overlap if end < text_length else end
            chunk_index += 1
        
        logger.info(f"Split text into {len(chunks)} chunks")
        return chunks
    
    def process_document_for_vectorization(
        self, 
        file_path: str,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        处理文档以准备向量化
        
        Args:
            file_path: 文档路径
            chunk_size: 块大小
            chunk_overlap: 块重叠大小
            
        Returns:
            处理结果，包含文档信息和文本块
        """
        # 解析文档
        parsed_doc = self.parse_document(file_path)
        
        # 提取全文
        full_content = parsed_doc['content']
        
        # 分割成块
        chunks = self.split_text_into_chunks(
            full_content, 
            chunk_size, 
            chunk_overlap
        )
        
        # 为每个块添加元数据
        for chunk in chunks:
            chunk['metadata'] = {
                'file_name': parsed_doc['file_info']['file_name'],
                'file_type': parsed_doc['metadata']['file_type'],
                'file_hash': parsed_doc['file_info']['file_hash']
            }
        
        return {
            'document_info': parsed_doc['file_info'],
            'metadata': parsed_doc['metadata'],
            'chunks': chunks,
            'total_chunks': len(chunks),
            'total_chars': len(full_content)
        }


# 创建全局实例
document_parser = DocumentParser() 