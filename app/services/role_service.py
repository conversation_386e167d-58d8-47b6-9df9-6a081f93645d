# app/services/role_service.py

from sqlalchemy.orm import Session
from typing import List, Optional

from app.db.models.role import Role as RoleModel
from app.schemas.role import RoleCreate, RoleUpdate

def get_role(db: Session, role_id: int) -> Optional[RoleModel]:
    return db.query(RoleModel).filter(RoleModel.id == role_id).first()

def get_role_by_name(db: Session, name: str) -> Optional[RoleModel]:
    return db.query(RoleModel).filter(RoleModel.name == name).first()

def get_roles(db: Session, skip: int = 0, limit: int = 100) -> List[RoleModel]:
    return db.query(RoleModel).offset(skip).limit(limit).all()

def create_role(db: Session, role_in: RoleCreate) -> RoleModel:
    db_role = RoleModel(name=role_in.name, description=role_in.description)
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    return db_role

def update_role(db: Session, role_id: int, role_in: RoleUpdate) -> Optional[RoleModel]:
    db_role = get_role(db, role_id)
    if not db_role:
        return None
    
    # For Pydantic V2, use model_dump instead of dict
    update_data = role_in.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_role, key, value)
    
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    return db_role

def delete_role(db: Session, role_id: int) -> Optional[RoleModel]:
    db_role = get_role(db, role_id)
    if not db_role:
        return None
    db.delete(db_role)
    db.commit()
    # db_role is now detached, but still holds the data before deletion
    return db_role
