"""
向量数据库适配器
用于让生产级向量化器与简单向量数据库协同工作
"""

import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class VectorDBAdapter:
    """向量数据库适配器"""
    
    def __init__(self, simple_vector_db):
        """
        初始化适配器
        
        Args:
            simple_vector_db: 简单向量数据库实例
        """
        self.vector_db = simple_vector_db
        self._query_cache = {}  # 缓存查询文本
        
    async def search(
        self,
        query_embedding: List[float],
        top_k: int = 5,
        metadata_filter: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索接口（适配生产级向量化器的调用方式）
        
        Args:
            query_embedding: 查询向量（未使用，但保持接口兼容）
            top_k: 返回结果数
            metadata_filter: 元数据过滤器
            
        Returns:
            搜索结果列表
        """
        # 由于简单向量数据库需要原始查询文本，
        # 我们需要从缓存中获取或使用默认查询
        query = self._query_cache.get('last_query', 'default search')
        
        # 调用简单向量数据库的搜索方法
        results = self.vector_db.search(
            query=query,
            n_results=top_k,
            filter_dict=metadata_filter
        )
        
        # 转换结果格式以匹配生产级向量化器的期望
        adapted_results = []
        for result in results:
            adapted_results.append({
                'id': result['id'],
                'content': result['content'],
                'similarity': result.get('relevance_score', 0),
                'metadata': result.get('metadata', {})
            })
        
        return adapted_results
    
    def set_query_text(self, query: str):
        """设置查询文本（供搜索时使用）"""
        self._query_cache['last_query'] = query
    
    # 代理其他方法到简单向量数据库
    def __getattr__(self, name):
        """代理未定义的方法到底层向量数据库"""
        return getattr(self.vector_db, name)


# 创建适配器包装的向量数据库服务
def create_adapted_vector_db():
    """创建适配后的向量数据库服务"""
    from .vector_db_simple import vector_db_service
    return VectorDBAdapter(vector_db_service) 