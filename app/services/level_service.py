# app/services/level_service.py

from sqlalchemy.orm import Session
from typing import List, Optional

from app.db.models.level import Level as LevelModel
from app.schemas.level import LevelCreate, LevelUpdate

def get_level(db: Session, level_id: int) -> Optional[LevelModel]:
    return db.query(LevelModel).filter(LevelModel.id == level_id).first()

def get_level_by_name(db: Session, name: str) -> Optional[LevelModel]:
    return db.query(LevelModel).filter(LevelModel.name == name).first()

def get_levels(db: Session, skip: int = 0, limit: int = 100) -> List[LevelModel]:
    return db.query(LevelModel).offset(skip).limit(limit).all()

def create_level(db: Session, level_in: LevelCreate) -> LevelModel:
    db_level = LevelModel(name=level_in.name, description=level_in.description)
    db.add(db_level)
    db.commit()
    db.refresh(db_level)
    return db_level

def update_level(db: Session, level_id: int, level_in: LevelUpdate) -> Optional[LevelModel]:
    db_level = get_level(db, level_id)
    if not db_level:
        return None
    
    update_data = level_in.model_dump(exclude_unset=True) # Pydantic V2
    for key, value in update_data.items():
        setattr(db_level, key, value)
    
    db.add(db_level)
    db.commit()
    db.refresh(db_level)
    return db_level

def delete_level(db: Session, level_id: int) -> Optional[LevelModel]:
    db_level = get_level(db, level_id)
    if not db_level:
        return None
    db.delete(db_level)
    db.commit()
    return db_level
