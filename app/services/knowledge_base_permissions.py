"""
知识库权限管理服务
实现基于角色、级别和数据分级的细粒度权限控制
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_

from app.db.models import User, Role, RoleModulePermission, RoleDataGradeAccess
from app.db.models import KnowledgeDocument, DataSourceGrade
from app.db.models.user_document_permission import UserDocumentPermission, PermissionType
from app.core.permissions import is_admin, is_super_admin


class KnowledgeBasePermissionService:
    """知识库权限管理服务"""
    
    # 知识库模块权限定义
    MODULE_NAME = "KNOWLEDGE_BASE"
    
    # 权限操作定义
    PERMISSION_CREATE = "create"
    PERMISSION_READ = "read"
    PERMISSION_UPDATE = "update"
    PERMISSION_DELETE = "delete"
    PERMISSION_SEARCH = "search"
    PERMISSION_DOWNLOAD = "download"
    PERMISSION_SHARE = "share"
    
    # 模型配置权限定义
    PERMISSION_MODEL_CONFIG_CREATE = "model_config_create"
    PERMISSION_MODEL_CONFIG_READ = "model_config_read"
    PERMISSION_MODEL_CONFIG_UPDATE = "model_config_update"
    PERMISSION_MODEL_CONFIG_DELETE = "model_config_delete"
    PERMISSION_MODEL_CONFIG_GLOBAL = "model_config_global"
    PERMISSION_MODEL_CONFIG_CATEGORY = "model_config_category"
    PERMISSION_MODEL_CONFIG_GRADE = "model_config_grade"
    
    def __init__(self, db):
        """初始化权限服务"""
        self.db = db
    
    def check_module_access(self, user: User) -> bool:
        """检查用户是否有知识库模块访问权限"""
        # 管理员直接放行
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 检查模块权限
        permission = self.db.query(RoleModulePermission).filter(
            RoleModulePermission.role_id == user.role_id,
            RoleModulePermission.module_name == self.MODULE_NAME,
            RoleModulePermission.can_access == True
        ).first()
        
        return permission is not None
    
    async def check_module_access_async(self, user: User) -> bool:
        """检查用户是否有知识库模块访问权限（异步版本）"""
        # 管理员直接放行
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 检查模块权限
        from sqlalchemy import select
        permission_result = await self.db.execute(select(RoleModulePermission).filter(
            RoleModulePermission.role_id == user.role_id,
            RoleModulePermission.module_name == self.MODULE_NAME,
            RoleModulePermission.can_access == True
        ))
        permission = permission_result.scalar_one_or_none()
        
        return permission is not None
    
    def check_document_access(
        self,
        user: User,
        document: KnowledgeDocument,
        operation: str = PERMISSION_READ
    ) -> bool:
        """
        检查用户对特定文档的访问权限

        Args:
            user: 用户对象
            document: 文档对象
            operation: 操作类型

        Returns:
            是否有权限
        """
        # 超级管理员拥有所有权限
        if is_super_admin(user):
            return True

        # 管理员可以执行所有操作
        if is_admin(user):
            return True

        # 检查模块访问权限
        if not self.check_module_access(user):
            return False

        # 文档创建者对自己的文档有完全权限
        if document.uploaded_by == user.id:
            return True

        # 🆕 检查用户级别的文档权限
        if self.check_user_document_permission(user, document, operation):
            return True

        # 对于读取和搜索操作，检查数据分级权限
        if operation in [self.PERMISSION_READ, self.PERMISSION_SEARCH]:
            return self.check_grade_access(user, document.grade_id)

        # 其他操作需要管理员权限
        return False
    
    def check_grade_access(self, user: User, grade_id: Optional[int]) -> bool:
        """
        检查用户是否有访问特定数据分级的权限
        
        Args:
            user: 用户对象
            grade_id: 数据分级ID
            
        Returns:
            是否有权限
        """
        # 如果没有分级，默认允许访问
        if grade_id is None:
            return True
        
        # 管理员可以访问所有分级
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 检查角色的数据分级访问权限
        access = self.db.query(RoleDataGradeAccess).filter(
            RoleDataGradeAccess.role_id == user.role_id,
            RoleDataGradeAccess.data_source_grade_id == grade_id,
            RoleDataGradeAccess.access_allowed == True
        ).first()
        
        return access is not None

    def check_user_document_permission(
        self,
        user: User,
        document: KnowledgeDocument,
        operation: str
    ) -> bool:
        """
        检查用户是否有特定文档的特定权限

        Args:
            user: 用户对象
            document: 文档对象
            operation: 操作类型

        Returns:
            是否有权限
        """
        # 将操作类型映射到权限类型
        permission_type_map = {
            self.PERMISSION_READ: PermissionType.READ,
            self.PERMISSION_SEARCH: PermissionType.SEARCH,
            self.PERMISSION_DOWNLOAD: PermissionType.DOWNLOAD,
            self.PERMISSION_SHARE: PermissionType.SHARE
        }

        permission_type = permission_type_map.get(operation)
        if not permission_type:
            return False

        return UserDocumentPermission.has_permission(
            self.db, user.id, document.id, permission_type
        )

    def get_user_accessible_documents(self, user: User, operation: str) -> List[int]:
        """
        获取用户通过特定权限可访问的文档ID列表

        Args:
            user: 用户对象
            operation: 操作类型

        Returns:
            可访问的文档ID列表
        """
        # 将操作类型映射到权限类型
        permission_type_map = {
            self.PERMISSION_READ: PermissionType.READ,
            self.PERMISSION_SEARCH: PermissionType.SEARCH,
            self.PERMISSION_DOWNLOAD: PermissionType.DOWNLOAD,
            self.PERMISSION_SHARE: PermissionType.SHARE
        }

        permission_type = permission_type_map.get(operation)
        if not permission_type:
            return []

        return UserDocumentPermission.get_accessible_documents(
            self.db, user.id, permission_type
        )
    
    def get_accessible_grades(self, user: User) -> List[int]:
        """
        获取用户可访问的数据分级ID列表
        
        Args:
            user: 用户对象
            
        Returns:
            可访问的数据分级ID列表
        """
        # 管理员可以访问所有分级
        if is_admin(user) or is_super_admin(user):
            grades = self.db.query(DataSourceGrade.id).all()
            return [g[0] for g in grades]
        
        # 查询用户角色可访问的数据分级
        accesses = self.db.query(RoleDataGradeAccess).filter(
            RoleDataGradeAccess.role_id == user.role_id,
            RoleDataGradeAccess.access_allowed == True
        ).all()
        
        return [access.data_source_grade_id for access in accesses]
    
    def filter_documents_by_permission(
        self, 
        user: User, 
        query,
        operation: str = PERMISSION_READ
    ):
        """
        根据用户权限过滤文档查询
        
        Args:
            user: 用户对象
            query: SQLAlchemy查询对象
            operation: 操作类型
            
        Returns:
            过滤后的查询对象
        """
        # 超级管理员可以看到所有文档
        if is_super_admin(user):
            return query
        
        # 管理员可以看到所有文档
        if is_admin(user):
            return query
        
        # 普通用户的过滤条件
        conditions = []

        # 自己创建的文档
        conditions.append(KnowledgeDocument.uploaded_by == user.id)

        # 🆕 添加用户级别权限的文档
        user_accessible_docs = self.get_user_accessible_documents(user, operation)
        if user_accessible_docs:
            conditions.append(KnowledgeDocument.id.in_(user_accessible_docs))

        # 如果是读取或搜索操作，添加数据分级过滤
        if operation in [self.PERMISSION_READ, self.PERMISSION_SEARCH]:
            accessible_grades = self.get_accessible_grades(user)
            if accessible_grades:
                # 可访问的分级或没有分级的文档
                conditions.append(
                    or_(
                        KnowledgeDocument.grade_id.in_(accessible_grades),
                        KnowledgeDocument.grade_id.is_(None)
                    )
                )
            else:
                # 只能访问没有分级的文档
                conditions.append(KnowledgeDocument.grade_id.is_(None))
        
        # 应用过滤条件
        if conditions:
            query = query.filter(or_(*conditions))
        else:
            # 没有任何权限，返回空结果
            query = query.filter(KnowledgeDocument.id == -1)
        
        return query
    
    def check_create_permission(self, user: User) -> bool:
        """检查用户是否有创建文档的权限"""
        # 管理员可以创建
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 检查模块权限和特定的创建权限
        # 这里可以扩展更细粒度的权限控制
        return self.check_module_access(user)
    
    def check_update_permission(self, user: User) -> bool:
        """检查用户是否有更新权限"""
        # 管理员可以更新
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 普通用户可以更新自己创建的内容
        return self.check_module_access(user)
    
    def check_delete_permission(self, user: User) -> bool:
        """检查用户是否有删除权限"""
        # 管理员可以删除
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 普通用户可以删除自己创建的内容
        return self.check_module_access(user)
    
    def check_bulk_operation_permission(self, user: User, operation: str) -> bool:
        """
        检查用户是否有执行批量操作的权限
        
        Args:
            user: 用户对象
            operation: 操作类型
            
        Returns:
            是否有权限
        """
        # 只有管理员可以执行批量操作
        return is_admin(user) or is_super_admin(user)
    
    def get_permission_summary(self, user: User) -> Dict[str, Any]:
        """
        获取用户的权限摘要
        
        Args:
            user: 用户对象
            
        Returns:
            权限摘要字典
        """
        is_admin_user = is_admin(user) or is_super_admin(user)
        
        return {
            "is_admin": is_admin_user,
            "has_module_access": self.check_module_access(user),
            "can_create": self.check_create_permission(user),
            "can_bulk_operate": self.check_bulk_operation_permission(user, "any"),
            "accessible_grades": self.get_accessible_grades(user) if not is_admin_user else "all",
            "permissions": {
                "create": self.check_create_permission(user),
                "read": self.check_module_access(user),
                "update": self.check_update_permission(user),
                "delete": self.check_delete_permission(user),
                "search": self.check_module_access(user),
                "download": self.check_module_access(user)
            },
            "model_config_permissions": self.get_model_config_permissions(user)
        }
    
    # 模型配置权限检查方法
    def check_model_config_permission(self, user: User, operation: str) -> bool:
        """
        检查用户的模型配置权限
        
        Args:
            user: 用户对象
            operation: 操作类型
            
        Returns:
            是否有权限
        """
        # 超级管理员拥有所有权限
        if is_super_admin(user):
            return True
        
        # 管理员拥有所有模型配置权限
        if is_admin(user):
            return True
        
        # 检查基本模块访问权限
        if not self.check_module_access(user):
            return False
        
        # 根据操作类型检查权限
        if operation == self.PERMISSION_MODEL_CONFIG_READ:
            # 有模块访问权限就可以查看
            return True
        elif operation == self.PERMISSION_MODEL_CONFIG_CREATE:
            # 可以创建用户自定义配置
            return self.check_create_permission(user)
        elif operation in [self.PERMISSION_MODEL_CONFIG_UPDATE, self.PERMISSION_MODEL_CONFIG_DELETE]:
            # 可以更新/删除自己创建的配置
            return self.check_update_permission(user)
        elif operation in [
            self.PERMISSION_MODEL_CONFIG_GLOBAL,
            self.PERMISSION_MODEL_CONFIG_CATEGORY,
            self.PERMISSION_MODEL_CONFIG_GRADE
        ]:
            # 只有管理员可以配置全局、分类、分级模型
            return False
        
        return False
    
    def check_model_config_access(self, user: User, config, operation: str) -> bool:
        """
        检查用户对特定模型配置的访问权限
        
        Args:
            user: 用户对象
            config: 模型配置对象
            operation: 操作类型
            
        Returns:
            是否有权限
        """
        # 超级管理员拥有所有权限
        if is_super_admin(user):
            return True
        
        # 管理员拥有所有权限
        if is_admin(user):
            return True
        
        # 检查基本权限
        if not self.check_model_config_permission(user, operation):
            return False
        
        # 配置创建者对自己的配置有完全权限
        if hasattr(config, 'created_by') and config.created_by == user.id:
            return True
        
        # 用户自定义配置，检查是否是配置的目标用户
        if hasattr(config, 'config_type') and config.config_type == "user":
            if hasattr(config, 'user_id') and config.user_id == user.id:
                return True
        
        # 全局配置对所有用户可见（读取权限）
        if operation == self.PERMISSION_MODEL_CONFIG_READ:
            if hasattr(config, 'config_type') and config.config_type == "global":
                return True
        
        return False
    
    def get_model_config_permissions(self, user: User) -> Dict[str, Any]:
        """
        获取用户的模型配置权限摘要
        
        Args:
            user: 用户对象
            
        Returns:
            模型配置权限摘要
        """
        is_admin_user = is_admin(user) or is_super_admin(user)
        
        return {
            "can_view": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_READ),
            "can_create": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_CREATE),
            "can_update": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_UPDATE),
            "can_delete": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_DELETE),
            "can_config_global": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_GLOBAL),
            "can_config_category": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_CATEGORY),
            "can_config_grade": self.check_model_config_permission(user, self.PERMISSION_MODEL_CONFIG_GRADE),
            "is_admin": is_admin_user
        }
    
    def filter_model_configs_by_permission(self, user: User, query, operation: str = None):
        """
        根据用户权限过滤模型配置查询
        
        Args:
            user: 用户对象
            query: SQLAlchemy查询对象
            operation: 操作类型
            
        Returns:
            过滤后的查询对象
        """
        from app.db.models import KnowledgeBaseModelConfig
        
        # 超级管理员可以看到所有配置
        if is_super_admin(user):
            return query
        
        # 管理员可以看到所有配置
        if is_admin(user):
            return query
        
        # 普通用户的过滤条件
        conditions = []
        
        # 自己创建的配置
        conditions.append(KnowledgeBaseModelConfig.created_by == user.id)
        
        # 自己的用户配置
        conditions.append(
            and_(
                KnowledgeBaseModelConfig.config_type == "user",
                KnowledgeBaseModelConfig.user_id == user.id
            )
        )
        
        # 全局配置对所有用户可见
        conditions.append(KnowledgeBaseModelConfig.config_type == "global")
        
        # 应用过滤条件
        if conditions:
            query = query.filter(or_(*conditions))
        else:
            # 没有任何权限，返回空结果
            query = query.filter(KnowledgeBaseModelConfig.id == -1)
        
        return query 