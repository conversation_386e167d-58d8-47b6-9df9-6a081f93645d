"""
RAG问答权限管理服务
"""

from sqlalchemy.ext.asyncio import AsyncSession
from app.db.models.user import User
from app.db.models.role_module_permission import RoleModulePermission
from app.core.permissions import is_admin, is_super_admin


class RAGPermissionService:
    """RAG问答权限管理服务"""
    
    # RAG问答模块权限定义
    MODULE_NAME = "RAG_CHAT"
    
    def __init__(self, db: AsyncSession):
        """初始化权限服务"""
        self.db = db
    
    async def check_rag_access(self, user: User) -> bool:
        """检查用户是否有RAG问答权限"""
        # 管理员直接放行
        if is_admin(user) or is_super_admin(user):
            return True
        
        # 检查RAG问答模块权限
        from sqlalchemy import select
        permission_result = await self.db.execute(select(RoleModulePermission).filter(
            RoleModulePermission.role_id == user.role_id,
            RoleModulePermission.module_name == self.MODULE_NAME,
            RoleModulePermission.can_access == True
        ))
        permission = permission_result.scalar_one_or_none()
        
        return permission is not None
    
    def get_permission_summary(self, user: User) -> dict:
        """
        获取用户的RAG权限摘要
        
        Args:
            user: 用户对象
            
        Returns:
            权限摘要字典
        """
        is_admin_user = is_admin(user) or is_super_admin(user)
        
        return {
            "is_admin": is_admin_user,
            "has_rag_access": self.check_rag_access(user),
            "permissions": {
                "query": self.check_rag_access(user),
                "statistics": self.check_rag_access(user),
            }
        }
