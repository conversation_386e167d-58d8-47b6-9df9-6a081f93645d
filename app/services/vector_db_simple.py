# app/services/vector_db_simple.py

"""
简化的向量数据库服务实现
暂时使用内存存储，后续可以替换为实际的向量数据库
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import hashlib
from collections import defaultdict
import numpy as np

logger = logging.getLogger(__name__)


class SimpleVectorDBService:
    """简化的向量数据库服务（内存实现）"""
    
    def __init__(self):
        """初始化服务"""
        # 内存存储
        self.documents = {}  # document_id -> chunks
        self.chunks = {}     # chunk_id -> chunk_data
        self.embeddings = {} # chunk_id -> embedding (暂时存储文本hash)
        
        # 持久化路径
        self.persist_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            "data", 
            "simple_vector_db.json"
        )
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
        
        # 加载已有数据
        self._load_data()
        
        logger.info("Simple Vector DB service initialized")
    
    def _load_data(self):
        """从文件加载数据"""
        if os.path.exists(self.persist_path):
            try:
                with open(self.persist_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.documents = data.get('documents', {})
                    self.chunks = data.get('chunks', {})
                    self.embeddings = data.get('embeddings', {})
                logger.info(f"Loaded data from {self.persist_path}")
            except Exception as e:
                logger.error(f"Failed to load data: {str(e)}")
    
    def _save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'documents': self.documents,
                'chunks': self.chunks,
                'embeddings': self.embeddings
            }
            with open(self.persist_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved data to {self.persist_path}")
        except Exception as e:
            logger.error(f"Failed to save data: {str(e)}")
    
    def _compute_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            vec1: 第一个向量
            vec2: 第二个向量
            
        Returns:
            余弦相似度（0-1之间）
        """
        # 转换为numpy数组
        v1 = np.array(vec1)
        v2 = np.array(vec2)
        
        # 计算点积
        dot_product = np.dot(v1, v2)
        
        # 计算范数
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)
        
        # 避免除零
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        # 计算余弦相似度
        cosine_sim = dot_product / (norm1 * norm2)
        
        # 将相似度从[-1, 1]映射到[0, 1]
        return (cosine_sim + 1) / 2
    
    def _compute_text_similarity(self, text1: str, text2: str) -> float:
        """
        改进的文本相似度计算，支持中文字符级匹配和语义理解
        """
        import re
        
        # 预处理文本
        def preprocess_text(text):
            # 统一转换为小写
            text = text.lower()
            # 移除多余空格
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        
        # 中文友好的分词方法
        def tokenize_text(text):
            text = preprocess_text(text)
            tokens = set()
            
            # 1. 按字符分割中文
            for char in text:
                if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
                    tokens.add(char)
            
            # 2. 按空格分割英文单词和数字
            words = text.split()
            for word in words:
                if word and len(word) > 0:
                    tokens.add(word)
                    # 对长词进行n-gram处理
                    if len(word) > 3:
                        for i in range(len(word) - 2):
                            tokens.add(word[i:i+3])
            
            # 3. 提取n-gram组合（中文2-gram）
            clean_text = re.sub(r'[^\u4e00-\u9fff\w]', '', text)
            if len(clean_text) > 1:
                for i in range(len(clean_text) - 1):
                    tokens.add(clean_text[i:i+2])
            
            return tokens
        
        query_tokens = tokenize_text(text1)
        content_tokens = tokenize_text(text2)
        
        if not query_tokens or not content_tokens:
            return 0.0
        
        # 计算交集和并集
        intersection = query_tokens.intersection(content_tokens)
        union = query_tokens.union(content_tokens)
        
        # Jaccard相似度
        jaccard_sim = len(intersection) / len(union) if union else 0.0
        
        # 计算多种匹配加权
        bonus = 0.0
        
        # 1. 完全子字符串匹配
        if text1.lower() in text2.lower():
            bonus += 0.4
        
        # 2. 关键词匹配加权
        query_words = set(preprocess_text(text1).split())
        content_words = set(preprocess_text(text2).split())
        word_overlap = len(query_words.intersection(content_words))
        if word_overlap > 0:
            bonus += min(0.3, word_overlap * 0.1)
        
        # 3. 中文字符匹配加权
        query_chars = set(char for char in text1 if '\u4e00' <= char <= '\u9fff')
        content_chars = set(char for char in text2 if '\u4e00' <= char <= '\u9fff')
        if query_chars and content_chars:
            char_overlap = len(query_chars.intersection(content_chars))
            if char_overlap > 0:
                bonus += min(0.2, char_overlap * 0.05)
        
        # 4. 长度归一化
        length_penalty = 0.0
        if len(text2) > len(text1) * 10:  # 内容过长时略微降权
            length_penalty = 0.1
        
        # 组合最终相似度
        final_similarity = min(1.0, jaccard_sim + bonus - length_penalty)
        
        return max(0.0, final_similarity)
    
    def add_document_chunks(
        self, 
        chunks: List[Dict[str, Any]], 
        document_id: int,
        embeddings: Optional[List[List[float]]] = None
    ) -> List[str]:
        """添加文档块"""
        try:
            chunk_ids = []
            doc_chunks = []
            
            for i, chunk in enumerate(chunks):
                # 生成块ID
                chunk_id = f"doc_{document_id}_chunk_{i}"
                chunk_ids.append(chunk_id)
                
                # 准备块数据
                chunk_data = {
                    'id': chunk_id,
                    'content': chunk.get('content', ''),
                    'metadata': chunk.get('metadata', {}),
                    'document_id': document_id,
                    'chunk_index': i,
                    'created_at': datetime.utcnow().isoformat()
                }
                
                # 存储块数据
                self.chunks[chunk_id] = chunk_data
                doc_chunks.append(chunk_id)
                
                # 存储"嵌入"（这里使用文本hash作为简单替代）
                if embeddings and i < len(embeddings):
                    self.embeddings[chunk_id] = embeddings[i]
                else:
                    # 使用文本hash作为简单的"嵌入"
                    self.embeddings[chunk_id] = hashlib.md5(
                        chunk_data['content'].encode()
                    ).hexdigest()
            
            # 存储文档索引
            self.documents[str(document_id)] = doc_chunks
            
            # 保存数据
            self._save_data()
            
            logger.info(f"Added {len(chunk_ids)} chunks for document {document_id}")
            return chunk_ids
            
        except Exception as e:
            logger.error(f"Failed to add document chunks: {str(e)}")
            raise

    async def add_chunk(
        self,
        content: str,
        metadata: Dict[str, Any],
        chunk_id: Optional[int] = None,
        document_id: Optional[int] = None,
        embedding: Optional[List[float]] = None
    ) -> str:
        """
        添加单个文档块（兼容生产级向量化器）
        
        Args:
            content: 块内容
            metadata: 元数据
            chunk_id: 块ID（可选）
            document_id: 文档ID（可选，如果metadata中没有）
            embedding: 嵌入向量（可选）
            
        Returns:
            生成的向量ID
        """
        try:
            # 获取文档ID
            doc_id = document_id or metadata.get('document_id')
            if not doc_id:
                raise ValueError("document_id is required")
            
            # 生成块ID
            if chunk_id is not None:
                vector_id = f"doc_{doc_id}_chunk_{chunk_id}"
            else:
                # 如果没有指定chunk_id，使用现有块数量作为索引
                doc_id_str = str(doc_id)
                existing_chunks = self.documents.get(doc_id_str, [])
                vector_id = f"doc_{doc_id}_chunk_{len(existing_chunks)}"
            
            # 准备块数据
            chunk_data = {
                'id': vector_id,
                'content': content,
                'metadata': metadata.copy(),
                'document_id': doc_id,
                'chunk_index': chunk_id if chunk_id is not None else len(self.documents.get(str(doc_id), [])),
                'created_at': datetime.utcnow().isoformat()
            }
            
            # 存储块数据
            self.chunks[vector_id] = chunk_data
            
            # 存储嵌入
            if embedding:
                self.embeddings[vector_id] = embedding
            else:
                # 使用文本hash作为简单的"嵌入"
                self.embeddings[vector_id] = hashlib.md5(
                    content.encode()
                ).hexdigest()
            
            # 更新文档索引
            doc_id_str = str(doc_id)
            if doc_id_str not in self.documents:
                self.documents[doc_id_str] = []
            self.documents[doc_id_str].append(vector_id)
            
            # 保存数据
            self._save_data()
            
            logger.info(f"Added chunk {vector_id} for document {doc_id}")
            return vector_id
            
        except Exception as e:
            logger.error(f"Failed to add chunk: {str(e)}")
            raise
    
    def search(
        self, 
        query: str, 
        n_results: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None,
        min_similarity: float = 0.01  # 降低最小相似度阈值
    ) -> List[Dict[str, Any]]:
        """搜索相似的文档块（基于改进的文本相似度）"""
        try:
            logger.info(f"SimpleVectorDB searching for: '{query}' with min_similarity={min_similarity}")
            results = []
            
            # 遍历所有块计算相似度
            for chunk_id, chunk_data in self.chunks.items():
                # 应用过滤条件
                if filter_dict:
                    skip = False
                    for key, value in filter_dict.items():
                        chunk_value = chunk_data.get('metadata', {}).get(key) or chunk_data.get(key)
                        if isinstance(value, list):
                            if chunk_value not in value:
                                skip = True
                                break
                        else:
                            if chunk_value != value:
                                skip = True
                                break
                    if skip:
                        continue
                
                # 使用改进的文本相似度计算
                similarity = self._compute_text_similarity(
                    query, 
                    chunk_data['content']
                )
                
                # 只返回超过最小相似度的结果
                if similarity >= min_similarity:
                    results.append({
                        'id': chunk_id,
                        'content': chunk_data['content'],
                        'metadata': chunk_data['metadata'],
                        'document_id': chunk_data['document_id'],
                        'chunk_index': chunk_data['chunk_index'],
                        'relevance_score': similarity,
                        'distance': 1 - similarity
                    })
            
            # 按相似度排序并返回前n个结果
            results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            logger.info(f"SimpleVectorDB found {len(results)} results, returning top {min(n_results, len(results))}")
            
            return results[:n_results]
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            raise
    
    def search_by_vector(
        self,
        query_embedding: List[float],
        n_results: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """基于向量相似度搜索文档块"""
        try:
            results = []
            
            # 遍历所有块计算向量相似度
            for chunk_id, chunk_data in self.chunks.items():
                # 应用过滤条件
                if filter_dict:
                    skip = False
                    for key, value in filter_dict.items():
                        if chunk_data.get(key) != value:
                            skip = True
                            break
                    if skip:
                        continue
                
                # 获取块的嵌入向量
                chunk_embedding = self.embeddings.get(chunk_id)
                
                # 如果嵌入是向量（列表），计算余弦相似度
                if isinstance(chunk_embedding, list):
                    similarity = self._compute_cosine_similarity(
                        query_embedding,
                        chunk_embedding
                    )
                    
                    if similarity > 0.3:  # 相似度阈值
                        results.append({
                            'id': chunk_id,
                            'content': chunk_data['content'],
                            'metadata': chunk_data['metadata'],
                            'document_id': chunk_data['document_id'],
                            'chunk_index': chunk_data['chunk_index'],
                            'relevance_score': similarity,
                            'distance': 1 - similarity
                        })
            
            # 按相似度排序并返回前n个结果
            results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return results[:n_results]
            
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            raise
    
    def delete_document_chunks(self, document_id: int) -> int:
        """删除指定文档的所有块"""
        try:
            doc_id_str = str(document_id)
            
            if doc_id_str not in self.documents:
                return 0
            
            # 获取要删除的块ID
            chunk_ids = self.documents[doc_id_str]
            
            # 删除块数据和嵌入
            for chunk_id in chunk_ids:
                self.chunks.pop(chunk_id, None)
                self.embeddings.pop(chunk_id, None)
            
            # 删除文档索引
            del self.documents[doc_id_str]
            
            # 保存数据
            self._save_data()
            
            logger.info(f"Deleted {len(chunk_ids)} chunks for document {document_id}")
            return len(chunk_ids)
            
        except Exception as e:
            logger.error(f"Failed to delete document chunks: {str(e)}")
            raise
    
    def update_chunk(
        self, 
        chunk_id: str, 
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """更新文档块"""
        try:
            if chunk_id not in self.chunks:
                raise ValueError(f"Chunk {chunk_id} not found")
            
            chunk_data = self.chunks[chunk_id]
            
            if content is not None:
                chunk_data['content'] = content
                # 更新"嵌入"
                self.embeddings[chunk_id] = hashlib.md5(
                    content.encode()
                ).hexdigest()
            
            if metadata is not None:
                chunk_data['metadata'].update(metadata)
            
            # 保存数据
            self._save_data()
            
            logger.info(f"Updated chunk {chunk_id}")
            
        except Exception as e:
            logger.error(f"Failed to update chunk: {str(e)}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            total_chunks = len(self.chunks)
            unique_documents = len(self.documents)
            
            stats = {
                "collection_name": "simple_knowledge_base",
                "total_chunks": total_chunks,
                "unique_documents": unique_documents,
                "persist_path": self.persist_path,
                "storage_type": "in-memory with persistence"
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            raise
    
    def reset_collection(self):
        """重置集合（删除所有数据）"""
        try:
            self.documents.clear()
            self.chunks.clear()
            self.embeddings.clear()
            
            # 删除持久化文件
            if os.path.exists(self.persist_path):
                os.remove(self.persist_path)
            
            logger.info("Collection reset successfully")
        except Exception as e:
            logger.error(f"Failed to reset collection: {str(e)}")
            raise


# 创建全局实例
vector_db_service = SimpleVectorDBService()
simple_vector_db = vector_db_service  # 别名，兼容旧代码 