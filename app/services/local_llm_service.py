"""
本地LLM服务 - 使用Transformers库的GPT-2模型
"""
import logging
from typing import List, Dict
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
import torch

logger = logging.getLogger(__name__)

class LocalLLMService:
    """本地大语言模型服务"""
    
    def __init__(self, model_name: str = "gpt2"):
        """初始化本地模型"""
        self.model_name = model_name
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"初始化本地模型 {model_name}，设备: {self.device}")
        
        try:
            # 使用pipeline加载模型（更简单）
            self.generator = pipeline(
                "text-generation",
                model=model_name,
                device=0 if self.device == "cuda" else -1,
                max_new_tokens=512,
                temperature=0.7,
                pad_token_id=50256  # GPT-2的EOS token
            )
            logger.info(f"✅ 本地模型 {model_name} 加载成功")
        except Exception as e:
            logger.error(f"加载模型失败: {str(e)}")
            raise
    
    def generate_response(self, prompt: str, max_tokens: int = 500) -> str:
        """生成回复"""
        try:
            # GPT-2需要特定的提示格式
            formatted_prompt = f"Question: {prompt}\n\nAnswer:"
            
            # 生成文本
            outputs = self.generator(
                formatted_prompt,
                max_new_tokens=max_tokens,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                top_p=0.9
            )
            
            # 提取生成的文本
            generated_text = outputs[0]['generated_text']
            
            # 去掉原始提示，只保留生成的部分
            answer = generated_text[len(formatted_prompt):].strip()
            
            return answer
        except Exception as e:
            logger.error(f"生成回复失败: {str(e)}")
            return f"抱歉，生成回复时出现错误: {str(e)}"
    
    def generate_from_context(self, query: str, context: str) -> str:
        """基于上下文生成回复（用于RAG）"""
        try:
            # 构建RAG提示
            prompt = f"""根据以下信息回答问题。

相关信息：
{context}

问题：{query}

回答："""
            
            return self.generate_response(prompt)
        except Exception as e:
            logger.error(f"基于上下文生成失败: {str(e)}")
            return "抱歉，无法生成回答。"

# 全局实例
local_llm_service = None

def get_local_llm_service():
    """获取本地LLM服务实例"""
    global local_llm_service
    if local_llm_service is None:
        local_llm_service = LocalLLMService()
    return local_llm_service 