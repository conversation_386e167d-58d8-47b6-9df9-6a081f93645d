"""
安全检测服务
使用统一安全策略管道架构进行安全检查
"""

import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app import models
from app.services.desensitization_service import desensitization_service
from app.services.pipeline.factory import SecurityPipelineFactory
from app.services.pipeline.context import PipelineContext
from app.services.pipeline.result import SecurityLevel

logger = logging.getLogger(__name__)


class SecurityDetectionService:
    """
    安全检测服务
    使用统一的安全策略管道进行全面的安全检查
    """
    
    def __init__(self):
        self._pipeline = None
        self._pipeline_initialized = False
        
    def _get_pipeline(self):
        """获取或创建安全管道实例"""
        if not self._pipeline_initialized:
            try:
                # 创建默认的安全管道
                self._pipeline = SecurityPipelineFactory.create_default_pipeline(
                    enable_keyword_filter=True,
                    enable_regex_pattern=True,
                    enable_data_desensitization=True
                )
                self._pipeline_initialized = True
                logger.info("安全管道初始化成功")
            except Exception as e:
                logger.error(f"安全管道初始化失败: {str(e)}")
                self._pipeline = None
                
        return self._pipeline
    
    def perform_security_check(self, content: str, db: Session, user_role_id: Optional[int] = None) -> Dict[str, Any]:
        """
        执行完整的安全检查
        
        Args:
            content: 要检查的文本内容
            db: 数据库会话
            user_role_id: 用户角色ID，用于角色相关的规则过滤
            
        Returns:
            安全检查结果字典
        """
        try:
            # 获取安全管道
            pipeline = self._get_pipeline()
            
            if not pipeline:
                logger.warning("安全管道不可用，使用备用检查")
                return self._get_fallback_result(content)
            
            # 创建管道执行上下文
            context = PipelineContext(
                content=content,
                user_role_id=user_role_id,  # 修正：使用正确的参数名
                enable_early_termination=True
            )
            
            # 执行管道检查
            pipeline_result = pipeline.execute(context, db)
            
            # 转换管道结果为兼容格式
            return self._convert_pipeline_result_to_legacy_format(pipeline_result, content)
            
        except Exception as e:
            logger.error(f"安全检查过程中发生错误: {str(e)}")
            return self._get_fallback_result(content)
    
    def perform_security_check_with_desensitization(self, content: str, db: Session, user_role_id: Optional[int] = None, apply_masking: bool = True) -> Dict[str, Any]:
        """
        执行完整的安全检查并可选择应用脱敏
        
        Args:
            content: 要检查的文本内容
            db: 数据库会话
            user_role_id: 用户角色ID
            apply_masking: 是否应用脱敏处理
            
        Returns:
            安全检查和脱敏结果字典
        """
        try:
            # 获取安全管道
            pipeline = self._get_pipeline()
            
            if not pipeline:
                logger.warning("安全管道不可用，使用备用检查")
                return self._get_fallback_result(content)
            
            # 创建管道执行上下文，启用脱敏
            context = PipelineContext(
                content=content,
                user_role_id=user_role_id,  # 修正：使用正确的参数名
                enable_early_termination=True,
                enable_desensitization=apply_masking
            )
            
            # 执行管道检查
            pipeline_result = pipeline.execute(context, db)
            
            # 转换管道结果为兼容格式（包含脱敏信息）
            result = self._convert_pipeline_result_to_legacy_format(pipeline_result, content)
            
            # 添加脱敏结果信息
            if apply_masking and pipeline_result.processed_content != pipeline_result.original_content:
                result["desensitization"] = {
                    "applied": True,
                    "original_text": pipeline_result.original_content,
                    "processed_text": pipeline_result.processed_content,
                    "detected_patterns": [],  # 可以从管道结果中提取
                    "total_matches": len([f for f in pipeline_result.all_findings if "脱敏" in str(f)]),
                    "rules_applied": []  # 可以从管道结果中提取
                }
                result["final_text"] = pipeline_result.processed_content
            else:
                result["desensitization"] = {
                    "applied": False,
                    "reason": "安全检查未通过或未启用脱敏" if result["is_blocked"] else "未检测到敏感数据"
                }
                result["final_text"] = content
            
            return result
            
        except Exception as e:
            logger.error(f"安全检查和脱敏过程中发生错误: {str(e)}")
            return self._get_fallback_result(content)
    
    def _convert_pipeline_result_to_legacy_format(self, pipeline_result, original_content: str) -> Dict[str, Any]:
        """
        将新管道结果转换为兼容旧API格式的结果
        """
        # 计算安全评分
        score = int(pipeline_result.security_score)
        
        # 确定是否通过（根据推荐动作判断）
        is_blocked = pipeline_result.is_blocked
        
        # 提取关键词相关信息
        keyword_findings = []
        regex_findings = []
        sensitive_data_findings = []
        
        for finding in pipeline_result.all_findings:
            finding_type = finding.get("type", "")
            if "keyword" in finding_type:
                keyword_findings.append(finding)
            elif "regex" in finding_type or "恶意意图" in finding_type:
                regex_findings.append(finding)
            elif "sensitive" in finding_type or "脱敏" in finding_type:
                sensitive_data_findings.append(finding)
        
        # 确定风险等级字符串
        risk_level_str = pipeline_result.overall_risk_level.value
        
        # 生成阻止原因
        block_reason = None
        if is_blocked:
            if keyword_findings:
                block_reason = f"检测到敏感关键词: {len(keyword_findings)}个"
            elif regex_findings:
                block_reason = f"检测到恶意意图模式: {len(regex_findings)}个"
            elif sensitive_data_findings:
                block_reason = f"检测到敏感数据: {len(sensitive_data_findings)}个"
            else:
                block_reason = "安全检查未通过"
        
        return {
            "is_blocked": is_blocked,
            "block_reason": block_reason,
            "security_check_passed": not is_blocked,
            "security_check_details": {
                "passed": not is_blocked,
                "score": score,
                "keyword_check": len(keyword_findings) == 0,
                "malicious_intent_check": len(regex_findings) == 0,
                "data_sensitivity_check": len(sensitive_data_findings) == 0,
                "blocked_keywords": [f.get("description", "") for f in keyword_findings],
                "matched_keywords_info": self._extract_keyword_info(keyword_findings),
                "regex_matches": [f.get("description", "") for f in regex_findings],
                "sensitive_data_patterns": [f.get("description", "") for f in sensitive_data_findings],
                "risk_level": risk_level_str,
                "confidence": pipeline_result.overall_confidence,
                "pipeline_mode": True,  # 标记使用了新管道
                "execution_time_ms": pipeline_result.total_execution_time_ms,
                "modules_executed": [r.module_name for r in pipeline_result.module_results],
                "early_terminated": pipeline_result.early_terminated
            }
        }
    
    def _extract_keyword_info(self, keyword_findings) -> list:
        """从关键词发现中提取详细信息"""
        keyword_info = []
        for finding in keyword_findings:
            # Finding现在是字典格式
            details = finding.get("details", {})
            keyword_info.append({
                "word": details.get("keyword", finding.get("description", "")),
                "group": details.get("group", "检测到"),
                "match_type": details.get("match_type", "pipeline"),
                "priority": details.get("priority", 0)
            })
        return keyword_info
    
    def _get_fallback_result(self, content: str) -> Dict[str, Any]:
        """获取备用检查结果（当主要检查失败时）"""
        # 简单的备用检查
        backup_keywords = ["密码", "账号", "银行卡", "身份证", "攻击", "破坏"]
        found_keywords = [keyword for keyword in backup_keywords if keyword in content]
        
        is_blocked = len(found_keywords) > 0
        score = 80 if not is_blocked else 30
        
        return {
            "is_blocked": is_blocked,
            "block_reason": f"备用检查检测到敏感内容: {', '.join(found_keywords)}" if is_blocked else None,
            "security_check_passed": not is_blocked,
            "security_check_details": {
                "passed": not is_blocked,
                "score": score,
                "risk_level": "high" if is_blocked else "medium",
                "keyword_check": {
                    "passed": not is_blocked,
                    "found_keywords": found_keywords,
                    "matched_keywords_info": [{"word": word, "group": "备用检查", "match_type": "exact"} for word in found_keywords]
                },
                "regex_check": {
                    "passed": True,
                    "matched_rules": [],
                    "total_matches": 0,
                    "error": "正则检查服务不可用，使用备用检查"
                },
                "sensitive_data_check": {
                    "passed": True,
                    "detected_patterns": [],
                    "pattern_count": 0
                },
                "blocked_keywords": found_keywords,
                "matched_keywords_info": [{"word": word, "group": "备用检查", "match_type": "exact"} for word in found_keywords],
                "matched_regex_rules": [],
                "detected_sensitive_patterns": []
            }
        }

# 创建全局实例
security_service = SecurityDetectionService() 