"""
LLM服务模块 - 负责调用配置的大语言模型
"""
import aiohttp
import asyncio
import json
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
import logging

from app.db.models.llm_model import LLMModel
from app.db.models.session import Session as ChatSession
from app.db.models.message import Message

logger = logging.getLogger(__name__)

class LLMService:
    """大语言模型服务"""
    
    @staticmethod
    async def generate_response(
        user_message: str,
        session,  # 可以是ChatSession或models.Session
        db: Session,
        stream: bool = False
    ) -> str:
        """
        生成AI响应

        Args:
            user_message: 用户消息
            session: 会话对象（数据库Session模型）
            db: 数据库会话
            stream: 是否流式返回

        Returns:
            AI生成的响应文本
        """
        # 获取会话绑定的模型
        llm_model = db.query(LLMModel).filter(
            LLMModel.id == session.llm_model_id,
            LLMModel.is_active == True
        ).first()

        if not llm_model:
            raise ValueError(f"模型 {session.llm_model_id} 不存在或未激活")

        # 获取历史消息构建上下文
        messages = LLMService._build_message_context_from_db(session, user_message, db)

        # 调用模型API
        try:
            response = await LLMService._call_llm_api(
                llm_model=llm_model,
                messages=messages,
                stream=stream
            )
            return response
        except Exception as e:
            logger.error(f"调用模型 {llm_model.name} 失败: {str(e)}")
            raise
    
    @staticmethod
    def _build_message_context(session: ChatSession, current_message: str) -> List[Dict[str, str]]:
        """构建消息上下文（兼容旧版本）"""
        messages = []

        # 添加系统提示（如果需要）
        messages.append({
            "role": "system",
            "content": "你是一个有帮助的AI助手。请友好、准确地回答用户的问题。"
        })

        # 添加历史消息（限制最近10条）
        recent_messages = session.messages[-10:] if session.messages else []
        for msg in recent_messages:
            if msg.sender_type == "USER":
                messages.append({
                    "role": "user",
                    "content": msg.content
                })
            elif msg.sender_type == "AI":
                messages.append({
                    "role": "assistant",
                    "content": msg.content
                })

        # 添加当前消息
        messages.append({
            "role": "user",
            "content": current_message
        })

        return messages

    @staticmethod
    def _build_message_context_from_db(session, current_message: str, db: Session) -> List[Dict[str, str]]:
        """从数据库构建消息上下文"""
        from ..db.models.message import Message, SenderType

        messages = []

        # 添加系统提示
        messages.append({
            "role": "system",
            "content": "你是一个有帮助的AI助手。请友好、准确地回答用户的问题。"
        })

        # 查询历史消息（限制最近10条）
        recent_messages = db.query(Message).filter(
            Message.session_id == session.id,
            Message.is_deleted == False
        ).order_by(Message.created_at.desc()).limit(10).all()

        # 按时间顺序排列消息
        recent_messages.reverse()

        for msg in recent_messages:
            if msg.sender_type == SenderType.USER:
                messages.append({"role": "user", "content": msg.content})
            elif msg.sender_type == SenderType.AI:
                messages.append({"role": "assistant", "content": msg.content})

        # 添加当前消息
        messages.append({
            "role": "user",
            "content": current_message
        })

        return messages
    
    @staticmethod
    def _messages_to_prompt(messages: List[Dict[str, str]]) -> str:
        """将messages数组转换为单个prompt字符串（用于Ollama等格式）"""
        prompt_parts = []
        
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
                
        # 添加最后的提示，让AI知道该回复了
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    @staticmethod
    async def _call_llm_api(
        llm_model: LLMModel,
        messages: List[Dict[str, str]],
        stream: bool = False
    ) -> str:
        """调用LLM API"""
        # 检查API格式类型
        api_format = llm_model.config_params.get("api_format", "openai") if llm_model.config_params else "openai"
        
        if api_format == "ollama":
            # Ollama 格式
            # 将messages转换为单个prompt
            prompt = LLMService._messages_to_prompt(messages)
            request_body = {
                "prompt": prompt,
                "stream": stream
            }
            
            # 必须指定model
            if llm_model.config_params and "model" in llm_model.config_params:
                request_body["model"] = llm_model.config_params["model"]
            else:
                raise ValueError("Ollama格式需要在config_params中指定model字段")
                
            # 添加其他参数
            if llm_model.config_params:
                # 添加 options 字段（Ollama格式）
                options = {}
                if "temperature" in llm_model.config_params:
                    options["temperature"] = llm_model.config_params["temperature"]
                if "top_p" in llm_model.config_params:
                    options["top_p"] = llm_model.config_params["top_p"]
                if "max_tokens" in llm_model.config_params:
                    options["num_predict"] = llm_model.config_params["max_tokens"]
                
                if options:
                    request_body["options"] = options
                    
        else:
            # OpenAI 格式（默认）
            request_body = {
                "messages": messages,
                "stream": stream
            }
            
            # 添加模型配置参数
            if llm_model.config_params:
                request_body.update({
                    "temperature": llm_model.config_params.get("temperature", 0.7),
                    "max_tokens": llm_model.config_params.get("max_tokens", 1000),
                    "top_p": llm_model.config_params.get("top_p", 0.9),
                })
                
                # 如果配置中有model字段，添加到请求中
                if "model" in llm_model.config_params:
                    request_body["model"] = llm_model.config_params["model"]
        
        # 准备请求头
        headers = {"Content-Type": "application/json"}
        
        # 如果有API密钥，添加到请求头
        if llm_model.api_key_encrypted:
            # TODO: 实际应用中需要解密
            api_key = llm_model.api_key_encrypted
            headers["Authorization"] = f"Bearer {api_key}"
        
        # 发送请求
        async with aiohttp.ClientSession() as session:
            try:
                # 为本地模型设置合适的超时时间
                timeout_seconds = 60 if api_format == "ollama" else 150
                logger.info(f"Calling LLM API: {llm_model.api_url}, model: {request_body.get('model', 'unknown')}, timeout: {timeout_seconds}s")
                logger.debug(f"Request body: {json.dumps(request_body, ensure_ascii=False)[:500]}...")
                
                async with session.post(
                    llm_model.api_url,
                    json=request_body,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=timeout_seconds)
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API返回错误 {response.status}: {error_text}")
                    
                    # 解析响应（支持不同格式）
                    if api_format == "ollama":
                        # Ollama 格式的响应
                        if stream:
                            # 流式响应处理
                            full_response = ""
                            async for chunk in response.content.iter_chunked(1024):
                                chunk_text = chunk.decode('utf-8')
                                # 按行分割处理多个JSON对象
                                for line in chunk_text.split('\n'):
                                    line = line.strip()
                                    if line:
                                        try:
                                            chunk_data = json.loads(line)
                                            if "response" in chunk_data:
                                                full_response += chunk_data["response"]
                                            if chunk_data.get("done", False):
                                                return full_response
                                        except json.JSONDecodeError:
                                            continue
                            return full_response
                        else:
                            # 非流式响应
                            result = await response.json()
                            if "response" in result:
                                return result["response"]
                            else:
                                logger.warning(f"Ollama响应格式异常: {result}")
                                return json.dumps(result, ensure_ascii=False)
                    else:
                        # OpenAI 和其他格式
                        result = await response.json()
                        if "choices" in result and result["choices"]:
                            # OpenAI格式
                            return result["choices"][0]["message"]["content"]
                        elif "response" in result:
                            # 一些本地模型格式
                            return result["response"]
                        elif "text" in result:
                            # 另一种格式
                            return result["text"]
                        else:
                            # 返回原始响应供调试
                            logger.warning(f"未知的响应格式: {result}")
                            return json.dumps(result, ensure_ascii=False)
                        
            except asyncio.TimeoutError:
                timeout_val = timeout_seconds if 'timeout_seconds' in locals() else 150
                raise Exception(f"请求超时（{timeout_val}秒）")
            except aiohttp.ClientError as e:
                raise Exception(f"网络错误: {str(e)}")
    
    @staticmethod
    async def test_model_connection(llm_model: LLMModel) -> Dict[str, Any]:
        """测试模型连接"""
        test_message = "Hello, please respond with a short greeting."
        
        try:
            response = await LLMService._call_llm_api(
                llm_model=llm_model,
                messages=[{"role": "user", "content": test_message}],
                stream=False
            )
            
            return {
                "success": True,
                "response": response,
                "message": "模型连接成功"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "模型连接失败"
            }

# 创建服务实例
llm_service = LLMService() 