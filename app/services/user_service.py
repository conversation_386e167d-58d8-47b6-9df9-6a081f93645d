# app/services/user_service.py

from sqlalchemy.orm import Session
from typing import Optional

from app.db.models.user import User as UserModel
from fastapi import HTTPException, status
from app.schemas.user import UserCreate # Pydantic model for user creation
from app.core.password_utils import get_password_hash, verify_password

def get_user_by_username(db: Session, username: str) -> Optional[UserModel]:
    """
    Retrieves a user by their username.
    """
    return db.query(UserModel).filter(UserModel.username == username).first()

def get_user_by_email(db: Session, email: str) -> Optional[UserModel]:
    """
    Retrieves a user by their email.
    """
    return db.query(UserModel).filter(UserModel.email == email).first()

def create_user(db: Session, user_in: UserCreate) -> UserModel:
    """
    Creates a new user in the database.
    """
    # Check if username already exists
    existing_user = get_user_by_username(db, username=user_in.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # Check if email already exists
    existing_email = get_user_by_email(db, email=user_in.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )

    # Validate role_id if provided
    if user_in.role_id is not None:
        from app.db.models.role import Role
        existing_role = db.query(Role).filter(Role.id == user_in.role_id).first()
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色ID {user_in.role_id} 不存在"
            )

    # Validate level_id if provided
    if user_in.level_id is not None:
        from app.db.models.level import Level
        existing_level = db.query(Level).filter(Level.id == user_in.level_id).first()
        if not existing_level:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"等级ID {user_in.level_id} 不存在"
            )

    hashed_password = get_password_hash(user_in.password)
    
    # Create a dictionary for the new user, excluding the plain password
    # and including the hashed password.
    db_user_data = user_in.model_dump(exclude={"password"})
    
    # UserCreate schema now includes role_id (mandatory) and level_id (optional).
    # These values will be in db_user_data if provided in the input schema.
    db_user = UserModel(
        **db_user_data,
        hashed_password=hashed_password
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_user_by_id(db: Session, user_id: int) -> Optional[UserModel]:
    """
    Retrieves a user by their ID.
    """
    return db.query(UserModel).filter(UserModel.id == user_id).first()

def authenticate_user(db: Session, username: str, password: str) -> Optional[UserModel]:
    """
    Authenticates a user by username and password.

    Args:
        db: The database session.
        username: The username of the user to authenticate.
        password: The plain text password to verify.

    Returns:
        The authenticated user object if credentials are valid, otherwise None.
    """
    user = get_user_by_username(db, username=username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

def update_user(db: Session, db_user: UserModel, user_update) -> UserModel:
    """
    Updates an existing user in the database.
    """
    # Convert Pydantic model to dict, excluding None values
    update_data = user_update.model_dump(exclude_unset=True)
    
    # Validate role_id if provided
    if "role_id" in update_data and update_data["role_id"] is not None:
        from app.db.models.role import Role
        existing_role = db.query(Role).filter(Role.id == update_data["role_id"]).first()
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色ID {update_data['role_id']} 不存在"
            )

    # Validate level_id if provided
    if "level_id" in update_data and update_data["level_id"] is not None:
        from app.db.models.level import Level
        existing_level = db.query(Level).filter(Level.id == update_data["level_id"]).first()
        if not existing_level:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"等级ID {update_data['level_id']} 不存在"
            )

    # Check if username is being updated and doesn't conflict
    if "username" in update_data:
        existing_user = get_user_by_username(db, username=update_data["username"])
        if existing_user and existing_user.id != db_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
    
    # Check if email is being updated and doesn't conflict
    if "email" in update_data:
        existing_email = get_user_by_email(db, email=update_data["email"])
        if existing_email and existing_email.id != db_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )

    # Hash password if it's being updated
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data["password"])
        del update_data["password"]

    # Update user fields
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

# We can add more functions here later, e.g., delete_user, etc.
