"""
真正的语义嵌入服务
使用sentence-transformers提供高质量的文本向量化
"""

import logging
from typing import List, Union, Optional
import numpy as np
from sentence_transformers import SentenceTransformer
import torch
from app.core.embedding_config import get_embedding_model_config, SELECTED_EMBEDDING_MODEL

logger = logging.getLogger(__name__)


class SemanticEmbeddingService:
    """语义嵌入服务，使用预训练的sentence-transformers模型"""
    
    def __init__(self, model_name: str = None):
        """
        初始化语义嵌入服务
        
        Args:
            model_name: 模型名称，如果不提供则使用配置文件中的默认模型
        """
        # 如果没有指定模型，使用配置中的模型
        if model_name is None:
            model_config = get_embedding_model_config()
            model_name = model_config["name"]
            logger.info(f"Using configured model: {SELECTED_EMBEDDING_MODEL} -> {model_name}")
            logger.info(f"Model description: {model_config['description']}")
        
        self.model_name = model_name
        
        try:
            # 加载模型
            logger.info(f"Loading sentence-transformer model: {model_name}")
            self.model = SentenceTransformer(model_name)
            
            # 获取模型维度
            self.dimension = self.model.get_sentence_embedding_dimension()
            logger.info(f"Model loaded successfully. Embedding dimension: {self.dimension}")
            
            # 检查是否有GPU可用
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
            logger.info(f"Using device: {self.device}")
            
            # 模型信息
            self.is_multilingual = 'multilingual' in model_name.lower()
            logger.info(f"Multilingual support: {self.is_multilingual}")
            
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {str(e)}")
            raise
    
    def encode(
        self, 
        texts: Union[str, List[str]], 
        batch_size: int = 32,
        show_progress_bar: bool = False,
        normalize_embeddings: bool = True
    ) -> np.ndarray:
        """
        将文本编码为语义向量
        
        Args:
            texts: 单个文本或文本列表
            batch_size: 批处理大小
            show_progress_bar: 是否显示进度条
            normalize_embeddings: 是否对向量进行L2归一化
            
        Returns:
            numpy数组，形状为 (n_texts, embedding_dim)
        """
        # 确保输入是列表
        if isinstance(texts, str):
            texts = [texts]
        
        try:
            # 编码文本
            embeddings = self.model.encode(
                texts,
                batch_size=batch_size,
                show_progress_bar=show_progress_bar,
                normalize_embeddings=normalize_embeddings,
                convert_to_numpy=True
            )
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Encoding failed: {str(e)}")
            raise
    
    def encode_single(self, text: str, normalize: bool = True) -> List[float]:
        """
        编码单个文本
        
        Args:
            text: 要编码的文本
            normalize: 是否归一化
            
        Returns:
            向量列表
        """
        embedding = self.encode(text, normalize_embeddings=normalize)
        return embedding[0].tolist()
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            embedding1: 第一个向量
            embedding2: 第二个向量
            
        Returns:
            余弦相似度（-1到1之间）
        """
        # 确保是numpy数组
        if not isinstance(embedding1, np.ndarray):
            embedding1 = np.array(embedding1)
        if not isinstance(embedding2, np.ndarray):
            embedding2 = np.array(embedding2)
        
        # 计算余弦相似度
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def compute_similarities(
        self, 
        query_embedding: np.ndarray, 
        embeddings: List[np.ndarray]
    ) -> List[float]:
        """
        计算查询向量与多个向量的相似度
        
        Args:
            query_embedding: 查询向量
            embeddings: 向量列表
            
        Returns:
            相似度列表
        """
        similarities = []
        for embedding in embeddings:
            similarity = self.compute_similarity(query_embedding, embedding)
            similarities.append(similarity)
        
        return similarities
    
    def semantic_search(
        self,
        query: str,
        documents: List[str],
        top_k: int = 5,
        threshold: float = 0.0
    ) -> List[dict]:
        """
        执行语义搜索
        
        Args:
            query: 查询文本
            documents: 文档列表
            top_k: 返回前k个结果
            threshold: 相似度阈值
            
        Returns:
            搜索结果列表，包含文档索引和相似度
        """
        # 编码查询和文档
        query_embedding = self.encode_single(query)
        doc_embeddings = self.encode(documents)
        
        # 计算相似度
        similarities = self.compute_similarities(
            np.array(query_embedding), 
            doc_embeddings
        )
        
        # 创建结果列表
        results = []
        for idx, (doc, similarity) in enumerate(zip(documents, similarities)):
            if similarity >= threshold:
                results.append({
                    'index': idx,
                    'document': doc,
                    'similarity': float(similarity)
                })
        
        # 按相似度排序
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        # 返回前k个结果
        return results[:top_k]
    
    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            'model_name': self.model_name,
            'embedding_dimension': self.dimension,
            'device': self.device,
            'max_sequence_length': getattr(self.model, 'max_seq_length', 512),
            'is_multilingual': self.is_multilingual
        }


# 创建全局实例（使用轻量级模型）
semantic_embedding_service = SemanticEmbeddingService()

# 导出常用函数
encode_text = semantic_embedding_service.encode_single
encode_texts = semantic_embedding_service.encode
compute_similarity = semantic_embedding_service.compute_similarity 