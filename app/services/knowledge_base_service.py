"""
生产级别的知识库管理服务
整合文档解析、向量化、存储和检索功能
"""

import os
import logging
import shutil
from typing import List, Dict, Any, Optional, Union, BinaryIO
from datetime import datetime
import asyncio
from pathlib import Path
import tempfile

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select

from ..db.models.knowledge_document import KnowledgeDocument
from ..db.models.knowledge_chunk import KnowledgeChunk
from ..db.models.knowledge_version import KnowledgeVersion
from ..db.models.data_source_category import DataSourceCategory
from ..db.models.data_source_grade import DataSourceGrade
from ..db.models.user import User

# 使用生产级服务
from .document_parser_production import production_document_parser
from .document_vectorizer_production import production_document_vectorizer
PRODUCTION_SERVICES = True  # 重新启用生产级服务

# 向量数据库服务
from .vector_db_simple import SimpleVectorDBService
vector_db_service = SimpleVectorDBService()
VECTOR_DB_AVAILABLE = True
logging.info("Using SimpleVectorDBService for production vectorizer")

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    """知识库管理服务"""
    
    def __init__(self):
        """初始化知识库服务"""
        self.parser = production_document_parser
        self.vectorizer = production_document_vectorizer
        self.vector_db = vector_db_service
        
        # 文件存储配置
        self.storage_path = Path("knowledge_base_storage")
        self.storage_path.mkdir(exist_ok=True)
        
        # 支持的文件类型
        self.supported_extensions = {
            '.txt', '.md', '.pdf', '.docx', '.doc', 
            '.xlsx', '.xls', '.pptx', '.ppt', '.csv'
        }
        
        # 文件大小限制（100MB）
        self.max_file_size = 100 * 1024 * 1024
        
        logger.info(f"Knowledge base service initialized (production={PRODUCTION_SERVICES})")
    
    def validate_file(self, file_name: str, file_size: int) -> None:
        """验证文件"""
        # 检查文件扩展名
        ext = Path(file_name).suffix.lower()
        if ext not in self.supported_extensions:
            raise ValueError(f"Unsupported file type: {ext}")
        
        # 检查文件大小
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size})")
        
        if file_size == 0:
            raise ValueError("File is empty")
    
    def save_file(self, file_data: BinaryIO, file_name: str) -> str:
        """保存上传的文件"""
        # 生成安全的文件名
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        safe_name = f"{timestamp}_{Path(file_name).name}"
        file_path = self.storage_path / safe_name
        
        # 保存文件
        with open(file_path, 'wb') as f:
            shutil.copyfileobj(file_data, f)
        
        return str(file_path)
    
    async def upload_document(
        self,
        db: AsyncSession,
        file_data: BinaryIO,
        file_name: str,
        file_size: int,
        uploaded_by: int,
        category_id: Optional[int] = None,
        grade_id: Optional[int] = None,
        description: Optional[str] = None,
        auto_process: bool = True
    ) -> KnowledgeDocument:
        """
        上传文档到知识库
        
        Args:
            db: 数据库会话
            file_data: 文件数据流
            file_name: 文件名
            file_size: 文件大小
            uploaded_by: 上传用户ID
            category_id: 分类ID
            grade_id: 分级ID
            description: 文档描述
            auto_process: 是否自动处理（解析和向量化）
            
        Returns:
            创建的文档对象
        """
        try:
            # 验证文件
            self.validate_file(file_name, file_size)
            
            # 保存文件
            file_path = self.save_file(file_data, file_name)
            
            # 计算文件哈希
            file_hash = self.parser.get_file_hash(file_path)
            
            # 检查重复
            existing_result = await db.execute(select(KnowledgeDocument).filter_by(
                file_hash=file_hash
            ))
            existing = existing_result.scalar_one_or_none()
            
            if existing:
                # 删除临时文件
                os.remove(file_path)
                raise ValueError(f"Document already exists: {existing.file_name}")
            
            # 创建文档记录
            document = KnowledgeDocument(
                title=file_name,  # 使用文件名作为标题
                file_name=file_name,
                file_path=file_path,
                file_size=file_size,
                file_hash=file_hash,
                document_type=Path(file_name).suffix.lower()[1:],  # 使用正确的字段名
                description=description,
                category_id=category_id,
                grade_id=grade_id,
                uploaded_by=uploaded_by,
                status='pending',
            )
            
            db.add(document)
            await db.commit()
            await db.refresh(document)
            
            logger.info(f"Document uploaded: {document.id} - {file_name}")
            
            # 自动处理
            if auto_process:
                # 异步处理，不阻塞响应
                asyncio.create_task(self.process_document_async(document.id))
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to upload document: {str(e)}")
            # 清理文件
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise
    
    async def process_document_async(self, document_id: int) -> None:
        """异步处理文档"""
        try:
            # 创建新的异步数据库会话
            from ..db.session import SessionLocal
            async with SessionLocal() as db:
                try:
                    await self.process_document(db, document_id)
                    await db.commit()
                except Exception as e:
                    await db.rollback()
                    raise e
                
        except Exception as e:
            logger.error(f"Async document processing failed: {str(e)}")
    
    async def process_document(
        self,
        db: AsyncSession,
        document_id: int
    ) -> Dict[str, Any]:
        """
        处理文档（解析和向量化）
        
        Args:
            db: 数据库会话
            document_id: 文档ID
            
        Returns:
            处理结果
        """
        try:
            # 获取文档
            document_result = await db.execute(select(KnowledgeDocument).filter_by(id=document_id))
            document = document_result.scalar_one_or_none()
            if not document:
                raise ValueError(f"Document not found: {document_id}")
            
            if document.status == 'processing':
                raise ValueError("Document is already being processed")
            
            # 更新状态
            document.status = 'processing'
            await db.commit()
            
            # 1. 解析文档
            logger.info(f"Parsing document {document_id}: {document.file_name}")
            parsed_doc = self.parser.process_document_for_vectorization(
                document.file_path
            )
            
            # 2. 向量化并存储
            logger.info(f"Vectorizing document {document_id}")
            
            if PRODUCTION_SERVICES:
                # 使用生产级向量化
                result = await self.vectorizer.process_document(
                    document_id=document_id,
                    file_path=document.file_path,
                    parsed_doc=parsed_doc,
                    category_id=document.category_id,
                    grade_id=document.grade_id,
                    db_session=db,
                    vector_db=self.vector_db if VECTOR_DB_AVAILABLE else None
                )
            else:
                # 使用生产级向量化
                result = await self.vectorizer.process_document(
                    document_id=document_id,
                    file_path=document.file_path,
                    category_id=document.category_id,
                    grade_id=document.grade_id,
                    db_session=db
                )
            
            # 3. 更新文档元数据和状态
            if result['success']:
                document.doc_metadata = document.doc_metadata or {}
                document.doc_metadata.update({
                    'parsed_metadata': parsed_doc.get('metadata', {}),
                    'processing_result': result,
                    'parser_used': parsed_doc.get('document_info', {}).get('parser_used', 'unknown')
                })
                # 处理成功，更新状态为完成
                document.status = 'completed'
                document.is_vectorized = True
                document.chunk_count = result.get('chunk_count', 0)
                document.processed_at = datetime.utcnow()
                await db.commit()
            else:
                # 处理失败
                document.status = 'failed'
                document.error_message = result.get('error', 'Processing failed')
                await db.commit()
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process document {document_id}: {str(e)}")
            
            # 更新错误状态
            if 'document' in locals():
                document.status = 'failed'
                document.error_message = str(e)
                await db.commit()
            
            raise
    
    async def search_knowledge_base(
        self,
        db: AsyncSession,
        query: str,
        user_id: int,
        category_ids: Optional[List[int]] = None,
        grade_ids: Optional[List[int]] = None,
        top_k: int = 5,
        threshold: float = 0.01,  # 降低默认阈值以确保能找到相关结果
        include_content: bool = True
    ) -> List[Dict[str, Any]]:
        """
        搜索知识库
        
        Args:
            db: 数据库会话
            query: 查询文本
            user_id: 用户ID（用于权限检查）
            category_ids: 限制的分类ID
            grade_ids: 限制的分级ID
            top_k: 返回结果数
            threshold: 相似度阈值
            include_content: 是否包含文档内容
            
        Returns:
            搜索结果列表
        """
        try:
            # TODO: 实现基于用户权限的过滤
            
            # 搜索向量数据库 - 使用生产级向量化器
            logger.info(f"Using production vectorizer for search: query='{query}', top_k={top_k}, threshold={threshold}")
            try:
                results = await self.vectorizer.search_similar_chunks(
                    query=query,
                    vector_db=self.vector_db,
                    category_ids=category_ids,
                    grade_ids=grade_ids,
                    top_k=top_k,
                    threshold=threshold
                )
                logger.info(f"Vectorizer returned {len(results)} results")
            except Exception as e:
                logger.error(f"Production vectorizer failed: {e}")
                # 降级到直接使用向量数据库搜索
                logger.info("Falling back to direct vector database search")
                # 使用改进的搜索参数
                results = self.vector_db.search(
                    query=query, 
                    n_results=top_k,
                    min_similarity=max(0.001, threshold)  # 使用更低的阈值
                )
                # 转换格式以匹配期望的结果格式
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        'chunk_id': result.get('id', ''),
                        'content': result.get('content', ''),
                        'similarity': result.get('relevance_score', 0),
                        'metadata': result.get('metadata', {})
                    })
                results = formatted_results
                logger.info(f"Direct search returned {len(results)} results")
            
            # 增强结果信息
            enhanced_results = []
            logger.info(f"Processing {len(results)} search results")
            for i, result in enumerate(results):
                logger.info(f"Processing result {i+1}: {result.keys()}")
                # 获取文档信息 - 优先从metadata获取，如果没有则从chunk_id解析
                doc_id = result.get('metadata', {}).get('document_id')
                chunk_id = result.get('chunk_id', '')

                # 如果metadata中没有document_id，从chunk_id解析
                if not doc_id:
                    if chunk_id.startswith('doc_'):
                        # 从chunk_id解析document_id (格式: doc_{id}_chunk_{index})
                        try:
                            parts = chunk_id.split('_')
                            doc_id = int(parts[1])
                            logger.info(f"Parsed document_id {doc_id} from chunk_id {chunk_id}")
                        except (IndexError, ValueError) as e:
                            logger.warning(f"Failed to parse document_id from chunk_id {chunk_id}: {e}")
                            continue
                    elif chunk_id.startswith('chunk_'):
                        # 处理新格式的chunk_id，需要从数据库查询
                        try:
                            from sqlalchemy import text
                            chunk_result = (await db.execute(
                                text("SELECT document_id FROM knowledge_chunks WHERE id = :chunk_id"),
                                {"chunk_id": chunk_id.replace('chunk_', '')}
                            )).fetchone()
                            if chunk_result:
                                doc_id = chunk_result[0]
                                logger.info(f"Found document_id {doc_id} for chunk_id {chunk_id}")
                            else:
                                logger.warning(f"No document found for chunk_id {chunk_id}")
                                continue
                        except Exception as e:
                            logger.warning(f"Failed to query document_id for chunk_id {chunk_id}: {e}")
                            continue
                    else:
                        # 如果chunk_id格式不对，跳过这个结果
                        logger.warning(f"Invalid chunk_id format: {chunk_id}")
                        continue

                # 如果仍然没有document_id，尝试从result中直接获取
                if not doc_id:
                    doc_id = result.get('document_id')
                
                # 如果还是没有，记录警告并跳过
                if not doc_id:
                    logger.warning(f"No document_id found for chunk {chunk_id}, skipping")
                    continue
                
                if doc_id:
                    # 使用原生SQL查询避免枚举转换问题
                    from sqlalchemy import text
                    result_doc = (await db.execute(
                        text("SELECT id, file_name, document_type, category_id, grade_id FROM knowledge_documents WHERE id = :doc_id"),
                        {"doc_id": doc_id}
                    )).fetchone()
                    
                    if result_doc:
                        enhanced_result = {
                            'chunk_id': result['chunk_id'],
                            'document_id': doc_id,
                            'document_name': result_doc.file_name,
                            'document_type': result_doc.document_type,  # 原生字符串值
                            'similarity': result['similarity'],
                            'chunk_index': result['metadata'].get('chunk_index', 0),
                            'category_id': result_doc.category_id,
                            'grade_id': result_doc.grade_id
                        }
                        
                        if include_content:
                            enhanced_result['content'] = result['content']
                            enhanced_result['content_preview'] = result['content'][:200] + '...' if len(result['content']) > 200 else result['content']
                        
                        # 添加分类和分级信息
                        if result_doc.category_id:
                            category_result = await db.execute(select(DataSourceCategory).filter_by(id=result_doc.category_id))
                            category = category_result.scalar_one_or_none()
                            if category:
                                enhanced_result['category_name'] = category.name
                        if result_doc.grade_id:
                            grade_result = await db.execute(select(DataSourceGrade).filter_by(id=result_doc.grade_id))
                            grade = grade_result.scalar_one_or_none()
                            if grade:
                                enhanced_result['grade_name'] = grade.name
                        
                        enhanced_results.append(enhanced_result)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            raise
    
    async def advanced_search(
        self,
        db: AsyncSession,
        query: str,
        user_id: int,
        search_mode: str = "semantic",
        category_ids: Optional[List[int]] = None,
        grade_ids: Optional[List[int]] = None,
        document_types: Optional[List[str]] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        uploaded_by: Optional[List[int]] = None,
        top_k: int = 10,
        threshold: float = 0.7,
        include_content: bool = True,
        include_metadata: bool = True,
        highlight_keywords: bool = True,
        group_by_document: bool = False
    ) -> Dict[str, Any]:
        """
        高级搜索功能
        
        Args:
            db: 数据库会话
            query: 查询文本
            user_id: 用户ID
            search_mode: 搜索模式（semantic/keyword/hybrid）
            category_ids: 分类ID列表
            grade_ids: 分级ID列表
            document_types: 文档类型列表
            date_from: 创建时间起始
            date_to: 创建时间结束
            uploaded_by: 上传者ID列表
            top_k: 返回结果数
            threshold: 相似度阈值
            include_content: 是否包含内容
            include_metadata: 是否包含元数据
            highlight_keywords: 是否高亮关键词
            group_by_document: 是否按文档分组
            
        Returns:
            搜索结果字典
        """
        try:
            # 1. 首先进行数据库级别的过滤
            doc_query = select(KnowledgeDocument).filter(
                KnowledgeDocument.is_vectorized == True,
                KnowledgeDocument.status == 'completed'
            )
            
            # 应用过滤条件
            if category_ids:
                doc_query = doc_query.filter(KnowledgeDocument.category_id.in_(category_ids))
            if grade_ids:
                doc_query = doc_query.filter(KnowledgeDocument.grade_id.in_(grade_ids))
            if document_types:
                doc_query = doc_query.filter(KnowledgeDocument.document_type.in_(document_types))
            if date_from:
                doc_query = doc_query.filter(KnowledgeDocument.created_at >= date_from)
            if date_to:
                doc_query = doc_query.filter(KnowledgeDocument.created_at <= date_to)
            if uploaded_by:
                doc_query = doc_query.filter(KnowledgeDocument.uploaded_by.in_(uploaded_by))
            
            # 获取符合条件的文档ID
            doc_result = await db.execute(doc_query)
            valid_docs = doc_result.scalars().all()
            valid_doc_ids = [doc.id for doc in valid_docs]
            
            if not valid_doc_ids:
                return {
                    "total_documents": 0,
                    "total_chunks": 0,
                    "documents": [],
                    "facets": {}
                }
            
            # 2. 根据搜索模式执行搜索
            if search_mode == "semantic":
                # 语义搜索
                results = await self._semantic_search(
                    query, valid_doc_ids, top_k * 3, threshold  # 获取更多结果用于分组
                )
            elif search_mode == "keyword":
                # 关键词搜索
                results = await self._keyword_search(
                    db, query, valid_doc_ids, top_k * 3
                )
            else:  # hybrid
                # 混合搜索
                semantic_results = await self._semantic_search(
                    query, valid_doc_ids, top_k * 2, threshold
                )
                keyword_results = await self._keyword_search(
                    db, query, valid_doc_ids, top_k * 2
                )
                results = self._merge_search_results(semantic_results, keyword_results)
            
            # 3. 增强结果信息
            enhanced_results = []
            doc_info_cache = {}
            
            for result in results:
                doc_id = result.get('document_id')
                if doc_id not in doc_info_cache:
                    # 使用原生SQL查询避免枚举转换问题
                    from sqlalchemy import text
                    result_doc_result = await db.execute(
                        text("SELECT id, file_name, document_type, category_id, grade_id, uploaded_by FROM knowledge_documents WHERE id = :doc_id"),
                        {"doc_id": doc_id}
                    )
                    result_doc = result_doc_result.fetchone()
                    
                    if result_doc:
                        # 获取分类名称
                        category_name = None
                        if result_doc.category_id:
                            cat_result = await db.execute(select(DataSourceCategory).filter_by(id=result_doc.category_id))
                            category = cat_result.scalar_one_or_none()
                            category_name = category.name if category else None
                        
                        # 获取分级名称
                        grade_name = None
                        if result_doc.grade_id:
                            grade_result = await db.execute(select(DataSourceGrade).filter_by(id=result_doc.grade_id))
                            grade = grade_result.scalar_one_or_none()
                            grade_name = grade.name if grade else None
                        
                        # 获取上传者名称
                        uploader_name = None
                        if result_doc.uploaded_by:
                            user_result = await db.execute(select(User).filter_by(id=result_doc.uploaded_by))
                            user = user_result.scalar_one_or_none()
                            uploader_name = user.username if user else None
                        
                        doc_info_cache[doc_id] = {
                            'document': result_doc,
                            'category_name': category_name,
                            'grade_name': grade_name,
                            'uploader_name': uploader_name
                        }
                
                if doc_id in doc_info_cache:
                    enhanced_result = {
                        **result,
                        'document_info': doc_info_cache[doc_id]
                    }
                    
                    # 高亮关键词
                    if highlight_keywords and search_mode != "semantic":
                        enhanced_result['highlight'] = self._highlight_keywords(
                            result['content'], query
                        )
                    
                    enhanced_results.append(enhanced_result)
            
            # 4. 按文档分组（如果需要）
            if group_by_document:
                grouped_results = self._group_results_by_document(
                    enhanced_results, include_content, include_metadata
                )
                
                # 5. 生成分面统计
                facets = self._generate_facets(grouped_results, db)
                
                return {
                    "total_documents": len(grouped_results),
                    "total_chunks": len(enhanced_results),
                    "documents": grouped_results[:top_k],
                    "facets": facets
                }
            else:
                # 不分组，直接返回片段
                return {
                    "total_documents": len(set(r['document_id'] for r in enhanced_results)),
                    "total_chunks": len(enhanced_results),
                    "chunks": enhanced_results[:top_k],
                    "facets": None
                }
                
        except Exception as e:
            logger.error(f"Advanced search failed: {str(e)}")
            raise
    
    async def _semantic_search(
        self, 
        query: str, 
        doc_ids: List[int], 
        top_k: int, 
        threshold: float
    ) -> List[Dict[str, Any]]:
        """执行语义搜索"""
        # 暂时不使用 doc_ids 过滤，因为向量数据库不支持
        # TODO: 在搜索后过滤结果

        # 使用生产级向量化器（现在只有这一个）
        results = await self.vectorizer.search_similar_chunks(
            query=query,
            vector_db=self.vector_db,
            top_k=top_k,
            threshold=threshold
        )
        
        # 过滤结果，只保留指定文档的块
        filtered_results = []
        for result in results:
            doc_id = result.get('metadata', {}).get('document_id')
            if doc_id in doc_ids:
                result['document_id'] = doc_id  # 确保有 document_id 字段
                filtered_results.append(result)
        
        return filtered_results
    
    async def _keyword_search(
        self,
        db: AsyncSession,
        query: str,
        doc_ids: List[int],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """执行关键词搜索"""
        from sqlalchemy import or_
        from ..db.models import KnowledgeChunk
        
        # 分词
        keywords = query.lower().split()
        
        # 构建查询
        chunk_query = select(KnowledgeChunk).filter(
            KnowledgeChunk.document_id.in_(doc_ids)
        )
        
        # 添加关键词过滤
        keyword_filters = []
        for keyword in keywords:
            keyword_filters.append(KnowledgeChunk.content.ilike(f'%{keyword}%'))
        
        if keyword_filters:
            chunk_query = chunk_query.filter(or_(*keyword_filters))
        
        # 获取结果
        chunk_result = await db.execute(chunk_query.limit(top_k))
        chunks = chunk_result.scalars().all()
        
        # 转换为统一格式
        results = []
        for chunk in chunks:
            # 计算简单的相关性分数
            score = sum(1 for kw in keywords if kw in chunk.content.lower()) / len(keywords)
            
            results.append({
                'chunk_id': chunk.id,
                'document_id': chunk.document_id,
                'content': chunk.content,
                'similarity': score,
                'metadata': chunk.chunk_metadata or {}
            })
        
        # 按分数排序
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return results
    
    def _merge_search_results(
        self,
        semantic_results: List[Dict],
        keyword_results: List[Dict]
    ) -> List[Dict]:
        """合并语义搜索和关键词搜索结果"""
        # 使用chunk_id去重
        seen_chunks = set()
        merged = []
        
        # 交替添加结果
        for i in range(max(len(semantic_results), len(keyword_results))):
            if i < len(semantic_results):
                chunk = semantic_results[i]
                if chunk['chunk_id'] not in seen_chunks:
                    seen_chunks.add(chunk['chunk_id'])
                    chunk['search_type'] = 'semantic'
                    merged.append(chunk)
            
            if i < len(keyword_results):
                chunk = keyword_results[i]
                if chunk['chunk_id'] not in seen_chunks:
                    seen_chunks.add(chunk['chunk_id'])
                    chunk['search_type'] = 'keyword'
                    merged.append(chunk)
        
        return merged
    
    def _highlight_keywords(self, content: str, query: str) -> str:
        """高亮关键词"""
        keywords = query.lower().split()
        highlighted = content
        
        for keyword in keywords:
            # 使用HTML标签高亮
            import re
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            highlighted = pattern.sub(f'<mark>{keyword}</mark>', highlighted)
        
        return highlighted
    
    def _group_results_by_document(
        self,
        results: List[Dict],
        include_content: bool,
        include_metadata: bool
    ) -> List[Dict]:
        """按文档分组搜索结果"""
        from collections import defaultdict
        
        # 按文档ID分组
        doc_groups = defaultdict(list)
        for result in results:
            doc_groups[result['document_id']].append(result)
        
        # 构建分组结果
        grouped = []
        for doc_id, chunks in doc_groups.items():
            doc_info = chunks[0]['document_info']
            doc = doc_info['document']
            
            # 按相似度排序
            chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
            
            # 构建文档结果
            doc_result = {
                'document_id': doc_id,
                'document_title': doc.file_name,
                'file_name': doc.file_name,
                'document_type': doc.document_type,
                'category_name': doc_info['category_name'],
                'grade_name': doc_info['grade_name'],
                'total_chunks': doc.chunk_count,
                'matched_chunks': len(chunks),
                'max_similarity_score': chunks[0].get('similarity', 0),
                'created_at': doc.created_at,
                'uploaded_by_name': doc_info['uploader_name']
            }
            
            # 添加匹配的片段
            if include_content:
                doc_result['chunks'] = [
                    {
                        'chunk_id': c['chunk_id'],
                        'content': c['content'],
                        'similarity_score': c.get('similarity', 0),
                        'highlight': c.get('highlight'),
                        'metadata': c['metadata'] if include_metadata else None
                    }
                    for c in chunks[:5]  # 每个文档最多显示5个片段
                ]
            
            grouped.append(doc_result)
        
        # 按最高相似度排序
        grouped.sort(key=lambda x: x['max_similarity_score'], reverse=True)
        
        return grouped
    
    def _generate_facets(self, grouped_results: List[Dict], db: AsyncSession) -> Dict[str, Any]:
        """生成搜索结果的分面统计"""
        facets = {
            'categories': {},
            'grades': {},
            'document_types': {},
            'uploaders': {},
            'date_range': {
                'min': None,
                'max': None
            }
        }
        
        for doc in grouped_results:
            # 分类统计
            cat_name = doc.get('category_name', '未分类')
            facets['categories'][cat_name] = facets['categories'].get(cat_name, 0) + 1
            
            # 分级统计
            grade_name = doc.get('grade_name', '未分级')
            facets['grades'][grade_name] = facets['grades'].get(grade_name, 0) + 1
            
            # 类型统计
            doc_type = doc.get('document_type', 'other')
            facets['document_types'][doc_type] = facets['document_types'].get(doc_type, 0) + 1
            
            # 上传者统计
            uploader = doc.get('uploaded_by_name', '未知')
            facets['uploaders'][uploader] = facets['uploaders'].get(uploader, 0) + 1
            
            # 日期范围
            created_at = doc.get('created_at')
            if created_at:
                if facets['date_range']['min'] is None or created_at < facets['date_range']['min']:
                    facets['date_range']['min'] = created_at
                if facets['date_range']['max'] is None or created_at > facets['date_range']['max']:
                    facets['date_range']['max'] = created_at
        
        return facets
    
    async def get_document_content(
        self,
        db: AsyncSession,
        document_id: int,
        user_id: int,
        chunk_indices: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        获取文档内容
        
        Args:
            db: 数据库会话
            document_id: 文档ID
            user_id: 用户ID（用于权限检查）
            chunk_indices: 指定的块索引（None表示所有）
            
        Returns:
            文档内容
        """
        # 获取文档
        document_result = await db.execute(select(KnowledgeDocument).filter_by(id=document_id))
        document = document_result.scalar_one_or_none()
        if not document:
            raise ValueError(f"Document not found: {document_id}")
        
        # TODO: 检查用户权限
        
        # 获取文档块
        chunk_query = select(KnowledgeChunk).filter_by(document_id=document_id)
        
        if chunk_indices:
            chunk_query = chunk_query.filter(KnowledgeChunk.chunk_index.in_(chunk_indices))
        
        chunk_result = await db.execute(chunk_query.order_by(KnowledgeChunk.chunk_index))
        chunks = chunk_result.scalars().all()
        
        # 组装内容
        content_parts = []
        for chunk in chunks:
            content_parts.append({
                'chunk_index': chunk.chunk_index,
                'content': chunk.content,
                'start_char': chunk.start_char,
                'end_char': chunk.end_char
            })
        
        return {
            'document_id': document_id,
            'document_name': document.file_name,
            'document_type': document.file_type,
            'total_chunks': document.chunk_count,
            'returned_chunks': len(content_parts),
            'chunks': content_parts
        }
    
    async def update_document(
        self,
        db: AsyncSession,
        document_id: int,
        user_id: int,
        category_id: Optional[int] = None,
        grade_id: Optional[int] = None,
        description: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> KnowledgeDocument:
        """更新文档信息"""
        # 获取文档
        document = db.query(KnowledgeDocument).filter_by(id=document_id).first()
        if not document:
            raise ValueError(f"Document not found: {document_id}")
        
        # TODO: 检查用户权限
        
        # 更新字段
        if category_id is not None:
            document.category_id = category_id
        if grade_id is not None:
            document.grade_id = grade_id
        if description is not None:
            document.description = description
        if is_active is not None:
            document.is_active = is_active
        
        document.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(document)
        
        return document
    
    async def delete_document(
        self,
        db: AsyncSession,
        document_id: int,
        user_id: int,
        hard_delete: bool = False
    ) -> bool:
        """
        删除文档
        
        Args:
            db: 数据库会话
            document_id: 文档ID
            user_id: 用户ID
            hard_delete: 是否物理删除
            
        Returns:
            是否成功
        """
        # 获取文档
        document_result = await db.execute(select(KnowledgeDocument).filter_by(id=document_id))
        document = document_result.scalar_one_or_none()
        if not document:
            raise ValueError(f"Document not found: {document_id}")
        
        # TODO: 检查用户权限
        
        if hard_delete:
            # 删除向量数据
            if hasattr(self.vectorizer, 'delete_document_chunks'):
                await self.vectorizer.delete_document_chunks(document_id, db)
            
            # 删除文件
            if os.path.exists(document.file_path):
                os.remove(document.file_path)
            
            # 删除数据库记录
            db.delete(document)
        else:
            # 软删除
            document.is_active = False
            document.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"Document deleted: {document_id} (hard={hard_delete})")
        return True
    
    def get_statistics(self, db: AsyncSession) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            # 基本统计
            total_docs = db.query(KnowledgeDocument).count()
            active_docs = db.query(KnowledgeDocument).filter(
                KnowledgeDocument.status.in_(['pending', 'processing', 'completed'])
            ).count()
            vectorized_docs = db.query(KnowledgeDocument).filter_by(is_vectorized=True).count()
            
            # 按状态统计
            status_counts = {}
            for status in ['pending', 'processing', 'completed', 'failed']:
                count = db.query(KnowledgeDocument).filter_by(status=status).count()
                status_counts[status] = count
            
            # 简化的分类和分级统计
            category_counts = {'未分类': total_docs}  # 默认值
            grade_counts = {'未分级': total_docs}     # 默认值
            
            # 计算总存储大小和分块数
            from sqlalchemy import func
            total_size = db.query(func.sum(KnowledgeDocument.file_size)).scalar() or 0
            total_chunks = db.query(func.sum(KnowledgeDocument.chunk_count)).scalar() or 0
            
            return {
                'total_documents': total_docs,
                'active_documents': active_docs,
                'vectorized_documents': vectorized_docs,
                'total_chunks': total_chunks,
                'total_size': total_size,
                'documents_by_status': status_counts,
                'documents_by_category': category_counts,
                'documents_by_grade': grade_counts,
            }
        except Exception as e:
            # 如果查询失败，返回默认值
            return {
                'total_documents': 0,
                'active_documents': 0,
                'vectorized_documents': 0,
                'total_chunks': 0,
                'total_size': 0,
                'documents_by_status': {'pending': 0, 'processing': 0, 'completed': 0, 'failed': 0},
                'documents_by_category': {'未分类': 0},
                'documents_by_grade': {'未分级': 0},
            }


# 创建全局实例
knowledge_base_service = KnowledgeBaseService() 