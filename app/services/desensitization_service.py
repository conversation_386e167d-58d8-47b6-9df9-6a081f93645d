"""
数据脱敏服务
提供数据脱敏的核心功能，包括模式检测、脱敏应用、统计管理等
"""

import re
import time
from typing import List, Dict, Any, Optional, Set, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import logging

from app.db.models.desensitization_rule import DesensitizationRule, DataType, MaskingLevel, MaskingStrategy
from app.db.models.role import Role

logger = logging.getLogger(__name__)

class DesensitizationService:
    """数据脱敏服务类"""
    
    def __init__(self):
        """初始化脱敏服务"""
        # 预定义的脱敏级别配置
        self.masking_configs = {
            MaskingLevel.LOW: {
                'show_ratio': 0.6,  # 显示60%的字符
                'min_mask': 2,      # 最少脱敏2个字符
                'max_mask': 6       # 最多脱敏6个字符
            },
            MaskingLevel.MEDIUM: {
                'show_ratio': 0.4,  # 显示40%的字符
                'min_mask': 3,      # 最少脱敏3个字符
                'max_mask': 10      # 最多脱敏10个字符
            },
            MaskingLevel.HIGH: {
                'show_ratio': 0.2,  # 显示20%的字符
                'min_mask': 4,      # 最少脱敏4个字符
                'max_mask': 15      # 最多脱敏15个字符
            },
            MaskingLevel.COMPLETE: {
                'show_ratio': 0.0,  # 不显示原字符
                'min_mask': 5,      # 最少脱敏5个字符
                'max_mask': 20      # 最多脱敏20个字符
            }
        }
    
    def process_text_desensitization(
        self, 
        text: str, 
        db: Session,
        role_id: Optional[int] = None,
        apply_masking: bool = True
    ) -> Dict[str, Any]:
        """
        处理文本脱敏
        
        Args:
            text: 待处理的文本
            db: 数据库会话
            role_id: 角色ID，用于角色特定规则
            apply_masking: 是否应用脱敏，False时只检测不脱敏
            
        Returns:
            处理结果字典
        """
        start_time = time.time()
        
        try:
            # 获取适用的规则
            rules = self._get_applicable_rules(db, role_id)
            
            if not rules:
                return {
                    "original_text": text,
                    "processed_text": text,
                    "detected_patterns": [],
                    "total_matches": 0,
                    "rules_applied": 0,
                    "processing_time": time.time() - start_time,
                    "risk_level": "safe"
                }
            
            # 检测所有敏感模式
            detected_patterns = []
            for rule in rules:
                patterns = self._detect_patterns(text, rule)
                detected_patterns.extend(patterns)
            
            # 如果不需要应用脱敏，直接返回检测结果
            if not apply_masking:
                return {
                    "original_text": text,
                    "processed_text": text,
                    "detected_patterns": detected_patterns,
                    "total_matches": len(detected_patterns),
                    "rules_applied": len(set(p["rule_id"] for p in detected_patterns)),
                    "processing_time": time.time() - start_time,
                    "risk_level": self._calculate_risk_level(detected_patterns)
                }
            
            # 解决重叠冲突并应用脱敏
            resolved_patterns = self._resolve_overlaps(detected_patterns)
            processed_text = self._apply_masking(text, resolved_patterns)
            
            # 更新统计信息
            self._update_statistics(db, resolved_patterns, applied=True)
            
            return {
                "original_text": text,
                "processed_text": processed_text,
                "detected_patterns": resolved_patterns,
                "total_matches": len(resolved_patterns),
                "rules_applied": len(set(p["rule_id"] for p in resolved_patterns)),
                "processing_time": time.time() - start_time,
                "risk_level": self._calculate_risk_level(resolved_patterns)
            }
            
        except Exception as e:
            logger.error(f"脱敏处理失败: {str(e)}")
            return {
                "original_text": text,
                "processed_text": text,
                "detected_patterns": [],
                "total_matches": 0,
                "rules_applied": 0,
                "processing_time": time.time() - start_time,
                "risk_level": "error",
                "error": str(e)
            }
    
    def test_rule(self, rule_id: int, text: str, db: Session) -> Dict[str, Any]:
        """
        测试单个脱敏规则
        
        Args:
            rule_id: 规则ID
            text: 测试文本
            db: 数据库会话
            
        Returns:
            测试结果
        """
        try:
            rule = db.query(DesensitizationRule).filter(
                DesensitizationRule.id == rule_id
            ).first()
            
            if not rule:
                return {"error": f"规则 {rule_id} 不存在"}
            
            # 检测模式
            patterns = self._detect_patterns(text, rule)
            
            if not patterns:
                return {
                    "original_text": text,
                    "processed_text": text,
                    "matches": [],
                    "total_matches": 0
                }
            
            # 应用脱敏
            processed_text = self._apply_masking(text, patterns)
            
            return {
                "original_text": text,
                "processed_text": processed_text,
                "matches": patterns,
                "total_matches": len(patterns)
            }
            
        except Exception as e:
            logger.error(f"规则测试失败: {str(e)}")
            return {"error": str(e)}
    
    def batch_process_texts(
        self, 
        texts: List[str], 
        db: Session,
        role_id: Optional[int] = None,
        apply_masking: bool = True
    ) -> Dict[str, Any]:
        """
        批量处理文本脱敏
        
        Args:
            texts: 文本列表
            db: 数据库会话
            role_id: 角色ID
            apply_masking: 是否应用脱敏
            
        Returns:
            批量处理结果
        """
        results = []
        total_matches = 0
        rules_applied_set = set()
        
        for text in texts:
            result = self.process_text_desensitization(
                text, db, role_id, apply_masking
            )
            
            results.append({
                "original_text": result["original_text"],
                "processed_text": result["processed_text"],
                "matches": result["detected_patterns"],
                "has_sensitive_data": result["total_matches"] > 0
            })
            
            total_matches += result["total_matches"]
            rules_applied_set.update(
                p["rule_id"] for p in result["detected_patterns"]
            )
        
        return {
            "results": results,
            "total_processed": len(texts),
            "total_matches": total_matches,
            "rules_applied": len(rules_applied_set)
        }
    
    def create_default_rules(self, db: Session) -> List[DesensitizationRule]:
        """
        创建默认脱敏规则
        
        Args:
            db: 数据库会话
            
        Returns:
            创建的规则列表
        """
        created_rules = []
        
        try:
            # 简单的默认规则
            default_rules_data = [
                ("身份证号脱敏", "检测并脱敏中国身份证号码", DataType.IDENTITY_CARD, r"\b[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]\b", MaskingLevel.HIGH, MaskingStrategy.PARTIAL_SHOW, 100),
                ("手机号脱敏", "检测并脱敏中国手机号码", DataType.PHONE, r"\b1[3-9]\d{9}\b", MaskingLevel.MEDIUM, MaskingStrategy.PARTIAL_SHOW, 90),
                ("邮箱地址脱敏", "检测并脱敏电子邮件地址", DataType.EMAIL, r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", MaskingLevel.MEDIUM, MaskingStrategy.PARTIAL_SHOW, 80),
            ]
            
            for name, description, data_type, pattern, masking_level, masking_strategy, priority in default_rules_data:
                # 检查是否已存在同名规则
                existing = db.query(DesensitizationRule).filter(
                    DesensitizationRule.name == name
                ).first()
                
                if existing:
                    continue
                
                rule = DesensitizationRule(
                    name=name,
                    description=description,
                    data_type=data_type,
                    pattern=pattern,
                    masking_level=masking_level,
                    masking_strategy=masking_strategy,
                    show_first=3,
                    show_last=4,
                    min_mask_length=3,
                    confidence_threshold=0.8,
                    priority=priority,
                    is_active=True,
                    created_by="system"
                )
                
                db.add(rule)
                created_rules.append(rule)
            
            if created_rules:
                db.commit()
                for rule in created_rules:
                    db.refresh(rule)
                    
        except Exception as e:
            logger.error(f"创建默认规则失败: {str(e)}")
            db.rollback()
            raise e
        
        return created_rules
    
    def _get_applicable_rules(
        self, 
        db: Session, 
        role_id: Optional[int] = None
    ) -> List[DesensitizationRule]:
        """获取适用的脱敏规则"""
        query = db.query(DesensitizationRule).filter(
            DesensitizationRule.is_active == True
        )
        
        if role_id:
            # 获取角色特定规则和全局规则
            query = query.filter(
                or_(
                    DesensitizationRule.role_id == role_id,
                    DesensitizationRule.role_id.is_(None)
                )
            )
        else:
            # 只获取全局规则
            query = query.filter(DesensitizationRule.role_id.is_(None))
        
        return query.order_by(DesensitizationRule.priority.desc()).all()
    
    def _detect_patterns(self, text: str, rule: DesensitizationRule) -> List[Dict[str, Any]]:
        """检测文本中的敏感模式"""
        patterns = []
        
        try:
            regex = re.compile(rule.pattern, rule.flags or 0)
            
            for match in regex.finditer(text):
                # 计算置信度
                confidence = self._calculate_confidence(match.group(), rule)
                
                if confidence >= rule.confidence_threshold:
                    patterns.append({
                        "rule_id": rule.id,
                        "rule_name": rule.name,
                        "data_type": rule.data_type.value,
                        "start": match.start(),
                        "end": match.end(),
                        "matched_text": match.group(),
                        "confidence": confidence,
                        "masking_level": rule.masking_level,
                        "masking_strategy": rule.masking_strategy,
                        "priority": rule.priority
                    })
                    
        except re.error as e:
            logger.warning(f"正则表达式错误 (规则 {rule.id}): {str(e)}")
        
        return patterns
    
    def _calculate_confidence(self, matched_text: str, rule: DesensitizationRule) -> float:
        """计算匹配置信度"""
        base_confidence = 0.7
        
        # 根据数据类型调整置信度
        type_confidence_map = {
            DataType.IDENTITY_CARD: 0.9,    # 身份证格式相对固定
            DataType.CREDIT_CARD: 0.85,     # 信用卡格式较固定
            DataType.PHONE: 0.8,            # 手机号格式较固定
            DataType.EMAIL: 0.75,           # 邮箱格式较固定
            DataType.IP_ADDRESS: 0.7,       # IP地址格式较固定
            DataType.URL: 0.6,              # URL格式变化较大
            DataType.ADDRESS: 0.5,          # 地址格式变化很大
            DataType.USERNAME: 0.4,         # 用户名格式变化很大
            DataType.CUSTOM: 0.5            # 自定义类型
        }
        
        type_confidence = type_confidence_map.get(rule.data_type, base_confidence)
        
        # 根据匹配长度调整置信度
        length_factor = min(len(matched_text) / 10.0, 1.0)
        
        # 最终置信度
        final_confidence = type_confidence * (0.7 + 0.3 * length_factor)
        
        return min(final_confidence, 1.0)
    
    def _resolve_overlaps(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解决重叠模式冲突"""
        if not patterns:
            return []
        
        # 按优先级和置信度排序
        sorted_patterns = sorted(
            patterns, 
            key=lambda x: (-x["priority"], -x["confidence"])
        )
        
        resolved = []
        used_ranges = []
        
        for pattern in sorted_patterns:
            start, end = pattern["start"], pattern["end"]
            
            # 检查是否与已选择的模式重叠
            overlaps = False
            for used_start, used_end in used_ranges:
                if not (end <= used_start or start >= used_end):
                    overlaps = True
                    break
            
            if not overlaps:
                resolved.append(pattern)
                used_ranges.append((start, end))
        
        return resolved
    
    def _apply_masking(self, text: str, patterns: List[Dict[str, Any]]) -> str:
        """应用脱敏处理"""
        if not patterns:
            return text
        
        # 按位置倒序排序，从后往前处理避免位置偏移
        sorted_patterns = sorted(patterns, key=lambda x: x["start"], reverse=True)
        
        result_text = text
        
        for pattern in sorted_patterns:
            start = pattern["start"]
            end = pattern["end"]
            original_text = pattern["matched_text"]
            
            # 根据脱敏策略生成脱敏文本
            masked_text = self._generate_masked_text(original_text, pattern)
            
            # 替换文本
            result_text = result_text[:start] + masked_text + result_text[end:]
        
        return result_text
    
    def _generate_masked_text(self, original_text: str, pattern: Dict[str, Any]) -> str:
        """生成脱敏文本"""
        strategy = pattern["masking_strategy"]
        level = pattern["masking_level"]
        
        if strategy == MaskingStrategy.REPLACEMENT:
            # 完全替换
            return pattern.get("replacement_text", "[已脱敏]")
        
        elif strategy == MaskingStrategy.PARTIAL_SHOW:
            # 部分显示
            return self._partial_show_masking(original_text, level, pattern)
        
        elif strategy == MaskingStrategy.FORMAT_PRESERVE:
            # 格式保留
            return self._format_preserve_masking(original_text, level)
        
        else:
            # 字符替换 (asterisk, x_mark, hash)
            return self._character_replacement_masking(original_text, level, strategy)
    
    def _partial_show_masking(
        self, 
        text: str, 
        level: MaskingLevel, 
        pattern: Dict[str, Any]
    ) -> str:
        """部分显示脱敏"""
        length = len(text)
        
        # 从模式或配置获取显示位数
        show_first = pattern.get("show_first", 0)
        show_last = pattern.get("show_last", 0)
        min_mask_length = pattern.get("min_mask_length", 3)
        
        # 如果没有指定，使用级别配置
        if show_first == 0 and show_last == 0:
            config = self.masking_configs[level]
            show_ratio = config["show_ratio"]
            total_show = int(length * show_ratio)
            show_first = total_show // 2
            show_last = total_show - show_first
        
        # 确保脱敏部分不小于最小长度
        if length - show_first - show_last < min_mask_length:
            total_show = max(0, length - min_mask_length)
            show_first = total_show // 2
            show_last = total_show - show_first
        
        # 生成脱敏文本
        mask_length = max(min_mask_length, length - show_first - show_last)
        
        result = ""
        if show_first > 0:
            result += text[:show_first]
        result += "*" * mask_length
        if show_last > 0:
            result += text[-show_last:]
        
        return result
    
    def _format_preserve_masking(self, text: str, level: MaskingLevel) -> str:
        """格式保留脱敏"""
        result = ""
        for char in text:
            if char.isalnum():
                result += "*"
            else:
                result += char
        return result
    
    def _character_replacement_masking(
        self, 
        text: str, 
        level: MaskingLevel, 
        strategy: MaskingStrategy
    ) -> str:
        """字符替换脱敏"""
        char_map = {
            MaskingStrategy.ASTERISK: "*",
            MaskingStrategy.X_MARK: "X",
            MaskingStrategy.HASH: "#"
        }
        
        replacement_char = char_map.get(strategy, "*")
        config = self.masking_configs[level]
        
        # 根据级别决定替换程度
        if level == MaskingLevel.COMPLETE:
            return replacement_char * max(config["min_mask"], len(text))
        else:
            show_ratio = config["show_ratio"]
            show_count = int(len(text) * show_ratio)
            mask_count = len(text) - show_count
            
            # 保留前面的字符，后面用替换字符
            return text[:show_count] + replacement_char * mask_count
    
    def _calculate_risk_level(self, patterns: List[Dict[str, Any]]) -> str:
        """计算风险等级"""
        if not patterns:
            return "safe"
        
        high_risk_types = {
            DataType.IDENTITY_CARD, 
            DataType.CREDIT_CARD, 
            DataType.BANK_ACCOUNT,
            DataType.SOCIAL_SECURITY
        }
        
        max_confidence = max(p["confidence"] for p in patterns)
        has_high_risk = any(
            DataType(p["data_type"]) in high_risk_types for p in patterns
        )
        
        if has_high_risk and max_confidence > 0.8:
            return "critical"
        elif max_confidence > 0.7:
            return "high"
        elif max_confidence > 0.5:
            return "medium"
        else:
            return "low"
    
    def _update_statistics(
        self, 
        db: Session, 
        patterns: List[Dict[str, Any]], 
        applied: bool = True
    ):
        """更新统计信息"""
        try:
            rule_ids = set(p["rule_id"] for p in patterns)
            
            for rule_id in rule_ids:
                rule = db.query(DesensitizationRule).filter(
                    DesensitizationRule.id == rule_id
                ).first()
                
                if rule:
                    rule.match_count += len([p for p in patterns if p["rule_id"] == rule_id])
                    if applied:
                        rule.mask_count += len([p for p in patterns if p["rule_id"] == rule_id])
            
            db.commit()
            
        except Exception as e:
            logger.error(f"统计信息更新失败: {str(e)}")
            db.rollback()

# 创建全局实例
desensitization_service = DesensitizationService() 