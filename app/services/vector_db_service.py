# app/services/vector_db_service.py

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
import hashlib

from app.core.config import settings
from app.core.vector_db_config import VectorDBConfig

# 创建全局配置实例
vector_db_config = VectorDBConfig()

logger = logging.getLogger(__name__)


class VectorDBService:
    """向量数据库服务类，管理Chroma数据库的连接和操作"""
    
    def __init__(self):
        """初始化向量数据库服务"""
        # 配置Chroma数据库路径
        self.persist_directory = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            vector_db_config.CHROMA_PERSIST_DIRECTORY
        )
        
        # 确保目录存在
        os.makedirs(self.persist_directory, exist_ok=True)
        
        # 初始化Chroma客户端
        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(**vector_db_config.get_chroma_settings())
        )
        
        # 初始化嵌入函数
        embedding_config = vector_db_config.get_embedding_config()
        if embedding_config["provider"] == "openai":
            # 使用OpenAI嵌入（需要API密钥）
            self.embedding_function = embedding_functions.OpenAIEmbeddingFunction(
                api_key=os.getenv("OPENAI_API_KEY"),
                model_name=embedding_config["model_name"]
            )
        else:
            # 使用本地sentence-transformers模型
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=embedding_config["model_name"]
            )
        
        # 知识库集合名称
        self.collection_name = vector_db_config.CHROMA_COLLECTION_NAME
        
        # 配置参数
        self.chunk_size = vector_db_config.CHUNK_SIZE
        self.chunk_overlap = vector_db_config.CHUNK_OVERLAP
        self.default_search_results = vector_db_config.DEFAULT_SEARCH_RESULTS
        self.max_search_results = vector_db_config.MAX_SEARCH_RESULTS
        
        # 获取或创建集合
        self._init_collection()
        
        logger.info(f"Vector DB service initialized with persist directory: {self.persist_directory}")
    
    def _init_collection(self):
        """初始化或获取知识库集合"""
        try:
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                embedding_function=self.embedding_function,
                metadata={"description": "AI Security System Knowledge Base"}
            )
            logger.info(f"Collection '{self.collection_name}' initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize collection: {str(e)}")
            raise
    
    def add_document_chunks(
        self, 
        chunks: List[Dict[str, Any]], 
        document_id: int
    ) -> List[str]:
        """
        添加文档块到向量数据库
        
        Args:
            chunks: 文档块列表，每个块包含 content, chunk_index, metadata等
            document_id: 文档ID
            
        Returns:
            向量ID列表
        """
        try:
            # 准备数据
            ids = []
            documents = []
            metadatas = []
            
            for chunk in chunks:
                # 生成唯一ID
                chunk_id = f"doc_{document_id}_chunk_{chunk['chunk_index']}"
                ids.append(chunk_id)
                
                # 文档内容
                documents.append(chunk['content'])
                
                # 元数据
                metadata = {
                    "document_id": document_id,
                    "chunk_index": chunk['chunk_index'],
                    "start_char": chunk.get('start_char', 0),
                    "end_char": chunk.get('end_char', 0),
                    "created_at": datetime.utcnow().isoformat()
                }
                
                # 添加额外的元数据
                if 'page_number' in chunk:
                    metadata['page_number'] = chunk['page_number']
                if 'section_title' in chunk:
                    metadata['section_title'] = chunk['section_title']
                    
                # 清理metadata，移除None值（ChromaDB不支持None）
                cleaned_metadata = {}
                for key, value in metadata.items():
                    if value is not None:
                        # 确保值是ChromaDB支持的类型
                        if isinstance(value, (str, int, float, bool)):
                            cleaned_metadata[key] = value
                        else:
                            # 转换为字符串
                            cleaned_metadata[key] = str(value)
                    
                metadatas.append(cleaned_metadata)
            
            # 批量添加到向量数据库
            self.collection.add(
                ids=ids,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Added {len(chunks)} chunks for document {document_id}")
            return ids
            
        except Exception as e:
            logger.error(f"Failed to add document chunks: {str(e)}")
            raise
    
    async def add_chunk(
        self,
        content: str,
        metadata: Dict[str, Any],
        chunk_id: Optional[int] = None,
        document_id: Optional[int] = None,
        embedding: Optional[List[float]] = None
    ) -> str:
        """
        添加单个文档块（兼容document_vectorizer_production.py）
        
        Args:
            content: 块内容
            metadata: 元数据
            chunk_id: 块ID（可选）
            document_id: 文档ID（可选，如果metadata中没有）
            embedding: 预计算的嵌入向量（可选，如果提供则跳过自动嵌入）
            
        Returns:
            向量ID
        """
        try:
            # 确保metadata中有document_id
            if document_id and 'document_id' not in metadata:
                metadata['document_id'] = document_id
            
            # 生成唯一的向量ID
            if chunk_id is not None:
                vector_id = f"chunk_{chunk_id}"
            else:
                # 使用document_id和chunk_index生成
                doc_id = metadata.get('document_id', 'unknown')
                chunk_index = metadata.get('chunk_index', 0)
                vector_id = f"doc_{doc_id}_chunk_{chunk_index}"
            
            # 添加创建时间
            metadata['created_at'] = datetime.utcnow().isoformat()
            
            # 清理metadata，移除None值（ChromaDB不支持None）
            cleaned_metadata = {}
            for key, value in metadata.items():
                if value is not None:
                    # 确保值是ChromaDB支持的类型
                    if isinstance(value, (str, int, float, bool)):
                        cleaned_metadata[key] = value
                    else:
                        # 转换为字符串
                        cleaned_metadata[key] = str(value)
            
            # 准备添加参数
            add_args = {
                'ids': [vector_id],
                'documents': [content],
                'metadatas': [cleaned_metadata]
            }
            
            # 如果提供了预计算的嵌入向量，使用它（注意：ChromaDB通常自动计算嵌入）
            # 这里我们记录到metadata中，但让ChromaDB自己计算嵌入
            if embedding is not None:
                metadata['custom_embedding_provided'] = True
                metadata['embedding_dimension'] = len(embedding)
            
            # 添加到向量数据库
            self.collection.add(**add_args)
            
            logger.info(f"Added chunk to vector database with ID: {vector_id}")
            return vector_id
            
        except Exception as e:
            logger.error(f"Failed to add chunk: {str(e)}")
            raise
    
    def search(
        self, 
        query: str, 
        n_results: int = 5,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索相似的文档块
        
        Args:
            query: 查询文本
            n_results: 返回结果数量
            filter_dict: 过滤条件，如 {"document_id": 123}
            
        Returns:
            搜索结果列表
        """
        try:
            # 执行搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=filter_dict if filter_dict else None,
                include=["documents", "metadatas", "distances"]
            )
            
            # 格式化结果
            formatted_results = []
            for i in range(len(results['ids'][0])):
                result = {
                    "id": results['ids'][0][i],
                    "content": results['documents'][0][i],
                    "metadata": results['metadatas'][0][i],
                    "distance": results['distances'][0][i],
                    "relevance_score": 1 - results['distances'][0][i]  # 转换为相似度分数
                }
                formatted_results.append(result)
            
            logger.info(f"Found {len(formatted_results)} results for query")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            raise
    
    def delete_document_chunks(self, document_id: int) -> int:
        """
        删除指定文档的所有块
        
        Args:
            document_id: 文档ID
            
        Returns:
            删除的块数量
        """
        try:
            # 获取文档的所有块ID
            results = self.collection.get(
                where={"document_id": document_id}
            )
            
            if results['ids']:
                # 删除所有块
                self.collection.delete(ids=results['ids'])
                logger.info(f"Deleted {len(results['ids'])} chunks for document {document_id}")
                return len(results['ids'])
            
            return 0
            
        except Exception as e:
            logger.error(f"Failed to delete document chunks: {str(e)}")
            raise
    
    def update_chunk(
        self, 
        chunk_id: str, 
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        更新文档块
        
        Args:
            chunk_id: 块ID
            content: 新内容（可选）
            metadata: 新元数据（可选）
        """
        try:
            update_args = {"ids": [chunk_id]}
            
            if content is not None:
                update_args["documents"] = [content]
            
            if metadata is not None:
                # 获取现有元数据并更新
                existing = self.collection.get(ids=[chunk_id])
                if existing['ids']:
                    current_metadata = existing['metadatas'][0]
                    current_metadata.update(metadata)
                    update_args["metadatas"] = [current_metadata]
            
            self.collection.update(**update_args)
            logger.info(f"Updated chunk {chunk_id}")
            
        except Exception as e:
            logger.error(f"Failed to update chunk: {str(e)}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            count = self.collection.count()
            
            # 获取唯一文档数
            all_data = self.collection.get()
            unique_docs = set()
            if all_data['metadatas']:
                for metadata in all_data['metadatas']:
                    if 'document_id' in metadata:
                        unique_docs.add(metadata['document_id'])
            
            stats = {
                "collection_name": self.collection_name,
                "total_chunks": count,
                "unique_documents": len(unique_docs),
                "persist_directory": self.persist_directory,
                "embedding_model": "all-MiniLM-L6-v2"
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            raise
    
    def reset_collection(self):
        """重置集合（删除所有数据）"""
        try:
            self.client.delete_collection(name=self.collection_name)
            self._init_collection()
            logger.info("Collection reset successfully")
        except Exception as e:
            logger.error(f"Failed to reset collection: {str(e)}")
            raise


# 创建全局实例
vector_db_service = VectorDBService() 