"""
RAG (Retrieval-Augmented Generation) 服务
实现基于知识库的增强问答功能
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..db.models.knowledge_document import KnowledgeDocument
from ..db.models.knowledge_chunk import KnowledgeChunk
from ..services.knowledge_base_service import KnowledgeBaseService
from ..services.llm_service import LLMService
from ..services.knowledge_base_permissions import KnowledgeBasePermissionService
from ..services.knowledge_base_audit import KnowledgeBaseAuditService
from ..services.pipeline.factory import SecurityPipelineFactory
from ..services.pipeline.context import PipelineContext
from ..core.config import settings
from ..db.models.user import User as UserModel

logger = logging.getLogger(__name__)


class RAGService:
    """RAG服务类，处理基于知识库的问答"""
    
    def __init__(
        self,
        db: AsyncSession,
        knowledge_service: KnowledgeBaseService,
        llm_service: LLMService,
        permission_service: KnowledgeBasePermissionService,
        audit_service: KnowledgeBaseAuditService,
        security_pipeline_factory: SecurityPipelineFactory
    ):
        self.db = db
        self.knowledge_service = knowledge_service
        self.llm_service = llm_service
        self.permission_service = permission_service
        self.audit_service = audit_service
        self.security_pipeline_factory = security_pipeline_factory
    
    async def generate_answer(
        self,
        query: str,
        user_id: int,
        model_id: Optional[int] = None,
        category_ids: Optional[List[int]] = None,
        grade_ids: Optional[List[int]] = None,
        top_k: int = 5,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        include_sources: bool = True
    ) -> Dict[str, Any]:
        """
        生成基于知识库的答案
        
        Args:
            query: 用户查询
            user_id: 用户ID
            model_id: AI模型ID
            category_ids: 限制搜索的分类ID列表
            grade_ids: 限制搜索的分级ID列表
            top_k: 检索的文档块数量
            temperature: 生成温度
            max_tokens: 最大生成token数
            include_sources: 是否包含来源信息
            
        Returns:
            包含答案和相关信息的字典
        """
        try:
            # 1. 检查用户权限
            # 需要先获取用户对象
            user_result = await self.db.execute(select(UserModel).filter(UserModel.id == user_id))
            user = user_result.scalar_one_or_none()
            if not user:
                return {
                    "success": False,
                    "answer": None,
                    "sources": [],
                    "context_used": False,
                    "documents_retrieved": 0,
                    "model_id": None,
                    "error": "User not found"
                }
            
            if not await self.permission_service.check_module_access(user):
                logger.warning(f"User {user_id} does not have access to knowledge base")
                return {
                    "success": False,
                    "answer": None,
                    "sources": [],
                    "context_used": False,
                    "documents_retrieved": 0,
                    "model_id": None,
                    "error": "Access denied to knowledge base"
                }
            
            # 2. 安全检查用户查询
            security_context = PipelineContext(
                content=query,
                user_id=user_id,
                message_type="text"
            )
            
            # 创建默认安全管道
            security_pipeline = self.security_pipeline_factory.create_default_pipeline()
            security_result = security_pipeline.execute(security_context, self.db)
            
            if security_result.is_blocked:
                reason = security_result.termination_reason or "Security policy violation"
                logger.warning(f"Query blocked by security pipeline: {reason}")
                # 记录安全违规到审计日志
                await self.audit_service.audit_logger.log_operation_async(
                    operator_id=user_id,
                    operation_type="RAG_QUERY_BLOCKED",
                    operation_details=f"Query blocked: {reason}",
                    success=False
                )
                return {
                    "success": False,
                    "answer": None,
                    "sources": [],
                    "context_used": False,
                    "documents_retrieved": 0,
                    "model_id": None,
                    "error": f"Query blocked: {reason}"
                }
            
            # 3. 检索相关文档块
            logger.info(f"Searching knowledge base for query: {query[:100]}...")
            search_results = await self.knowledge_service.search_knowledge_base(
                db=self.db,
                query=query,
                user_id=user_id,
                category_ids=category_ids,
                grade_ids=grade_ids,
                top_k=top_k * 2,  # 获取更多结果用于重排序
                threshold=0.01,  # 使用非常低的阈值以确保能检索到相关结果
                include_content=True
            )
            
            # 过滤和重排序结果
            if search_results:
                # 按相似度重新排序并过滤低质量结果
                filtered_results = []
                for result in search_results:
                    similarity = result.get('similarity', 0)
                    # 动态阈值：根据查询长度调整
                    dynamic_threshold = 0.01 if len(query) < 10 else 0.005
                    if similarity >= dynamic_threshold:
                        filtered_results.append(result)
                
                # 按相似度重新排序
                filtered_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
                search_results = filtered_results[:top_k]
                
                logger.info(f"After filtering: {len(search_results)} results")
            
            if not search_results:
                logger.info("No relevant documents found")
                return {
                    "success": True,
                    "answer": "抱歉，我在知识库中没有找到相关信息来回答您的问题。您可以尝试使用不同的关键词或者更具体的描述。",
                    "sources": [],
                    "context_used": False,
                    "documents_retrieved": 0,
                    "model_id": model_id if 'model_id' in locals() else None,
                    "error": None
                }
            
            # 4. 构建上下文
            context_chunks = []
            sources = []
            
            for result in search_results:
                chunk_content = result.get("content", "")
                chunk_metadata = result.get("metadata", {})
                
                context_chunks.append(chunk_content)
                
                if include_sources:
                    sources.append({
                        # 前端期望的字段名称 - 使用正确的映射
                        "document_id": result.get("document_id", 0),
                        "document_name": result.get("document_name", "Unknown"),  # 知识库服务返回这个字段
                        "content": chunk_content,  # 使用实际内容
                        "chunk_id": result.get("chunk_id", ""),  # 知识库服务返回这个字段
                        "similarity": result.get("similarity", 0.0),  # 知识库服务返回这个字段
                        "metadata": chunk_metadata,  # 元数据
                        # 保留原有字段用于兼容性
                        "document_title": result.get("document_name", "Unknown"),
                        "chunk_index": result.get("chunk_index", 0),  # 知识库服务返回这个字段
                        "relevance_score": result.get("similarity", 0.0)
                    })
            
            # 5. 构建增强提示
            # 限制context长度，避免prompt过长，提高响应速度
            MAX_CONTEXT_LENGTH = 8000  # 大幅增加字符数限制，确保包含足够的上下文信息
            context_text = "\n\n".join(context_chunks)
            if len(context_text) > MAX_CONTEXT_LENGTH:
                context_text = context_text[:MAX_CONTEXT_LENGTH] + "..."
                # 计算原始长度
                original_length = len("\n\n".join(context_chunks))
                logger.info(f"Context truncated from {original_length} to {MAX_CONTEXT_LENGTH} chars")
            else:
                logger.info(f"Context length: {len(context_text)} chars (within limit)")
            
            enhanced_prompt = self._build_enhanced_prompt(query, context_text)
            logger.info(f"Enhanced prompt length: {len(enhanced_prompt)} chars")
            
            # 6. 生成答案
            logger.info(f"Generating answer with LLM (model_id: {model_id}, max_tokens: {max_tokens})...")
            
            # 获取或使用配置的模型
            if not model_id:
                # 使用知识库模型配置选择模型
                model_id, model_params = await self._get_configured_model(
                    user_id=user_id,
                    category_ids=category_ids,
                    grade_ids=grade_ids
                )
                
                # 如果有配置的模型参数，更新当前参数
                if model_params:
                    if 'temperature' in model_params:
                        temperature = model_params['temperature']
                    if 'max_tokens' in model_params:
                        max_tokens = model_params['max_tokens']
            
            # 获取模型信息
            from ..db.models.llm_model import LLMModel
            llm_model_result = await self.db.execute(select(LLMModel).filter(
                LLMModel.id == model_id,
                LLMModel.is_active == True
            ))
            llm_model = llm_model_result.scalar_one_or_none()
            
            if not llm_model:
                logger.info(f"Model {model_id} not found or inactive")
                return {
                    "success": False,
                    "answer": None,
                    "sources": [],
                    "context_used": False,
                    "documents_retrieved": len(search_results) if 'search_results' in locals() else 0,
                    "model_id": model_id,
                    "error": "LLM model not available"
                }
            
            # 直接调用LLM API（不使用会话）
            messages = [
                {"role": "system", "content": "你是一个基于知识库的问答助手。请根据提供的上下文准确回答问题。"},
                {"role": "user", "content": enhanced_prompt}
            ]
            
            # 创建模型副本以避免修改原始配置
            import copy
            temp_model = copy.deepcopy(llm_model)

            # 将max_tokens参数传递给模型配置，使用合理的token限制
            optimized_max_tokens = min(max_tokens, 400)  # 统一使用适中的token限制

            if temp_model.config_params:
                temp_model.config_params['max_tokens'] = optimized_max_tokens
            else:
                temp_model.config_params = {'max_tokens': optimized_max_tokens}

            try:
                llm_response = await self.llm_service._call_llm_api(
                    llm_model=temp_model,
                    messages=messages,
                    stream=False
                )
                answer = llm_response
            except Exception as e:
                logger.info(f"LLM API call failed: {str(e)}")
                import traceback
                traceback.print_exc()
                return {
                    "success": False,
                    "answer": None,
                    "sources": sources if include_sources and 'sources' in locals() else [],
                    "context_used": len(search_results) > 0 if 'search_results' in locals() else False,
                    "documents_retrieved": len(search_results) if 'search_results' in locals() else 0,
                    "model_id": model_id,
                    "error": f"LLM API call failed: {str(e)}"
                }

            
            # 7. 对答案进行安全检查和脱敏
            answer_context = PipelineContext(
                content=answer,
                user_id=user_id,
                message_type="text"
            )
            
            # 使用相同的安全管道检查答案
            answer_result = security_pipeline.execute(answer_context, self.db)
            
            if answer_result.processed_content:
                answer = answer_result.processed_content
            
            # 8. 记录审计日志
            await self.audit_service.log_rag_query(
                db=self.db,
                user_id=user_id,
                query=query,
                documents_retrieved=len(search_results),
                answer_generated=True,
                model_used=model_id
            )
            
            return {
                "success": True,
                "answer": answer,
                "sources": sources if include_sources else None,
                "context_used": True,
                "documents_retrieved": len(search_results),
                "model_id": model_id,
                "error": None
            }
            
        except Exception as e:
            logger.info(f"Error in RAG answer generation: {str(e)}")
            return {
                "success": False,
                "answer": None,
                "sources": [],
                "context_used": False,
                "documents_retrieved": 0,
                "model_id": None,
                "error": f"Internal error: {str(e)}"
            }
    
    def _build_enhanced_prompt(self, query: str, context: str) -> str:
        """
        构建通用的增强提示词

        Args:
            query: 用户查询
            context: 检索到的上下文

        Returns:
            增强的提示词
        """
        # 检测是否是文档列表查询
        list_queries = ["知识库中有什么文档", "知识库有哪些文档", "文档列表", "所有文档", "有什么文档"]
        is_document_list_query = any(q in query.lower() for q in list_queries)
        
        if is_document_list_query:
            prompt = f"""你是一个专业的知识库助手。用户询问知识库中的文档情况。

以下是从知识库中检索到的文档片段信息：
{context}

用户问题：{query}

请基于检索到的信息，总结知识库中包含的文档类型和主要内容。请按照以下格式回答：

知识库中包含以下类型的文档：
1. [文档类型] - [主要内容简述]
2. [文档类型] - [主要内容简述]
...

如果能从文档片段中识别出具体的文档名称，请列出来。请避免重复列出相同的文档。"""
        else:
            prompt = f"""你是一个专业的知识库助手。请根据以下文档内容准确回答用户问题。

文档内容：
{context}

用户问题：{query}

请仔细阅读文档内容，基于文档中的具体信息进行回答。如果文档包含相关信息，请详细列出；如果文档中没有相关信息，请明确说明。"""
        
        return prompt


    async def get_rag_statistics(self, user_id: int) -> Dict[str, Any]:
        """
        获取RAG使用统计
        
        Args:
            user_id: 用户ID
            
        Returns:
            统计信息
        """
        try:
            # 这里可以从审计日志中统计RAG使用情况
            # 暂时返回模拟数据
            return {
                "total_queries": 0,
                "successful_queries": 0,
                "failed_queries": 0,
                "average_documents_retrieved": 0,
                "most_used_categories": [],
                "query_trends": []
            }
            
        except Exception as e:
            logger.info(f"Error getting RAG statistics: {str(e)}")
            return {}

    async def _get_configured_model(
        self, 
        user_id: int, 
        category_ids: Optional[List[int]] = None,
        grade_ids: Optional[List[int]] = None
    ) -> tuple[Optional[int], Optional[Dict[str, Any]]]:
        """
        获取配置的模型ID和参数
        
        Args:
            user_id: 用户ID
            category_ids: 文档分类ID列表
            grade_ids: 文档分级ID列表
            
        Returns:
            (model_id, model_params) 元组
        """
        try:
            from ..db.models.knowledge_base_model_config import KnowledgeBaseModelConfig
            from ..db.models.llm_model import LLMModel
            
            # 确定分类和分级ID（取第一个）
            category_id = category_ids[0] if category_ids else None
            grade_id = grade_ids[0] if grade_ids else None
            
            # 使用模型配置表的静态方法获取适用配置
            config = await KnowledgeBaseModelConfig.get_applicable_config(
                self.db, user_id, category_id, grade_id
            )
            
            if config:
                logger.info(f"Using configured model: {config.config_name} (model_id: {config.model_id}, type: {config.config_type})")
                return config.model_id, config.model_params
            
            # 如果没有找到配置，使用默认模型选择逻辑
            logger.info("No model configuration found, using default model selection")
            
            # 优先使用阿里云通义千问-Plus模型（性能更好，更稳定）
            preferred_result = await self.db.execute(select(LLMModel).filter(
                LLMModel.is_active == True,
                LLMModel.name.like('%阿里云%')
            ))
            preferred_model = preferred_result.scalar_one_or_none()
            
            if preferred_model:
                logger.info(f"Using preferred Aliyun model: {preferred_model.name} (ID: {preferred_model.id})")
                return preferred_model.id, None
            
            # 如果没有阿里云模型，则使用本地qwen:0.5b模型（避免超时）
            local_result = await self.db.execute(select(LLMModel).filter(
                LLMModel.is_active == True,
                LLMModel.api_url.like('%localhost%'),
                LLMModel.name.like('%0.5b%')
            ))
            local_model = local_result.scalar_one_or_none()
            
            if local_model:
                logger.info(f"Using local fast model: {local_model.name} (ID: {local_model.id})")
                return local_model.id, None
            
            # 最后才使用任意活跃模型
            default_result = await self.db.execute(select(LLMModel).filter(
                LLMModel.is_active == True
            ))
            default_model = default_result.scalar_one_or_none()
            
            if default_model:
                logger.info(f"Using fallback model: {default_model.name} (ID: {default_model.id})")
                return default_model.id, None
            
            logger.warning("No active LLM models found")
            return None, None
            
        except Exception as e:
            logger.error(f"Error getting configured model: {str(e)}")
            # 发生错误时返回None，让上层处理
            return None, None