# app/services/document_parser_production.py

"""
生产级别的文档解析器服务
支持多种文档格式的解析和文本提取
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime
import mimetypes
import hashlib
from abc import ABC, abstractmethod
import tempfile
import shutil

# 条件导入各种文档解析库
try:
    import pypdf
    from pypdf import PdfReader
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logging.warning("pypdf not available, PDF parsing will be disabled")

try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logging.warning("python-docx not available, Word parsing will be disabled")

try:
    import openpyxl
    from openpyxl import load_workbook
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logging.warning("openpyxl not available, Excel parsing will be disabled")

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False
    logging.warning("python-pptx not available, PowerPoint parsing will be disabled")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logging.warning("pandas not available, advanced Excel parsing will be limited")

# OCR服务
try:
    from .ocr_service import ocr_service
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logging.warning("OCR service not available")

logger = logging.getLogger(__name__)


class BaseParser(ABC):
    """文档解析器基类"""
    
    @abstractmethod
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析该文件"""
        pass
    
    @abstractmethod
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文档，返回结构化内容"""
        pass
    
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """提取文件元数据"""
        stat = os.stat(file_path)
        return {
            'file_size': stat.st_size,
            'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
        }


class TextParser(BaseParser):
    """纯文本文件解析器"""
    
    SUPPORTED_ENCODINGS = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii', 'latin-1']
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return (mime_type in ['text/plain', 'text/csv', 'text/markdown'] or 
                file_path.lower().endswith(('.txt', '.md', '.csv', '.log')))
    
    def detect_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        for encoding in self.SUPPORTED_ENCODINGS:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)  # 读取前1KB测试
                return encoding
            except UnicodeDecodeError:
                continue
        return 'utf-8'  # 默认
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文本文件"""
        try:
            encoding = self.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            # 提取基本统计信息
            lines = content.split('\n')
            words = content.split()
            
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'file_type': 'text',
                'encoding': encoding,
                'char_count': len(content),
                'line_count': len(lines),
                'word_count': len(words),
                'avg_line_length': len(content) / len(lines) if lines else 0
            })
            
            return {
                'content': content,
                'metadata': metadata,
                'sections': [{'content': content, 'page': 1}]
            }
        except Exception as e:
            logger.error(f"Failed to parse text file {file_path}: {str(e)}")
            raise


class PDFParser(BaseParser):
    """PDF文件解析器"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return PDF_AVAILABLE and (mime_type == 'application/pdf' or 
                                 file_path.lower().endswith('.pdf'))
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        if not PDF_AVAILABLE:
            raise ImportError("pypdf is not installed")
        
        try:
            content_parts = []
            sections = []
            
            with open(file_path, 'rb') as f:
                pdf = PdfReader(f)
                
                # 提取PDF元数据
                pdf_metadata = {
                    'page_count': len(pdf.pages),
                    'is_encrypted': pdf.is_encrypted,
                }
                
                # 尝试提取PDF信息
                if pdf.metadata:
                    pdf_metadata.update({
                        'title': pdf.metadata.get('/Title', ''),
                        'author': pdf.metadata.get('/Author', ''),
                        'subject': pdf.metadata.get('/Subject', ''),
                        'creator': pdf.metadata.get('/Creator', ''),
                        'producer': pdf.metadata.get('/Producer', ''),
                    })
                
                # 提取每页内容
                for i, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            content_parts.append(page_text)
                            sections.append({
                                'content': page_text,
                                'page': i + 1,
                                'char_count': len(page_text)
                            })
                    except Exception as e:
                        logger.warning(f"Failed to extract text from page {i+1}: {str(e)}")
            
            content = '\n\n'.join(content_parts)

            # 如果没有提取到文本内容，尝试使用OCR
            if not content.strip() and OCR_AVAILABLE:
                logger.info(f"No text extracted from PDF {file_path}, trying OCR...")
                try:
                    ocr_result = ocr_service.extract_text_from_pdf_with_ocr(file_path)
                    content = ocr_result['content']
                    sections = ocr_result['sections']
                    pdf_metadata.update(ocr_result['metadata'])
                    pdf_metadata['ocr_used'] = True
                    logger.info(f"OCR extracted {len(content)} characters from PDF")
                except Exception as e:
                    logger.warning(f"OCR processing failed: {str(e)}")
                    pdf_metadata['ocr_used'] = False
                    pdf_metadata['ocr_error'] = str(e)
            else:
                pdf_metadata['ocr_used'] = False

            metadata = self.extract_metadata(file_path)
            metadata.update({
                'file_type': 'pdf',
                **pdf_metadata,
                'total_chars': len(content),
                'has_text': bool(content.strip())
            })

            return {
                'content': content,
                'metadata': metadata,
                'sections': sections
            }
            
        except Exception as e:
            logger.error(f"Failed to parse PDF file {file_path}: {str(e)}")
            raise


class WordParser(BaseParser):
    """Word文档解析器"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return DOCX_AVAILABLE and (
            mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                         'application/msword'] or 
            file_path.lower().endswith(('.docx', '.doc'))
        )
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析Word文档"""
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx is not installed")
        
        try:
            doc = DocxDocument(file_path)
            
            # 提取文本内容
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text)
            
            # 提取表格内容
            tables_text = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    if any(row_data):
                        table_data.append(' | '.join(row_data))
                if table_data:
                    tables_text.append('\n'.join(table_data))
            
            # 合并内容
            content_parts = paragraphs
            if tables_text:
                content_parts.extend(['\n[表格]\n' + table for table in tables_text])
            
            content = '\n\n'.join(content_parts)
            
            # 提取元数据
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'file_type': 'word',
                'paragraph_count': len(paragraphs),
                'table_count': len(doc.tables),
                'has_images': bool(doc.inline_shapes),
                'char_count': len(content),
                'word_count': len(content.split())
            })
            
            # 尝试提取文档属性
            try:
                core_props = doc.core_properties
                metadata.update({
                    'title': core_props.title or '',
                    'author': core_props.author or '',
                    'subject': core_props.subject or '',
                    'keywords': core_props.keywords or '',
                    'created': core_props.created.isoformat() if core_props.created else '',
                    'modified': core_props.modified.isoformat() if core_props.modified else '',
                })
            except:
                pass
            
            return {
                'content': content,
                'metadata': metadata,
                'sections': [{'content': content, 'page': 1}]
            }
            
        except Exception as e:
            logger.error(f"Failed to parse Word file {file_path}: {str(e)}")
            raise


class ExcelParser(BaseParser):
    """Excel文件解析器"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return EXCEL_AVAILABLE and (
            mime_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                         'application/vnd.ms-excel'] or 
            file_path.lower().endswith(('.xlsx', '.xls'))
        )
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析Excel文件"""
        if not EXCEL_AVAILABLE:
            raise ImportError("openpyxl is not installed")
        
        try:
            wb = load_workbook(file_path, read_only=True, data_only=True)
            
            content_parts = []
            sections = []
            total_rows = 0
            total_cells = 0
            
            # 处理每个工作表
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                sheet_content = [f"## 工作表: {sheet_name}\n"]
                
                # 提取数据
                rows_data = []
                for row in sheet.iter_rows(values_only=True):
                    # 过滤空行
                    if any(cell is not None for cell in row):
                        row_text = ' | '.join(str(cell) if cell is not None else '' for cell in row)
                        rows_data.append(row_text)
                        total_cells += len(row)
                
                total_rows += len(rows_data)
                
                if rows_data:
                    sheet_content.extend(rows_data[:100])  # 限制每个表的行数
                    if len(rows_data) > 100:
                        sheet_content.append(f"\n... 还有 {len(rows_data) - 100} 行数据 ...\n")
                    
                    sheet_text = '\n'.join(sheet_content)
                    content_parts.append(sheet_text)
                    sections.append({
                        'content': sheet_text,
                        'sheet': sheet_name,
                        'row_count': len(rows_data)
                    })
            
            content = '\n\n'.join(content_parts)
            
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'file_type': 'excel',
                'sheet_count': len(wb.sheetnames),
                'total_rows': total_rows,
                'total_cells': total_cells,
                'char_count': len(content)
            })
            
            wb.close()
            
            return {
                'content': content,
                'metadata': metadata,
                'sections': sections
            }
            
        except Exception as e:
            logger.error(f"Failed to parse Excel file {file_path}: {str(e)}")
            raise


class PPTParser(BaseParser):
    """PowerPoint文件解析器"""
    
    def can_parse(self, file_path: str, mime_type: str) -> bool:
        """判断是否可以解析"""
        return PPTX_AVAILABLE and (
            mime_type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation',
                         'application/vnd.ms-powerpoint'] or 
            file_path.lower().endswith(('.pptx', '.ppt'))
        )
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析PowerPoint文件"""
        if not PPTX_AVAILABLE:
            raise ImportError("python-pptx is not installed")
        
        try:
            prs = Presentation(file_path)
            
            content_parts = []
            sections = []
            
            # 处理每个幻灯片
            for i, slide in enumerate(prs.slides):
                slide_content = [f"## 幻灯片 {i+1}\n"]
                
                # 提取文本框内容
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        slide_content.append(shape.text.strip())
                    
                    # 处理表格
                    if shape.has_table:
                        table_data = []
                        for row in shape.table.rows:
                            row_text = ' | '.join(cell.text.strip() for cell in row.cells)
                            if row_text.strip():
                                table_data.append(row_text)
                        if table_data:
                            slide_content.append('\n[表格]\n' + '\n'.join(table_data))
                
                if len(slide_content) > 1:  # 有实际内容
                    slide_text = '\n\n'.join(slide_content)
                    content_parts.append(slide_text)
                    sections.append({
                        'content': slide_text,
                        'slide': i + 1,
                        'has_images': any(shape.shape_type == 13 for shape in slide.shapes)  # 13 = PICTURE
                    })
            
            content = '\n\n'.join(content_parts)
            
            metadata = self.extract_metadata(file_path)
            metadata.update({
                'file_type': 'ppt',
                'slide_count': len(prs.slides),
                'char_count': len(content)
            })
            
            # 尝试提取演示文稿属性
            try:
                core_props = prs.core_properties
                metadata.update({
                    'title': core_props.title or '',
                    'author': core_props.author or '',
                    'subject': core_props.subject or '',
                    'created': core_props.created.isoformat() if core_props.created else '',
                    'modified': core_props.modified.isoformat() if core_props.modified else '',
                })
            except:
                pass
            
            return {
                'content': content,
                'metadata': metadata,
                'sections': sections
            }
            
        except Exception as e:
            logger.error(f"Failed to parse PPT file {file_path}: {str(e)}")
            raise


class ProductionDocumentParser:
    """生产级文档解析器主类"""
    
    def __init__(self):
        """初始化解析器"""
        # 注册所有可用的解析器
        self.parsers = []
        
        # 按优先级添加解析器
        self.parsers.append(TextParser())
        
        if PDF_AVAILABLE:
            self.parsers.append(PDFParser())
        
        if DOCX_AVAILABLE:
            self.parsers.append(WordParser())
        
        if EXCEL_AVAILABLE:
            self.parsers.append(ExcelParser())
        
        if PPTX_AVAILABLE:
            self.parsers.append(PPTParser())
        
        # 文档处理配置
        self.chunk_size = 1000  # 生产环境使用更大的块
        self.chunk_overlap = 200  # 更大的重叠以保持上下文
        self.max_file_size = 100 * 1024 * 1024  # 100MB限制
        
        logger.info(f"Production document parser initialized with {len(self.parsers)} parsers")
    
    def get_mime_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    def get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def validate_file(self, file_path: str) -> None:
        """验证文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size})")
        
        if file_size == 0:
            raise ValueError("File is empty")
    
    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """
        解析文档
        
        Args:
            file_path: 文档路径
            
        Returns:
            解析结果，包含content、metadata、sections等
        """
        # 验证文件
        self.validate_file(file_path)
        
        # 获取文件信息
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        mime_type = self.get_mime_type(file_path)
        file_hash = self.get_file_hash(file_path)
        
        # 查找合适的解析器
        parser = None
        for p in self.parsers:
            if p.can_parse(file_path, mime_type):
                parser = p
                break
        
        if not parser:
            # 尝试作为文本文件处理
            logger.warning(f"No specific parser for {mime_type}, trying text parser")
            parser = TextParser()
        
        # 解析文档
        try:
            result = parser.parse(file_path)
            
            # 添加通用元数据
            result['file_info'] = {
                'file_name': file_name,
                'file_path': file_path,
                'file_size': file_size,
                'file_hash': file_hash,
                'mime_type': mime_type,
                'parsed_at': datetime.utcnow().isoformat(),
                'parser_used': parser.__class__.__name__
            }
            
            logger.info(f"Successfully parsed document: {file_name} using {parser.__class__.__name__}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to parse document {file_name}: {str(e)}")
            raise
    
    def split_text_into_chunks(
        self, 
        text: str, 
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None,
        separator: str = "\n\n"
    ) -> List[Dict[str, Any]]:
        """
        智能文本分块，优先在段落边界分割
        
        Args:
            text: 要分割的文本
            chunk_size: 块大小（字符数）
            chunk_overlap: 块之间的重叠字符数
            separator: 优先分割符
            
        Returns:
            文本块列表
        """
        chunk_size = chunk_size or self.chunk_size
        chunk_overlap = chunk_overlap or self.chunk_overlap
        
        if chunk_size <= 0:
            raise ValueError("Chunk size must be positive")
        
        if chunk_overlap >= chunk_size:
            raise ValueError("Chunk overlap must be less than chunk size")
        
        # 首先尝试按段落分割
        paragraphs = text.split(separator)
        
        chunks = []
        current_chunk = []
        current_size = 0
        chunk_index = 0
        
        for para in paragraphs:
            para_size = len(para)
            
            # 如果单个段落超过块大小，需要进一步分割
            if para_size > chunk_size:
                # 先保存当前块
                if current_chunk:
                    chunk_content = separator.join(current_chunk)
                    chunks.append({
                        'content': chunk_content,
                        'chunk_index': chunk_index,
                        'start_char': len(separator.join(paragraphs[:paragraphs.index(para)])),
                        'end_char': len(separator.join(paragraphs[:paragraphs.index(para)])) + len(chunk_content),
                        'char_count': len(chunk_content)
                    })
                    chunk_index += 1
                    current_chunk = []
                    current_size = 0
                
                # 分割大段落
                words = para.split()
                temp_chunk = []
                temp_size = 0
                
                for word in words:
                    if temp_size + len(word) + 1 > chunk_size:
                        if temp_chunk:
                            chunk_content = ' '.join(temp_chunk)
                            chunks.append({
                                'content': chunk_content,
                                'chunk_index': chunk_index,
                                'char_count': len(chunk_content)
                            })
                            chunk_index += 1
                            
                            # 保留重叠部分
                            overlap_words = int(len(temp_chunk) * chunk_overlap / temp_size)
                            temp_chunk = temp_chunk[-overlap_words:] if overlap_words > 0 else []
                            temp_size = sum(len(w) + 1 for w in temp_chunk)
                    
                    temp_chunk.append(word)
                    temp_size += len(word) + 1
                
                if temp_chunk:
                    chunk_content = ' '.join(temp_chunk)
                    chunks.append({
                        'content': chunk_content,
                        'chunk_index': chunk_index,
                        'char_count': len(chunk_content)
                    })
                    chunk_index += 1
            
            # 正常大小的段落
            elif current_size + para_size + len(separator) > chunk_size:
                # 保存当前块
                if current_chunk:
                    chunk_content = separator.join(current_chunk)
                    chunks.append({
                        'content': chunk_content,
                        'chunk_index': chunk_index,
                        'char_count': len(chunk_content)
                    })
                    chunk_index += 1
                
                # 开始新块，包含重叠
                if chunk_overlap > 0 and current_chunk:
                    # 计算需要保留的段落数
                    overlap_size = 0
                    overlap_paras = []
                    for i in range(len(current_chunk) - 1, -1, -1):
                        overlap_size += len(current_chunk[i])
                        overlap_paras.insert(0, current_chunk[i])
                        if overlap_size >= chunk_overlap:
                            break
                    current_chunk = overlap_paras
                    current_size = overlap_size
                else:
                    current_chunk = []
                    current_size = 0
                
                current_chunk.append(para)
                current_size += para_size + len(separator)
            else:
                current_chunk.append(para)
                current_size += para_size + len(separator)
        
        # 保存最后一个块
        if current_chunk:
            chunk_content = separator.join(current_chunk)
            chunks.append({
                'content': chunk_content,
                'chunk_index': chunk_index,
                'char_count': len(chunk_content)
            })
        
        logger.info(f"Split text into {len(chunks)} chunks")
        return chunks
    
    def process_document_for_vectorization(
        self, 
        file_path: str,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        处理文档以准备向量化
        
        Args:
            file_path: 文档路径
            chunk_size: 块大小
            chunk_overlap: 块重叠大小
            
        Returns:
            处理结果，包含文档信息和文本块
        """
        # 解析文档
        parsed_doc = self.parse_document(file_path)
        
        # 提取全文
        full_content = parsed_doc['content']
        
        # 智能分块
        chunks = self.split_text_into_chunks(
            full_content, 
            chunk_size, 
            chunk_overlap
        )
        
        # 为每个块添加元数据
        for i, chunk in enumerate(chunks):
            chunk['metadata'] = {
                'file_name': parsed_doc['file_info']['file_name'],
                'file_type': parsed_doc['metadata']['file_type'],
                'file_hash': parsed_doc['file_info']['file_hash'],
                'parser_used': parsed_doc['file_info']['parser_used']
            }
            
            # 如果有页面信息，尝试关联
            if 'sections' in parsed_doc and parsed_doc['sections']:
                # 简单的页面关联逻辑
                char_count = 0
                for section in parsed_doc['sections']:
                    section_chars = len(section.get('content', ''))
                    if char_count <= chunk.get('start_char', 0) < char_count + section_chars:
                        if 'page' in section:
                            chunk['metadata']['page'] = section['page']
                        if 'sheet' in section:
                            chunk['metadata']['sheet'] = section['sheet']
                        if 'slide' in section:
                            chunk['metadata']['slide'] = section['slide']
                        break
                    char_count += section_chars
        
        return {
            'document_info': parsed_doc['file_info'],
            'metadata': parsed_doc['metadata'],
            'chunks': chunks,
            'total_chunks': len(chunks),
            'total_chars': len(full_content),
            'sections': parsed_doc.get('sections', [])
        }


# 创建全局实例
production_document_parser = ProductionDocumentParser() 