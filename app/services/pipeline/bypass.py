"""
绕过管理器
用于管理安全模块的绕过功能
"""

from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class BypassManager:
    """绕过管理器"""
    
    def __init__(self):
        self.bypasses: Dict[str, Dict[str, Any]] = {}
        
    def add_bypass(self, module_name: str, reason: str, authorized_by: str = "system") -> None:
        """添加绕过规则"""
        self.bypasses[module_name] = {
            "reason": reason,
            "authorized_by": authorized_by,
            "active": True
        }
        logger.info(f"添加绕过规则: {module_name} - {reason}")
        
    def remove_bypass(self, module_name: str) -> bool:
        """移除绕过规则"""
        if module_name in self.bypasses:
            del self.bypasses[module_name]
            logger.info(f"移除绕过规则: {module_name}")
            return True
        return False
        
    def is_bypassed(self, module_name: str) -> bool:
        """检查模块是否被绕过"""
        return module_name in self.bypasses and self.bypasses[module_name].get("active", False)
        
    def get_bypass_reason(self, module_name: str) -> Optional[str]:
        """获取绕过原因"""
        if self.is_bypassed(module_name):
            return self.bypasses[module_name].get("reason", "未知原因")
        return None
        
    def list_bypasses(self) -> List[Dict[str, Any]]:
        """列出所有绕过规则"""
        return [
            {
                "module_name": module_name,
                **bypass_info
            }
            for module_name, bypass_info in self.bypasses.items()
        ]
        
    def clear_all(self) -> None:
        """清除所有绕过规则"""
        self.bypasses.clear()
        logger.info("清除所有绕过规则")


# 全局绕过管理器实例
bypass_manager = BypassManager() 