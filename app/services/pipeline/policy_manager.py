"""
基于角色的安全策略管理器
为不同角色和级别的用户提供差异化的安全策略配置
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from sqlalchemy.orm import Session
import logging

from app.db.models.role import Role
from app.db.models.level import Level
from app.db.models.user import User

logger = logging.getLogger(__name__)


@dataclass
class SecurityPolicy:
    """安全策略配置"""
    # 基础配置
    enabled: bool = True
    max_severity_threshold: str = "high"  # low, medium, high, critical
    enable_early_termination: bool = True
    timeout_seconds: int = 30
    
    # 模块配置
    enabled_modules: List[str] = field(default_factory=lambda: [
        "keyword_filter", "regex_pattern", "malicious_intent", "data_desensitization"
    ])
    disabled_modules: List[str] = field(default_factory=list)
    
    # 模块特定配置
    module_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 绕过配置  
    bypass_enabled: bool = False
    bypassable_modules: List[str] = field(default_factory=list)
    
    # 特定行为配置
    auto_desensitize: bool = True
    block_on_critical: bool = True
    log_all_requests: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "enabled": self.enabled,
            "max_severity_threshold": self.max_severity_threshold,
            "enable_early_termination": self.enable_early_termination,
            "timeout_seconds": self.timeout_seconds,
            "enabled_modules": self.enabled_modules,
            "disabled_modules": self.disabled_modules,
            "module_configs": self.module_configs,
            "bypass_enabled": self.bypass_enabled,
            "bypassable_modules": self.bypassable_modules,
            "auto_desensitize": self.auto_desensitize,
            "block_on_critical": self.block_on_critical,
            "log_all_requests": self.log_all_requests
        }


class RoleBasedPolicyManager:
    """基于角色的策略管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("security.policy_manager")
        self._default_policies = self._init_default_policies()
        self._role_policies: Dict[int, SecurityPolicy] = {}
        self._level_policies: Dict[int, SecurityPolicy] = {}
        self._user_policies: Dict[int, SecurityPolicy] = {}
        
    def _init_default_policies(self) -> Dict[str, SecurityPolicy]:
        """初始化默认策略"""
        return {
            # 管理员策略 - 最宽松
            "admin": SecurityPolicy(
                max_severity_threshold="critical",
                enable_early_termination=False,
                timeout_seconds=60,
                bypass_enabled=True,
                bypassable_modules=["keyword_filter", "regex_pattern"],
                auto_desensitize=False,
                block_on_critical=False
            ),
            
            # 普通用户策略 - 标准
            "user": SecurityPolicy(
                max_severity_threshold="medium",
                enable_early_termination=True,
                timeout_seconds=30,
                bypass_enabled=False,
                auto_desensitize=True,
                block_on_critical=True
            ),
            
            # 访客策略 - 最严格
            "guest": SecurityPolicy(
                max_severity_threshold="low",
                enable_early_termination=True,
                timeout_seconds=15,
                bypass_enabled=False,
                auto_desensitize=True,
                block_on_critical=True,
                enabled_modules=["keyword_filter", "regex_pattern", "malicious_intent", "data_desensitization"]
            ),
            
            # 默认策略
            "default": SecurityPolicy()
        }
        
    def get_policy_for_user(self, user_id: int, db: Session) -> SecurityPolicy:
        """获取用户的安全策略"""
        try:
            # 1. 检查用户特定策略
            if user_id in self._user_policies:
                self.logger.debug(f"使用用户 {user_id} 的特定策略")
                return self._user_policies[user_id]
                
            # 2. 获取用户信息
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                self.logger.warning(f"用户 {user_id} 不存在，使用默认策略")
                return self._default_policies["default"]
                
            # 3. 检查级别特定策略
            if user.level_id and user.level_id in self._level_policies:
                self.logger.debug(f"使用用户 {user_id} 级别 {user.level_id} 的策略")
                return self._level_policies[user.level_id]
                
            # 4. 检查角色特定策略
            if user.role_id and user.role_id in self._role_policies:
                self.logger.debug(f"使用用户 {user_id} 角色 {user.role_id} 的策略")
                return self._role_policies[user.role_id]
                
            # 5. 根据角色名称使用默认策略
            if user.role:
                role_name = user.role.name.lower()
                if "admin" in role_name or "管理员" in role_name:
                    policy_key = "admin"
                elif "user" in role_name or "用户" in role_name:
                    policy_key = "user"
                elif "guest" in role_name or "访客" in role_name:
                    policy_key = "guest"
                else:
                    policy_key = "default"
                    
                self.logger.debug(f"根据角色名称 '{user.role.name}' 使用 '{policy_key}' 策略")
                return self._default_policies[policy_key]
                
            # 6. 使用默认策略
            self.logger.debug(f"用户 {user_id} 使用默认策略")
            return self._default_policies["default"]
            
        except Exception as e:
            self.logger.error(f"获取用户 {user_id} 策略时出错: {str(e)}", exc_info=True)
            return self._default_policies["default"]
            
    def set_role_policy(self, role_id: int, policy: SecurityPolicy) -> None:
        """设置角色的安全策略"""
        self._role_policies[role_id] = policy
        self.logger.info(f"设置角色 {role_id} 的安全策略")
        
    def set_level_policy(self, level_id: int, policy: SecurityPolicy) -> None:
        """设置级别的安全策略"""
        self._level_policies[level_id] = policy
        self.logger.info(f"设置级别 {level_id} 的安全策略")
        
    def set_user_policy(self, user_id: int, policy: SecurityPolicy) -> None:
        """设置用户的特定安全策略"""
        self._user_policies[user_id] = policy
        self.logger.info(f"设置用户 {user_id} 的特定安全策略")
        
    def remove_role_policy(self, role_id: int) -> bool:
        """移除角色策略"""
        if role_id in self._role_policies:
            del self._role_policies[role_id]
            self.logger.info(f"移除角色 {role_id} 的安全策略")
            return True
        return False
        
    def remove_level_policy(self, level_id: int) -> bool:
        """移除级别策略"""
        if level_id in self._level_policies:
            del self._level_policies[level_id]
            self.logger.info(f"移除级别 {level_id} 的安全策略")
            return True
        return False
        
    def remove_user_policy(self, user_id: int) -> bool:
        """移除用户特定策略"""
        if user_id in self._user_policies:
            del self._user_policies[user_id]
            self.logger.info(f"移除用户 {user_id} 的特定安全策略")
            return True
        return False
        
    def get_policy_summary(self, db: Session) -> Dict[str, Any]:
        """获取策略配置摘要"""
        return {
            "default_policies": list(self._default_policies.keys()),
            "role_policies": len(self._role_policies),
            "level_policies": len(self._level_policies),
            "user_policies": len(self._user_policies),
            "role_policy_details": {
                role_id: policy.to_dict() 
                for role_id, policy in self._role_policies.items()
            },
            "level_policy_details": {
                level_id: policy.to_dict()
                for level_id, policy in self._level_policies.items()
            }
        }
        
    def validate_policy(self, policy: SecurityPolicy) -> Dict[str, Any]:
        """验证策略配置是否有效"""
        validation = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证超时时间
        if policy.timeout_seconds <= 0:
            validation["errors"].append("超时时间必须大于0")
            validation["valid"] = False
            
        # 验证阈值
        valid_thresholds = ["low", "medium", "high", "critical"]
        if policy.max_severity_threshold not in valid_thresholds:
            validation["errors"].append(f"无效的严重性阈值: {policy.max_severity_threshold}")
            validation["valid"] = False
            
        # 检查模块冲突
        conflicting_modules = set(policy.enabled_modules) & set(policy.disabled_modules)
        if conflicting_modules:
            validation["errors"].append(f"模块配置冲突: {conflicting_modules}")
            validation["valid"] = False
            
        # 警告检查
        if policy.bypass_enabled and policy.block_on_critical:
            validation["warnings"].append("绕过启用时阻止严重威胁可能无效")
            
        return validation
        
    def load_policies_from_db(self, db: Session) -> None:
        """从数据库加载策略配置（如果实现了策略存储表）"""
        # TODO: 实现从数据库加载策略的逻辑
        # 这需要创建策略配置表来持久化策略设置
        self.logger.info("策略加载功能待实现")
        
    def save_policies_to_db(self, db: Session) -> None:
        """保存策略配置到数据库（如果实现了策略存储表）"""
        # TODO: 实现保存策略到数据库的逻辑
        self.logger.info("策略保存功能待实现") 