"""
安全模块包
包含各种具体的安全检测模块实现
"""

# 延迟导入以避免循环依赖
def get_keyword_filter_module():
    from .keyword_filter import KeywordFilterModule
    return KeywordFilterModule

def get_regex_pattern_module():
    from .regex_pattern import RegexPatternModule
    return RegexPatternModule

def get_data_desensitization_module():
    from .data_desensitization import DataDesensitizationModule
    return DataDesensitizationModule

__all__ = [
    'get_keyword_filter_module',
    'get_regex_pattern_module', 
    'get_data_desensitization_module'
] 