"""
正则表达式模式匹配安全模块
将现有的正则表达式恶意意图检测功能集成到安全管道中
"""

from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
import logging

from app import models
from ..base import SecurityModule
from ..context import PipelineContext
from ..result import ModuleResult, SecurityLevel

logger = logging.getLogger(__name__)


class RegexPatternModule(SecurityModule):
    """正则模式匹配安全模块"""
    
    def __init__(self, enabled: bool = True, priority: int = 20):
        super().__init__(name="regex_pattern", enabled=enabled, priority=priority)
        
    def check(self, context: PipelineContext, db: Session) -> ModuleResult:
        """执行正则模式检测"""
        result = ModuleResult(
            module_name=self.name,
            passed=True,
            risk_level=SecurityLevel.LOW,
            confidence=0.0,
            execution_time_ms=0.0
        )
        
        try:
            # 构建查询
            query = db.query(models.RegexRule).filter(
                models.RegexRule.is_active == True
            )
            
            # 如果提供了用户角色，包括角色特定的规则和全局规则
            if context.user_role_id:
                query = query.filter(
                    (models.RegexRule.role_id == context.user_role_id) | 
                    (models.RegexRule.role_id.is_(None))
                )
            else:
                # 只使用全局规则
                query = query.filter(models.RegexRule.role_id.is_(None))
            
            # 按优先级排序
            active_rules = query.order_by(models.RegexRule.priority.desc()).all()
            
            matched_rules = []
            highest_severity = "low"
            total_matches = 0
            max_confidence = 0.0
            
            for rule in active_rules:
                try:
                    # 使用规则的test_pattern方法，包含超时保护
                    if rule.test_pattern(context.content):
                        confidence = self._calculate_confidence(rule, context.content)
                        
                        match_info = {
                            "rule_id": rule.id,
                            "rule_name": rule.name,
                            "category": rule.category.value,
                            "severity": rule.severity,
                            "confidence": confidence
                        }
                        
                        matched_rules.append(match_info)
                        total_matches += 1
                        max_confidence = max(max_confidence, confidence)
                        
                        # 记录发现
                        result.add_finding(
                            finding_type="regex_match",
                            description=f"检测到{rule.category.value}: {rule.name}",
                            details=match_info
                        )
                        
                        # 更新最高严重性
                        if self._severity_level(rule.severity) > self._severity_level(highest_severity):
                            highest_severity = rule.severity
                        
                        # 更新匹配统计（在数据库中）
                        rule.increment_match_count()
                        
                except Exception as rule_error:
                    self.logger.warning(f"执行正则规则 {rule.name} 时出错: {str(rule_error)}")
                    continue
                    
            # 设置检查结果
            if matched_rules:
                result.passed = False
                result.risk_level = self._map_severity_to_security_level(highest_severity)
                result.confidence = max_confidence
            else:
                result.passed = True
                result.risk_level = SecurityLevel.LOW
                result.confidence = 0.9
                
            # 记录元数据
            result.metadata.update({
                "total_rules_checked": len(active_rules),
                "matched_rules_count": len(matched_rules),
                "matched_rules": matched_rules,
                "highest_severity": highest_severity,
                "total_matches": total_matches,
                "user_role_id": context.user_role_id
            })
            
            self.logger.debug(f"正则检测完成: 检查了{len(active_rules)}个规则，发现{len(matched_rules)}个匹配")
            
            return result
            
        except Exception as e:
            error_msg = f"正则模式检测失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # 返回错误结果，但不阻止流程
            result.error = error_msg
            result.metadata["error_occurred"] = True
            
            return result
            
    def _calculate_confidence(self, rule: models.RegexRule, content: str) -> float:
        """计算匹配置信度"""
        try:
            # 基础置信度根据规则类型
            base_confidence = 0.8
            
            # 根据规则匹配历史调整置信度
            if hasattr(rule, 'accuracy_rate') and rule.accuracy_rate:
                base_confidence = rule.accuracy_rate
            
            # 根据内容长度调整（内容越长，误报可能性越小）
            content_length_factor = min(1.0, len(content) / 100)
            
            # 最终置信度
            confidence = base_confidence * (0.7 + 0.3 * content_length_factor)
            
            return max(0.5, min(1.0, confidence))
            
        except Exception:
            return 0.7  # 默认置信度
            
    def _severity_level(self, severity: str) -> int:
        """将严重性级别转换为数值"""
        return {
            "low": 1,
            "medium": 2,
            "high": 3,
            "critical": 4
        }.get(severity.lower(), 1)
        
    def _map_severity_to_security_level(self, severity: str) -> SecurityLevel:
        """将严重性映射到SecurityLevel"""
        mapping = {
            "low": SecurityLevel.LOW,
            "medium": SecurityLevel.MEDIUM,
            "high": SecurityLevel.HIGH,
            "critical": SecurityLevel.CRITICAL
        }
        return mapping.get(severity.lower(), SecurityLevel.LOW) 