"""
关键词过滤安全模块
将现有的关键词检测功能集成到安全管道中
"""

from typing import Dict, Any
from sqlalchemy.orm import Session
import logging

from app import models
from ..base import SecurityModule
from ..context import PipelineContext
from ..result import ModuleResult, SecurityLevel

logger = logging.getLogger(__name__)


class KeywordFilterModule(SecurityModule):
    """关键词过滤安全模块"""
    
    def __init__(self, enabled: bool = True, priority: int = 10):
        super().__init__(name="keyword_filter", enabled=enabled, priority=priority)
        
    def check(self, context: PipelineContext, db: Session) -> ModuleResult:
        """执行关键词检测"""
        result = ModuleResult(
            module_name=self.name,
            passed=True,
            risk_level=SecurityLevel.LOW,
            confidence=0.0,
            execution_time_ms=0.0
        )
        
        try:
            # 获取激活的关键词
            active_keywords = db.query(models.Keyword).join(models.KeywordGroup).filter(
                models.Keyword.is_active == True,
                models.KeywordGroup.is_active == True
            ).order_by(models.Keyword.priority.desc()).all()
            
            found_keywords = []
            highest_priority = 0
            
            for keyword in active_keywords:
                if keyword.matches_text(context.content):
                    found_keywords.append({
                        "word": keyword.word,
                        "group": keyword.keyword_group.name,
                        "match_type": keyword.match_type.value,
                        "priority": keyword.priority
                    })
                    
                    # 记录发现
                    result.add_finding(
                        finding_type="keyword_match",
                        description=f"检测到关键词: {keyword.word}",
                        details={
                            "keyword": keyword.word,
                            "group": keyword.keyword_group.name,
                            "match_type": keyword.match_type.value,
                            "priority": keyword.priority
                        }
                    )
                    
                    highest_priority = max(highest_priority, keyword.priority)
            
            # 设置检查结果
            if found_keywords:
                result.passed = False
                
                # 根据关键词优先级确定风险级别
                if highest_priority >= 90:
                    result.risk_level = SecurityLevel.CRITICAL
                    result.confidence = 0.95
                elif highest_priority >= 70:
                    result.risk_level = SecurityLevel.HIGH
                    result.confidence = 0.85
                elif highest_priority >= 50:
                    result.risk_level = SecurityLevel.MEDIUM
                    result.confidence = 0.75
                else:
                    result.risk_level = SecurityLevel.LOW
                    result.confidence = 0.65
            else:
                result.passed = True
                result.risk_level = SecurityLevel.LOW
                result.confidence = 0.9
                
            # 记录元数据
            result.metadata.update({
                "total_keywords_checked": len(active_keywords),
                "found_keywords_count": len(found_keywords),
                "found_keywords": found_keywords,
                "highest_priority": highest_priority
            })
            
            self.logger.debug(f"关键词检测完成: 检查了{len(active_keywords)}个关键词，发现{len(found_keywords)}个匹配")
            
            return result
            
        except Exception as e:
            error_msg = f"关键词检测失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # 备用检查
            backup_keywords = ["密码", "账号", "银行卡", "身份证", "信用卡"]
            found_backup = [kw for kw in backup_keywords if kw in context.content]
            
            if found_backup:
                result.passed = False
                result.risk_level = SecurityLevel.MEDIUM
                result.confidence = 0.7
                result.add_finding(
                    finding_type="backup_keyword_match",
                    description=f"备用关键词检测: {', '.join(found_backup)}",
                    details={"backup_keywords": found_backup}
                )
            
            result.error = error_msg
            result.metadata["backup_check_used"] = True
            
            return result 