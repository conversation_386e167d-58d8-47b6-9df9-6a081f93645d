"""
数据脱敏安全模块
将现有的数据脱敏功能集成到安全管道中
"""

from typing import Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
import logging

from app import models
from ..base import SecurityModule
from ..context import PipelineContext
from ..result import ModuleResult, SecurityLevel
from app.services.desensitization_service import desensitization_service

logger = logging.getLogger(__name__)


class DataDesensitizationModule(SecurityModule):
    """数据脱敏安全模块"""
    
    def __init__(self, enabled: bool = True, priority: int = 30):
        super().__init__(name="data_desensitization", enabled=enabled, priority=priority)
        self.auto_apply_masking = True  # 是否自动应用脱敏
        
    def check(self, context: PipelineContext, db: Session) -> ModuleResult:
        """执行数据脱敏检测和处理"""
        result = ModuleResult(
            module_name=self.name,
            passed=True,
            risk_level=SecurityLevel.LOW,
            confidence=0.0,
            execution_time_ms=0.0
        )
        
        try:
            # 检测敏感数据模式
            detection_result = desensitization_service.process_text_desensitization(
                context.content, 
                db, 
                context.user_role_id,
                apply_masking=False  # 首先只检测，不应用脱敏
            )
            
            total_matches = detection_result.get("total_matches", 0)
            detected_patterns = detection_result.get("detected_patterns", [])
            
            if total_matches > 0:
                # 有敏感数据被检测到
                result.passed = False
                
                # 根据敏感数据类型和数量确定风险级别
                risk_level, confidence = self._assess_risk_level(detected_patterns, total_matches)
                result.risk_level = risk_level
                result.confidence = confidence
                
                # 记录发现
                for pattern in detected_patterns:
                    result.add_finding(
                        finding_type="sensitive_data_detected",
                        description=f"检测到敏感数据: {pattern.get('rule_name', '未知类型')}",
                        details={
                            "rule_name": pattern.get("rule_name"),
                            "pattern_type": pattern.get("pattern_type"),
                            "matches_count": pattern.get("matches_count", 0),
                            "sample_matches": pattern.get("matches", [])[:3]  # 只显示前3个匹配
                        }
                    )
                
                # 如果启用自动脱敏，应用脱敏处理
                if self.auto_apply_masking:
                    masking_result = desensitization_service.process_text_desensitization(
                        context.content,
                        db,
                        context.user_role_id,
                        apply_masking=True
                    )
                    
                    # 更新上下文中的处理后内容
                    if masking_result.get("processed_text"):
                        context.set_shared_data("desensitized_content", masking_result["processed_text"])
                        
                        # 记录脱敏信息
                        result.metadata["desensitization_applied"] = True
                        result.metadata["original_content_length"] = len(context.content)
                        result.metadata["processed_content_length"] = len(masking_result["processed_text"])
                        
                        # 降低风险级别（因为已经应用了脱敏）
                        if result.risk_level == SecurityLevel.CRITICAL:
                            result.risk_level = SecurityLevel.HIGH
                        elif result.risk_level == SecurityLevel.HIGH:
                            result.risk_level = SecurityLevel.MEDIUM
                        elif result.risk_level == SecurityLevel.MEDIUM:
                            result.risk_level = SecurityLevel.LOW
                            
                        # 如果脱敏后风险变低，可以设置为通过
                        if result.risk_level == SecurityLevel.LOW:
                            result.passed = True
                            
            else:
                # 没有检测到敏感数据
                result.passed = True
                result.risk_level = SecurityLevel.LOW
                result.confidence = 0.9
                
            # 记录元数据
            result.metadata.update({
                "total_matches": total_matches,
                "detected_patterns_count": len(detected_patterns),
                "patterns_summary": [
                    {
                        "rule_name": p.get("rule_name"),
                        "matches_count": p.get("matches_count", 0)
                    }
                    for p in detected_patterns
                ],
                "auto_masking_enabled": self.auto_apply_masking,
                "user_role_id": context.user_role_id
            })
            
            self.logger.debug(f"数据脱敏检测完成: 发现{total_matches}个敏感数据匹配")
            
            return result
            
        except Exception as e:
            error_msg = f"数据脱敏检测失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # 返回错误结果，但不阻止流程
            result.error = error_msg
            result.metadata["error_occurred"] = True
            
            return result
            
    def _assess_risk_level(self, patterns: list, total_matches: int) -> Tuple[SecurityLevel, float]:
        """评估敏感数据的风险级别"""
        # 高危模式类型
        high_risk_patterns = ["身份证", "银行卡", "信用卡", "密码", "私钥"]
        medium_risk_patterns = ["手机号", "邮箱", "姓名", "地址"]
        
        high_risk_count = 0
        medium_risk_count = 0
        
        for pattern in patterns:
            rule_name = pattern.get("rule_name", "").lower()
            matches_count = pattern.get("matches_count", 0)
            
            if any(risk_type in rule_name for risk_type in high_risk_patterns):
                high_risk_count += matches_count
            elif any(risk_type in rule_name for risk_type in medium_risk_patterns):
                medium_risk_count += matches_count
                
        # 确定风险级别
        if high_risk_count > 0:
            if high_risk_count >= 3:
                return SecurityLevel.CRITICAL, 0.95
            else:
                return SecurityLevel.HIGH, 0.85
        elif medium_risk_count > 0:
            if medium_risk_count >= 5:
                return SecurityLevel.HIGH, 0.80
            else:
                return SecurityLevel.MEDIUM, 0.75
        elif total_matches > 0:
            return SecurityLevel.LOW, 0.70
        else:
            return SecurityLevel.LOW, 0.90
            
    def set_auto_masking(self, enabled: bool) -> None:
        """设置是否自动应用脱敏"""
        self.auto_apply_masking = enabled
        self.logger.info(f"自动脱敏设置更新为: {enabled}")
        
    def validate_config(self) -> bool:
        """验证模块配置"""
        try:
            # 验证脱敏服务是否可用
            return desensitization_service is not None
        except Exception:
            return False 