"""
统一安全策略管道
协调执行多个安全模块，提供统一的安全检查接口
"""

import time
import asyncio
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import logging

from .context import PipelineContext
from .result import PipelineResult, SecurityLevel, ActionType
from .base import SecurityModule
from .policy_manager import RoleBasedPolicyManager, SecurityPolicy
from .logger import pipeline_logger, DecisionType
from .bypass import bypass_manager, BypassManager

logger = logging.getLogger(__name__)


class SecurityPipeline:
    """统一安全策略管道"""
    
    def __init__(
        self, 
        policy_manager: Optional[RoleBasedPolicyManager] = None,
        bypass_manager_param: Optional[BypassManager] = None
    ):
        """初始化安全管道"""
        self.modules: List[SecurityModule] = []
        self.policy_manager = policy_manager or RoleBasedPolicyManager()
        self.bypass_manager = bypass_manager_param or bypass_manager
        self.logger = logging.getLogger("security.pipeline")
        
    def add_module(self, module: SecurityModule) -> None:
        """添加安全模块"""
        if not isinstance(module, SecurityModule):
            raise ValueError(f"模块必须是SecurityModule的实例: {type(module)}")
            
        self.modules.append(module)
        self.logger.debug(f"添加安全模块: {module.name}")
        
        # 按优先级重新排序
        self.modules.sort()
        
    def remove_module(self, module_name: str) -> bool:
        """移除安全模块"""
        for i, module in enumerate(self.modules):
            if module.name == module_name:
                removed_module = self.modules.pop(i)
                self.logger.debug(f"移除安全模块: {removed_module.name}")
                return True
        return False
        
    def get_module(self, module_name: str) -> Optional[SecurityModule]:
        """获取指定名称的模块"""
        for module in self.modules:
            if module.name == module_name:
                return module
        return None
        
    def list_modules(self) -> List[Dict[str, Any]]:
        """列出所有模块的配置信息"""
        return [module.get_config() for module in self.modules]
        
    def execute(self, context: PipelineContext, db: Session) -> PipelineResult:
        """
        执行安全管道检查
        
        Args:
            context: 管道执行上下文
            db: 数据库会话
            
        Returns:
            管道执行结果
        """
        self.logger.info(f"开始执行安全管道检查，内容长度: {len(context.content)}")
        
        # 设置绕过管理器到上下文
        context.bypass_manager = self.bypass_manager
        
        # 应用基于角色的策略
        policy_name = "default"
        if context.user_id:
            try:
                policy = self.policy_manager.get_policy_for_user(context.user_id, db)
                self._apply_policy_to_context(context, policy)
                policy_name = getattr(policy, 'name', f'user_{context.user_id}')
                self.logger.debug(f"为用户 {context.user_id} 应用安全策略")
            except Exception as e:
                self.logger.error(f"应用用户策略时出错: {str(e)}")
        
        # 记录管道开始
        session_id = getattr(context, 'session_id', '')
        user_id = context.user_id or 0
        active_modules = [m for m in self.modules if m.enabled]
        
        pipeline_logger.log_pipeline_start(
            session_id=session_id,
            user_id=user_id,
            content=context.content,
            policy_name=policy_name,
            modules=[m.name for m in active_modules]
        )
        
        # 初始化结果
        result = PipelineResult()
        result.original_content = context.content
        result.processed_content = context.content
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 过滤启用的模块
            active_modules = [m for m in self.modules if m.enabled]
            
            if not active_modules:
                self.logger.warning("没有启用的安全模块")
                result.finalize()
                return result
                
            self.logger.debug(f"将执行 {len(active_modules)} 个安全模块")
            
            # 执行每个模块
            for module in active_modules:
                try:
                    # 检查绕过条件
                    if context.check_bypass(module.name):
                        self.logger.info(f"模块 {module.name} 被绕过")
                        pipeline_logger.log_bypass_event(
                            session_id=session_id,
                            user_id=user_id,
                            bypass_type="module_bypass",
                            reason=f"Module {module.name} bypassed",
                            authorized_by=context.bypass_reason
                        )
                        continue
                    
                    # 记录模块开始时间
                    module_start_time = time.time()
                    
                    # 执行模块检查
                    module_result = module.execute(context, db)
                    result.add_module_result(module_result)
                    
                    # 计算模块执行时间
                    module_execution_time = (time.time() - module_start_time) * 1000
                    
                    # 记录模块执行结果
                    pipeline_logger.log_module_execution(
                        module_name=module.name,
                        session_id=session_id,
                        module_result=module_result,
                        execution_time_ms=module_execution_time
                    )
                    
                    # 记录安全决策
                    if not module_result.passed:
                        decision_type = DecisionType.BLOCK if module_result.risk_level in [SecurityLevel.CRITICAL, SecurityLevel.HIGH] else DecisionType.FLAG
                        
                        pipeline_logger.log_security_decision(
                            decision_type=decision_type,
                            risk_level=module_result.risk_level,
                            confidence=module_result.confidence,
                            reason=f"Module {module.name} failed: {module_result.error or 'Security check failed'}",
                            module_name=module.name,
                            user_id=user_id,
                            session_id=session_id,
                            details={
                                "findings": [str(f) for f in module_result.findings],
                                "execution_time_ms": module_execution_time
                            }
                        )
                    
                    # 检查是否需要早期终止
                    if (context.enable_early_termination and 
                        not module_result.passed and 
                        module_result.risk_level in [SecurityLevel.CRITICAL, SecurityLevel.HIGH]):
                        
                        self.logger.warning(
                            f"模块 {module.name} 检测到高风险({module_result.risk_level.value})，触发早期终止"
                        )
                        result.early_terminated = True
                        result.termination_reason = f"High risk detected by module {module.name}"
                        break
                        
                except Exception as e:
                    self.logger.error(f"执行模块 {module.name} 时发生错误: {str(e)}", exc_info=True)
                    pipeline_logger.log_error(
                        error_message=str(e),
                        module_name=module.name,
                        session_id=session_id,
                        exception=e
                    )
                    continue
                    
            # 记录总执行时间
            total_time = (time.time() - start_time) * 1000
            result.total_execution_time_ms = total_time
            
            # 完成结果计算
            result.finalize()
            
            # 记录管道结束
            pipeline_logger.log_pipeline_end(
                session_id=session_id,
                result=result,
                execution_time_ms=total_time
            )
            
            # 记录性能指标
            pipeline_logger.log_performance_metric(
                metric_name="pipeline_execution_time",
                value=total_time,
                unit="ms",
                context={
                    "session_id": session_id,
                    "modules_count": len(active_modules),
                    "early_terminated": result.early_terminated
                }
            )
            
            self.logger.info(
                f"管道执行完成: "
                f"blocked={result.is_blocked}, "
                f"score={result.security_score:.1f}, "
                f"action={result.recommended_action.value}, "
                f"time={total_time:.2f}ms"
            )
            
            return result
            
        except Exception as e:
            # 管道级别的错误处理
            total_time = (time.time() - start_time) * 1000
            error_msg = f"安全管道执行失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # 记录管道级别错误
            pipeline_logger.log_error(
                error_message=error_msg,
                module_name="pipeline",
                session_id=session_id,
                exception=e,
                context={
                    "execution_time_ms": total_time,
                    "modules_executed": len([r for r in result.module_results])
                }
            )
            
            result.total_execution_time_ms = total_time
            result.early_terminated = True
            result.termination_reason = error_msg
            result.overall_risk_level = SecurityLevel.LOW
            result.recommended_action = ActionType.ALLOW
            
            return result
            
    def validate_pipeline(self) -> Dict[str, Any]:
        """验证管道配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "module_count": len(self.modules),
            "enabled_modules": len([m for m in self.modules if m.enabled])
        }
        
        # 检查是否有模块
        if not self.modules:
            validation_result["warnings"].append("管道中没有安全模块")
            
        # 检查是否有启用的模块
        enabled_modules = [m for m in self.modules if m.enabled]
        if not enabled_modules:
            validation_result["warnings"].append("没有启用的安全模块")
            
        # 验证每个模块
        for module in self.modules:
            try:
                if not module.validate_config():
                    validation_result["errors"].append(f"模块 {module.name} 配置无效")
                    validation_result["valid"] = False
            except Exception as e:
                validation_result["errors"].append(f"验证模块 {module.name} 时出错: {str(e)}")
                validation_result["valid"] = False
                
        return validation_result
        
    def get_pipeline_info(self) -> Dict[str, Any]:
        """获取管道信息"""
        return {
            "total_modules": len(self.modules),
            "enabled_modules": len([m for m in self.modules if m.enabled]),
            "modules": self.list_modules(),
            "validation": self.validate_pipeline()
        }
        
    def clear_modules(self) -> None:
        """清空所有模块"""
        self.modules.clear()
        self.logger.debug("已清空所有安全模块")
        
    def __str__(self) -> str:
        return f"SecurityPipeline(modules={len(self.modules)}, enabled={len([m for m in self.modules if m.enabled])})"
        
    def __repr__(self) -> str:
        return self.__str__()
        
    def _apply_policy_to_context(self, context: PipelineContext, policy: SecurityPolicy) -> None:
        """将策略应用到执行上下文"""
        if not policy.enabled:
            # 如果策略禁用，设置绕过所有模块
            context.bypass_enabled = True
            context.bypass_reason = "Security policy disabled"
            context.bypass_modules = []  # 空列表表示绕过所有模块
            return
            
        # 应用策略配置
        context.max_severity_threshold = policy.max_severity_threshold
        context.enable_early_termination = policy.enable_early_termination
        context.timeout_seconds = policy.timeout_seconds
        
        # 应用绕过配置
        context.bypass_enabled = policy.bypass_enabled
        if policy.bypass_enabled:
            context.bypass_modules = policy.bypassable_modules
            context.bypass_reason = "Policy-based bypass enabled"
            
        # 记录策略应用信息
        context.set_metadata("applied_policy", policy.to_dict())
        context.set_metadata("policy_applied_at", time.time())
        
    def get_policy_manager(self) -> RoleBasedPolicyManager:
        """获取策略管理器"""
        return self.policy_manager
        
    def set_policy_manager(self, policy_manager: RoleBasedPolicyManager) -> None:
        """设置策略管理器"""
        self.policy_manager = policy_manager 