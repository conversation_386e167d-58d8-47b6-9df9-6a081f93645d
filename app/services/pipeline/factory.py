"""
安全管道工厂
用于创建和配置标准的安全管道
"""

from typing import List, Optional, Dict, Any
import logging

from .pipeline import SecurityPipeline
from .policy_manager import RoleBasedPolicyManager
from .modules import get_keyword_filter_module, get_regex_pattern_module, get_data_desensitization_module

logger = logging.getLogger(__name__)


class SecurityPipelineFactory:
    """安全管道工厂类"""
    
    @staticmethod
    def create_default_pipeline(
        enable_keyword_filter: bool = True,
        enable_regex_pattern: bool = True, 
        enable_data_desensitization: bool = True,
        policy_manager: Optional[RoleBasedPolicyManager] = None
    ) -> SecurityPipeline:
        """
        创建默认配置的安全管道
        
        Args:
            enable_keyword_filter: 是否启用关键词过滤
            enable_regex_pattern: 是否启用正则模式匹配
            enable_data_desensitization: 是否启用数据脱敏
            policy_manager: 策略管理器（可选）
            
        Returns:
            配置好的安全管道
        """
        # 创建管道
        pipeline = SecurityPipeline(policy_manager=policy_manager)
        
        # 添加模块（按优先级顺序）
        if enable_keyword_filter:
            KeywordFilterModule = get_keyword_filter_module()
            keyword_module = KeywordFilterModule(enabled=True, priority=10)
            pipeline.add_module(keyword_module)
            logger.debug("添加关键词过滤模块")
            
        if enable_regex_pattern:
            RegexPatternModule = get_regex_pattern_module()
            regex_module = RegexPatternModule(enabled=True, priority=20)
            pipeline.add_module(regex_module)
            logger.debug("添加正则模式匹配模块")
            
        if enable_data_desensitization:
            DataDesensitizationModule = get_data_desensitization_module()
            desensitization_module = DataDesensitizationModule(enabled=True, priority=30)
            pipeline.add_module(desensitization_module)
            logger.debug("添加数据脱敏模块")
            
        logger.info(f"创建默认安全管道: {pipeline}")
        return pipeline
        
    @staticmethod
    def create_custom_pipeline(
        modules_config: List[Dict[str, Any]],
        policy_manager: Optional[RoleBasedPolicyManager] = None
    ) -> SecurityPipeline:
        """
        根据自定义配置创建安全管道
        
        Args:
            modules_config: 模块配置列表，每个配置包含type, enabled, priority等
            policy_manager: 策略管理器（可选）
            
        Returns:
            配置好的安全管道
        """
        pipeline = SecurityPipeline(policy_manager=policy_manager)
        
        for config in modules_config:
            module_type = config.get("type")
            enabled = config.get("enabled", True)
            priority = config.get("priority", 100)
            
            module = None
            
            if module_type == "keyword_filter":
                KeywordFilterModule = get_keyword_filter_module()
                module = KeywordFilterModule(enabled=enabled, priority=priority)
            elif module_type == "regex_pattern":
                RegexPatternModule = get_regex_pattern_module()
                module = RegexPatternModule(enabled=enabled, priority=priority)
            elif module_type == "data_desensitization":
                DataDesensitizationModule = get_data_desensitization_module()
                module = DataDesensitizationModule(enabled=enabled, priority=priority)
                # 设置自动脱敏配置
                if "auto_apply_masking" in config:
                    module.set_auto_masking(config["auto_apply_masking"])
            else:
                logger.warning(f"未知的模块类型: {module_type}")
                continue
                
            if module:
                pipeline.add_module(module)
                logger.debug(f"添加自定义模块: {module_type} (enabled={enabled}, priority={priority})")
                
        logger.info(f"创建自定义安全管道: {pipeline}")
        return pipeline
        
    @staticmethod
    def create_minimal_pipeline(
        policy_manager: Optional[RoleBasedPolicyManager] = None
    ) -> SecurityPipeline:
        """
        创建最小配置的安全管道（仅关键词过滤）
        
        Args:
            policy_manager: 策略管理器（可选）
            
        Returns:
            最小配置的安全管道
        """
        pipeline = SecurityPipeline(policy_manager=policy_manager)
        
        # 只添加关键词过滤模块
        KeywordFilterModule = get_keyword_filter_module()
        keyword_module = KeywordFilterModule(enabled=True, priority=10)
        pipeline.add_module(keyword_module)
        
        logger.info(f"创建最小安全管道: {pipeline}")
        return pipeline
        
    @staticmethod
    def create_strict_pipeline(
        policy_manager: Optional[RoleBasedPolicyManager] = None
    ) -> SecurityPipeline:
        """
        创建严格配置的安全管道（所有模块高优先级）
        
        Args:
            policy_manager: 策略管理器（可选）
            
        Returns:
            严格配置的安全管道
        """
        pipeline = SecurityPipeline(policy_manager=policy_manager)
        
        # 添加所有模块，使用较高的优先级
        KeywordFilterModule = get_keyword_filter_module()
        RegexPatternModule = get_regex_pattern_module()
        DataDesensitizationModule = get_data_desensitization_module()
        
        keyword_module = KeywordFilterModule(enabled=True, priority=5)
        regex_module = RegexPatternModule(enabled=True, priority=10)
        desensitization_module = DataDesensitizationModule(enabled=True, priority=15)
        desensitization_module.set_auto_masking(True)  # 强制脱敏
        
        pipeline.add_module(keyword_module)
        pipeline.add_module(regex_module)
        pipeline.add_module(desensitization_module)
        
        logger.info(f"创建严格安全管道: {pipeline}")
        return pipeline
        
    @staticmethod
    def get_available_modules() -> List[Dict[str, Any]]:
        """获取可用的安全模块信息"""
        return [
            {
                "type": "keyword_filter",
                "name": "关键词过滤",
                "description": "检测和过滤敏感关键词",
                "default_priority": 10
            },
            {
                "type": "regex_pattern", 
                "name": "正则模式匹配",
                "description": "基于正则表达式检测恶意意图",
                "default_priority": 20
            },
            {
                "type": "data_desensitization",
                "name": "数据脱敏",
                "description": "检测和脱敏敏感数据",
                "default_priority": 30
            }
        ] 