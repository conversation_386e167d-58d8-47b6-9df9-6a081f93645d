"""
安全模块基础接口和抽象类
定义安全模块的标准接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Optional
from sqlalchemy.orm import Session
import time
import logging

from .context import PipelineContext
from .result import ModuleResult, SecurityLevel

logger = logging.getLogger(__name__)


class SecurityModule(ABC):
    """安全模块抽象基类"""
    
    def __init__(self, name: str, enabled: bool = True, priority: int = 100):
        """
        初始化安全模块
        
        Args:
            name: 模块名称
            enabled: 是否启用
            priority: 优先级（数字越小优先级越高）
        """
        self.name = name
        self.enabled = enabled
        self.priority = priority
        self.logger = logging.getLogger(f"security.module.{name}")
        
    @abstractmethod
    def check(self, context: PipelineContext, db: Session) -> ModuleResult:
        """
        执行安全检查
        
        Args:
            context: 管道执行上下文
            db: 数据库会话
            
        Returns:
            模块执行结果
        """
        pass
        
    def execute(self, context: PipelineContext, db: Session) -> ModuleResult:
        """
        执行模块检查的包装方法，包含错误处理和性能监控
        
        Args:
            context: 管道执行上下文  
            db: 数据库会话
            
        Returns:
            模块执行结果
        """
        start_time = time.time()
        
        try:
            # 检查模块是否启用
            if not self.enabled:
                self.logger.debug(f"模块 {self.name} 已禁用，跳过执行")
                return ModuleResult(
                    module_name=self.name,
                    passed=True,
                    risk_level=SecurityLevel.LOW,
                    confidence=0.0,
                    execution_time_ms=0.0,
                    metadata={"status": "disabled"}
                )
                
            # 检查是否启用绕过
            if context.is_bypass_enabled_for_module(self.name):
                self.logger.info(f"模块 {self.name} 绕过已启用: {context.bypass_reason}")
                return ModuleResult(
                    module_name=self.name,
                    passed=True,
                    risk_level=SecurityLevel.LOW,
                    confidence=0.0,
                    execution_time_ms=0.0,
                    metadata={
                        "status": "bypassed",
                        "bypass_reason": context.bypass_reason
                    }
                )
                
            # 检查超时
            if context.is_timeout():
                self.logger.warning(f"管道执行超时，跳过模块 {self.name}")
                return ModuleResult(
                    module_name=self.name,
                    passed=True,
                    risk_level=SecurityLevel.LOW,
                    confidence=0.0,
                    execution_time_ms=0.0,
                    metadata={"status": "timeout_skipped"},
                    error="Pipeline timeout exceeded"
                )
                
            self.logger.debug(f"开始执行安全模块: {self.name}")
            
            # 执行实际检查
            result = self.check(context, db)
            
            # 记录执行时间
            execution_time = (time.time() - start_time) * 1000
            result.execution_time_ms = execution_time
            
            self.logger.debug(
                f"模块 {self.name} 执行完成: "
                f"passed={result.passed}, "
                f"risk_level={result.risk_level.value}, "
                f"time={execution_time:.2f}ms"
            )
            
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            error_msg = f"模块 {self.name} 执行失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            return ModuleResult(
                module_name=self.name,
                passed=True,  # 错误时默认通过，避免因模块错误阻止正常流程
                risk_level=SecurityLevel.LOW,
                confidence=0.0,
                execution_time_ms=execution_time,
                error=error_msg,
                metadata={"status": "error"}
            )
            
    def get_config(self) -> dict:
        """获取模块配置信息"""
        return {
            "name": self.name,
            "enabled": self.enabled,
            "priority": self.priority
        }
        
    def validate_config(self) -> bool:
        """验证模块配置是否有效"""
        return True
        
    def __str__(self) -> str:
        return f"SecurityModule(name={self.name}, enabled={self.enabled}, priority={self.priority})"
        
    def __repr__(self) -> str:
        return self.__str__()
        
    def __lt__(self, other):
        """用于按优先级排序"""
        if not isinstance(other, SecurityModule):
            return NotImplemented
        return self.priority < other.priority 