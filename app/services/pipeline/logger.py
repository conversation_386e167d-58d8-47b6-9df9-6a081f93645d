"""
安全管道日志记录器
提供详细的安全决策日志记录功能
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
import json
import logging
from pathlib import Path
import uuid

from app.services.pipeline.result import PipelineResult, ModuleResult, SecurityLevel


class LogLevel(str, Enum):
    """日志级别"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class DecisionType(str, Enum):
    """决策类型"""
    ALLOW = "allow"
    BLOCK = "block"
    SANITIZE = "sanitize"
    FLAG = "flag"
    ESCALATE = "escalate"


@dataclass
class SecurityDecision:
    """安全决策记录"""
    decision_id: str
    timestamp: datetime
    decision_type: DecisionType
    risk_level: SecurityLevel
    confidence: float
    reason: str
    module_name: str
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    rule_id: Optional[int] = None
    rule_name: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class PipelineLogEntry:
    """管道日志条目"""
    log_id: str
    timestamp: datetime
    level: LogLevel
    message: str
    context: Dict[str, Any]
    execution_time_ms: Optional[float] = None
    memory_usage: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class PipelineLogger:
    """安全管道日志记录器"""
    
    def __init__(
        self,
        log_file_path: Optional[Path] = None,
        enable_file_logging: bool = True,
        enable_console_logging: bool = True,
        log_level: LogLevel = LogLevel.INFO
    ):
        self.log_file_path = log_file_path or Path("logs/security_pipeline.log")
        self.enable_file_logging = enable_file_logging
        self.enable_console_logging = enable_console_logging
        self.log_level = log_level
        
        # 创建日志目录
        if self.enable_file_logging:
            self.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 设置Python标准日志记录器
        self._setup_logger()
        
        # 存储会话期间的日志条目
        self.log_entries: List[PipelineLogEntry] = []
        self.security_decisions: List[SecurityDecision] = []
    
    def _setup_logger(self):
        """设置Python标准日志记录器"""
        self.logger = logging.getLogger("security_pipeline")
        self.logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        if self.enable_console_logging:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # 文件处理器
        if self.enable_file_logging:
            file_handler = logging.FileHandler(self.log_file_path)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def log_pipeline_start(
        self,
        session_id: str,
        user_id: int,
        content: str,
        policy_name: str,
        modules: List[str]
    ):
        """记录管道开始执行"""
        context = {
            "session_id": session_id,
            "user_id": user_id,
            "content_length": len(content),
            "policy_name": policy_name,
            "modules": modules,
            "event_type": "pipeline_start"
        }
        
        self._log(
            LogLevel.INFO,
            f"Security pipeline started for session {session_id}",
            context
        )
    
    def log_pipeline_end(
        self,
        session_id: str,
        result: PipelineResult,
        execution_time_ms: float
    ):
        """记录管道执行结束"""
        context = {
            "session_id": session_id,
            "risk_level": result.overall_risk_level.value,
            "confidence": result.overall_confidence,
            "decision": result.recommended_action.value,
            "modules_executed": len(result.module_results),
            "execution_time_ms": execution_time_ms,
            "event_type": "pipeline_end"
        }
        
        level = LogLevel.WARNING if result.overall_risk_level in [SecurityLevel.HIGH, SecurityLevel.CRITICAL] else LogLevel.INFO
        
        self._log(
            level,
            f"Security pipeline completed for session {session_id} - Risk: {result.overall_risk_level.value}",
            context,
            execution_time_ms
        )
    
    def log_module_execution(
        self,
        module_name: str,
        session_id: str,
        module_result: ModuleResult,
        execution_time_ms: float
    ):
        """记录模块执行结果"""
        context = {
            "session_id": session_id,
            "module_name": module_name,
            "risk_level": module_result.risk_level.value,
            "confidence": module_result.confidence,
            "findings_count": len(module_result.findings),
            "execution_time_ms": execution_time_ms,
            "event_type": "module_execution"
        }
        
        # 添加模块特定的详细信息
        if module_result.metadata:
            context["module_metadata"] = module_result.metadata
        
        level = LogLevel.WARNING if module_result.risk_level in [SecurityLevel.HIGH, SecurityLevel.CRITICAL] else LogLevel.INFO
        
        self._log(
            level,
            f"Module {module_name} executed - Risk: {module_result.risk_level.value}",
            context,
            execution_time_ms
        )
    
    def log_security_decision(
        self,
        decision_type: DecisionType,
        risk_level: SecurityLevel,
        confidence: float,
        reason: str,
        module_name: str,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        rule_id: Optional[int] = None,
        rule_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """记录安全决策"""
        decision_id = str(uuid.uuid4())
        
        decision = SecurityDecision(
            decision_id=decision_id,
            timestamp=datetime.now(),
            decision_type=decision_type,
            risk_level=risk_level,
            confidence=confidence,
            reason=reason,
            module_name=module_name,
            user_id=user_id,
            session_id=session_id,
            rule_id=rule_id,
            rule_name=rule_name,
            details=details
        )
        
        self.security_decisions.append(decision)
        
        context = decision.to_dict()
        context["event_type"] = "security_decision"
        
        level = LogLevel.CRITICAL if decision_type == DecisionType.BLOCK else LogLevel.WARNING
        
        self._log(
            level,
            f"Security decision: {decision_type.value} - {reason}",
            context
        )
        
        return decision_id
    
    def log_bypass_event(
        self,
        session_id: str,
        user_id: int,
        bypass_type: str,
        reason: str,
        authorized_by: Optional[str] = None
    ):
        """记录绕过事件"""
        context = {
            "session_id": session_id,
            "user_id": user_id,
            "bypass_type": bypass_type,
            "reason": reason,
            "authorized_by": authorized_by,
            "event_type": "bypass_event"
        }
        
        self._log(
            LogLevel.WARNING,
            f"Security bypass executed: {bypass_type}",
            context
        )
    
    def log_error(
        self,
        error_message: str,
        module_name: str,
        session_id: Optional[str] = None,
        exception: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        """记录错误"""
        error_context = {
            "module_name": module_name,
            "error_message": error_message,
            "event_type": "error"
        }
        
        if session_id:
            error_context["session_id"] = session_id
        
        if exception:
            error_context["exception_type"] = type(exception).__name__
            error_context["exception_details"] = str(exception)
        
        if context:
            error_context.update(context)
        
        self._log(
            LogLevel.ERROR,
            f"Error in {module_name}: {error_message}",
            error_context
        )
    
    def log_performance_metric(
        self,
        metric_name: str,
        value: Union[int, float],
        unit: str,
        context: Optional[Dict[str, Any]] = None
    ):
        """记录性能指标"""
        perf_context = {
            "metric_name": metric_name,
            "value": value,
            "unit": unit,
            "event_type": "performance_metric"
        }
        
        if context:
            perf_context.update(context)
        
        self._log(
            LogLevel.DEBUG,
            f"Performance metric: {metric_name} = {value} {unit}",
            perf_context
        )
    
    def _log(
        self,
        level: LogLevel,
        message: str,
        context: Dict[str, Any],
        execution_time_ms: Optional[float] = None
    ):
        """内部日志记录方法"""
        log_entry = PipelineLogEntry(
            log_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            level=level,
            message=message,
            context=context,
            execution_time_ms=execution_time_ms
        )
        
        self.log_entries.append(log_entry)
        
        # 使用Python标准logger
        log_method = getattr(self.logger, level.value)
        log_method(f"{message} - Context: {json.dumps(context, default=str)}")
    
    def get_session_logs(self, session_id: str) -> List[PipelineLogEntry]:
        """获取指定会话的日志"""
        return [
            entry for entry in self.log_entries
            if entry.context.get('session_id') == session_id
        ]
    
    def get_security_decisions(self, session_id: Optional[str] = None) -> List[SecurityDecision]:
        """获取安全决策记录"""
        if session_id:
            return [
                decision for decision in self.security_decisions
                if decision.session_id == session_id
            ]
        return self.security_decisions.copy()
    
    def export_logs(self, output_path: Path, session_id: Optional[str] = None):
        """导出日志到文件"""
        logs_to_export = self.get_session_logs(session_id) if session_id else self.log_entries
        decisions_to_export = self.get_security_decisions(session_id)
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "session_id": session_id,
            "logs": [entry.to_dict() for entry in logs_to_export],
            "security_decisions": [decision.to_dict() for decision in decisions_to_export]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    def clear_session_logs(self, session_id: str):
        """清除指定会话的日志"""
        self.log_entries = [
            entry for entry in self.log_entries
            if entry.context.get('session_id') != session_id
        ]
        
        self.security_decisions = [
            decision for decision in self.security_decisions
            if decision.session_id != session_id
        ]


# 全局日志记录器实例
pipeline_logger = PipelineLogger() 