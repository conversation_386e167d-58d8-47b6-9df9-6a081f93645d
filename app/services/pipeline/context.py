"""
管道执行上下文类
存储管道执行过程中的状态和配置信息
"""

from typing import Dict, Any, Optional, TYPE_CHECKING
from dataclasses import dataclass, field
from datetime import datetime

if TYPE_CHECKING:
    from app.services.pipeline.bypass import BypassManager


@dataclass
class PipelineContext:
    """管道执行上下文"""
    
    # 输入内容
    content: str
    
    # 用户相关信息
    user_id: Optional[int] = None
    user_role_id: Optional[int] = None
    user_level_id: Optional[int] = None
    
    # 会话相关信息
    session_id: Optional[str] = None  # 改为字符串类型支持UUID
    message_type: str = "text"  # text, image, etc.
    
    # 执行配置
    enable_early_termination: bool = True
    timeout_seconds: int = 30
    max_severity_threshold: str = "critical"  # low, medium, high, critical
    
    # 绕过配置
    bypass_enabled: bool = False
    bypass_reason: Optional[str] = None
    bypass_modules: list = field(default_factory=list)
    bypass_manager: Optional["BypassManager"] = None
    
    # 执行状态
    start_time: datetime = field(default_factory=datetime.utcnow)
    execution_metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 模块间共享数据
    shared_data: Dict[str, Any] = field(default_factory=dict)
    
    def set_metadata(self, key: str, value: Any) -> None:
        """设置执行元数据"""
        self.execution_metadata[key] = value
        
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取执行元数据"""
        return self.execution_metadata.get(key, default)
        
    def set_shared_data(self, key: str, value: Any) -> None:
        """设置模块间共享数据"""
        self.shared_data[key] = value
        
    def get_shared_data(self, key: str, default: Any = None) -> Any:
        """获取模块间共享数据"""
        return self.shared_data.get(key, default)
        
    def is_bypass_enabled_for_module(self, module_name: str) -> bool:
        """检查特定模块是否启用绕过"""
        if not self.bypass_enabled:
            return False
        return module_name in self.bypass_modules or len(self.bypass_modules) == 0
        
    def check_bypass(
        self,
        module_name: str,
        rule_name: Optional[str] = None,
        category: Optional[str] = None
    ) -> bool:
        """检查是否有适用的绕过规则"""
        # 检查传统绕过配置
        if self.is_bypass_enabled_for_module(module_name):
            return True
            
        # 检查绕过管理器
        if self.bypass_manager and self.session_id:
            bypass_rule = self.bypass_manager.check_bypass(
                session_id=self.session_id,
                module_name=module_name,
                rule_name=rule_name,
                category=category
            )
            return bypass_rule is not None
            
        return False
    
    def get_bypass_context(self) -> Dict[str, Any]:
        """获取用于绕过条件评估的上下文"""
        return {
            "user_id": self.user_id,
            "user_role_id": self.user_role_id,
            "user_level": self.user_level_id,
            "user_roles": [],  # 需要从数据库获取
            "session_id": self.session_id,
            "content_length": len(self.content),
            "message_type": self.message_type,
            "execution_time": self.get_elapsed_time(),
            "session_count": self.get_metadata("session_count", 1),
            "metadata": self.execution_metadata.copy()
        }
        
    def get_elapsed_time(self) -> float:
        """获取执行耗时（秒）"""
        return (datetime.utcnow() - self.start_time).total_seconds()
        
    def is_timeout(self) -> bool:
        """检查是否超时"""
        return self.get_elapsed_time() > self.timeout_seconds 