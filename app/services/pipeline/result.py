"""
管道执行结果类
收集和汇总安全检查结果
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime


class SecurityLevel(Enum):
    """安全级别枚举"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    CRITICAL = "critical"


class ActionType(Enum):
    """处理动作类型"""
    ALLOW = "allow"
    BLOCK = "block"
    WARN = "warn"
    DESENSITIZE = "desensitize"


@dataclass
class ModuleResult:
    """单个模块的执行结果"""
    module_name: str
    passed: bool
    risk_level: SecurityLevel
    confidence: float  # 0.0 - 1.0
    execution_time_ms: float
    findings: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    
    def add_finding(self, finding_type: str, description: str, details: Dict[str, Any] = None):
        """添加检测发现"""
        finding = {
            "type": finding_type,
            "description": description,
            "details": details or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        self.findings.append(finding)


@dataclass 
class PipelineResult:
    """管道执行总体结果"""
    
    # 总体结果
    is_blocked: bool = False
    overall_risk_level: SecurityLevel = SecurityLevel.LOW
    recommended_action: ActionType = ActionType.ALLOW
    overall_confidence: float = 0.0
    
    # 执行信息
    total_execution_time_ms: float = 0.0
    modules_executed: int = 0
    modules_passed: int = 0
    modules_failed: int = 0
    early_terminated: bool = False
    termination_reason: Optional[str] = None
    
    # 模块结果
    module_results: List[ModuleResult] = field(default_factory=list)
    
    # 处理结果
    original_content: str = ""
    processed_content: Optional[str] = None
    applied_transformations: List[str] = field(default_factory=list)
    
    # 详细信息
    all_findings: List[Dict[str, Any]] = field(default_factory=list)
    security_score: float = 100.0  # 0.0 - 100.0
    execution_metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_module_result(self, result: ModuleResult) -> None:
        """添加模块执行结果"""
        self.module_results.append(result)
        self.modules_executed += 1
        
        if result.passed:
            self.modules_passed += 1
        else:
            self.modules_failed += 1
            
        # 累加执行时间
        self.total_execution_time_ms += result.execution_time_ms
        
        # 收集所有发现
        self.all_findings.extend(result.findings)
        
        # 更新总体风险级别（取最高）
        if self._severity_level(result.risk_level.value) > self._severity_level(self.overall_risk_level.value):
            self.overall_risk_level = result.risk_level
            
    def finalize(self) -> None:
        """完成结果计算"""
        if self.modules_executed == 0:
            return
            
        # 计算总体置信度（平均值）
        total_confidence = sum(r.confidence for r in self.module_results)
        self.overall_confidence = total_confidence / self.modules_executed
        
        # 计算安全评分
        self._calculate_security_score()
        
        # 确定推荐动作
        self._determine_recommended_action()
        
    def _calculate_security_score(self) -> None:
        """计算安全评分"""
        if not self.module_results:
            self.security_score = 100.0
            return
            
        total_penalty = 0.0
        
        for result in self.module_results:
            if not result.passed:
                # 根据风险级别和置信度计算扣分
                risk_penalty = {
                    SecurityLevel.LOW: 10.0,
                    SecurityLevel.MEDIUM: 25.0, 
                    SecurityLevel.HIGH: 40.0,
                    SecurityLevel.CRITICAL: 60.0
                }.get(result.risk_level, 10.0)
                
                # 置信度越高，扣分越多
                penalty = risk_penalty * result.confidence
                total_penalty += penalty
                
        self.security_score = max(0.0, 100.0 - total_penalty)
        
    def _determine_recommended_action(self) -> None:
        """确定推荐的处理动作"""
        # 检查是否有高风险或严重风险的模块失败
        has_high_risk = any(
            r.risk_level in [SecurityLevel.HIGH, SecurityLevel.CRITICAL] and not r.passed
            for r in self.module_results
        )
        
        if has_high_risk:
            # 如果有高风险或严重风险，直接拦截
            self.recommended_action = ActionType.BLOCK
            self.is_blocked = True
        elif self.security_score >= 80:
            self.recommended_action = ActionType.ALLOW
            self.is_blocked = False
        elif self.security_score >= 60:
            self.recommended_action = ActionType.WARN
            self.is_blocked = False
        elif self.security_score >= 40:
            self.recommended_action = ActionType.DESENSITIZE
            self.is_blocked = False
        else:
            self.recommended_action = ActionType.BLOCK
            self.is_blocked = True
            
    def _severity_level(self, severity: str) -> int:
        """将严重性级别转换为数值"""
        return {
            "low": 1,
            "medium": 2,
            "high": 3,
            "critical": 4
        }.get(severity.lower(), 1)
        
    def get_failed_modules(self) -> List[ModuleResult]:
        """获取失败的模块结果"""
        return [r for r in self.module_results if not r.passed]
        
    def get_highest_risk_finding(self) -> Optional[Dict[str, Any]]:
        """获取风险最高的发现"""
        if not self.all_findings:
            return None
            
        # 简单返回第一个，实际可以根据风险级别排序
        return self.all_findings[0] if self.all_findings else None
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "is_blocked": self.is_blocked,
            "overall_risk_level": self.overall_risk_level.value,
            "recommended_action": self.recommended_action.value,
            "overall_confidence": self.overall_confidence,
            "security_score": self.security_score,
            "total_execution_time_ms": self.total_execution_time_ms,
            "modules_executed": self.modules_executed,
            "modules_passed": self.modules_passed,
            "modules_failed": self.modules_failed,
            "early_terminated": self.early_terminated,
            "termination_reason": self.termination_reason,
            "original_content": self.original_content,
            "processed_content": self.processed_content,
            "applied_transformations": self.applied_transformations,
            "all_findings": self.all_findings,
            "execution_metadata": self.execution_metadata,
            "module_results": [
                {
                    "module_name": r.module_name,
                    "passed": r.passed,
                    "risk_level": r.risk_level.value,
                    "confidence": r.confidence,
                    "execution_time_ms": r.execution_time_ms,
                    "findings": r.findings,
                    "metadata": r.metadata,
                    "error": r.error
                }
                for r in self.module_results
            ]
        } 