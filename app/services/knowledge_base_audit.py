"""
知识库审计服务
集成审计日志记录、安全检查和合规性验证
"""

import json
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, func
from fastapi import Request

from app.core.audit import AuditLogger, OperationType, get_client_info, format_operation_details
from app.db.models.audit_log import AuditLog
from app.db.models.operation_log import OperationLog
from app.db.models.knowledge_document import KnowledgeDocument
from app.db.models.knowledge_chunk import KnowledgeChunk
from app.db.models.knowledge_version import KnowledgeVersion
from app.db.models.user import User
from app.services.pipeline.context import PipelineContext
from app.services.pipeline.pipeline import SecurityPipeline
from app.services.pipeline.result import PipelineResult, SecurityLevel


class KnowledgeBaseAuditService:
    """知识库审计服务"""
    
    def __init__(self, security_pipeline: Optional[SecurityPipeline] = None):
        """
        初始化审计服务
        
        Args:
            security_pipeline: 安全检查管道（可选）
        """
        self.security_pipeline = security_pipeline
        self.audit_logger = AuditLogger()
    
    async def log_document_upload(
        self,
        db: AsyncSession,
        user_id: int,
        document_id: int,
        filename: str,
        content_type: str,
        file_size: int,
        request: Optional[Request] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> OperationLog:
        """记录文档上传操作"""
        client_info = get_client_info(request) if request else {}
        
        details = {
            "document_id": document_id,
            "filename": filename,
            "content_type": content_type,
            "file_size": file_size,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        operation_details = format_operation_details(
            OperationType.KNOWLEDGE_UPLOAD,
            details
        )
        
        # 记录到操作日志
        log = self.audit_logger.log_operation_sync(
            db=db,
            operator_id=user_id,
            operation_type=OperationType.KNOWLEDGE_UPLOAD,
            operation_details=operation_details,
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent"),
            success=success,
            error_message=error_message
        )
        
        # 同时记录到审计日志
        audit_log = AuditLog.create_log(
            db_session=db,
            action_type=OperationType.KNOWLEDGE_UPLOAD,
            user_id=user_id,
            target_entity="knowledge_documents",
            target_id=document_id,
            details=json.dumps(details),
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent"),
            result="SUCCESS" if success else "FAILED"
        )
        
        return log
    
    async def log_document_search(
        self,
        db: AsyncSession,
        user_id: int,
        query: str,
        result_count: int,
        search_params: Dict[str, Any],
        request: Optional[Request] = None
    ) -> OperationLog:
        """记录文档搜索操作"""
        client_info = get_client_info(request) if request else {}
        
        details = {
            "query": query[:100],  # 限制查询长度
            "result_count": result_count,
            "search_params": search_params,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        operation_details = format_operation_details(
            OperationType.KNOWLEDGE_SEARCH,
            details
        )
        
        # 记录到操作日志
        log = self.audit_logger.log_operation_sync(
            db=db,
            operator_id=user_id,
            operation_type=OperationType.KNOWLEDGE_SEARCH,
            operation_details=operation_details,
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent"),
            success=True
        )
        
        return log
    
    async def log_document_access(
        self,
        db: AsyncSession,
        user_id: int,
        document_id: int,
        document_name: str,
        access_type: str,  # view, download
        request: Optional[Request] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> OperationLog:
        """记录文档访问操作"""
        client_info = get_client_info(request) if request else {}
        
        operation_type = (
            OperationType.KNOWLEDGE_VIEW 
            if access_type == "view" 
            else OperationType.KNOWLEDGE_DOWNLOAD
        )
        
        details = {
            "document_id": document_id,
            "document_name": document_name,
            "access_type": access_type,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        operation_details = format_operation_details(operation_type, details)
        
        # 记录到操作日志
        log = self.audit_logger.log_operation_sync(
            db=db,
            operator_id=user_id,
            operation_type=operation_type,
            operation_details=operation_details,
            ip_address=client_info.get("ip_address"),
            user_agent=client_info.get("user_agent"),
            success=success,
            error_message=error_message
        )
        
        return log
    
    async def check_content_security(
        self,
        db: AsyncSession,
        user_id: int,
        content: str,
        document_id: Optional[int] = None,
        operation: str = "upload"
    ) -> PipelineResult:
        """
        检查内容安全性
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            content: 要检查的内容
            document_id: 文档ID（可选）
            operation: 操作类型（upload, update等）
            
        Returns:
            PipelineResult: 安全检查结果
        """
        if not self.security_pipeline:
            # 如果没有配置安全管道，返回默认通过结果
            result = PipelineResult()
            result.passed = True
            result.processed_content = content
            return result
        
        # 创建管道上下文
        context = PipelineContext(
            content=content,
            user_id=user_id,
            metadata={
                "operation": operation,
                "document_id": document_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
        # 执行安全检查
        result = self.security_pipeline.execute(context, db)
        
        # 记录安全检查结果
        if not result.passed:
            await self._log_security_violation(
                db=db,
                user_id=user_id,
                document_id=document_id,
                operation=operation,
                risk_level=result.overall_risk_level,
                findings=result.get_all_findings()
            )
        
        return result
    
    async def _log_security_violation(
        self,
        db: AsyncSession,
        user_id: int,
        document_id: Optional[int],
        operation: str,
        risk_level: SecurityLevel,
        findings: List[Dict[str, Any]]
    ):
        """记录安全违规"""
        details = {
            "operation": operation,
            "document_id": document_id,
            "risk_level": risk_level.value,
            "findings": [
                {
                    "module": f.get("module"),
                    "type": f.get("type"),
                    "severity": f.get("severity"),
                    "message": f.get("message")
                }
                for f in findings
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        audit_log = AuditLog.create_log(
            db_session=db,
            action_type="SECURITY_VIOLATION",
            user_id=user_id,
            target_entity="knowledge_documents",
            target_id=document_id,
            details=json.dumps(details),
            result="BLOCKED"
        )
    
    async def get_user_audit_trail(
        self,
        db: AsyncSession,
        user_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        operation_types: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取用户的知识库操作审计记录
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            operation_types: 操作类型列表
            limit: 返回记录数限制
            
        Returns:
            List[Dict]: 审计记录列表
        """
        query = db.query(OperationLog).filter(
            OperationLog.operator_id == user_id
        )
        
        # 过滤知识库相关操作
        knowledge_operations = [
            OperationType.KNOWLEDGE_UPLOAD,
            OperationType.KNOWLEDGE_UPDATE,
            OperationType.KNOWLEDGE_DELETE,
            OperationType.KNOWLEDGE_SEARCH,
            OperationType.KNOWLEDGE_VIEW,
            OperationType.KNOWLEDGE_DOWNLOAD,
            OperationType.KNOWLEDGE_VERSION_CREATE,
            OperationType.KNOWLEDGE_VERSION_ROLLBACK,
            OperationType.KNOWLEDGE_PERMISSION_CHANGE
        ]
        
        if operation_types:
            # 只包含指定的操作类型
            filtered_ops = [op for op in operation_types if op in knowledge_operations]
            query = query.filter(OperationLog.operation_type.in_(filtered_ops))
        else:
            # 默认只返回知识库相关操作
            query = query.filter(OperationLog.operation_type.in_(knowledge_operations))
        
        # 时间范围过滤
        if start_date:
            query = query.filter(OperationLog.created_at >= start_date)
        if end_date:
            query = query.filter(OperationLog.created_at <= end_date)
        
        # 排序和限制
        logs = query.order_by(OperationLog.created_at.desc()).limit(limit).all()
        
        # 转换为字典格式
        return [
            {
                "id": log.id,
                "operation_type": log.operation_type,
                "operation_details": log.operation_details,
                "success": log.success,
                "error_message": log.error_message,
                "ip_address": log.ip_address,
                "created_at": log.created_at.isoformat()
            }
            for log in logs
        ]
    
    async def get_document_audit_trail(
        self,
        db: AsyncSession,
        document_id: int,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        获取文档的审计记录
        
        Args:
            db: 数据库会话
            document_id: 文档ID
            limit: 返回记录数限制
            
        Returns:
            List[Dict]: 审计记录列表
        """
        # 查询审计日志
        logs = db.query(AuditLog).filter(
            and_(
                AuditLog.target_entity == "knowledge_documents",
                AuditLog.target_id == document_id
            )
        ).order_by(AuditLog.created_at.desc()).limit(limit).all()
        
        # 转换为字典格式
        result = []
        for log in logs:
            user = db.query(User).filter(User.id == log.user_id).first()
            result.append({
                "id": log.id,
                "action_type": log.action_type,
                "user_id": log.user_id,
                "username": user.username if user else "Unknown",
                "details": json.loads(log.details) if log.details else {},
                "ip_address": log.ip_address,
                "result": log.result,
                "created_at": log.created_at.isoformat()
            })
        
        return result
    
    async def get_security_statistics(
        self,
        db: AsyncSession,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取知识库安全统计信息
        
        Args:
            db: 数据库会话
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 统计信息
        """
        # 默认统计最近30天
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=30)
        if not end_date:
            end_date = datetime.utcnow()
        
        # 统计各类操作数量
        operation_stats = {}
        knowledge_operations = [
            OperationType.KNOWLEDGE_UPLOAD,
            OperationType.KNOWLEDGE_UPDATE,
            OperationType.KNOWLEDGE_DELETE,
            OperationType.KNOWLEDGE_SEARCH,
            OperationType.KNOWLEDGE_VIEW,
            OperationType.KNOWLEDGE_DOWNLOAD
        ]
        
        for op_type in knowledge_operations:
            count = db.query(func.count(OperationLog.id)).filter(
                and_(
                    OperationLog.operation_type == op_type,
                    OperationLog.created_at >= start_date,
                    OperationLog.created_at <= end_date
                )
            ).scalar()
            operation_stats[op_type] = count
        
        # 统计安全违规
        security_violations = db.query(func.count(AuditLog.id)).filter(
            and_(
                AuditLog.action_type == "SECURITY_VIOLATION",
                AuditLog.target_entity == "knowledge_documents",
                AuditLog.created_at >= start_date,
                AuditLog.created_at <= end_date
            )
        ).scalar()
        
        # 统计失败的操作
        failed_operations = db.query(func.count(OperationLog.id)).filter(
            and_(
                OperationLog.operation_type.in_(knowledge_operations),
                OperationLog.success == False,
                OperationLog.created_at >= start_date,
                OperationLog.created_at <= end_date
            )
        ).scalar()
        
        # 获取最活跃的用户
        top_users = db.query(
            OperationLog.operator_id,
            func.count(OperationLog.id).label("operation_count")
        ).filter(
            and_(
                OperationLog.operation_type.in_(knowledge_operations),
                OperationLog.created_at >= start_date,
                OperationLog.created_at <= end_date
            )
        ).group_by(
            OperationLog.operator_id
        ).order_by(
            func.count(OperationLog.id).desc()
        ).limit(10).all()
        
        # 获取用户信息
        top_users_info = []
        for user_id, count in top_users:
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                top_users_info.append({
                    "user_id": user_id,
                    "username": user.username,
                    "operation_count": count
                })
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "operation_statistics": operation_stats,
            "security_violations": security_violations,
            "failed_operations": failed_operations,
            "top_users": top_users_info
        }
    
    async def check_compliance(
        self,
        db: AsyncSession,
        document_id: int,
        compliance_rules: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        检查文档的合规性
        
        Args:
            db: 数据库会话
            document_id: 文档ID
            compliance_rules: 合规规则（可选）
            
        Returns:
            Dict: 合规性检查结果
        """
        # 获取文档信息
        document = db.query(KnowledgeDocument).filter(
            KnowledgeDocument.id == document_id
        ).first()
        
        if not document:
            return {
                "compliant": False,
                "issues": ["Document not found"]
            }
        
        issues = []
        
        # 默认合规规则
        if not compliance_rules:
            compliance_rules = {
                "require_data_grade": True,
                "require_category": True,
                "max_retention_days": 365,
                "require_version_control": True,
                "min_access_level": 1
            }
        
        # 检查数据分级
        if compliance_rules.get("require_data_grade") and not document.data_grade_id:
            issues.append("Document missing data grade classification")
        
        # 检查数据分类
        if compliance_rules.get("require_category") and not document.category_id:
            issues.append("Document missing category classification")
        
        # 检查保留期限
        max_retention = compliance_rules.get("max_retention_days")
        if max_retention:
            age_days = (datetime.utcnow() - document.created_at).days
            if age_days > max_retention:
                issues.append(f"Document exceeds retention period ({age_days} > {max_retention} days)")
        
        # 检查版本控制
        if compliance_rules.get("require_version_control"):
            version_count = db.query(func.count(KnowledgeVersion.id)).filter(
                KnowledgeVersion.document_id == document_id
            ).scalar()
            if version_count == 0:
                issues.append("Document has no version history")
        
        # 检查访问权限
        min_access_level = compliance_rules.get("min_access_level", 1)
        if document.created_by:
            user = db.query(User).filter(User.id == document.created_by).first()
            if user and user.level_id < min_access_level:
                issues.append(f"Document owner has insufficient access level")
        
        return {
            "compliant": len(issues) == 0,
            "issues": issues,
            "document_id": document_id,
            "document_name": document.name,
            "checked_at": datetime.utcnow().isoformat()
        }
    
    async def log_rag_query(
        self,
        db: AsyncSession,
        user_id: int,
        query: str,
        documents_retrieved: int,
        answer_generated: bool,
        model_used: Optional[int] = None
    ) -> None:
        """记录RAG查询"""
        details = {
            "query": query[:200],  # 只记录前200字符
            "documents_retrieved": documents_retrieved,
            "answer_generated": answer_generated,
            "model_used": model_used,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 记录到审计日志
        await self.audit_logger.log_operation_async(
            db=db,
            operator_id=user_id,
            operation_type="KNOWLEDGE_RAG_QUERY",
            operation_details=json.dumps(details),
            success=answer_generated
        ) 