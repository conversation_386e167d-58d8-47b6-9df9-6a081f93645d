# app/services/document_vectorizer_production.py

"""
生产级别的文档向量化服务
处理文档的向量化和知识库存储
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import hashlib
import asyncio
from concurrent.futures import ThreadPoolExecutor
import numpy as np

# 条件导入
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("sentence-transformers not available, using simple embedding")

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False
    logging.warning("tiktoken not available, using simple tokenization")

from ..db.models.knowledge_document import KnowledgeDocument
from ..db.models.knowledge_chunk import KnowledgeChunk
from ..db.models.data_source_category import DataSourceCategory
from ..db.models.data_source_grade import DataSourceGrade

logger = logging.getLogger(__name__)


class ProductionEmbedding:
    """生产级别的嵌入模型管理器"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化嵌入模型
        
        Args:
            model_name: 模型名称，支持：
                - all-MiniLM-L6-v2 (快速，384维)
                - all-mpnet-base-v2 (平衡，768维)
                - multi-qa-mpnet-base-dot-v1 (问答优化，768维)
        """
        self.model_name = model_name
        self.model = None
        self.dimension = 384  # 默认维度
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                # 设置环境变量强制使用本地缓存
                import os
                os.environ['HF_HUB_OFFLINE'] = '1'
                os.environ['TRANSFORMERS_OFFLINE'] = '1'
                os.environ['HF_DATASETS_OFFLINE'] = '1'

                logger.info(f"Loading model with offline mode: {model_name}")

                # 尝试多种加载方式
                try:
                    # 方式1：直接使用模型名称
                    self.model = SentenceTransformer(model_name)
                    logger.info("Loaded model using direct name")
                except Exception as e1:
                    logger.warning(f"Direct name failed: {e1}")
                    try:
                        # 方式2：使用完整快照路径
                        cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
                        snapshot_base = f"{cache_dir}/models--sentence-transformers--all-MiniLM-L6-v2/snapshots"
                        if os.path.exists(snapshot_base):
                            # 找到快照目录
                            snapshots = [d for d in os.listdir(snapshot_base) if os.path.isdir(os.path.join(snapshot_base, d))]
                            if snapshots:
                                # 使用第一个（通常是唯一的）快照
                                snapshot_path = os.path.join(snapshot_base, snapshots[0])
                                logger.info(f"Trying snapshot path: {snapshot_path}")
                                self.model = SentenceTransformer(snapshot_path)
                                logger.info("Loaded model using snapshot path")
                            else:
                                raise Exception("No snapshots found")
                        else:
                            raise Exception("Cache directory not found")
                    except Exception as e2:
                        logger.error(f"Snapshot path failed: {e2}")
                        raise e1  # 抛出原始错误

                # 获取模型维度
                self.dimension = self.model.get_sentence_embedding_dimension()
                logger.info(f"Successfully loaded embedding model: {model_name} (dim={self.dimension})")

            except Exception as e:
                logger.error(f"Failed to load model {model_name}: {str(e)}")
                logger.warning("Falling back to simple embedding")
                self.model = None
        
        # 用于缓存的线程池
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def encode(self, texts: Union[str, List[str]], batch_size: int = 32) -> np.ndarray:
        """
        编码文本为向量
        
        Args:
            texts: 单个文本或文本列表
            batch_size: 批处理大小
            
        Returns:
            向量数组
        """
        if isinstance(texts, str):
            texts = [texts]
        
        if self.model:
            # 使用真实的嵌入模型
            try:
                embeddings = self.model.encode(
                    texts,
                    batch_size=batch_size,
                    show_progress_bar=False,
                    normalize_embeddings=True  # L2归一化
                )
                return embeddings
            except Exception as e:
                logger.error(f"Embedding failed: {str(e)}")
                # 降级到简单实现
        
        # 简单的哈希向量化（降级方案）
        embeddings = []
        for text in texts:
            embedding = self._simple_embedding(text)
            embeddings.append(embedding)
        
        return np.array(embeddings)
    
    def _simple_embedding(self, text: str) -> np.ndarray:
        """简单的哈希嵌入实现（降级方案）"""
        # 使用多个哈希函数生成向量
        hash_funcs = [hashlib.md5, hashlib.sha1, hashlib.sha256]
        vector = []
        
        for i, hash_func in enumerate(hash_funcs):
            hash_obj = hash_func(text.encode())
            hash_bytes = hash_obj.digest()
            
            # 将哈希字节转换为浮点数
            for j in range(min(len(hash_bytes), self.dimension // len(hash_funcs))):
                value = (hash_bytes[j] / 255.0) * 2 - 1  # 归一化到[-1, 1]
                vector.append(value)
        
        # 填充到目标维度
        while len(vector) < self.dimension:
            vector.append(0.0)
        
        vector = np.array(vector[:self.dimension])
        # L2归一化
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        
        return vector
    
    async def encode_async(self, texts: Union[str, List[str]]) -> np.ndarray:
        """异步编码文本"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.encode, texts)


class ProductionTokenizer:
    """生产级别的文本分词器"""
    
    def __init__(self, model_name: str = "cl100k_base"):
        """
        初始化分词器
        
        Args:
            model_name: tiktoken模型名称
        """
        self.tokenizer = None
        
        if TIKTOKEN_AVAILABLE:
            try:
                self.tokenizer = tiktoken.get_encoding(model_name)
                logger.info(f"Loaded tokenizer: {model_name}")
            except Exception as e:
                logger.error(f"Failed to load tokenizer: {str(e)}")
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        if self.tokenizer:
            return len(self.tokenizer.encode(text))
        else:
            # 简单的近似：平均每个词约1.3个token
            return int(len(text.split()) * 1.3)
    
    def truncate_text(self, text: str, max_tokens: int) -> str:
        """截断文本到指定token数"""
        if self.tokenizer:
            tokens = self.tokenizer.encode(text)
            if len(tokens) > max_tokens:
                tokens = tokens[:max_tokens]
                return self.tokenizer.decode(tokens)
        else:
            # 简单截断
            words = text.split()
            max_words = int(max_tokens / 1.3)
            if len(words) > max_words:
                return ' '.join(words[:max_words])
        
        return text


class ProductionDocumentVectorizer:
    """生产级别的文档向量化服务"""
    
    def __init__(
        self,
        embedding_model: str = "all-MiniLM-L6-v2",
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        max_chunks_per_doc: int = 1000
    ):
        """
        初始化向量化服务
        
        Args:
            embedding_model: 嵌入模型名称
            chunk_size: 块大小（字符数）
            chunk_overlap: 块重叠大小
            max_chunks_per_doc: 每个文档的最大块数
        """
        # 初始化组件
        self.embedding = ProductionEmbedding(embedding_model)
        self.tokenizer = ProductionTokenizer()
        
        # 配置参数
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_chunks_per_doc = max_chunks_per_doc
        
        # 批处理配置
        self.batch_size = 32
        
        logger.info(f"Production document vectorizer initialized")
    
    async def process_document(
        self,
        document_id: int,
        file_path: str,
        parsed_doc: Dict[str, Any],
        category_id: Optional[int] = None,
        grade_id: Optional[int] = None,
        db_session: Optional[Any] = None,
        vector_db: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        处理文档：向量化并存储
        
        Args:
            document_id: 文档ID
            file_path: 文件路径
            parsed_doc: 已解析的文档数据
            category_id: 分类ID
            grade_id: 分级ID
            db_session: 数据库会话
            vector_db: 向量数据库实例
            
        Returns:
            处理结果
        """
        try:
            start_time = datetime.utcnow()
            
            # 1. 获取文档块
            chunks = parsed_doc.get('chunks', [])
            
            # 限制块数
            if len(chunks) > self.max_chunks_per_doc:
                logger.warning(f"Document has {len(chunks)} chunks, limiting to {self.max_chunks_per_doc}")
                chunks = chunks[:self.max_chunks_per_doc]
            
            # 2. 批量向量化
            logger.info(f"Vectorizing {len(chunks)} chunks for document {document_id}")
            
            chunk_texts = [chunk['content'] for chunk in chunks]
            chunk_ids = []
            
            # 分批处理以避免内存问题
            for i in range(0, len(chunk_texts), self.batch_size):
                batch_texts = chunk_texts[i:i + self.batch_size]
                batch_chunks = chunks[i:i + self.batch_size]
                
                # 生成向量
                embeddings = await self.embedding.encode_async(batch_texts)
                
                # 存储到向量数据库
                for j, (chunk_data, embedding) in enumerate(zip(batch_chunks, embeddings)):
                    # 准备元数据
                    metadata = {
                        'document_id': document_id,
                        'chunk_index': chunk_data['chunk_index'],
                        'start_char': chunk_data.get('start_char', 0),
                        'end_char': chunk_data.get('end_char', 0),
                        'category_id': category_id,
                        'grade_id': grade_id,
                        'file_name': parsed_doc['document_info']['file_name'],
                        'file_type': parsed_doc['metadata']['file_type'],
                        'token_count': self.tokenizer.count_tokens(chunk_data['content'])
                    }
                    
                    # 添加额外的元数据
                    if 'metadata' in chunk_data:
                        metadata.update(chunk_data['metadata'])
                    
                    # 存储到向量数据库
                    if vector_db:
                        chunk_id = await vector_db.add_chunk(
                            content=chunk_data['content'],
                            embedding=embedding.tolist(),
                            metadata=metadata
                        )
                    else:
                        # 生成模拟ID
                        chunk_id = f"doc_{document_id}_chunk_{chunk_data['chunk_index']}"
                    
                    chunk_ids.append(chunk_id)
                    
                    # 如果有数据库会话，创建 KnowledgeChunk 记录
                    if db_session:
                        chunk = KnowledgeChunk(
                            document_id=document_id,
                            content=chunk_data['content'],
                            chunk_index=chunk_data['chunk_index'],
                            start_char=chunk_data.get('start_char', 0),
                            end_char=chunk_data.get('end_char', 0),
                            vector_id=chunk_id,
                            chunk_metadata=metadata
                        )
                        db_session.add(chunk)
                
                # 定期提交以避免内存问题
                if db_session and (i + self.batch_size) % (self.batch_size * 10) == 0:
                    db_session.commit()
            
            # 3. 更新文档状态
            if db_session:
                document = db_session.query(KnowledgeDocument).filter_by(id=document_id).first()
                if document:
                    document.is_vectorized = True
                    document.chunk_count = len(chunk_ids)
                    document.status = 'completed'
                    document.processed_at = datetime.utcnow()
                    
                    # 计算处理统计
                    processing_time = (datetime.utcnow() - start_time).total_seconds()
                    document.doc_metadata = document.doc_metadata or {}
                    document.doc_metadata.update({
                        'processing_time_seconds': processing_time,
                        'embedding_model': self.embedding.model_name,
                        'embedding_dimension': self.embedding.dimension,
                        'total_tokens': sum(self.tokenizer.count_tokens(text) for text in chunk_texts)
                    })
                    
                db_session.commit()
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"Successfully processed document {document_id} with {len(chunk_ids)} chunks in {processing_time:.2f}s")
            
            return {
                'success': True,
                'document_id': document_id,
                'chunk_count': len(chunk_ids),
                'chunk_ids': chunk_ids,
                'total_chars': parsed_doc.get('total_chars', 0),
                'processing_time': processing_time,
                'embedding_model': self.embedding.model_name,
                'embedding_dimension': self.embedding.dimension
            }
            
        except Exception as e:
            logger.error(f"Failed to process document {document_id}: {str(e)}")
            
            # 更新文档错误状态
            if db_session:
                document = db_session.query(KnowledgeDocument).filter_by(id=document_id).first()
                if document:
                    document.status = 'failed'
                    document.error_message = str(e)
                db_session.commit()
            
            return {
                'success': False,
                'document_id': document_id,
                'error': str(e)
            }
    
    async def search_similar_chunks(
        self,
        query: str,
        vector_db: Any,
        category_ids: Optional[List[int]] = None,
        grade_ids: Optional[List[int]] = None,
        top_k: int = 5,
        threshold: float = 0.1,  # 降低阈值以确保能找到结果
        rerank: bool = True
    ) -> List[Dict[str, Any]]:
        """
        搜索相似的文档块
        
        Args:
            query: 查询文本
            vector_db: 向量数据库实例
            category_ids: 限制的分类ID列表
            grade_ids: 限制的分级ID列表
            top_k: 返回的最大结果数
            threshold: 相似度阈值
            rerank: 是否重新排序结果
            
        Returns:
            相似文档块列表
        """
        try:
            logger.info(f"Production vectorizer searching for: {query}")
            
            # 构建元数据过滤器
            metadata_filter = {}
            if category_ids:
                metadata_filter['category_id'] = category_ids
            if grade_ids:
                metadata_filter['grade_id'] = grade_ids
            
            # 搜索相似块（获取更多结果用于重排序）
            search_k = top_k * 3  # 获取更多结果
            
            # 优先使用改进的文本搜索（对中文更友好）
            results = []
            if hasattr(vector_db, 'search'):
                logger.info("Using improved text search")
                
                # 使用更低的相似度阈值以获取更多结果
                min_similarity = max(0.001, threshold)
                
                results = vector_db.search(
                    query=query,
                    n_results=search_k,
                    filter_dict=metadata_filter,
                    min_similarity=min_similarity
                )
                logger.info(f"Text search found {len(results)} results")
                
                # 如果结果不够，尝试关键词拆分搜索
                if len(results) < top_k:
                    logger.info("Trying keyword-based search")
                    keyword_results = self._search_by_keywords(vector_db, query, metadata_filter, search_k)
                    
                    # 合并结果，避免重复
                    existing_ids = {r['id'] for r in results}
                    for kr in keyword_results:
                        if kr['id'] not in existing_ids:
                            results.append(kr)
                    
                    logger.info(f"Combined search found {len(results)} total results")
            
            # 如果文本搜索结果不够，尝试向量搜索作为补充
            if len(results) < top_k and hasattr(vector_db, 'search_by_vector'):
                logger.info("Supplementing with vector search")
                try:
                    # 生成查询向量
                    query_embedding = await self.embedding.encode_async(query)
                    
                    # 使用向量搜索
                    vector_results = vector_db.search_by_vector(
                        query_embedding=query_embedding,
                        n_results=search_k,
                        filter_dict=metadata_filter
                    )
                    
                    # 合并结果，避免重复
                    existing_ids = {r['id'] for r in results}
                    for vr in vector_results:
                        if vr['id'] not in existing_ids:
                            results.append(vr)
                    
                    logger.info(f"Combined search found {len(results)} total results")
                except Exception as e:
                    logger.warning(f"Vector search failed: {str(e)}")
            
            # 过滤低相似度结果
            filtered_results = []
            for result in results:
                # 计算正确的相似度分数
                # 如果有distance字段，将距离转换为相似度
                if 'distance' in result:
                    distance = result['distance']
                    # 对于ChromaDB，使用更合适的相似度计算
                    # 距离越小，相似度越高，使用 max(0, 1 - distance/2) 确保在0-1范围内
                    similarity = max(0, 1 - distance / 2)
                else:
                    # 兼容其他相似度字段名
                    similarity = result.get('similarity', result.get('relevance_score', 0))
                    # 如果相似度是负数，使用绝对值并归一化
                    if similarity < 0:
                        similarity = max(0, 1 + similarity)  # 将负数转换为0-1范围

                # 使用更宽松的阈值检查
                if similarity >= max(0, threshold):
                    filtered_results.append({
                        'chunk_id': result['id'],
                        'content': result['content'],
                        'similarity': similarity,
                        'metadata': result.get('metadata', {})
                    })
            
            logger.info(f"Filtered to {len(filtered_results)} results above threshold {threshold}")
            
            # 重新排序（可选）
            if rerank and len(filtered_results) > 1:
                # 这里可以实现更复杂的重排序逻辑
                # 例如：考虑关键词匹配、上下文相关性等
                filtered_results = self._rerank_results(query, filtered_results)
            
            # 限制返回数量
            final_results = filtered_results[:top_k]
            logger.info(f"Returning {len(final_results)} final results")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    def _rerank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        重新排序搜索结果
        
        Args:
            query: 查询文本
            results: 初始搜索结果
            
        Returns:
            重新排序的结果
        """
        # 简单的关键词匹配增强
        query_words = set(query.lower().split())
        
        for result in results:
            content_words = set(result['content'].lower().split())
            keyword_overlap = len(query_words & content_words) / len(query_words)
            
            # 组合向量相似度和关键词重叠度
            result['combined_score'] = (
                result['similarity'] * 0.7 +  # 70% 向量相似度
                keyword_overlap * 0.3          # 30% 关键词重叠
            )
        
        # 按组合分数排序
        results.sort(key=lambda x: x['combined_score'], reverse=True)
        
        return results
    
    def _search_by_keywords(self, vector_db, query: str, metadata_filter: dict, n_results: int) -> List[Dict[str, Any]]:
        """
        基于关键词的搜索增强
        """
        import re
        
        # 提取中文关键词
        chinese_chars = re.findall(r'[\u4e00-\u9fff]+', query)
        english_words = re.findall(r'[a-zA-Z]+', query)
        
        keywords = chinese_chars + english_words
        
        if not keywords:
            return []
        
        # 尝试使用各个关键词搜索
        all_results = []
        for keyword in keywords:
            if len(keyword) >= 2:  # 只使用足够长的关键词
                try:
                    keyword_results = vector_db.search(
                        query=keyword,
                        n_results=n_results // len(keywords) + 1,
                        filter_dict=metadata_filter,
                        min_similarity=0.001
                    )
                    
                    # 为关键词结果添加标识
                    for result in keyword_results:
                        result['search_type'] = 'keyword'
                        result['keyword'] = keyword
                        all_results.append(result)
                        
                except Exception as e:
                    logger.warning(f"Keyword search failed for '{keyword}': {e}")
        
        # 去重并按相似度排序
        unique_results = {}
        for result in all_results:
            result_id = result['id']
            if result_id not in unique_results or result['relevance_score'] > unique_results[result_id]['relevance_score']:
                unique_results[result_id] = result
        
        final_results = list(unique_results.values())
        final_results.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return final_results[:n_results]
    
    def calculate_stats(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算文档块统计信息"""
        if not chunks:
            return {}
        
        char_counts = [chunk.get('char_count', len(chunk['content'])) for chunk in chunks]
        token_counts = [self.tokenizer.count_tokens(chunk['content']) for chunk in chunks]
        
        return {
            'total_chunks': len(chunks),
            'total_chars': sum(char_counts),
            'total_tokens': sum(token_counts),
            'avg_chunk_chars': sum(char_counts) / len(char_counts),
            'avg_chunk_tokens': sum(token_counts) / len(token_counts),
            'min_chunk_chars': min(char_counts),
            'max_chunk_chars': max(char_counts),
            'min_chunk_tokens': min(token_counts),
            'max_chunk_tokens': max(token_counts)
        }


# 创建全局实例
production_document_vectorizer = ProductionDocumentVectorizer() 