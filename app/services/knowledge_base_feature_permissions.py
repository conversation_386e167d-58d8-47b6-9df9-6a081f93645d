"""
知识库功能权限管理服务
提供细粒度的知识库功能权限控制
"""

from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.db.models.role_feature_permission import RoleFeaturePermission
from app.db.models.user import User


class KnowledgeBaseFeaturePermissionService:
    """知识库功能权限服务"""
    
    # 知识库模块的功能定义
    KNOWLEDGE_BASE_FEATURES = {
        'CHAT': {
            'name': '知识库问答',
            'description': '允许用户与知识库进行对话',
            'route': '/knowledge-base/chat'
        },
        'DOCUMENTS': {
            'name': '文档管理',
            'description': '管理知识库中的文档',
            'route': '/knowledge-base/documents'
        },
        'MODELS': {
            'name': '模型管理',
            'description': '配置知识库使用的AI模型',
            'route': '/knowledge-base/models'
        },
        'USER_PERMISSIONS': {
            'name': '用户文档权限管理',
            'description': '管理用户对特定文档的访问权限',
            'route': '/admin/user-document-permissions'
        }
    }
    
    @classmethod
    async def check_feature_permission(
        cls,
        db: AsyncSession,
        user: User,
        feature_name: str,
        action: str = 'access'
    ) -> bool:
        """
        检查用户是否有特定功能的权限
        
        Args:
            db: 数据库会话
            user: 用户对象
            feature_name: 功能名称 (CHAT, DOCUMENTS, MODELS, USER_PERMISSIONS)
            action: 操作类型 (access, create, read, update, delete)
        
        Returns:
            bool: 是否有权限
        """
        if not user or not user.role_id:
            return False
            
        # 查询用户角色的功能权限
        query = select(RoleFeaturePermission).where(
            RoleFeaturePermission.role_id == user.role_id,
            RoleFeaturePermission.module_name == 'KNOWLEDGE_BASE',
            RoleFeaturePermission.feature_name == feature_name
        )
        
        result = await db.execute(query)
        permission = result.scalar_one_or_none()
        
        if not permission:
            return False
            
        # 检查具体的操作权限
        action_map = {
            'access': permission.can_access,
            'create': permission.can_create,
            'read': permission.can_read,
            'update': permission.can_update,
            'delete': permission.can_delete
        }
        
        return action_map.get(action, False)
    
    @classmethod
    async def get_user_knowledge_base_permissions(
        cls,
        db: AsyncSession,
        user: User
    ) -> Dict[str, Dict[str, bool]]:
        """
        获取用户在知识库模块的所有功能权限
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            Dict: 功能权限字典
        """
        if not user or not user.role_id:
            return {}
            
        # 查询用户角色的所有知识库功能权限
        query = select(RoleFeaturePermission).where(
            RoleFeaturePermission.role_id == user.role_id,
            RoleFeaturePermission.module_name == 'KNOWLEDGE_BASE'
        )
        
        result = await db.execute(query)
        permissions = result.scalars().all()
        
        # 构建权限字典
        user_permissions = {}
        for perm in permissions:
            user_permissions[perm.feature_name] = {
                'can_access': perm.can_access,
                'can_create': perm.can_create,
                'can_read': perm.can_read,
                'can_update': perm.can_update,
                'can_delete': perm.can_delete,
                'description': perm.description,
                'feature_info': cls.KNOWLEDGE_BASE_FEATURES.get(perm.feature_name, {})
            }
            
        return user_permissions
    
    @classmethod
    async def get_accessible_features(
        cls,
        db: AsyncSession,
        user: User
    ) -> List[Dict[str, Any]]:
        """
        获取用户可访问的知识库功能列表
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            List: 可访问的功能列表
        """
        permissions = await cls.get_user_knowledge_base_permissions(db, user)
        
        accessible_features = []
        for feature_name, perm_info in permissions.items():
            if perm_info.get('can_access', False):
                feature_data = cls.KNOWLEDGE_BASE_FEATURES.get(feature_name, {})
                accessible_features.append({
                    'feature_name': feature_name,
                    'name': feature_data.get('name', feature_name),
                    'description': feature_data.get('description', ''),
                    'route': feature_data.get('route', ''),
                    'permissions': {
                        'can_create': perm_info.get('can_create', False),
                        'can_read': perm_info.get('can_read', False),
                        'can_update': perm_info.get('can_update', False),
                        'can_delete': perm_info.get('can_delete', False)
                    }
                })
                
        return accessible_features
    
    @classmethod
    def get_feature_definition(cls, feature_name: str) -> Optional[Dict[str, str]]:
        """
        获取功能定义信息
        
        Args:
            feature_name: 功能名称
            
        Returns:
            Dict: 功能定义信息
        """
        return cls.KNOWLEDGE_BASE_FEATURES.get(feature_name)
    
    @classmethod
    def get_all_features(cls) -> Dict[str, Dict[str, str]]:
        """
        获取所有知识库功能定义
        
        Returns:
            Dict: 所有功能定义
        """
        return cls.KNOWLEDGE_BASE_FEATURES.copy()


# 权限装饰器
def require_knowledge_base_feature_permission(feature_name: str, action: str = 'access'):
    """
    知识库功能权限装饰器
    
    Args:
        feature_name: 功能名称
        action: 操作类型
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 从参数中获取数据库会话和当前用户
            db = kwargs.get('db') or (args[1] if len(args) > 1 else None)
            current_user = kwargs.get('current_user') or (args[2] if len(args) > 2 else None)
            
            if not db or not current_user:
                raise ValueError("Missing required parameters: db or current_user")
            
            # 检查权限
            has_permission = await KnowledgeBaseFeaturePermissionService.check_feature_permission(
                db, current_user, feature_name, action
            )
            
            if not has_permission:
                from fastapi import HTTPException, status
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions for {feature_name} {action}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator