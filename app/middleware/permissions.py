"""
权限验证中间件
拦截请求并验证用户权限，支持层级管理权限检查
"""

from typing import Callable, List, Optional
from fastapi import Request, HTTPException, status, Depends
from functools import wraps
import asyncio

from app.core.permissions import (
    can_manage_user, 
    can_assign_models_to_user,
    can_view_user_permissions,
    get_all_subordinates
)
from app.core.audit import AuditLogger, get_client_info, OperationType, format_operation_details
from app.db.models.user import User
from app.db.session import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

class PermissionChecker:
    """权限检查器类"""
    
    def __init__(self):
        self._user_cache: dict = {}
        self._cache_timeout = 300  # 5分钟缓存
    
    async def get_all_users(self, db: AsyncSession) -> List[User]:
        """
        获取所有用户（用于权限检查）
        带缓存机制提高性能
        """
        # 这里可以添加缓存逻辑，暂时直接查询
        result = await db.execute(select(User))
        return result.scalars().all()
    
    async def check_user_management_permission(
        self, 
        db: AsyncSession,
        current_user: User, 
        target_user_id: int
    ) -> bool:
        """
        检查用户管理权限
        
        Args:
            db: 数据库会话
            current_user: 当前用户
            target_user_id: 目标用户ID
        
        Returns:
            bool: 是否有权限
        """
        all_users = await self.get_all_users(db)
        target_user = next((u for u in all_users if u.id == target_user_id), None)
        
        if not target_user:
            return False
        
        return can_manage_user(current_user, target_user, all_users)
    
    async def check_model_assignment_permission(
        self,
        db: AsyncSession,
        current_user: User,
        target_user_id: int
    ) -> bool:
        """
        检查模型分配权限
        
        Args:
            db: 数据库会话
            current_user: 当前用户
            target_user_id: 目标用户ID
        
        Returns:
            bool: 是否有权限
        """
        all_users = await self.get_all_users(db)
        target_user = next((u for u in all_users if u.id == target_user_id), None)
        
        if not target_user:
            return False
        
        return can_assign_models_to_user(current_user, target_user, all_users)

# 全局权限检查器实例
permission_checker = PermissionChecker()

def require_user_management_permission(target_user_id_param: str = "user_id"):
    """
    装饰器：要求用户管理权限
    
    Args:
        target_user_id_param: 路径参数中目标用户ID的参数名
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取依赖项
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            request = kwargs.get('request')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Missing required dependencies"
                )
            
            # 获取目标用户ID
            target_user_id = kwargs.get(target_user_id_param)
            if not target_user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing parameter: {target_user_id_param}"
                )
            
            # 检查权限
            has_permission = await permission_checker.check_user_management_permission(
                db, current_user, target_user_id
            )
            
            if not has_permission:
                # 记录权限拒绝日志
                client_info = get_client_info(request) if request else {}
                await AuditLogger.log_operation_async(
                    db=db,
                    operator_id=current_user.id,
                    operation_type=OperationType.PERMISSION_ASSIGN,
                    operation_details=f"尝试管理用户 {target_user_id} 被拒绝",
                    target_user_id=target_user_id,
                    success=False,
                    error_message="权限不足",
                    **client_info
                )
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限管理该用户"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_model_assignment_permission(target_user_id_param: str = "user_id"):
    """
    装饰器：要求模型分配权限
    
    Args:
        target_user_id_param: 路径参数中目标用户ID的参数名
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取依赖项
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            request = kwargs.get('request')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Missing required dependencies"
                )
            
            # 获取目标用户ID
            target_user_id = kwargs.get(target_user_id_param)
            if not target_user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing parameter: {target_user_id_param}"
                )
            
            # 检查权限
            has_permission = await permission_checker.check_model_assignment_permission(
                db, current_user, target_user_id
            )
            
            if not has_permission:
                # 记录权限拒绝日志
                client_info = get_client_info(request) if request else {}
                await AuditLogger.log_operation_async(
                    db=db,
                    operator_id=current_user.id,
                    operation_type=OperationType.MODEL_ASSIGN,
                    operation_details=f"尝试为用户 {target_user_id} 分配模型被拒绝",
                    target_user_id=target_user_id,
                    success=False,
                    error_message="权限不足",
                    **client_info
                )
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限为该用户分配模型"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_subordinate_access(target_user_id_param: str = "user_id"):
    """
    装饰器：要求下属访问权限（查看下属信息）
    
    Args:
        target_user_id_param: 路径参数中目标用户ID的参数名
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取依赖项
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            request = kwargs.get('request')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Missing required dependencies"
                )
            
            # 获取目标用户ID
            target_user_id = kwargs.get(target_user_id_param)
            if not target_user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing parameter: {target_user_id_param}"
                )
            
            # 检查权限（可以查看下属或自己的信息）
            all_users = await permission_checker.get_all_users(db)
            target_user = next((u for u in all_users if u.id == target_user_id), None)
            
            if not target_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            has_permission = can_view_user_permissions(current_user, target_user, all_users)
            
            if not has_permission:
                # 记录权限拒绝日志
                client_info = get_client_info(request) if request else {}
                await AuditLogger.log_operation_async(
                    db=db,
                    operator_id=current_user.id,
                    operation_type=OperationType.DATA_VIEW,
                    operation_details=f"尝试查看用户 {target_user_id} 信息被拒绝",
                    target_user_id=target_user_id,
                    success=False,
                    error_message="权限不足",
                    **client_info
                )
                
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限查看该用户信息"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

async def log_successful_operation(
    db: AsyncSession,
    current_user: User,
    operation_type: str,
    operation_details: str,
    request: Optional[Request] = None,
    target_user_id: Optional[int] = None
):
    """
    记录成功的操作日志
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        operation_type: 操作类型
        operation_details: 操作详情
        request: 请求对象
        target_user_id: 目标用户ID
    """
    client_info = get_client_info(request) if request else {}
    await AuditLogger.log_operation_async(
        db=db,
        operator_id=current_user.id,
        operation_type=operation_type,
        operation_details=operation_details,
        target_user_id=target_user_id,
        success=True,
        **client_info
    ) 