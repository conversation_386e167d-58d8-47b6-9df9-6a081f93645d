from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from app.api.v1.api import api_router

# 导入数据库模型以确保它们被注册
import app.db.base  # 这会导入所有模型并注册到SQLAlchemy中

# 加载 .env 文件中的环境变量 (如果存在)
load_dotenv()

# 初始化数据库 (创建表) - 已移除，因为使用 init_db.py 脚本初始化
# init_db()

app = FastAPI(
    title="AI Security System API",
    description="API for AI Security System, managing users, sessions, and security policies.",
    version="0.1.0"
)

# 配置 CORS - 修复跨域问题
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000", 
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:5174",
        "http://127.0.0.1:5174",
        "*"  # 开发环境允许所有来源
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "X-Csrftoken",
        "Cache-Control",
        "Pragma",
        "Expires"
    ],
    expose_headers=["*"],
    max_age=86400,  # 24小时缓存预检请求
)

# Include the v1 API router
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def read_root():
    """
    Root endpoint for the API.
    Provides a welcome message.
    """
    return {"message": "Welcome to the AI Security System API!"}

@app.get("/health")
async def health_check():
    """
    Health check endpoint.
    Returns the status of the API.
    """
    return {"status": "healthy", "version": app.version}

@app.get("/api/v1/health")
async def api_health_check():
    """
    API health check endpoint.
    Returns the status of the API v1.
    """
    return {"status": "healthy", "version": app.version, "api_version": "v1"}

if __name__ == "__main__":
    import uvicorn
    # 从环境变量获取端口和主机，或者使用默认值
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "127.0.0.1")
    uvicorn.run(app, host=host, port=port, log_level="info")
