from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional, List, Any

from app.db.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password
from app.core.config import settings # For ADMIN_USER_ROLE_NAME in is_superuser_check

async def get_user(db: AsyncSession, user_id: int) -> Optional[User]:
    result = await db.execute(select(User).filter(User.id == user_id))
    return result.scalars().first()

async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
    result = await db.execute(select(User).filter(User.username == username))
    return result.scalars().first()

async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    result = await db.execute(select(User).filter(User.email == email))
    return result.scalars().first()

async def create_user(db: AsyncSession, user_in: UserCreate) -> User:
    hashed_password = get_password_hash(user_in.password)
    db_user = User(
        username=user_in.username,
        email=user_in.email,
        hashed_password=hashed_password,
        full_name=user_in.full_name,
        is_active=user_in.is_active if user_in.is_active is not None else True,
        role_id=user_in.role_id,
        level_id=user_in.level_id
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user

async def update_user(db: AsyncSession, db_user: User, user_in: UserUpdate) -> User:
    update_data = user_in.model_dump(exclude_unset=True)
    if "password" in update_data and update_data["password"]:
        hashed_password = get_password_hash(update_data["password"])
        db_user.hashed_password = hashed_password
        del update_data["password"] # Avoid setting it directly

    for key, value in update_data.items():
        setattr(db_user, key, value)

    await db.commit()
    await db.refresh(db_user)
    return db_user

async def delete_user(db: AsyncSession, user_id: int) -> Optional[User]:
    db_user = await get_user(db, user_id)
    if db_user:
        await db.delete(db_user)
        await db.commit()
    return db_user

async def get_users(
    db: AsyncSession, skip: int = 0, limit: int = 100
) -> List[User]:
    result = await db.execute(select(User).offset(skip).limit(limit).order_by(User.id))
    return result.scalars().all()

async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    user = await get_user_by_username(db, username=username)
    if not user:
        return None
    if not user.hashed_password: # Should not happen with proper creation
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

async def is_superuser(user: User) -> bool:
    # This check assumes that the User model has a 'role' relationship loaded,
    # and the Role model has a 'name' attribute.
    # For this to work reliably, ensure the user object passed to this function
    # has its 'role' relationship eagerly loaded if you call this outside of
    # a context where it's already loaded (e.g. after fetching a user with select(User).options(selectinload(User.role))).
    # However, for the initial_data script, the role_id is set, and if we fetch the user later
    # with its relationships, this will work.
    if user.role and hasattr(user.role, 'name'):
        return user.role.name == settings.ADMIN_USER_ROLE_NAME
    # Fallback or if role is not loaded, you might need to fetch the role separately
    # or decide that a user without a role or role name cannot be a superuser.
    return False
