from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func
from typing import Optional, List, Tuple
from fastapi import HTTPException, status

from app.db.models.role import Role
from app.schemas.role import RoleCreate, RoleUpdate

async def get_role(db: AsyncSession, role_id: int) -> Optional[Role]:
    """根据ID获取角色"""
    result = await db.execute(select(Role).filter(Role.id == role_id))
    return result.scalars().first()

async def get_role_by_name(db: AsyncSession, name: str) -> Optional[Role]:
    """根据名称获取角色"""
    result = await db.execute(select(Role).filter(Role.name == name))
    return result.scalars().first()

async def get_roles(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100,
    is_active: Optional[bool] = None,
    search: Optional[str] = None
) -> Tuple[List[Role], int]:
    """分页查询角色列表"""
    query = select(Role)
    
    # 添加过滤条件
    filters = []
    if is_active is not None:
        filters.append(Role.is_active == is_active)
    if search:
        filters.append(or_(
            Role.name.contains(search),
            Role.description.contains(search)
        ))
    
    if filters:
        query = query.filter(and_(*filters))
    
    # 计算总数
    count_query = select(func.count()).select_from(Role)
    if filters:
        count_query = count_query.filter(and_(*filters))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    query = query.offset(skip).limit(limit).order_by(Role.created_at.desc())
    result = await db.execute(query)
    roles = result.scalars().all()
    
    return list(roles), total

async def create_role(db: AsyncSession, role: RoleCreate) -> Role:
    """创建角色"""
    # 检查名称是否已存在
    existing_role = await get_role_by_name(db, role.name)
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"角色名称 '{role.name}' 已存在"
        )
    
    db_role = Role(**role.model_dump())
    db.add(db_role)
    await db.commit()
    await db.refresh(db_role)
    return db_role

async def update_role(db: AsyncSession, role_id: int, role_update: RoleUpdate) -> Optional[Role]:
    """更新角色"""
    db_role = await get_role(db, role_id)
    if not db_role:
        return None
    
    # 检查是否为系统内置角色
    if db_role.is_system:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="系统内置角色不允许修改"
        )
    
    # 检查名称是否与其他角色冲突
    if role_update.name and role_update.name != db_role.name:
        existing_role = await get_role_by_name(db, role_update.name)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"角色名称 '{role_update.name}' 已存在"
            )
    
    update_data = role_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_role, key, value)
    
    await db.commit()
    await db.refresh(db_role)
    return db_role

async def delete_role(db: AsyncSession, role_id: int) -> Optional[Role]:
    """删除角色"""
    db_role = await get_role(db, role_id)
    if not db_role:
        return None
    
    # 安全检查
    if not db_role.can_be_deleted():
        if db_role.is_system:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="系统内置角色不允许删除"
            )
        elif db_role.user_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"该角色下还有 {db_role.user_count} 个用户，无法删除"
            )
    
    await db.delete(db_role)
    await db.commit()
    return db_role

async def get_active_roles(db: AsyncSession) -> List[Role]:
    """获取所有激活的角色"""
    result = await db.execute(
        select(Role).filter(Role.is_active == True).order_by(Role.name)
    )
    return list(result.scalars().all())
