from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func
from typing import Optional, List, Tuple
from fastapi import HTTPException, status

from app.db.models.level import Level
from app.schemas.level import LevelCreate, LevelUpdate

async def get_level(db: AsyncSession, level_id: int) -> Optional[Level]:
    """根据ID获取级别"""
    result = await db.execute(select(Level).filter(Level.id == level_id))
    return result.scalars().first()

async def get_level_by_name_and_role(db: AsyncSession, name: str, role_id: int) -> Optional[Level]:
    """根据名称和角色ID获取级别"""
    result = await db.execute(
        select(Level).filter(and_(Level.name == name, Level.role_id == role_id))
    )
    return result.scalars().first()

async def get_level_by_rank_and_role(db: AsyncSession, rank_value: int, role_id: int) -> Optional[Level]:
    """根据排序值和角色ID获取级别"""
    result = await db.execute(
        select(Level).filter(and_(Level.rank_value == rank_value, Level.role_id == role_id))
    )
    return result.scalars().first()

async def get_levels(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100,
    role_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None
) -> Tuple[List[Level], int]:
    """分页查询级别列表"""
    query = select(Level)
    
    # 添加过滤条件
    filters = []
    if role_id is not None:
        filters.append(Level.role_id == role_id)
    if is_active is not None:
        filters.append(Level.is_active == is_active)
    if search:
        filters.append(or_(
            Level.name.contains(search),
            Level.description.contains(search)
        ))
    
    if filters:
        query = query.filter(and_(*filters))
    
    # 计算总数
    count_query = select(func.count()).select_from(Level)
    if filters:
        count_query = count_query.filter(and_(*filters))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询，按角色ID和rank_value排序
    query = query.offset(skip).limit(limit).order_by(Level.role_id, Level.rank_value)
    result = await db.execute(query)
    levels = result.scalars().all()
    
    return list(levels), total

async def create_level(db: AsyncSession, level: LevelCreate) -> Level:
    """创建级别"""
    # 检查同一角色下名称是否已存在
    existing_level = await get_level_by_name_and_role(db, level.name, level.role_id)
    if existing_level:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"角色下级别名称 '{level.name}' 已存在"
        )
    
    # 检查同一角色下排序值是否已存在
    existing_rank = await get_level_by_rank_and_role(db, level.rank_value, level.role_id)
    if existing_rank:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"角色下级别排序值 {level.rank_value} 已存在"
        )
    
    db_level = Level(**level.model_dump())
    db.add(db_level)
    await db.commit()
    await db.refresh(db_level)
    return db_level

async def update_level(db: AsyncSession, level_id: int, level_update: LevelUpdate) -> Optional[Level]:
    """更新级别"""
    db_level = await get_level(db, level_id)
    if not db_level:
        return None
    
    # 检查名称是否与同角色下其他级别冲突
    if level_update.name and level_update.name != db_level.name:
        existing_level = await get_level_by_name_and_role(db, level_update.name, db_level.role_id)
        if existing_level:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"角色下级别名称 '{level_update.name}' 已存在"
            )
    
    # 检查排序值是否与同角色下其他级别冲突
    if level_update.rank_value and level_update.rank_value != db_level.rank_value:
        existing_rank = await get_level_by_rank_and_role(db, level_update.rank_value, db_level.role_id)
        if existing_rank:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"角色下级别排序值 {level_update.rank_value} 已存在"
            )
    
    update_data = level_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_level, key, value)
    
    await db.commit()
    await db.refresh(db_level)
    return db_level

async def delete_level(db: AsyncSession, level_id: int) -> Optional[Level]:
    """删除级别"""
    db_level = await get_level(db, level_id)
    if not db_level:
        return None
    
    # 安全检查
    if not db_level.can_be_deleted():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"该级别下还有 {db_level.user_count} 个用户，无法删除"
        )
    
    await db.delete(db_level)
    await db.commit()
    return db_level

async def get_levels_by_role(db: AsyncSession, role_id: int) -> List[Level]:
    """获取指定角色下的所有激活级别，按rank_value排序"""
    result = await db.execute(
        select(Level)
        .filter(and_(Level.role_id == role_id, Level.is_active == True))
        .order_by(Level.rank_value)
    )
    return list(result.scalars().all())

async def get_active_levels(db: AsyncSession) -> List[Level]:
    """获取所有激活的级别，按角色和排序值排序"""
    result = await db.execute(
        select(Level)
        .filter(Level.is_active == True)
        .order_by(Level.role_id, Level.rank_value)
    )
    return list(result.scalars().all())
