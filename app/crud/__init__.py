# This file makes the 'crud' directory a Python package.

# Import individual CRUD modules so they can be accessed e.g. app.crud.crud_user
from . import crud_user
from . import crud_role
from . import crud_level
from . import crud_llm_model

# 创建同步版本的用户认证功能供登录使用
from sqlalchemy.orm import Session
from typing import Optional
from app.db.models.user import User
from app.core.security import verify_password

class UserCRUD:
    """同步版本的用户CRUD操作"""
    
    def authenticate(self, db: Session, username: str, password: str) -> Optional[User]:
        """同步认证用户"""
        user = db.query(User).filter(User.username == username).first()
        if not user:
            return None
        if not user.hashed_password:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    def get_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username).first()
    
    def get(self, db: Session, id: int) -> Optional[User]:
        """根据ID获取用户"""
        return db.query(User).filter(User.id == id).first()

# 创建实例供登录端点使用
user_crud = UserCRUD()

# Optionally, you can also expose specific CRUD objects or functions directly if preferred,
# but the initial_data.py script imports the modules themselves.

# Example of exposing specific items (not strictly needed for initial_data.py current imports):
# from .crud_user import create_user, get_user_by_username # etc.
# from .crud_role import create_role, get_role_by_name
# from .crud_level import create_level, get_level_by_name
from .crud_llm_model import llm_model_crud
