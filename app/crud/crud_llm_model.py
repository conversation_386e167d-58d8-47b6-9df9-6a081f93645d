from typing import Any, Dict, Optional, Union
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.db.models.llm_model import LLMModel
from app.schemas.llm_model import LLMModelCreate, LLMModelUpdate
from fastapi.encoders import jsonable_encoder

class CRUDLLMModel(CRUDBase[LLMModel, LLMModelCreate, LLMModelUpdate]):
    def create(self, db: Session, *, obj_in: LLMModelCreate) -> LLMModel:
        """创建模型，处理 api_key 到 api_key_encrypted 的映射"""
        obj_in_data = jsonable_encoder(obj_in)
        
        # 处理 api_key 字段
        if "api_key" in obj_in_data:
            api_key = obj_in_data.pop("api_key")
            if api_key:  # 如果有值，则加密存储
                # TODO: 这里应该加密，现在暂时直接存储
                obj_in_data["api_key_encrypted"] = api_key
            # 如果没有值或为空，不设置 api_key_encrypted 字段
        
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(
        self, 
        db: Session, 
        *, 
        db_obj: LLMModel, 
        obj_in: Union[LLMModelUpdate, Dict[str, Any]]
    ) -> LLMModel:
        """更新模型，处理 api_key 到 api_key_encrypted 的映射"""
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
            
        # 处理 api_key 字段
        if "api_key" in update_data:
            api_key = update_data.pop("api_key")
            if api_key:  # 如果有值，则加密存储
                # TODO: 这里应该加密，现在暂时直接存储
                update_data["api_key_encrypted"] = api_key
            # 如果没有值，不更新 api_key_encrypted 字段
            
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

llm_model_crud = CRUDLLMModel(LLMModel)
