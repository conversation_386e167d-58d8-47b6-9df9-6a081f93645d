# app/core/vector_db_config.py

from typing import Optional, Dict, Any
import os


class VectorDBConfig:
    """向量数据库配置"""
    
    # Chroma配置
    CHROMA_PERSIST_DIRECTORY: str = "data/chroma_db"
    CHROMA_COLLECTION_NAME: str = "knowledge_base"
    
    # 嵌入模型配置
    EMBEDDING_MODEL_NAME: str = "all-MiniLM-L6-v2"  # 默认使用本地模型
    EMBEDDING_DIMENSION: int = 384  # all-MiniLM-L6-v2的维度
    
    # OpenAI嵌入配置（可选）
    USE_OPENAI_EMBEDDINGS: bool = False
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-ada-002"
    OPENAI_EMBEDDING_DIMENSION: int = 1536
    
    # 文档处理配置
    CHUNK_SIZE: int = 500  # 文档块大小（字符数）
    CHUNK_OVERLAP: int = 50  # 块之间的重叠字符数
    MAX_CHUNKS_PER_DOCUMENT: int = 1000  # 每个文档的最大块数
    
    # 搜索配置
    DEFAULT_SEARCH_RESULTS: int = 5  # 默认返回的搜索结果数
    MAX_SEARCH_RESULTS: int = 20  # 最大返回的搜索结果数
    SIMILARITY_THRESHOLD: float = -0.5  # 相似度阈值（ChromaDB使用距离，负值表示更相似）
    
    # 性能配置
    BATCH_SIZE: int = 100  # 批量处理大小
    MAX_CONCURRENT_OPERATIONS: int = 5  # 最大并发操作数
    

    
    def get_embedding_config(self) -> Dict[str, Any]:
        """获取嵌入模型配置"""
        if self.USE_OPENAI_EMBEDDINGS:
            return {
                "model_name": self.OPENAI_EMBEDDING_MODEL,
                "dimension": self.OPENAI_EMBEDDING_DIMENSION,
                "provider": "openai"
            }
        else:
            return {
                "model_name": self.EMBEDDING_MODEL_NAME,
                "dimension": self.EMBEDDING_DIMENSION,
                "provider": "sentence-transformers"
            }
    
    def get_chroma_settings(self) -> Dict[str, Any]:
        """获取Chroma数据库设置"""
        return {
            "anonymized_telemetry": False,
            "allow_reset": True,
            "is_persistent": True
        }


# 创建配置实例
vector_db_config = VectorDBConfig() 