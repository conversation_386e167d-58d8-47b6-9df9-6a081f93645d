"""
嵌入模型配置
支持多种语言的语义嵌入模型选择
"""

import os
from typing import Dict, Any

# 支持的嵌入模型
EMBEDDING_MODELS = {
    # 多语言模型（推荐用于中文）
    "multilingual-e5-base": {
        "name": "intfloat/multilingual-e5-base",
        "dimension": 768,
        "max_seq_length": 512,
        "languages": ["zh", "en", "ja", "ko", "es", "fr", "de", "ru", "ar", "hi"],
        "description": "微软E5多语言模型，中文效果最佳，768维",
        "recommended_for_chinese": True
    },
    "multilingual-e5-small": {
        "name": "intfloat/multilingual-e5-small",
        "dimension": 384,
        "max_seq_length": 512,
        "languages": ["zh", "en", "ja", "ko", "es", "fr", "de", "ru", "ar", "hi"],
        "description": "微软E5多语言模型小版本，中文效果好，384维",
        "recommended_for_chinese": True
    },
    "paraphrase-multilingual-MiniLM-L12-v2": {
        "name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        "dimension": 384,
        "max_seq_length": 128,
        "languages": ["zh", "en", "de", "fr", "es", "pt", "it", "ru", "ar", "ja"],
        "description": "多语言MiniLM，支持50+语言，384维",
        "recommended_for_chinese": True
    },
    "paraphrase-multilingual-mpnet-base-v2": {
        "name": "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
        "dimension": 768,
        "max_seq_length": 128,
        "languages": ["zh", "en", "de", "fr", "es", "pt", "it", "ru", "ar", "ja"],
        "description": "多语言MPNet，高质量，768维",
        "recommended_for_chinese": True
    },
    "distiluse-base-multilingual-cased-v2": {
        "name": "sentence-transformers/distiluse-base-multilingual-cased-v2",
        "dimension": 512,
        "max_seq_length": 128,
        "languages": ["zh", "en", "ar", "bg", "ca", "cs", "da", "de", "el", "es"],
        "description": "多语言DistilBERT，512维",
        "recommended_for_chinese": False
    },
    
    # 中文专用模型
    "text2vec-base-chinese": {
        "name": "shibing624/text2vec-base-chinese",
        "dimension": 768,
        "max_seq_length": 512,
        "languages": ["zh"],
        "description": "中文专用模型，基于MacBERT，768维",
        "recommended_for_chinese": True
    },
    "m3e-base": {
        "name": "moka-ai/m3e-base",
        "dimension": 768,
        "max_seq_length": 512,
        "languages": ["zh", "en"],
        "description": "M3E中英双语模型，768维",
        "recommended_for_chinese": True
    },
    
    # 英文模型
    "all-MiniLM-L6-v2": {
        "name": "sentence-transformers/all-MiniLM-L6-v2",
        "dimension": 384,
        "max_seq_length": 256,
        "languages": ["en"],
        "description": "英文轻量级模型，快速，384维",
        "recommended_for_chinese": False
    },
    "all-mpnet-base-v2": {
        "name": "sentence-transformers/all-mpnet-base-v2",
        "dimension": 768,
        "max_seq_length": 384,
        "languages": ["en"],
        "description": "英文高质量模型，768维",
        "recommended_for_chinese": False
    }
}

# 默认模型选择
DEFAULT_EMBEDDING_MODEL = "multilingual-e5-small"  # 默认使用E5多语言小模型

# 从环境变量读取模型选择
SELECTED_EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", DEFAULT_EMBEDDING_MODEL)


def get_embedding_model_config(model_key: str = None) -> Dict[str, Any]:
    """
    获取嵌入模型配置
    
    Args:
        model_key: 模型键名，如果不提供则使用默认配置
        
    Returns:
        模型配置字典
    """
    if model_key is None:
        model_key = SELECTED_EMBEDDING_MODEL
    
    if model_key not in EMBEDDING_MODELS:
        raise ValueError(f"Unknown embedding model: {model_key}. Available models: {list(EMBEDDING_MODELS.keys())}")
    
    return EMBEDDING_MODELS[model_key]


def get_recommended_chinese_models() -> Dict[str, Dict[str, Any]]:
    """获取推荐的中文模型"""
    return {
        key: config 
        for key, config in EMBEDDING_MODELS.items() 
        if config.get("recommended_for_chinese", False)
    }


def get_model_by_dimension(dimension: int) -> Dict[str, Dict[str, Any]]:
    """根据维度获取模型"""
    return {
        key: config 
        for key, config in EMBEDDING_MODELS.items() 
        if config["dimension"] == dimension
    } 