# app/core/security.py

from datetime import datetime, timedelta, timezone
from typing import Optional, Union, Any, List

from jose import jwt
from app.core.config import settings # For SECRET_KEY and ALGORITHM
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session # Keep for get_db type hint if get_db is async Session
from app.db.session import get_db # Ensure get_db is compatible (async or sync)
# This import is part of the original circular dependency, ensure user_service doesn't import security directly
from app.services import user_service 
from app.db.models.user import User as UserModel
# from app.schemas.token import TokenPayload # If not used, can be removed
from passlib.context import CryptContext

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/login/token")

credentials_exception = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Could not validate credentials",
    headers={"WWW-Authenticate": "Bearer"},
)

# --- JWT Token Functions ---

# ALGORITHM = "HS256" # This will come from settings
# ACCESS_TOKEN_EXPIRE_MINUTES = 30 # This will come from settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(subject: Union[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Creates a new JWT access token.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> str: # Returns subject (e.g., username)
    """
    Verifies a JWT token and returns the subject (username) if valid.
    Raises credentials_exception if the token is invalid or expired.
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        # subject: Optional[str] = payload.get("sub") # Original, if using TokenPayload
        username: Optional[str] = payload.get("sub") # Assuming 'sub' field contains the username
        if username is None:
            raise credentials_exception
        # If you were to use TokenPayload, you might do:
        # token_data = TokenPayload(sub=username) # Or however TokenPayload is structured
        return username
    except jwt.ExpiredSignatureError:
        raise credentials_exception
    except jwt.JWTError:
        raise credentials_exception

def get_current_active_user():
    """
    这是一个占位符函数，实际的用户认证逻辑应该在API层实现
    这里只提供权限检查函数
    """
    pass

# 新增：基于模块权限的检查函数
def require_module_permission(module_name: str):
    """
    依赖注入函数：检查当前用户是否有指定模块的权限
    """
    from app.api.deps import get_current_active_user as deps_get_current_active_user
    from app.api.deps import get_db as deps_get_db
    
    def permission_checker(
        current_user = Depends(deps_get_current_active_user),
        db: Session = Depends(deps_get_db)
    ):
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户未认证"
            )
        
        # 检查用户是否有该模块的权限
        from app.db.models import RoleModulePermission
        
        permission = db.query(RoleModulePermission).filter(
            RoleModulePermission.role_id == current_user.role_id,
            RoleModulePermission.module_name == module_name
        ).first()
        
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"用户没有访问 {module_name} 模块的权限"
            )
        
        return current_user
    
    return permission_checker

# RBAC Dependency (保留原有的角色检查，向后兼容)
def require_role(allowed_roles: List[str]):
    """
    Dependency that checks if the current user has one of the allowed roles.
    """
    from app.api.deps import get_current_active_user as deps_get_current_active_user
    
    def role_checker(current_user = Depends(deps_get_current_active_user)):
        if not current_user or not current_user.role or current_user.role.name not in allowed_roles:
            # It's good practice to also check if current_user.role exists
            # though with non-nullable role_id, it should always be there if the user is valid.
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User does not have the required role(s). Allowed roles: {', '.join(allowed_roles)}"
            )
        return current_user
    return role_checker

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

# 新增：权限检查函数
def is_admin(user) -> bool:
    """检查用户是否为管理员"""
    if not user or not user.role:
        return False
    # 假设管理员角色名称为 "admin" 或 "管理员"
    admin_role_names = ["admin", "管理员", "administrator"]
    return user.role.name.lower() in [name.lower() for name in admin_role_names]

def is_super_admin(user) -> bool:
    """检查用户是否为超级管理员"""
    if not user or not user.role:
        return False
    # 假设超级管理员角色名称为 "super_admin" 或 "超级管理员"
    super_admin_role_names = ["super_admin", "超级管理员", "system_admin"]
    return user.role.name.lower() in [name.lower() for name in super_admin_role_names]

def can_access_user_data(current_user, target_user_id: int) -> bool:
    """检查当前用户是否可以访问目标用户的数据"""
    # 管理员可以访问所有用户数据
    if is_admin(current_user):
        return True
    # 普通用户只能访问自己的数据
    return current_user.id == target_user_id

def can_access_session(current_user, session) -> bool:
    """检查当前用户是否可以访问指定会话"""
    # 管理员可以访问所有会话
    if is_admin(current_user):
        return True
    # 普通用户只能访问自己的会话
    return session.user_id == current_user.id

def can_access_message(current_user, message) -> bool:
    """检查当前用户是否可以访问指定消息"""
    # 管理员可以访问所有消息
    if is_admin(current_user):
        return True
    # 普通用户只能访问自己的消息
    return message.user_id == current_user.id

def can_modify_user_data(current_user, target_user_id: int) -> bool:
    """检查当前用户是否可以修改目标用户的数据"""
    # 超级管理员可以修改所有用户数据
    if is_super_admin(current_user):
        return True
    # 普通管理员可以修改普通用户数据，但不能修改其他管理员
    if is_admin(current_user):
        # 这里需要查询目标用户来判断，暂时允许
        return True
    # 普通用户只能修改自己的数据
    return current_user.id == target_user_id

# 新增：基于级别的权限控制装饰器
def require_level_permission(module_name: str, min_rank_value: int = None):
    """
    依赖注入函数：检查当前用户是否有指定模块和级别的权限
    
    Args:
        module_name: 模块名称
        min_rank_value: 最小级别要求(数值越小级别越高)，None表示不限制级别
    """
    from app.api.deps import get_current_active_user as deps_get_current_active_user
    from app.api.deps import get_db as deps_get_db
    
    def permission_checker(
        current_user = Depends(deps_get_current_active_user),
        db: Session = Depends(deps_get_db)
    ):
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户未认证"
            )
        
        # 1. 检查模块权限
        from app.db.models import RoleModulePermission
        
        permission = db.query(RoleModulePermission).filter(
            RoleModulePermission.role_id == current_user.role_id,
            RoleModulePermission.module_name == module_name
        ).first()
        
        if not permission or not permission.can_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"用户没有访问 {module_name} 模块的权限"
            )
        
        # 2. 检查级别权限（如果指定了级别要求）
        if min_rank_value is not None:
            if not current_user.level:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户未分配权限级别"
                )
            
            if current_user.level.rank_value > min_rank_value:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"用户权限级别不足，需要级别 {min_rank_value} 或更高"
                )
        
        return current_user
    
    return permission_checker

# 新增：级别权限检查函数
def check_level_permission(user, required_rank_value: int) -> bool:
    """
    检查用户级别是否满足要求
    
    Args:
        user: 用户对象
        required_rank_value: 要求的最小级别值（数值越小级别越高）
    
    Returns:
        bool: 是否满足级别要求
    """
    if not user or not user.level:
        return False
    
    # rank_value越小级别越高，所以用户的rank_value要小于等于要求值
    return user.level.rank_value <= required_rank_value

def can_manage_level_range(manager_user, target_user) -> bool:
    """
    检查管理者是否可以管理目标用户（基于级别）
    
    Args:
        manager_user: 管理者用户
        target_user: 目标用户
    
    Returns:
        bool: 是否可以管理
    """
    if not manager_user or not target_user:
        return False
    
    # 超级管理员可以管理所有人
    if is_super_admin(manager_user):
        return True
    
    # 不能管理自己
    if manager_user.id == target_user.id:
        return False
    
    # 检查级别权限：只能管理比自己级别低的用户
    if manager_user.level and target_user.level:
        # rank_value越小级别越高
        if manager_user.level.rank_value >= target_user.level.rank_value:
            return False
    
    # 检查角色权限：管理员可以管理普通用户
    if is_admin(manager_user) and not is_admin(target_user):
        return True
    
    return False

def get_accessible_data_sensitivity_levels(user) -> List[int]:
    """
    获取用户可访问的数据敏感度级别
    
    Args:
        user: 用户对象
    
    Returns:
        List[int]: 可访问的敏感度级别列表
    """
    if not user or not user.level:
        return [4]  # 默认只能访问公开数据
    
    # 新版本：从数据库中读取可配置的权限规则
    try:
        import sqlite3
        import os
        
        # 使用直接的SQLite连接避免异步问题
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'ai_security.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 查询该级别的数据访问权限配置
            cursor.execute("""
                SELECT sensitivity_level 
                FROM level_data_access 
                WHERE level_id = ? AND can_access = 1
                ORDER BY sensitivity_level
            """, (user.level.id,))
            
            results = cursor.fetchall()
            
            if results:
                # 如果有配置，使用配置的权限
                accessible_levels = [row[0] for row in results]
                return sorted(accessible_levels)
            else:
                # 如果没有配置，使用默认规则（向后兼容）
                return _get_default_accessibility_by_rank(user.level.rank_value)
                
        finally:
            conn.close()
            
    except Exception as e:
        print(f"Error reading level data access config: {e}")
        # 发生错误时使用默认规则
        return _get_default_accessibility_by_rank(user.level.rank_value)

def _get_default_accessibility_by_rank(rank_value: int) -> List[int]:
    """
    根据级别排序值获取默认的数据访问权限（向后兼容）
    
    Args:
        rank_value: 级别排序值
    
    Returns:
        List[int]: 可访问的敏感度级别列表
    """
    # 原有的硬编码规则，作为默认值和向后兼容
    if rank_value <= 1:  # 最高级别
        return [1, 2, 3, 4]  # 可访问所有级别
    elif rank_value <= 2:  # 中级
        return [2, 3, 4]  # 不能访问最敏感数据
    elif rank_value <= 3:  # 高级普通用户
        return [3, 4]  # 只能访问低敏感度数据
    else:  # 初级用户
        return [4]  # 只能访问公开数据

