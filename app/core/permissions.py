"""
权限检查模块 - 独立的权限验证函数
不依赖异步数据库连接，可以在同步和异步环境中使用
包含层级权限管理功能
"""

from typing import List, Set, Optional

def is_admin(user) -> bool:
    """检查用户是否为管理员"""
    if not user or not user.role:
        return False
    # 假设管理员角色名称为 "admin" 或 "管理员"
    admin_role_names = ["admin", "管理员", "administrator"]
    return user.role.name.lower() in [name.lower() for name in admin_role_names]

def is_super_admin(user) -> bool:
    """检查用户是否为超级管理员"""
    if not user or not user.role:
        return False
    # 假设超级管理员角色名称为 "super_admin" 或 "超级管理员"
    super_admin_role_names = ["super_admin", "超级管理员", "system_admin"]
    return user.role.name.lower() in [name.lower() for name in super_admin_role_names]

def can_access_user_data(current_user, target_user_id: int) -> bool:
    """检查当前用户是否可以访问目标用户的数据"""
    # 管理员可以访问所有用户数据
    if is_admin(current_user):
        return True
    # 普通用户只能访问自己的数据
    return current_user.id == target_user_id

def can_access_session(current_user, session) -> bool:
    """检查当前用户是否可以访问指定会话"""
    # 如果session为None（创建新会话时），允许所有登录用户
    if session is None:
        return current_user is not None
    # 管理员可以访问所有会话
    if is_admin(current_user):
        return True
    # 普通用户只能访问自己的会话
    return session.user_id == current_user.id

def can_access_message(current_user, message) -> bool:
    """检查当前用户是否可以访问指定消息"""
    # 管理员可以访问所有消息
    if is_admin(current_user):
        return True
    # 普通用户只能访问自己的消息
    return message.user_id == current_user.id

def can_modify_user_data(current_user, target_user_id: int) -> bool:
    """检查当前用户是否可以修改目标用户的数据"""
    # 超级管理员可以修改所有用户数据
    if is_super_admin(current_user):
        return True
    # 普通管理员可以修改普通用户数据，但不能修改其他管理员
    if is_admin(current_user):
        # 这里需要查询目标用户来判断，暂时允许
        return True
    # 普通用户只能修改自己的数据
    return current_user.id == target_user_id

def can_delete_message(current_user, message) -> bool:
    """检查当前用户是否可以删除指定消息"""
    # 管理员可以删除所有消息
    if is_admin(current_user):
        return True
    # 普通用户只能删除自己的消息
    return message.user_id == current_user.id

def can_modify_session(current_user, session) -> bool:
    """检查当前用户是否可以修改指定会话"""
    # 管理员可以修改所有会话
    if is_admin(current_user):
        return True
    # 普通用户只能修改自己的会话
    return session.user_id == current_user.id

def get_user_role_level(user) -> int:
    """获取用户角色级别（数字越小权限越高）"""
    if is_super_admin(user):
        return 1
    elif is_admin(user):
        return 2
    else:
        return 3

def has_higher_privilege(user1, user2) -> bool:
    """检查用户1是否比用户2有更高权限"""
    return get_user_role_level(user1) < get_user_role_level(user2)

# ====== 层级权限管理功能 ======

def is_manager_of(manager_user, target_user) -> bool:
    """检查manager_user是否为target_user的直接上级"""
    if not manager_user or not target_user:
        return False
    return target_user.manager_id == manager_user.id

def get_all_subordinates(user, all_users: List) -> Set[int]:
    """
    获取用户的所有下属（包括间接下属）
    使用递归方式获取整个下属树
    
    Args:
        user: 上级用户对象
        all_users: 所有用户列表（避免数据库查询）
    
    Returns:
        Set[int]: 所有下属用户ID的集合
    """
    if not user:
        return set()
    
    subordinates = set()
    # 查找直接下属
    direct_subordinates = [u for u in all_users if u.manager_id == user.id]
    
    for subordinate in direct_subordinates:
        subordinates.add(subordinate.id)
        # 递归获取间接下属
        indirect_subordinates = get_all_subordinates(subordinate, all_users)
        subordinates.update(indirect_subordinates)
    
    return subordinates

def get_management_chain(user, all_users: List) -> List[int]:
    """
    获取用户的管理链（从当前用户到最高级）
    
    Args:
        user: 当前用户对象
        all_users: 所有用户列表
    
    Returns:
        List[int]: 管理链用户ID列表，第一个是当前用户，最后一个是最高级
    """
    if not user:
        return []
    
    chain = [user.id]
    current_user = user
    visited = set([user.id])  # 防止循环引用
    
    while current_user.manager_id and current_user.manager_id not in visited:
        manager = next((u for u in all_users if u.id == current_user.manager_id), None)
        if not manager:
            break
        chain.append(manager.id)
        visited.add(manager.id)
        current_user = manager
    
    return chain

def can_manage_user(manager_user, target_user, all_users: List) -> bool:
    """
    检查manager_user是否可以管理target_user
    包括直接和间接管理关系
    
    Args:
        manager_user: 管理者用户对象
        target_user: 目标用户对象
        all_users: 所有用户列表
    
    Returns:
        bool: 是否可以管理
    """
    if not manager_user or not target_user:
        return False
    
    # 超级管理员可以管理所有人
    if is_super_admin(manager_user):
        return True
    
    # 不能管理自己
    if manager_user.id == target_user.id:
        return False
    
    # 不能管理同级或上级管理员
    if is_admin(target_user) and not is_super_admin(manager_user):
        return False
    
    # 检查是否在下属关系链中
    subordinates = get_all_subordinates(manager_user, all_users)
    return target_user.id in subordinates

def can_assign_models_to_user(assigner_user, target_user, all_users: List) -> bool:
    """
    检查assigner_user是否可以为target_user分配模型
    
    Args:
        assigner_user: 分配者用户对象
        target_user: 目标用户对象
        all_users: 所有用户列表
    
    Returns:
        bool: 是否可以分配模型
    """
    if not assigner_user or not target_user:
        return False
    
    # 超级管理员可以为所有人分配模型
    if is_super_admin(assigner_user):
        return True
    
    # 普通管理员可以为所有人分配模型（临时简化权限）
    if is_admin(assigner_user):
        return True
    
    # 普通用户不能分配模型
    return False

def can_view_user_permissions(viewer_user, target_user, all_users: List) -> bool:
    """
    检查viewer_user是否可以查看target_user的权限信息
    
    Args:
        viewer_user: 查看者用户对象
        target_user: 目标用户对象
        all_users: 所有用户列表
    
    Returns:
        bool: 是否可以查看权限
    """
    if not viewer_user or not target_user:
        return False
    
    # 可以查看自己的权限
    if viewer_user.id == target_user.id:
        return True
    
    # 超级管理员可以查看所有人的权限
    if is_super_admin(viewer_user):
        return True
    
    # 管理员可以查看下属的权限
    if is_admin(viewer_user):
        return can_manage_user(viewer_user, target_user, all_users)
    
    return False

def validate_hierarchy_integrity(all_users: List) -> List[str]:
    """
    验证用户层级结构的完整性
    检查是否存在循环引用等问题
    
    Args:
        all_users: 所有用户列表
    
    Returns:
        List[str]: 发现的问题列表
    """
    issues = []
    user_ids = {u.id for u in all_users}
    
    for user in all_users:
        if user.manager_id:
            # 检查manager_id是否存在
            if user.manager_id not in user_ids:
                issues.append(f"用户 {user.username}(ID:{user.id}) 的上级ID {user.manager_id} 不存在")
            
            # 检查循环引用
            visited = set()
            current_user = user
            while current_user.manager_id and current_user.manager_id not in visited:
                if current_user.manager_id == user.id:
                    issues.append(f"检测到循环引用：用户 {user.username}(ID:{user.id})")
                    break
                visited.add(current_user.id)
                current_user = next((u for u in all_users if u.id == current_user.manager_id), None)
                if not current_user:
                    break
    
    return issues 