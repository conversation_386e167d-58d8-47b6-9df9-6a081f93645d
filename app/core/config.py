# app/core/config.py

import os
from dotenv import load_dotenv
from typing import Optional

# Load environment variables from .env file
load_dotenv()

class Settings:
    PROJECT_NAME: str = "AI Security System"
    PROJECT_VERSION: str = "0.1.0"

    # Database settings
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL", "sqlite:///./ai_security.db") # Default to SQLite, can be overridden with env var

    # JWT Settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "a_very_secret_key_please_change_this") # CHANGE THIS!
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30 # 30 minutes
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7    # 7 days

    # First Superuser - for initial setup
    FIRST_SUPERUSER_USERNAME: str = os.getenv("FIRST_SUPERUSER_USERNAME", "admin")
    FIRST_SUPERUSER_PASSWORD: str = os.getenv("FIRST_SUPERUSER_PASSWORD", "adminpassword")
    FIRST_SUPERUSER_EMAIL: Optional[str] = os.getenv("FIRST_SUPERUSER_EMAIL", "<EMAIL>")

    # Admin Role configuration (used during initial setup and for permission checks)
    ADMIN_USER_ROLE_NAME: str = os.getenv("ADMIN_USER_ROLE_NAME", "admin")
    ADMIN_USER_ROLE_DESCRIPTION: str = os.getenv("ADMIN_USER_ROLE_DESCRIPTION", "Administrator role with full permissions.")
    DEFAULT_ADMIN_LEVEL_NAME: str = os.getenv("DEFAULT_ADMIN_LEVEL_NAME", "Tier 1")
    DEFAULT_ADMIN_LEVEL_DESCRIPTION: str = os.getenv("DEFAULT_ADMIN_LEVEL_DESCRIPTION", "Highest access level.")

    # API URL prefix
    API_V1_STR: str = "/api/v1"

    # CORS settings
    # Example for .env: BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:8080"
    _raw_cors_origins = os.getenv("BACKEND_CORS_ORIGINS", "*") # Default to all origins if not set
    if _raw_cors_origins == "*":
        BACKEND_CORS_ORIGINS: list[str] = ["*"]
    else:
        BACKEND_CORS_ORIGINS: list[str] = [origin.strip() for origin in _raw_cors_origins.split(',')]

    # Add other settings here as needed
    # For example, API keys for external services
    # OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")

settings = Settings()

# You can add helper functions here to get specific settings
# def get_database_url() -> str:
#     db_url = settings.DATABASE_URL
#     if not db_url:
#         raise ValueError("DATABASE_URL environment variable not set")
#     return db_url
