"""
审计日志记录模块
记录用户操作行为，支持权限管理和安全审计
"""

from typing import Optional, Dict, Any
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.models.operation_log import OperationLog
from app.db.models.user import User

class AuditLogger:
    """审计日志记录器"""
    
    @staticmethod
    async def log_operation_async(
        db: AsyncSession,
        operator_id: int,
        operation_type: str,
        operation_details: Optional[str] = None,
        target_user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> OperationLog:
        """
        异步记录操作日志
        
        Args:
            db: 异步数据库会话
            operator_id: 操作者用户ID
            operation_type: 操作类型
            operation_details: 操作详情
            target_user_id: 被操作的用户ID
            ip_address: 操作IP地址
            user_agent: 用户代理
            success: 操作是否成功
            error_message: 错误信息
        
        Returns:
            OperationLog: 创建的日志记录
        """
        log_entry = OperationLog(
            operator_id=operator_id,
            target_user_id=target_user_id,
            operation_type=operation_type,
            operation_details=operation_details,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message
        )
        
        db.add(log_entry)
        await db.commit()
        await db.refresh(log_entry)
        
        return log_entry
    
    @staticmethod
    def log_operation_sync(
        db: Session,
        operator_id: int,
        operation_type: str,
        operation_details: Optional[str] = None,
        target_user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> OperationLog:
        """
        同步记录操作日志
        
        Args:
            db: 同步数据库会话
            operator_id: 操作者用户ID
            operation_type: 操作类型
            operation_details: 操作详情
            target_user_id: 被操作的用户ID
            ip_address: 操作IP地址
            user_agent: 用户代理
            success: 操作是否成功
            error_message: 错误信息
        
        Returns:
            OperationLog: 创建的日志记录
        """
        log_entry = OperationLog(
            operator_id=operator_id,
            target_user_id=target_user_id,
            operation_type=operation_type,
            operation_details=operation_details,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message
        )
        
        db.add(log_entry)
        db.commit()
        db.refresh(log_entry)
        
        return log_entry

def get_client_info(request: Request) -> Dict[str, Optional[str]]:
    """
    从请求中提取客户端信息
    
    Args:
        request: FastAPI请求对象
    
    Returns:
        Dict: 包含IP地址和用户代理的字典
    """
    ip_address = None
    user_agent = None
    
    if request:
        # 获取真实IP地址（考虑代理）
        ip_address = (
            request.headers.get("X-Forwarded-For", "").split(",")[0].strip() or
            request.headers.get("X-Real-IP") or
            request.client.host if request.client else None
        )
        
        # 获取用户代理
        user_agent = request.headers.get("User-Agent")
    
    return {
        "ip_address": ip_address,
        "user_agent": user_agent
    }

# 操作类型常量
class OperationType:
    """操作类型枚举"""
    
    # 用户管理操作
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_ACTIVATE = "user_activate"
    USER_DEACTIVATE = "user_deactivate"
    
    # 权限管理操作
    PERMISSION_ASSIGN = "permission_assign"
    PERMISSION_REVOKE = "permission_revoke"
    ROLE_CHANGE = "role_change"
    MANAGER_ASSIGN = "manager_assign"
    MANAGER_REMOVE = "manager_remove"
    
    # 模型分配操作
    MODEL_ASSIGN = "model_assign"
    MODEL_REVOKE = "model_revoke"
    MODEL_DEFAULT_SET = "model_default_set"
    
    # 系统操作
    LOGIN = "login"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"
    
    # 安全策略操作
    SECURITY_POLICY_CREATE = "security_policy_create"
    SECURITY_POLICY_UPDATE = "security_policy_update"
    SECURITY_POLICY_DELETE = "security_policy_delete"
    
    # 数据访问操作
    DATA_VIEW = "data_view"
    DATA_EXPORT = "data_export"
    
    # 系统配置操作
    SYSTEM_CONFIG_UPDATE = "system_config_update"
    
    # 知识库操作
    KNOWLEDGE_UPLOAD = "knowledge_upload"
    KNOWLEDGE_UPDATE = "knowledge_update"
    KNOWLEDGE_DELETE = "knowledge_delete"
    KNOWLEDGE_SEARCH = "knowledge_search"
    KNOWLEDGE_VIEW = "knowledge_view"
    KNOWLEDGE_DOWNLOAD = "knowledge_download"
    KNOWLEDGE_VERSION_CREATE = "knowledge_version_create"
    KNOWLEDGE_VERSION_ROLLBACK = "knowledge_version_rollback"
    KNOWLEDGE_PERMISSION_CHANGE = "knowledge_permission_change"

def format_operation_details(operation_type: str, details: Dict[str, Any]) -> str:
    """
    格式化操作详情为可读的字符串
    
    Args:
        operation_type: 操作类型
        details: 操作详情字典
    
    Returns:
        str: 格式化后的操作详情
    """
    if operation_type == OperationType.USER_CREATE:
        return f"创建用户: {details.get('username', 'N/A')}, 角色: {details.get('role_name', 'N/A')}"
    
    elif operation_type == OperationType.MODEL_ASSIGN:
        return f"分配模型: {details.get('model_name', 'N/A')} 给用户: {details.get('target_username', 'N/A')}"
    
    elif operation_type == OperationType.MANAGER_ASSIGN:
        return f"设置管理关系: {details.get('manager_username', 'N/A')} 管理 {details.get('subordinate_username', 'N/A')}"
    
    elif operation_type == OperationType.PERMISSION_ASSIGN:
        return f"分配权限: {details.get('permission_name', 'N/A')} 给用户: {details.get('target_username', 'N/A')}"
    
    elif operation_type == OperationType.LOGIN:
        return f"用户登录: {details.get('username', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_UPLOAD:
        return f"上传知识文档: {details.get('filename', 'N/A')}, 类型: {details.get('content_type', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_UPDATE:
        return f"更新知识文档: {details.get('document_name', 'N/A')}, ID: {details.get('document_id', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_DELETE:
        return f"删除知识文档: {details.get('document_name', 'N/A')}, ID: {details.get('document_id', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_SEARCH:
        return f"搜索知识库: 查询: {details.get('query', 'N/A')}, 结果数: {details.get('result_count', 0)}"
    
    elif operation_type == OperationType.KNOWLEDGE_VIEW:
        return f"查看知识文档: {details.get('document_name', 'N/A')}, ID: {details.get('document_id', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_DOWNLOAD:
        return f"下载知识文档: {details.get('document_name', 'N/A')}, ID: {details.get('document_id', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_VERSION_CREATE:
        return f"创建文档版本: {details.get('document_name', 'N/A')}, 版本: {details.get('version', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_VERSION_ROLLBACK:
        return f"回滚文档版本: {details.get('document_name', 'N/A')}, 从版本: {details.get('from_version', 'N/A')} 到版本: {details.get('to_version', 'N/A')}"
    
    elif operation_type == OperationType.KNOWLEDGE_PERMISSION_CHANGE:
        return f"修改文档权限: {details.get('document_name', 'N/A')}, 操作: {details.get('action', 'N/A')}"
    
    else:
        # 通用格式
        return ", ".join([f"{k}: {v}" for k, v in details.items()]) 