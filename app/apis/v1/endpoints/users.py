# app/apis/v1/endpoints/users.py

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Any

from app import schemas # Use this for UserCreate, UserRead
from app.services import user_service, role_service, level_service
from app.api.deps import get_current_user
from app.db.models.user import User as UserModel # For type hinting current_user
from app.db.session import get_db

router = APIRouter()

@router.get("/", summary="List users (Placeholder)", response_model=List[schemas.UserRead])
async def read_users(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    Retrieve a list of users.
    (This is a placeholder and does not fetch real data yet)
    """
    # users = user_service.get_users(db, skip=skip, limit=limit) # Example
    # return users
    return [
        schemas.UserRead(id=1, username="user1_placeholder", email="<EMAIL>", is_active=True, role_id=1, created_at="2024-01-01T00:00:00Z", updated_at="2024-01-01T00:00:00Z"),
        schemas.UserRead(id=2, username="user2_placeholder", email="<EMAIL>", is_active=True, role_id=1, created_at="2024-01-01T00:00:00Z", updated_at="2024-01-01T00:00:00Z")
    ]

@router.post("/", response_model=schemas.UserRead, status_code=status.HTTP_201_CREATED)
async def create_new_user(
    user: schemas.UserCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    Create a new user.
    - **username**: Each username must be unique.
    - **email**: Each email must be unique.
    - **password**: Password will be hashed.
    - **role_id**: ID of the role for this user.
    - **level_id**: (Optional) ID of the level for this user.
    """
    db_user_by_username = user_service.get_user_by_username(db, username=user.username)
    if db_user_by_username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered.",
        )
    if user.email: # Email is optional in UserBase, so check if provided
        db_user_by_email = user_service.get_user_by_email(db, email=user.email)
        if db_user_by_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered.",
            )
    
    created_user = user_service.create_user(db=db, user_in=user)
    return created_user

@router.get("/me", summary="Get current user", response_model=schemas.UserRead)
async def read_users_me(current_user: UserModel = Depends(get_current_user)):
    """
    Fetch the current logged-in user.
    """
    return current_user

@router.get("/{user_id}", summary="Get user by ID", response_model=schemas.UserRead)
async def read_user_by_id(
    user_id: int,
    db: Session = Depends(get_db)
) -> Any:
    """
    Get a specific user by their ID.
    """
    db_user = user_service.get_user_by_id(db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return db_user

# We would also need to include this router in the main app or a v1 router
# For example, in app/apis/v1/api.py (which we might create next):
# from fastapi import APIRouter
# from .endpoints import users
#
# api_router_v1 = APIRouter()
# api_router_v1.include_router(users.router, prefix="/users", tags=["users"])
