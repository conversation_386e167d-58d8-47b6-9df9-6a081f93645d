# app/apis/v1/endpoints/token.py

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm # Standard form for username and password
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.services import user_service
from app.core import security
from app.schemas.token import Token

router = APIRouter()

@router.post("/login/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(), 
    db: Session = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests.
    
    Uses OAuth2PasswordRequestForm, so expects 'username' and 'password'
    in a form-data body.
    """
    user = user_service.authenticate_user(db, username=form_data.username, password=form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = security.create_access_token(
        subject=user.username # Or user.id, if get_current_user expects user id
    )
    return {"access_token": access_token, "token_type": "bearer"}
