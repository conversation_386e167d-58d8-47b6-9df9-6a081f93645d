# app/apis/v1/endpoints/role.py

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.db.session import get_db
from app.schemas.role import RoleCreate, RoleRead, RoleUpdate
from app.services import role_service # Use the imported service module
from app.api.deps import get_current_user
from app.core.security import require_role
from app.core.config import settings
from app.db.models.user import User as UserModel # For type hinting current_user
# from app.db.models.role import Role # Not strictly needed if using RoleRead schema

router = APIRouter()

@router.post("/", response_model=RoleRead, status_code=status.HTTP_201_CREATED)
def create_new_role(
    role_in: RoleCreate, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_role([settings.ADMIN_USER_ROLE_NAME]))
):
    existing_role = role_service.get_role_by_name(db, name=role_in.name)
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role with this name already exists.",
        )
    role = role_service.create_role(db=db, role_in=role_in)
    return role

@router.get("/{role_id}", response_model=RoleRead)
def read_role_by_id(
    role_id: int, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    role = role_service.get_role(db, role_id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found.",
        )
    return role

@router.get("/", response_model=List[RoleRead])
def read_all_roles(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    roles = role_service.get_roles(db, skip=skip, limit=limit)
    return roles

@router.put("/{role_id}", response_model=RoleRead)
def update_existing_role(
    role_id: int, 
    role_in: RoleUpdate, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_role([settings.ADMIN_USER_ROLE_NAME]))
):
    # Check if role with new name already exists (if name is being changed)
    if role_in.name:
        existing_role_with_name = role_service.get_role_by_name(db, name=role_in.name)
        if existing_role_with_name and existing_role_with_name.id != role_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Another role with this name already exists.",
            )
            
    updated_role = role_service.update_role(db, role_id=role_id, role_in=role_in)
    if not updated_role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found to update.",
        )
    return updated_role

@router.delete("/{role_id}", response_model=RoleRead) # Or a simple status code
def delete_existing_role(
    role_id: int, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_role([settings.ADMIN_USER_ROLE_NAME]))
):
    deleted_role = role_service.delete_role(db, role_id=role_id)
    if not deleted_role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found to delete.",
        )
    return deleted_role # Returns the deleted role data
