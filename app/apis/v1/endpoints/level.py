# app/apis/v1/endpoints/level.py

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.db.session import get_db
from app.schemas.level import LevelCreate, LevelRead, LevelUpdate
from app.services import level_service # Use the imported service module
from app.api.deps import get_current_user
from app.core.security import require_role
from app.core.config import settings
from app.db.models.user import User as UserModel # For type hinting current_user

router = APIRouter()

@router.post("/", response_model=LevelRead, status_code=status.HTTP_201_CREATED)
def create_new_level(
    level_in: LevelCreate, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_role([settings.ADMIN_USER_ROLE_NAME]))
):
    existing_level = level_service.get_level_by_name(db, name=level_in.name)
    if existing_level:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Level with this name already exists.",
        )
    level = level_service.create_level(db=db, level_in=level_in)
    return level

@router.get("/{level_id}", response_model=LevelRead)
def read_level_by_id(
    level_id: int, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    level = level_service.get_level(db, level_id=level_id)
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Level not found.",
        )
    return level

@router.get("/", response_model=List[LevelRead])
def read_all_levels(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    levels = level_service.get_levels(db, skip=skip, limit=limit)
    return levels

@router.put("/{level_id}", response_model=LevelRead)
def update_existing_level(
    level_id: int, 
    level_in: LevelUpdate, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_role([settings.ADMIN_USER_ROLE_NAME]))
):
    if level_in.name:
        existing_level_with_name = level_service.get_level_by_name(db, name=level_in.name)
        if existing_level_with_name and existing_level_with_name.id != level_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Another level with this name already exists.",
            )
            
    updated_level = level_service.update_level(db, level_id=level_id, level_in=level_in)
    if not updated_level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Level not found to update.",
        )
    return updated_level

@router.delete("/{level_id}", response_model=LevelRead)
def delete_existing_level(
    level_id: int, 
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_role([settings.ADMIN_USER_ROLE_NAME]))
):
    deleted_level = level_service.delete_level(db, level_id=level_id)
    if not deleted_level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Level not found to delete.",
        )
    return deleted_level
