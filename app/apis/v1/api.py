# app/apis/v1/api.py

from fastapi import APIRouter

from .endpoints import users, role, level, token # <--- Changed login to token

api_router_v1 = APIRouter()

# Include routers from endpoint modules
api_router_v1.include_router(users.router, prefix="/users", tags=["Users"])
api_router_v1.include_router(role.router, prefix="/roles", tags=["Roles"]) # <--- Added role router
api_router_v1.include_router(level.router, prefix="/levels", tags=["Levels"]) # <--- Added level router
api_router_v1.include_router(token.router, tags=["Login"]) # Prefix is handled by the endpoint in token.py

# Add other endpoint routers here as they are created
# For example:
# from .endpoints import items
# api_router_v1.include_router(items.router, prefix="/items", tags=["Items"])
