#!/usr/bin/env python3
"""
批量修复前端代码中硬编码的API地址
"""
import os
import re
import glob

def fix_file(file_path):
    """修复单个文件中的硬编码URL"""
    print(f"处理文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 检查是否已经导入了apiFetch
    has_api_fetch_import = 'apiFetch' in content
    
    # 如果文件包含硬编码URL但没有导入apiFetch，添加导入
    if 'localhost:8000' in content and not has_api_fetch_import:
        # 找到最后一个import语句的位置
        import_pattern = r"import\s+.*?from\s+['\"].*?['\"];?\s*\n"
        imports = list(re.finditer(import_pattern, content, re.MULTILINE))
        
        if imports:
            last_import = imports[-1]
            insert_pos = last_import.end()
            
            # 插入apiFetch导入
            api_import = "import { apiFetch } from '../../config/api';\n"
            
            # 根据文件路径调整导入路径
            if '/pages/' in file_path:
                api_import = "import { apiFetch } from '../../config/api';\n"
            elif '/components/' in file_path:
                if file_path.count('/') >= 4:  # components/xxx/yyy.tsx
                    api_import = "import { apiFetch } from '../../config/api';\n"
                else:  # components/xxx.tsx
                    api_import = "import { apiFetch } from '../config/api';\n"
            elif '/services/' in file_path:
                api_import = "import { apiFetch } from '../config/api';\n"
            
            content = content[:insert_pos] + api_import + content[insert_pos:]
    
    # 替换硬编码的fetch调用
    patterns = [
        # 基本的fetch调用
        (r"fetch\(\s*['\"]http://localhost:8000/api/v1/([^'\"]*)['\"](?:\s*,\s*\{[^}]*headers[^}]*\})?\s*\)", 
         r"apiFetch('\1')"),
        
        # 带有复杂选项的fetch调用
        (r"fetch\(\s*['\"]http://localhost:8000/api/v1/([^'\"]*)['\"](?:\s*,\s*\{[^}]*\})?\s*\)", 
         r"apiFetch('\1')"),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # 手动处理一些特殊情况
    # 处理模板字符串
    content = re.sub(
        r"fetch\(\s*`http://localhost:8000/api/v1/([^`]*)`(?:\s*,\s*\{[^}]*\})?\s*\)",
        r"apiFetch(`\1`)",
        content
    )
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 已修复")
        return True
    else:
        print(f"  ⏭️  无需修改")
        return False

def main():
    """主函数"""
    frontend_dir = "ai-security-frontend/src"
    
    if not os.path.exists(frontend_dir):
        print(f"错误: 前端目录 {frontend_dir} 不存在")
        return
    
    # 查找所有TypeScript和JavaScript文件
    file_patterns = [
        f"{frontend_dir}/**/*.ts",
        f"{frontend_dir}/**/*.tsx",
        f"{frontend_dir}/**/*.js",
        f"{frontend_dir}/**/*.jsx"
    ]
    
    files_to_fix = []
    for pattern in file_patterns:
        files_to_fix.extend(glob.glob(pattern, recursive=True))
    
    # 过滤出包含硬编码URL的文件
    files_with_hardcoded_urls = []
    for file_path in files_to_fix:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'localhost:8000' in content:
                    files_with_hardcoded_urls.append(file_path)
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
    
    print(f"找到 {len(files_with_hardcoded_urls)} 个包含硬编码URL的文件")
    
    fixed_count = 0
    for file_path in files_with_hardcoded_urls:
        try:
            if fix_file(file_path):
                fixed_count += 1
        except Exception as e:
            print(f"修复文件失败 {file_path}: {e}")
    
    print(f"\n修复完成: {fixed_count}/{len(files_with_hardcoded_urls)} 个文件")

if __name__ == "__main__":
    main()
