#!/usr/bin/env python3
"""
检查向量数据库中的向量
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.services.document_vectorizer import DocumentVectorizer
import sqlite3

async def check_vectors():
    """检查向量数据库状态"""
    
    # 1. 检查数据库中的chunks
    print("1. 检查数据库中的chunks...")
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT document_id, COUNT(*) FROM knowledge_chunks GROUP BY document_id')
    chunk_counts = cursor.fetchall()
    
    print("数据库中的chunks分布:")
    for doc_id, count in chunk_counts:
        print(f"  文档ID {doc_id}: {count} 个chunks")
    
    # 查看文档ID 2的chunks
    cursor.execute('SELECT id, chunk_index, content FROM knowledge_chunks WHERE document_id = 2 ORDER BY chunk_index')
    doc2_chunks = cursor.fetchall()
    
    print(f"\n文档ID 2的chunks:")
    for chunk in doc2_chunks:
        print(f"  Chunk {chunk[1]} (ID: {chunk[0]}): {chunk[2][:100]}...")
    
    conn.close()
    
    # 2. 检查向量化器的向量数据库
    print("\n2. 检查向量化器...")
    vectorizer = DocumentVectorizer()
    
    # 检查向量数据库中的向量
    print("向量数据库中的向量:")
    try:
        # 尝试搜索一个通用词汇，看看能返回什么
        results = await vectorizer.search_similar_chunks("路径", top_k=10, threshold=0.1)
        print(f"搜索'路径'的结果数量: {len(results)}")
        
        for i, result in enumerate(results):
            print(f"  {i+1}. ID: {result.get('chunk_id', 'N/A')}, 相似度: {result.get('similarity', 0):.4f}")
            print(f"      内容: {result.get('content', '')[:100]}...")
            print()
            
    except Exception as e:
        print(f"搜索失败: {e}")
    
    # 3. 检查特定的chunk_id
    print("\n3. 检查特定chunk_id...")
    target_ids = ['doc_2_chunk_0', 'doc_2_chunk_1', 'doc_2_chunk_2']
    
    for chunk_id in target_ids:
        try:
            # 尝试直接搜索这个chunk_id
            results = await vectorizer.search_similar_chunks("模型", top_k=20, threshold=0.0)
            found = False
            for result in results:
                if result.get('chunk_id') == chunk_id:
                    print(f"找到 {chunk_id}: 相似度 {result.get('similarity', 0):.4f}")
                    found = True
                    break
            if not found:
                print(f"未找到 {chunk_id}")
        except Exception as e:
            print(f"检查 {chunk_id} 失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_vectors())
