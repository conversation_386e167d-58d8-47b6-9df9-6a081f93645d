#!/usr/bin/env python3
"""
AI安全防护系统后端启动脚本 - 异步版本
解决MissingGreenlet和其他异步相关问题
"""

import os
import sys
import uvicorn
from pathlib import Path

def setup_environment():
    """设置环境变量和Python路径"""
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 设置PYTHONPATH
    sys.path.insert(0, str(project_root))
    os.environ['PYTHONPATH'] = str(project_root)
    
    # 设置环境变量 - 修复数据库路径
    db_path = project_root / 'ai_security.db'
    os.environ.setdefault('DATABASE_URL', f'sqlite:///{db_path}')
    os.environ.setdefault('SECRET_KEY', 'your-secret-key-here-change-in-production')
    os.environ.setdefault('JWT_ALGORITHM', 'HS256')
    os.environ.setdefault('ACCESS_TOKEN_EXPIRE_MINUTES', '30')
    
    print(f"项目根目录: {project_root}")
    print(f"数据库路径: {os.environ['DATABASE_URL']}")
    print(f"PYTHONPATH: {os.environ['PYTHONPATH']}")

def check_database():
    """检查数据库文件是否存在"""
    db_path = Path(__file__).parent / 'ai_security.db'
    if not db_path.exists():
        print(f"警告: 数据库文件不存在: {db_path}")
        print("请先运行数据库初始化脚本")
    else:
        print(f"数据库文件存在: {db_path}")

def main():
    """主函数"""
    print("=== AI安全防护系统后端启动 (异步版本) ===")
    
    # 设置环境
    setup_environment()
    
    # 检查数据库
    check_database()
    
    try:
        # 导入应用
        from app.main import app
        
        print("\n=== 启动FastAPI服务器 ===")
        print("访问地址:")
        print("- API文档: http://localhost:8000/docs")
        print("- 健康检查: http://localhost:8000/health")
        print("- API健康检查: http://localhost:8000/api/v1/health")
        print("\n按 Ctrl+C 停止服务器")
        
        # 启动服务器
        uvicorn.run(
            app,  # 直接传递app对象而不是字符串
            host="0.0.0.0",
            port=8000,
            reload=False,  # 关闭自动重载以避免异步问题
            log_level="info",
            access_log=True,
            # 异步相关配置
            loop="asyncio",
            workers=1,  # 单进程避免异步问题
        )
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请检查依赖是否正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 