#!/usr/bin/env python3
"""
配置使用本地GPT-2模型
"""

import sqlite3
import json
import sys

def test_gpt2():
    """测试GPT-2是否可用"""
    print("🔍 测试本地GPT-2模型...")
    try:
        from transformers import pipeline
        # 测试加载
        print("正在加载GPT-2模型（首次可能需要下载）...")
        generator = pipeline("text-generation", model="gpt2", max_new_tokens=50)
        
        # 测试生成
        result = generator("Hello, I am", num_return_sequences=1)
        print("✅ GPT-2模型测试成功!")
        print(f"测试输出: {result[0]['generated_text'][:100]}...")
        return True
    except Exception as e:
        print(f"❌ GPT-2测试失败: {str(e)}")
        return False

def configure_local_gpt2():
    """配置数据库使用本地GPT-2"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 停用所有现有模型
    cursor.execute("UPDATE llm_models SET is_active = 0")
    
    # 添加本地GPT-2配置
    config_params = json.dumps({
        "api_format": "local_transformers",
        "model": "gpt2",
        "temperature": 0.7,
        "max_tokens": 500,
        "device": "cpu"
    })
    
    # 检查是否已存在本地配置
    cursor.execute("""
        SELECT id FROM llm_models 
        WHERE name LIKE '%本地%' OR name LIKE '%GPT-2%'
    """)
    existing = cursor.fetchone()
    
    if existing:
        print(f"✅ 更新现有本地模型配置 (ID: {existing[0]})")
        cursor.execute("""
            UPDATE llm_models 
            SET name = ?, api_url = ?, config_params = ?, is_active = 1
            WHERE id = ?
        """, ("本地 GPT-2", "local://gpt2", config_params, existing[0]))
    else:
        print("✅ 创建新的本地GPT-2配置")
        cursor.execute("""
            INSERT INTO llm_models (name, api_url, config_params, is_active)
            VALUES (?, ?, ?, 1)
        """, ("本地 GPT-2", "local://gpt2", config_params))
    
    conn.commit()
    
    # 显示当前配置
    print("\n📋 当前LLM模型配置:")
    cursor.execute("SELECT id, name, api_url, is_active FROM llm_models")
    for row in cursor.fetchall():
        status = "✅" if row[3] else "❌"
        print(f"  {status} ID:{row[0]} - {row[1]}")
    
    conn.close()

def update_rag_service():
    """提示需要更新RAG服务"""
    print("\n⚠️  注意：需要修改RAG服务以支持本地模型")
    print("请在 app/services/rag_service.py 中添加对 local:// 协议的支持")
    print("或者继续使用Ollama作为本地模型方案")

def main():
    print("🚀 配置本地GPT-2模型")
    print("=" * 50)
    
    # 测试GPT-2
    if not test_gpt2():
        print("\n❌ GPT-2测试失败，请检查环境")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 配置数据库
    configure_local_gpt2()
    
    print("\n✨ 配置完成！")
    print("\n💡 提示:")
    print("1. GPT-2是英文模型，中文效果可能不理想")
    print("2. 响应速度会比远程模型快很多（几秒钟）")
    print("3. 如需更好的中文支持，建议使用Ollama + qwen模型")
    
    update_rag_service()

if __name__ == "__main__":
    main() 
"""
配置使用本地GPT-2模型
"""

import sqlite3
import json
import sys

def test_gpt2():
    """测试GPT-2是否可用"""
    print("🔍 测试本地GPT-2模型...")
    try:
        from transformers import pipeline
        # 测试加载
        print("正在加载GPT-2模型（首次可能需要下载）...")
        generator = pipeline("text-generation", model="gpt2", max_new_tokens=50)
        
        # 测试生成
        result = generator("Hello, I am", num_return_sequences=1)
        print("✅ GPT-2模型测试成功!")
        print(f"测试输出: {result[0]['generated_text'][:100]}...")
        return True
    except Exception as e:
        print(f"❌ GPT-2测试失败: {str(e)}")
        return False

def configure_local_gpt2():
    """配置数据库使用本地GPT-2"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 停用所有现有模型
    cursor.execute("UPDATE llm_models SET is_active = 0")
    
    # 添加本地GPT-2配置
    config_params = json.dumps({
        "api_format": "local_transformers",
        "model": "gpt2",
        "temperature": 0.7,
        "max_tokens": 500,
        "device": "cpu"
    })
    
    # 检查是否已存在本地配置
    cursor.execute("""
        SELECT id FROM llm_models 
        WHERE name LIKE '%本地%' OR name LIKE '%GPT-2%'
    """)
    existing = cursor.fetchone()
    
    if existing:
        print(f"✅ 更新现有本地模型配置 (ID: {existing[0]})")
        cursor.execute("""
            UPDATE llm_models 
            SET name = ?, api_url = ?, config_params = ?, is_active = 1
            WHERE id = ?
        """, ("本地 GPT-2", "local://gpt2", config_params, existing[0]))
    else:
        print("✅ 创建新的本地GPT-2配置")
        cursor.execute("""
            INSERT INTO llm_models (name, api_url, config_params, is_active)
            VALUES (?, ?, ?, 1)
        """, ("本地 GPT-2", "local://gpt2", config_params))
    
    conn.commit()
    
    # 显示当前配置
    print("\n📋 当前LLM模型配置:")
    cursor.execute("SELECT id, name, api_url, is_active FROM llm_models")
    for row in cursor.fetchall():
        status = "✅" if row[3] else "❌"
        print(f"  {status} ID:{row[0]} - {row[1]}")
    
    conn.close()

def update_rag_service():
    """提示需要更新RAG服务"""
    print("\n⚠️  注意：需要修改RAG服务以支持本地模型")
    print("请在 app/services/rag_service.py 中添加对 local:// 协议的支持")
    print("或者继续使用Ollama作为本地模型方案")

def main():
    print("🚀 配置本地GPT-2模型")
    print("=" * 50)
    
    # 测试GPT-2
    if not test_gpt2():
        print("\n❌ GPT-2测试失败，请检查环境")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 配置数据库
    configure_local_gpt2()
    
    print("\n✨ 配置完成！")
    print("\n💡 提示:")
    print("1. GPT-2是英文模型，中文效果可能不理想")
    print("2. 响应速度会比远程模型快很多（几秒钟）")
    print("3. 如需更好的中文支持，建议使用Ollama + qwen模型")
    
    update_rag_service()

if __name__ == "__main__":
    main() 