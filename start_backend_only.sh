#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "========================================"
echo "   Starting Backend Service"
echo "========================================"
echo

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查虚拟环境是否存在
if [ ! -d ".venv" ]; then
    echo -e "${RED}Error: Virtual environment not found. Please run: python -m venv .venv${NC}"
    exit 1
fi

# 检查后端依赖
if [ ! -f ".venv/bin/activate" ]; then
    echo -e "${RED}Error: Virtual environment activation script not found${NC}"
    exit 1
fi

echo -e "${BLUE}Activating virtual environment and starting backend...${NC}"
source .venv/bin/activate
    .venv/bin/python3 start_backend.py 