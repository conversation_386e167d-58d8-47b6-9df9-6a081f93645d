#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import asyncio
import logging
from sqlalchemy import text
from app.db.session import SyncSessionLocal
from app.core.security import get_password_hash

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_admin_user():
    """创建管理员用户"""
    db = SyncSessionLocal()
    try:
        # 检查是否已有admin用户
        result = db.execute(text("SELECT * FROM users WHERE username = 'admin'"))
        admin_user = result.fetchone()
        
        if admin_user:
            logger.info("Admin user already exists")
            return
        
        # 创建admin角色（如果不存在）
        result = db.execute(text("SELECT * FROM roles WHERE name = 'admin'"))
        admin_role = result.fetchone()
        
        if not admin_role:
            db.execute(text("""
                INSERT INTO roles (name, description) 
                VALUES ('admin', 'Administrator role')
            """))
            db.commit()
            logger.info("Admin role created")
            
            # 获取刚创建的角色ID
            result = db.execute(text("SELECT id FROM roles WHERE name = 'admin'"))
            admin_role = result.fetchone()
        
        role_id = admin_role[0] if admin_role else 1
        
        # 创建admin用户
        hashed_password = get_password_hash("admin123")
        db.execute(text("""
            INSERT INTO users (username, email, hashed_password, role_id, is_active)
            VALUES ('admin', '<EMAIL>', :password, :role_id, 1)
        """), {"password": hashed_password, "role_id": role_id})
        
        # 创建普通用户角色
        result = db.execute(text("SELECT * FROM roles WHERE name = 'user'"))
        user_role = result.fetchone()
        
        if not user_role:
            db.execute(text("""
                INSERT INTO roles (name, description) 
                VALUES ('user', 'Regular user role')
            """))
            db.commit()
            logger.info("User role created")
            
            result = db.execute(text("SELECT id FROM roles WHERE name = 'user'"))
            user_role = result.fetchone()
        
        user_role_id = user_role[0] if user_role else 2
        
        # 创建测试用户
        hashed_password_user = get_password_hash("user123")
        db.execute(text("""
            INSERT INTO users (username, email, hashed_password, role_id, is_active)
            VALUES ('user1', '<EMAIL>', :password, :role_id, 1)
        """), {"password": hashed_password_user, "role_id": user_role_id})
        
        db.commit()
        logger.info("Admin user created with password: admin123")
        logger.info("Test user created: user1 / user123")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating admin user: {e}")
        raise
    finally:
        db.close()

def main():
    logger.info("Initializing database with admin user...")
    create_admin_user()
    logger.info("Database initialization completed!")

if __name__ == "__main__":
    main()