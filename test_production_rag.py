#!/usr/bin/env python3
# test_production_rag.py

"""测试生产级别的RAG系统"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有生产级别的导入"""
    print("=== 测试生产级别依赖导入 ===\n")
    
    imports_status = []
    
    # 测试文档解析库
    try:
        import pypdf
        imports_status.append(("pypdf", "✅", pypdf.__version__))
    except ImportError as e:
        imports_status.append(("pypdf", "❌", str(e)))
    
    try:
        import docx
        imports_status.append(("python-docx", "✅", "已安装"))
    except ImportError as e:
        imports_status.append(("python-docx", "❌", str(e)))
    
    try:
        import openpyxl
        imports_status.append(("openpyxl", "✅", openpyxl.__version__))
    except ImportError as e:
        imports_status.append(("openpyxl", "❌", str(e)))
    
    try:
        import pptx
        imports_status.append(("python-pptx", "✅", pptx.__version__))
    except ImportError as e:
        imports_status.append(("python-pptx", "❌", str(e)))
    
    # 测试NLP库
    try:
        import sentence_transformers
        imports_status.append(("sentence-transformers", "✅", sentence_transformers.__version__))
    except ImportError as e:
        imports_status.append(("sentence-transformers", "❌", str(e)))
    
    try:
        import tiktoken
        imports_status.append(("tiktoken", "✅", "0.6.0"))
    except ImportError as e:
        imports_status.append(("tiktoken", "❌", str(e)))
    
    # 测试向量数据库
    try:
        import chromadb
        imports_status.append(("chromadb", "✅", chromadb.__version__))
    except ImportError as e:
        imports_status.append(("chromadb", "❌", str(e)))
    
    # 测试数据处理库
    try:
        import numpy
        imports_status.append(("numpy", "✅", numpy.__version__))
    except ImportError as e:
        imports_status.append(("numpy", "❌", str(e)))
    
    try:
        import pandas
        imports_status.append(("pandas", "✅", pandas.__version__))
    except ImportError as e:
        imports_status.append(("pandas", "❌", str(e)))
    
    # 打印结果
    for lib, status, version in imports_status:
        print(f"{lib:<25} {status} {version}")
    
    # 检查是否所有库都成功导入
    all_success = all(status == "✅" for _, status, _ in imports_status)
    
    print(f"\n总结: {'所有依赖都已成功安装！' if all_success else '部分依赖缺失，请检查安装'}")
    
    return all_success


def test_document_parser():
    """测试生产级别的文档解析器"""
    print("\n\n=== 测试文档解析器 ===\n")
    
    try:
        from app.services.document_parser_production import production_document_parser
        
        # 创建测试文件
        test_file = "test_doc.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文档。\n用于验证生产级别的文档解析器。")
        
        # 测试解析
        result = production_document_parser.parse_document(test_file)
        
        print(f"解析器类型: {production_document_parser.__class__.__name__}")
        print(f"支持的解析器数量: {len(production_document_parser.parsers)}")
        print(f"文件类型: {result['metadata']['file_type']}")
        print(f"内容长度: {len(result['content'])} 字符")
        
        # 测试分块
        chunks = production_document_parser.split_text_into_chunks(
            result['content'],
            chunk_size=50,
            chunk_overlap=10
        )
        print(f"生成块数: {len(chunks)}")
        
        # 清理
        os.remove(test_file)
        
        print("\n✅ 文档解析器测试通过")
        return True
        
    except Exception as e:
        print(f"\n❌ 文档解析器测试失败: {str(e)}")
        return False


def test_embedding_model():
    """测试嵌入模型"""
    print("\n\n=== 测试嵌入模型 ===\n")
    
    try:
        from app.services.document_vectorizer_production import ProductionEmbedding
        
        # 创建嵌入模型
        embedding = ProductionEmbedding()
        
        # 测试文本
        texts = [
            "这是第一个测试文本",
            "这是第二个测试文本",
            "AI安全防护系统"
        ]
        
        # 生成嵌入
        embeddings = embedding.encode(texts)
        
        print(f"嵌入模型: {embedding.model_name}")
        print(f"向量维度: {embedding.dimension}")
        print(f"生成向量数: {embeddings.shape[0]}")
        print(f"向量形状: {embeddings.shape}")
        
        # 计算相似度
        import numpy as np
        similarity = np.dot(embeddings[0], embeddings[1])
        print(f"\n文本1和文本2的相似度: {similarity:.4f}")
        
        print("\n✅ 嵌入模型测试通过")
        return True
        
    except Exception as e:
        print(f"\n❌ 嵌入模型测试失败: {str(e)}")
        return False


def test_vector_database():
    """测试向量数据库"""
    print("\n\n=== 测试向量数据库 ===\n")
    
    try:
        import chromadb
        import tempfile
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 初始化ChromaDB
            client = chromadb.PersistentClient(path=temp_dir)
            
            # 创建集合
            collection = client.create_collection(
                name="test_collection",
                metadata={"description": "测试集合"}
            )
            
            # 添加文档
            collection.add(
                documents=["这是第一个文档", "这是第二个文档"],
                metadatas=[{"source": "test1"}, {"source": "test2"}],
                ids=["doc1", "doc2"]
            )
            
            # 查询
            results = collection.query(
                query_texts=["文档"],
                n_results=2
            )
            
            print(f"ChromaDB版本: {chromadb.__version__}")
            print(f"集合名称: {collection.name}")
            print(f"文档数量: {collection.count()}")
            print(f"查询结果数: {len(results['ids'][0])}")
            
            print("\n✅ 向量数据库测试通过")
            return True
            
    except Exception as e:
        print(f"\n❌ 向量数据库测试失败: {str(e)}")
        return False


def test_full_pipeline():
    """测试完整的RAG管道"""
    print("\n\n=== 测试完整RAG管道 ===\n")
    
    try:
        import asyncio
        from app.services.document_parser_production import production_document_parser
        from app.services.document_vectorizer_production import production_document_vectorizer
        
        # 创建测试文档
        test_file = "test_rag_doc.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("""AI安全防护系统文档

本系统提供以下功能：
1. 关键词过滤 - 检测和过滤敏感词汇
2. 数据脱敏 - 保护用户隐私信息
3. 权限控制 - 基于角色的访问控制
4. 审计日志 - 记录所有操作

系统采用模块化设计，支持灵活扩展。""")
        
        # 1. 解析文档
        print("1. 解析文档...")
        parsed_doc = production_document_parser.process_document_for_vectorization(
            test_file,
            chunk_size=100,
            chunk_overlap=20
        )
        print(f"   - 生成 {len(parsed_doc['chunks'])} 个文本块")
        
        # 2. 向量化（模拟）
        print("\n2. 向量化处理...")
        stats = production_document_vectorizer.calculate_stats(parsed_doc['chunks'])
        print(f"   - 总字符数: {stats['total_chars']}")
        print(f"   - 平均块大小: {stats['avg_chunk_chars']:.1f} 字符")
        
        # 3. 搜索测试
        print("\n3. 语义搜索测试...")
        query = "权限控制"
        print(f"   - 查询: '{query}'")
        
        # 简单的关键词匹配模拟
        matches = []
        for chunk in parsed_doc['chunks']:
            if query in chunk['content']:
                matches.append(chunk)
        
        print(f"   - 找到 {len(matches)} 个相关块")
        
        # 清理
        os.remove(test_file)
        
        print("\n✅ 完整RAG管道测试通过")
        return True
        
    except Exception as e:
        print(f"\n❌ RAG管道测试失败: {str(e)}")
        if 'test_file' in locals() and os.path.exists(test_file):
            os.remove(test_file)
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("生产级别RAG系统测试")
    print("=" * 60)
    
    results = []
    
    # 1. 测试导入
    results.append(("依赖导入", test_imports()))
    
    # 2. 测试文档解析器
    results.append(("文档解析器", test_document_parser()))
    
    # 3. 测试嵌入模型
    results.append(("嵌入模型", test_embedding_model()))
    
    # 4. 测试向量数据库
    results.append(("向量数据库", test_vector_database()))
    
    # 5. 测试完整管道
    results.append(("完整RAG管道", test_full_pipeline()))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:<20} {status}")
    
    all_passed = all(passed for _, passed in results)
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！生产级别RAG系统已准备就绪！")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    print("=" * 60)


if __name__ == "__main__":
    main() 