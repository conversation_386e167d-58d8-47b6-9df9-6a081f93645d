#!/usr/bin/env python3
"""
带认证的RAG功能测试
"""

import requests
import json

def test_rag_with_auth():
    """测试带认证的RAG功能"""
    
    base_url = "http://localhost:8000"
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_response = requests.post(
        f"{base_url}/api/v1/login/access-token",
        data={
            "username": "admin",
            "password": "admin123"
        }
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 测试RAG查询
    test_queries = [
        "本地模型路径是什么？",
        "NSFW训练集在哪里？",
        "clip-vit模型的路径是什么？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 测试查询 {i}: {query}")
        print("-" * 50)
        
        try:
            response = requests.post(
                f"{base_url}/api/v1/rag/query",
                headers=headers,
                json={
                    "query": query,
                    "model_id": 1
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ 状态: 成功")
                print(f"📝 回答: {result.get('answer', '无回答')}")
                print(f"🔗 使用上下文: {result.get('context_used', False)}")
                
                sources = result.get('sources', [])
                print(f"📚 参考文档数量: {len(sources)}")
                
                for j, source in enumerate(sources[:2], 1):
                    similarity = source.get('similarity', 0)
                    if hasattr(similarity, 'item'):
                        similarity = similarity.item()
                    
                    print(f"  {j}. 文档: {source.get('document_name', '未知')}")
                    print(f"     相似度: {similarity:.1%}")
                    print(f"     内容: {source.get('content', '')[:150]}...")
                    
            else:
                print(f"❌ 查询失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_rag_with_auth()
