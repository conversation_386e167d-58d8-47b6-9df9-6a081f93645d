// 前端调试脚本
console.log('🔍 前端调试开始...');

// 检查认证存储
const authStorage = localStorage.getItem('auth-storage');
if (authStorage) {
    try {
        const parsed = JSON.parse(authStorage);
        console.log('✅ 找到认证数据:', parsed);
        
        let token = null;
        if (parsed.state?.token) {
            token = parsed.state.token;
        } else if (parsed.token) {
            token = parsed.token;
        }
        
        if (token) {
            console.log('✅ Token存在，前20字符:', token.substring(0, 20) + '...');
        } else {
            console.log('❌ 没有找到token');
        }
    } catch (e) {
        console.log('❌ 解析认证数据失败:', e);
    }
} else {
    console.log('❌ 没有找到认证数据');
}

// 快速修复函数
window.quickFix = function() {
    localStorage.clear();
    window.location.href = '/login';
}; 