#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}测试登录API${NC}"
echo "============================="

# 测试管理员登录
echo -e "\n${BLUE}测试管理员登录:${NC}"
ADMIN_RESULT=$(curl -s -X POST http://localhost:8000/api/v1/login/access-token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123")

if [[ $ADMIN_RESULT == *"access_token"* ]]; then
  echo -e "${GREEN}✓ 管理员登录成功!${NC}"
  echo "$ADMIN_RESULT" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4 | head -c 20
  echo "..."
else
  echo -e "${RED}✗ 管理员登录失败!${NC}"
  echo "$ADMIN_RESULT"
fi

# 测试普通用户登录
echo -e "\n${BLUE}测试普通用户登录:${NC}"
USER_RESULT=$(curl -s -X POST http://localhost:8000/api/v1/login/access-token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=user1&password=user123")

if [[ $USER_RESULT == *"access_token"* ]]; then
  echo -e "${GREEN}✓ 普通用户登录成功!${NC}"
  echo "$USER_RESULT" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4 | head -c 20
  echo "..."
else
  echo -e "${RED}✗ 普通用户登录失败!${NC}"
  echo "$USER_RESULT"
fi

# 测试API可访问性
echo -e "\n${BLUE}测试API可访问性:${NC}"
HEALTH_RESULT=$(curl -s http://localhost:8000/health)

if [[ $HEALTH_RESULT == *"healthy"* ]]; then
  echo -e "${GREEN}✓ API健康检查通过!${NC}"
else
  echo -e "${RED}✗ API健康检查失败!${NC}"
  echo "$HEALTH_RESULT"
fi

echo -e "\n${BLUE}前端代理配置:${NC}"
cat ai-security-frontend/vite.config.ts | grep -A 5 "proxy:"

echo -e "\n============================="
echo -e "${BLUE}测试完成${NC}" 