#!/usr/bin/env python3
"""
简单修复文档10的向量化
"""

import sys
import os
import asyncio
sys.path.append('.')

async def simple_fix_doc10():
    """简单修复文档10的向量化"""
    
    print("🔧 简单修复文档10的向量化")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from app.db.session import get_sync_db
        from app.services.document_vectorizer import DocumentVectorizer
        from sqlalchemy import text
        
        # 获取数据库会话
        db = next(get_sync_db())
        
        # 1. 检查文档10的chunks
        print("\n1. 检查文档10的chunks...")
        chunks = db.execute(text("SELECT id, chunk_index, content FROM knowledge_chunks WHERE document_id = 10 ORDER BY chunk_index LIMIT 10")).fetchall()
        print(f"   数据库中chunks数量: {len(chunks)}")
        
        if not chunks:
            print("   ❌ 文档10没有chunks")
            return
        
        # 显示前几个chunk的内容
        for i, chunk in enumerate(chunks[:3]):
            print(f"   Chunk {chunk[1]}: {chunk[2][:100]}...")
        
        # 2. 创建向量化器实例
        print("\n2. 创建向量化器实例...")
        vectorizer = DocumentVectorizer()
        vector_db = vectorizer.vector_db
        print(f"   向量数据库类型: {type(vector_db)}")
        print(f"   当前chunks数量: {len(vector_db.chunks)}")
        print(f"   当前embeddings数量: {len(vector_db.embeddings)}")
        
        # 3. 检查现有的文档10向量
        print("\n3. 检查现有的文档10向量...")
        doc10_chunks = []
        for chunk_id in vector_db.chunks.keys():
            if 'doc_10_' in chunk_id:
                doc10_chunks.append(chunk_id)
        
        print(f"   现有文档10chunks数: {len(doc10_chunks)}")
        
        # 4. 重新向量化前几个chunks进行测试
        print("\n4. 重新向量化前几个chunks...")
        
        for chunk in chunks[:5]:  # 只处理前5个
            chunk_id = f"doc_10_chunk_{chunk[1]}"
            content = chunk[2].strip()
            
            if not content:
                print(f"   ⚠️ 跳过空内容: {chunk_id}")
                continue
            
            try:
                # 生成向量
                embedding = vectorizer.simple_embedding(content)
                
                # 手动添加到向量数据库
                vector_db.chunks[chunk_id] = {
                    'content': content,
                    'metadata': {
                        'document_id': 10,
                        'chunk_index': chunk[1]
                    },
                    'document_id': 10,
                    'chunk_index': chunk[1]
                }
                
                # 存储embedding
                vector_db.embeddings[chunk_id] = embedding
                
                print(f"   ✅ 向量化成功: {chunk_id}")
                
            except Exception as e:
                print(f"   ❌ 向量化失败 {chunk_id}: {e}")
        
        # 保存数据
        vector_db._save_data()
        
        # 5. 测试搜索
        print("\n5. 测试搜索...")
        
        try:
            # 生成查询向量
            query_embedding = vectorizer.simple_embedding("数据脱敏")
            
            # 搜索相似向量
            results = vector_db.search_by_vector(
                query_embedding=query_embedding,
                n_results=5
            )
            
            print(f"   搜索 '数据脱敏': {len(results)} 个结果")
            
            doc10_found = False
            for result in results:
                chunk_id = result.get('id', '')
                if 'doc_10_' in chunk_id:
                    doc10_found = True
                    similarity = result.get('relevance_score', 0)
                    print(f"   ✅ 找到文档10: {chunk_id}, 相似度: {similarity:.4f}")
                    print(f"      内容: {result.get('content', '')[:100]}...")
                    break
            
            if not doc10_found:
                print(f"   ❌ 未找到文档10")
                # 显示找到的其他结果
                for i, result in enumerate(results[:2], 1):
                    chunk_id = result.get('id', '')
                    similarity = result.get('relevance_score', 0)
                    print(f"       {i}. {chunk_id}: {similarity:.4f}")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
        
        # 6. 测试知识库服务
        print("\n6. 测试知识库服务...")
        
        try:
            from app.services.knowledge_base_service import KnowledgeBaseService
            
            knowledge_service = KnowledgeBaseService()
            search_results = await knowledge_service.search_knowledge_base(
                db=db,
                query="数据脱敏的要求",
                user_id=1,
                top_k=5,
                threshold=0.1
            )
            
            print(f"   知识库搜索结果: {len(search_results)} 个")
            
            doc10_found_in_kb = False
            for result in search_results:
                if result.get('document_id') == 10:
                    doc10_found_in_kb = True
                    similarity = result.get('similarity', 0)
                    if hasattr(similarity, 'item'):
                        similarity = similarity.item()
                    print(f"   ✅ 知识库中找到文档10，相似度: {similarity:.4f}")
                    print(f"      内容: {result.get('content', '')[:100]}...")
                    break
            
            if not doc10_found_in_kb:
                print(f"   ❌ 知识库搜索中仍未找到文档10")
                # 显示实际找到的文档
                for i, result in enumerate(search_results[:3], 1):
                    doc_id = result.get('document_id', 'unknown')
                    similarity = result.get('similarity', 0)
                    if hasattr(similarity, 'item'):
                        similarity = similarity.item()
                    print(f"      {i}. 文档{doc_id}: {similarity:.4f}")
                
        except Exception as e:
            print(f"   ❌ 知识库搜索失败: {e}")
        
        db.close()
    
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_fix_doc10())
