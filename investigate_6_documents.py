#!/usr/bin/env python3
"""
调查为什么用户只看到6个文档而不是8个
"""

import requests
import json

def investigate_document_count():
    """调查文档数量差异"""
    print("=== 调查文档数量差异 ===\n")
    
    # API基础URL
    base_url = "http://localhost:8000"
    
    # 登录获取token
    login_data = {
        "username": "admin", 
        "password": "admin123"
    }
    
    try:
        # 登录
        login_response = requests.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        print("1. 测试修复前的默认行为 (limit=10):")
        response = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   修复前默认返回: {len(data['items'])}/{data['total']} 个文档")
            
            print("   文档列表:")
            for i, doc in enumerate(data['items'], 1):
                print(f"     {i}. {doc['title']}")
        
        print(f"\n2. 测试不同用户角色的权限影响:")
        
        # 测试普通用户登录
        user_login_data = {
            "username": "user", 
            "password": "user123"
        }
        
        user_login_response = requests.post(
            f"{base_url}/api/v1/login/access-token",
            data=user_login_data
        )
        
        if user_login_response.status_code == 200:
            user_token_data = user_login_response.json()
            user_access_token = user_token_data["access_token"]
            print(f"   ✅ 普通用户登录成功")
            
            user_headers = {
                "Authorization": f"Bearer {user_access_token}",
                "Content-Type": "application/json"
            }
            
            user_response = requests.get(
                f"{base_url}/api/v1/knowledge-base/documents?limit=1000",
                headers=user_headers
            )
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                print(f"   普通用户能看到: {len(user_data['items'])}/{user_data['total']} 个文档")
                
                # 比较两个用户看到的文档差异
                admin_titles = set(doc['title'] for doc in data['items'])
                user_titles = set(doc['title'] for doc in user_data['items'])
                
                if admin_titles == user_titles:
                    print(f"   ✅ 普通用户和管理员看到相同的文档")
                else:
                    hidden_from_user = admin_titles - user_titles
                    extra_for_user = user_titles - admin_titles
                    
                    if hidden_from_user:
                        print(f"   ⚠️  普通用户看不到的文档: {hidden_from_user}")
                    if extra_for_user:
                        print(f"   ⚠️  普通用户独有的文档: {extra_for_user}")
            else:
                print(f"   ❌ 普通用户获取文档失败: {user_response.status_code}")
                if user_response.status_code == 403:
                    print("   可能原因: 普通用户没有知识库访问权限")
        else:
            print(f"   ❌ 普通用户登录失败: {user_login_response.status_code}")
        
        print(f"\n3. 测试修复前可能的6个文档限制:")
        
        # 模拟修复前的可能行为
        old_limit_response = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?limit=6",
            headers=headers
        )
        
        if old_limit_response.status_code == 200:
            old_data = old_limit_response.json()
            print(f"   限制为6时返回: {len(old_data['items'])} 个文档")
            print("   前6个文档:")
            for i, doc in enumerate(old_data['items'][:6], 1):
                print(f"     {i}. {doc['title']}")
        
        print(f"\n4. 分析可能的原因:")
        print("   可能导致只显示6个文档的原因:")
        print("   a) 前端代码中hardcode了limit=6")
        print("   b) 某些用户角色的权限限制")
        print("   c) 前端分页逻辑的bug") 
        print("   d) 数据库中实际有更多文档但部分状态异常")
        print("   e) 前端表格组件的显示限制")
        
        # 检查是否有不同状态的文档
        print(f"\n5. 检查文档状态分布:")
        all_status_response = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?limit=1000",
            headers=headers
        )
        
        if all_status_response.status_code == 200:
            all_data = all_status_response.json()
            status_count = {}
            for doc in all_data['items']:
                status = doc['status']
                status_count[status] = status_count.get(status, 0) + 1
            
            print("   文档状态统计:")
            for status, count in status_count.items():
                print(f"     {status}: {count} 个")
                
    except Exception as e:
        print(f"❌ 调查过程中出错: {str(e)}")

if __name__ == "__main__":
    print("🔍 开始调查为什么用户只看到6个文档\n")
    investigate_document_count()
    print("\n📋 建议用户:")
    print("1. 刷新浏览器页面")
    print("2. 清除浏览器缓存")
    print("3. 检查是否使用了过滤条件")
    print("4. 确认是否有权限访问所有文档")