#!/usr/bin/env python3
"""
通过API测试优化后的RAG效果
"""

import requests
import json

def test_optimized_rag():
    """测试优化后的RAG效果"""
    print("🔍 通过API测试优化后的RAG效果...")
    
    # 后端API地址
    base_url = "http://localhost:8000"
    
    # 1. 先登录获取token
    print("1️⃣ 登录获取token...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data,
            timeout=30
        )
        
        if login_response.status_code == 200:
            token = login_response.json().get("access_token")
            print(f"✅ 登录成功，获取到token")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"响应: {login_response.text}")
            return
            
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return
    
    # 2. 设置请求头
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 3. 测试不同的查询
    test_queries = [
        "产品清单有什么产品",
        "有哪些AI产品",
        "数据安全产品有什么",
        "培训课程包括什么",
        "网络安全产品有哪些"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        
        # RAG查询数据
        rag_data = {
            "query": query,
            "top_k": 3,
            "temperature": 0.7,
            "max_tokens": 300,
            "include_sources": True
        }
        
        try:
            # 发送RAG查询请求
            rag_response = requests.post(
                f"{base_url}/api/v1/rag/query",
                headers=headers,
                json=rag_data,
                timeout=60
            )
            
            if rag_response.status_code == 200:
                result = rag_response.json()
                
                print(f"✅ 查询成功")
                print(f"   成功: {result.get('success', False)}")
                print(f"   答案: {result.get('answer', 'N/A')}")
                print(f"   检索文档数: {result.get('documents_retrieved', 0)}")
                print(f"   使用模型ID: {result.get('model_id', 'N/A')}")
                
                if result.get('error'):
                    print(f"   错误: {result.get('error')}")
                
                if result.get('sources'):
                    print(f"   来源文档数: {len(result['sources'])}")
                    for j, source in enumerate(result['sources'][:2]):
                        print(f"     {j+1}. {source.get('document_title', 'N/A')}")
                
            else:
                print(f"❌ 查询失败: {rag_response.status_code}")
                print(f"   响应: {rag_response.text}")
                
        except Exception as e:
            print(f"❌ 查询请求失败: {str(e)}")

if __name__ == "__main__":
    test_optimized_rag()
