#!/usr/bin/env python3
"""
调试RAG知识库搜索
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.knowledge_base_service import KnowledgeBaseService
from app.db.session import SyncSessionLocal

async def debug_rag_search():
    """调试RAG知识库搜索"""
    print("🔍 调试RAG知识库搜索...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 创建知识库服务
        knowledge_service = KnowledgeBaseService()
        
        # 测试查询
        query = "什么是人工智能安全？"
        user_id = 1
        top_k = 3
        
        print(f"📝 查询: {query}")
        print(f"👤 用户ID: {user_id}")
        print(f"📊 返回数量: {top_k}")
        
        # 测试搜索性能
        print(f"\n🚀 开始知识库搜索...")
        start_time = time.time()
        
        search_results = await knowledge_service.search_knowledge_base(
            db=db,
            query=query,
            user_id=user_id,
            top_k=top_k,
            threshold=0.1  # 降低阈值以确保能检索到相关结果
        )
        
        end_time = time.time()
        search_duration = end_time - start_time
        
        print(f"⏱️ 搜索耗时: {search_duration:.2f} 秒")
        print(f"📊 搜索结果数量: {len(search_results)}")
        
        if search_results:
            print(f"\n📄 搜索结果:")
            for i, result in enumerate(search_results):
                print(f"  {i+1}. 文档ID: {result.get('document_id', 'N/A')}")
                print(f"     文档名: {result.get('document_name', 'N/A')}")
                print(f"     相似度: {result.get('similarity', 0.0):.3f}")
                print(f"     内容长度: {len(result.get('content', ''))}")
                print(f"     内容预览: {result.get('content', '')[:100]}...")
                print()
        else:
            print("❌ 没有找到相关文档")
        
        # 测试向量数据库状态
        print(f"\n🔍 检查向量数据库状态...")
        try:
            from app.services.vector_db_service import VectorDBService
            vector_service = VectorDBService()
            
            # 尝试简单搜索
            vector_results = vector_service.search(query, n_results=3)
            print(f"✅ 向量数据库搜索成功，返回 {len(vector_results)} 个结果")
            
            for i, result in enumerate(vector_results):
                print(f"  {i+1}. ID: {result.get('id', 'N/A')}")
                print(f"     相似度: {1 - result.get('distance', 1.0):.3f}")
                print(f"     内容: {result.get('content', '')[:50]}...")
                
        except Exception as e:
            print(f"❌ 向量数据库搜索失败: {str(e)}")
        
        # 检查数据库中的文档数量
        print(f"\n📊 检查数据库统计...")
        from app.db.models.knowledge_document import KnowledgeDocument
        from app.db.models.knowledge_chunk import KnowledgeChunk
        
        doc_count = db.query(KnowledgeDocument).count()
        chunk_count = db.query(KnowledgeChunk).count()
        active_doc_count = db.query(KnowledgeDocument).filter(
            KnowledgeDocument.status == 'processed'
        ).count()
        
        print(f"   总文档数: {doc_count}")
        print(f"   已处理文档数: {active_doc_count}")
        print(f"   文档块数: {chunk_count}")
        
        # 查看一些文档示例
        docs = db.query(KnowledgeDocument).limit(5).all()
        print(f"\n📄 文档示例:")
        for doc in docs:
            print(f"  - ID {doc.id}: {doc.title} (状态: {doc.status})")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_rag_search())
