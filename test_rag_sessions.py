#!/usr/bin/env python3
"""
测试RAG会话功能的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_rag_sessions():
    """测试RAG会话功能"""
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 创建RAG会话
    print("\n📝 创建RAG会话...")
    session_data = {
        "title": "测试知识库问答会话",
        "llm_model_id": 13,
        "session_type": "rag"
    }
    
    response = requests.post(f"{BASE_URL}/rag-sessions/", headers=headers, json=session_data)
    if response.status_code == 201:
        session = response.json()
        session_id = session["id"]
        print(f"✅ RAG会话创建成功: {session['title']} (ID: {session_id})")
    else:
        print(f"❌ 创建RAG会话失败: {response.status_code}")
        print(response.text)
        return
    
    # 3. 发送RAG查询并保存到会话
    print(f"\n🤖 发送RAG查询到会话 {session_id}...")
    query_data = {
        "query": "什么是人工智能？",
        "session_id": session_id,
        "model_id": 13,
        "top_k": 5,
        "temperature": 0.7,
        "max_tokens": 500,
        "include_sources": True
    }
    
    response = requests.post(f"{BASE_URL}/rag/query", headers=headers, json=query_data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ RAG查询成功")
        print(f"   成功: {result.get('success', False)}")
        print(f"   答案: {result.get('answer', 'N/A')[:100]}...")
        print(f"   来源数量: {len(result.get('sources', []))}")
    else:
        print(f"❌ RAG查询失败: {response.status_code}")
        print(response.text)
    
    # 4. 获取会话消息历史
    print(f"\n📋 获取会话 {session_id} 的消息历史...")
    response = requests.get(f"{BASE_URL}/rag-sessions/{session_id}/messages", headers=headers)
    if response.status_code == 200:
        messages = response.json()
        print(f"✅ 获取到 {len(messages)} 条消息:")
        for i, msg in enumerate(messages, 1):
            sender = "用户" if msg["sender_type"] == "user" else "AI"
            content = msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
            print(f"   {i}. [{sender}] {content}")
    else:
        print(f"❌ 获取消息历史失败: {response.status_code}")
        print(response.text)
    
    # 5. 获取用户的RAG会话列表
    print("\n📂 获取用户的RAG会话列表...")
    response = requests.get(f"{BASE_URL}/rag-sessions/", headers=headers)
    if response.status_code == 200:
        sessions = response.json()
        print(f"✅ 找到 {len(sessions)} 个RAG会话:")
        for session in sessions:
            print(f"   - {session['title']} (ID: {session['id']}, 创建时间: {session['created_at']})")
    else:
        print(f"❌ 获取会话列表失败: {response.status_code}")
        print(response.text)
    
    # 6. 再发送一条消息测试对话连续性
    print(f"\n💬 发送第二条消息测试对话连续性...")
    query_data2 = {
        "query": "请详细解释一下机器学习的概念",
        "session_id": session_id,
        "model_id": 13,
        "top_k": 5,
        "temperature": 0.7,
        "max_tokens": 500,
        "include_sources": True
    }
    
    response = requests.post(f"{BASE_URL}/rag/query", headers=headers, json=query_data2)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 第二条RAG查询成功")
        
        # 再次获取消息历史验证保存
        response = requests.get(f"{BASE_URL}/rag-sessions/{session_id}/messages", headers=headers)
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 会话现在有 {len(messages)} 条消息")
            print("📝 完整对话历史:")
            for i, msg in enumerate(messages, 1):
                sender = "👤 用户" if msg["sender_type"] == "user" else "🤖 AI助手"
                content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                print(f"   {i}. {sender}: {content}")
        else:
            print(f"❌ 获取更新后的消息历史失败: {response.status_code}")
    else:
        print(f"❌ 第二条RAG查询失败: {response.status_code}")
        print(response.text)
    
    print(f"\n🎉 RAG会话功能测试完成！")
    print(f"✅ 会话创建: 成功")
    print(f"✅ 消息保存: 成功") 
    print(f"✅ 对话连续性: 成功")
    print(f"✅ 会话管理: 成功")

if __name__ == "__main__":
    test_rag_sessions()
