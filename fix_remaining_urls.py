#!/usr/bin/env python3
"""
修复剩余的硬编码URL
"""
import os
import re

def fix_remaining_files():
    """修复剩余的文件"""
    
    # 需要修复的文件列表
    files_to_fix = [
        "ai-security-frontend/src/pages/Admin/UserManagement.tsx",
        "ai-security-frontend/src/pages/Admin/HierarchyManagement.tsx", 
        "ai-security-frontend/src/pages/Admin/KeywordGroupManagement.tsx",
        "ai-security-frontend/src/pages/Admin/KeywordManagement.tsx",
        "ai-security-frontend/src/pages/Admin/ModelManagement.tsx",
        "ai-security-frontend/src/pages/Sessions/SessionsPage.tsx",
        "ai-security-frontend/src/services/api.ts"
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            continue
            
        print(f"处理文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 检查是否已经导入了apiFetch
        has_api_fetch_import = 'apiFetch' in content
        
        # 如果文件包含硬编码URL但没有导入apiFetch，添加导入
        if 'localhost:8000' in content and not has_api_fetch_import:
            # 找到最后一个import语句的位置
            import_pattern = r"import\s+.*?from\s+['\"].*?['\"];?\s*\n"
            imports = list(re.finditer(import_pattern, content, re.MULTILINE))
            
            if imports:
                last_import = imports[-1]
                insert_pos = last_import.end()
                
                # 插入apiFetch导入
                api_import = "import { apiFetch } from '../../config/api';\n"
                content = content[:insert_pos] + api_import + content[insert_pos:]
        
        # 替换所有形式的硬编码URL
        replacements = [
            # 基本fetch调用
            (r"fetch\(\s*['\"]http://localhost:8000/api/v1/([^'\"]*)['\"](?:\s*,\s*\{[^}]*\})?\s*\)", 
             r"apiFetch('\1')"),
            
            # 模板字符串
            (r"fetch\(\s*`http://localhost:8000/api/v1/([^`]*)`(?:\s*,\s*\{[^}]*\})?\s*\)",
             r"apiFetch(`\1`)"),
            
            # 字符串拼接
            (r"['\"]http://localhost:8000/api/v1/([^'\"]*)['\"]",
             r"'/api/v1/\1'"),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # 特殊处理一些复杂的情况
        # 处理带有复杂参数的fetch调用
        content = re.sub(
            r"fetch\(\s*(['\"`])http://localhost:8000/api/v1/([^'\"]*)\1\s*,\s*\{([^}]*)\}\s*\)",
            lambda m: f"apiFetch('{m.group(2)}', {{{m.group(3)}}})",
            content
        )
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 已修复")
        else:
            print(f"  ⏭️  无需修改")

if __name__ == "__main__":
    fix_remaining_files()
    print("修复完成！")
