#!/usr/bin/env python3
"""
测试数据脱敏文档搜索
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.db.session import get_sync_db
from app.services.knowledge_base_service import KnowledgeBaseService
from sqlalchemy import text

async def test_desensitization_search():
    """测试数据脱敏文档搜索"""
    
    print("🔍 测试数据脱敏文档搜索")
    print("=" * 60)
    
    # 获取数据库会话
    db = next(get_sync_db())
    
    try:
        # 1. 检查文档10的内容
        print("\n1. 检查文档10的内容...")
        result = db.execute(text("SELECT title, chunk_count FROM knowledge_documents WHERE id = 10")).fetchone()
        if result:
            print(f"   文档标题: {result[0]}")
            print(f"   块数量: {result[1]}")
        else:
            print("   文档10不存在")
            return
        
        # 2. 检查文档10的chunks
        print("\n2. 检查文档10的chunks...")
        chunks = db.execute(text("SELECT id, chunk_index, content FROM knowledge_chunks WHERE document_id = 10 LIMIT 3")).fetchall()
        print(f"   实际chunks数量: {len(chunks)}")
        
        for chunk in chunks:
            print(f"   Chunk {chunk[1]}: {chunk[2][:100]}...")
        
        # 3. 测试不同的搜索词
        print("\n3. 测试不同的搜索词...")
        knowledge_service = KnowledgeBaseService()
        
        search_queries = [
            "数据脱敏的要求有什么",
            "数据脱敏",
            "脱敏",
            "敏感数据",
            "水印",
            "溯源",
            "技术规范"
        ]
        
        for query in search_queries:
            print(f"\n   查询: '{query}'")
            try:
                search_results = await knowledge_service.search_knowledge_base(
                    db=db,
                    query=query,
                    user_id=1,
                    top_k=5,
                    threshold=0.1
                )
                
                print(f"   结果数量: {len(search_results)}")
                
                # 检查是否包含文档10
                doc_10_found = False
                for result in search_results:
                    if result.get('document_id') == 10:
                        doc_10_found = True
                        similarity = result.get('similarity', 0)
                        if hasattr(similarity, 'item'):
                            similarity = similarity.item()
                        print(f"   ✅ 找到文档10，相似度: {similarity:.4f}")
                        print(f"      内容: {result.get('content', '')[:100]}...")
                        break
                
                if not doc_10_found:
                    print(f"   ❌ 未找到文档10")
                    # 显示找到的其他文档
                    for i, result in enumerate(search_results[:2], 1):
                        similarity = result.get('similarity', 0)
                        if hasattr(similarity, 'item'):
                            similarity = similarity.item()
                        print(f"      {i}. 文档{result.get('document_id')}: {similarity:.4f}")
                        
            except Exception as e:
                print(f"   ❌ 搜索失败: {e}")
        
        # 4. 直接检查向量数据库中的文档10
        print("\n4. 检查向量数据库中的文档10...")
        from app.services.document_vectorizer import document_vectorizer
        
        # 搜索包含文档10的向量
        try:
            vector_results = document_vectorizer.vector_db.search(
                query="数据脱敏",
                n_results=10
            )
            
            print(f"   向量搜索结果数量: {len(vector_results)}")
            
            doc_10_in_vectors = False
            for result in vector_results:
                chunk_id = result.get('id', '')
                if 'doc_10_' in chunk_id:
                    doc_10_in_vectors = True
                    print(f"   ✅ 向量数据库中找到文档10: {chunk_id}")
                    print(f"      内容: {result.get('content', '')[:100]}...")
                    break
            
            if not doc_10_in_vectors:
                print(f"   ❌ 向量数据库中未找到文档10")
                
        except Exception as e:
            print(f"   ❌ 向量搜索失败: {e}")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_desensitization_search())
