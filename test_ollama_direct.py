#!/usr/bin/env python3
"""
直接测试Ollama API
"""

import requests
import json
import time

def test_ollama_direct():
    """直接测试Ollama API"""
    
    print("🔍 直接测试Ollama API")
    print("=" * 60)
    
    # 1. 测试qwen:0.5b模型
    print("\n1. 测试qwen:0.5b模型...")
    
    url = "http://localhost:11434/api/generate"
    
    data = {
        "model": "qwen:0.5b",
        "prompt": "本地模型路径是什么？请根据以下上下文回答：/root/pic_detect/content-moderation-system/models/",
        "stream": False,
        "options": {
            "max_tokens": 100,
            "temperature": 0.7
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, json=data, timeout=30)
        end_time = time.time()
        
        print(f"   响应时间: {end_time - start_time:.2f}秒")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应内容: {result.get('response', '')}")
        else:
            print(f"   错误: {response.text}")
            
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时（30秒）")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试qwen:1.8b模型
    print("\n2. 测试qwen:1.8b模型...")
    
    data2 = {
        "model": "qwen:1.8b",
        "prompt": "本地模型路径是什么？请根据以下上下文回答：/root/pic_detect/content-moderation-system/models/",
        "stream": False,
        "options": {
            "max_tokens": 100,
            "temperature": 0.7
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, json=data2, timeout=30)
        end_time = time.time()
        
        print(f"   响应时间: {end_time - start_time:.2f}秒")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应内容: {result.get('response', '')}")
        else:
            print(f"   错误: {response.text}")
            
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时（30秒）")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 3. 检查Ollama进程状态
    print("\n3. 检查Ollama进程状态...")
    try:
        ps_response = requests.get("http://localhost:11434/api/ps", timeout=5)
        if ps_response.status_code == 200:
            ps_data = ps_response.json()
            print(f"   当前加载的模型: {ps_data}")
        else:
            print(f"   无法获取进程状态: {ps_response.status_code}")
    except Exception as e:
        print(f"   检查进程状态异常: {e}")

if __name__ == "__main__":
    test_ollama_direct()
