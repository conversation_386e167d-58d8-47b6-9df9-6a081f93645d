# 数据分级权限管理功能使用说明

## 功能概述

数据分级权限管理功能允许管理员动态配置不同用户级别对各种敏感度数据的访问权限，替代了原有的硬编码规则，提供了更灵活和细粒度的权限控制。

## 功能特点

### 1. 可配置化权限管理
- **替代硬编码**：不再使用写死在代码中的权限规则
- **动态配置**：管理员可通过界面实时修改权限配置
- **即时生效**：权限修改立即生效，无需重启系统

### 2. 四级敏感度分类
- **高级敏感 (Level 1)**：最高级别敏感数据，如个人隐私、财务信息等
- **中级敏感 (Level 2)**：中等级别敏感数据，如业务数据、内部信息等
- **初级敏感 (Level 3)**：低级别敏感数据，如一般业务信息等
- **完全开放 (Level 4)**：公开数据，无敏感性限制

### 3. 灵活的权限配置
- **多级别管理**：支持为任意用户级别配置权限
- **多敏感度支持**：可为每个级别单独配置可访问的敏感度类型
- **向后兼容**：未配置的级别自动使用默认规则

## 访问路径

### 前端管理界面
1. 登录AI安全系统管理后台
2. 在左侧菜单中找到 **"系统管理"** 
3. 点击 **"数据访问权限管理"**
4. 进入级别数据访问权限管理页面

### 菜单路径
```
系统管理 → 数据访问权限管理
```

### URL路径
```
http://localhost:5173/admin/level-data-access
```

## 使用方法

### 1. 查看当前权限配置
- 页面会显示所有用户级别的权限汇总
- 权限矩阵以颜色标识：绿色表示允许访问，红色表示禁止访问
- 显示每个级别可访问的数据类型数量

### 2. 编辑权限配置
1. 点击要修改的级别对应的 **"编辑权限"** 按钮
2. 在弹出的对话框中勾选该级别可以访问的敏感度类型
3. 点击 **"保存"** 完成修改

### 3. 重置为默认规则
1. 点击要重置的级别对应的 **"重置默认"** 按钮
2. 系统会将该级别的权限恢复为基于rank_value的默认规则

### 4. 权限验证
- 修改权限后，可以通过用户登录测试权限是否生效
- 系统会根据新的权限配置控制用户对不同敏感度数据的访问

## API接口

### 管理员接口
- `GET /api/v1/admin/level-data-access/levels/data-access/summary` - 获取所有级别权限汇总
- `GET /api/v1/admin/level-data-access/levels/{level_id}/data-access` - 获取特定级别权限
- `PUT /api/v1/admin/level-data-access/levels/{level_id}/data-access` - 更新级别权限
- `POST /api/v1/admin/level-data-access/levels/{level_id}/data-access/reset-default` - 重置为默认

### 用户接口
- `GET /api/v1/data-classification/accessible-grades` - 获取当前用户可访问的数据分级

## 权限规则说明

### 默认规则（基于rank_value）
- **rank_value ≤ 1**：可访问所有级别 [1,2,3,4]
- **rank_value ≤ 2**：可访问中低级别 [2,3,4]
- **rank_value ≤ 3**：可访问低级别 [3,4]
- **rank_value > 3**：只能访问公开数据 [4]

### 自定义规则
- 管理员可为任意级别配置任意敏感度的访问权限
- 自定义规则优先于默认规则
- 未配置的级别自动使用默认规则

## 使用示例

### 示例1：提升"高级普通用户"权限
**场景**：希望高级普通用户能够访问中级敏感数据

**操作步骤**：
1. 进入数据访问权限管理页面
2. 找到"高级普通用户"行，点击"编辑权限"
3. 勾选"中级敏感"、"初级敏感"、"完全开放"
4. 点击"保存"

**结果**：高级普通用户现在可以访问敏感度级别2、3、4的数据

### 示例2：重置权限配置
**场景**：需要将某个级别的权限恢复为系统默认

**操作步骤**：
1. 找到目标级别行，点击"重置默认"
2. 系统自动根据该级别的rank_value恢复默认权限

## 注意事项

### 1. 权限安全
- 请谨慎配置权限，避免给低级别用户过高的数据访问权限
- 建议定期审查权限配置，确保符合业务安全要求

### 2. 系统兼容性
- 新功能完全向后兼容，不影响现有功能
- 系统在权限配置读取失败时会自动回退到默认规则

### 3. 权限生效
- 权限修改立即生效，用户下次访问数据时即应用新权限
- 无需重启系统或重新登录

## 技术实现

### 数据库表结构
- 新增 `level_data_access` 表存储权限配置
- 支持级别与敏感度的多对多关系映射

### 后端实现
- 修改核心权限检查函数，优先读取数据库配置
- 提供完整的权限管理API接口
- 支持权限配置的CRUD操作

### 前端实现
- 新增专门的权限管理页面
- 提供直观的权限矩阵显示
- 支持批量权限编辑和重置功能

## 演示页面

系统还提供了一个独立的演示页面，展示API的使用方法：
```
文件位置：level_data_access_demo.html
访问方式：直接在浏览器中打开该HTML文件
```

该演示页面包含：
- 权限配置的可视化展示
- 权限修改的交互式操作
- API调用的实际示例

## 总结

数据分级权限管理功能为AI安全系统提供了强大而灵活的权限控制能力，使管理员能够根据实际业务需求精确配置用户的数据访问权限，大大提升了系统的安全性和可管理性。 