#!/usr/bin/env python3
"""
直接测试LLM服务
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.db.session import get_sync_db
from app.db.models.llm_model import LLMModel
from app.services.llm_service import LLMService

async def test_llm_direct():
    """直接测试LLM服务"""
    
    print("🤖 测试LLM服务")
    print("=" * 50)
    
    # 获取数据库会话
    db = next(get_sync_db())
    
    try:
        # 1. 获取活跃模型
        print("\n1. 获取活跃模型...")
        model = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).first()
        
        if not model:
            print("❌ 没有活跃的LLM模型")
            return
        
        print(f"✅ 使用模型: {model.name} (ID: {model.id})")
        print(f"   API地址: {model.api_url}")
        
        # 2. 测试LLM服务
        print("\n2. 测试LLM服务...")
        llm_service = LLMService()
        
        # 构建测试消息
        messages = [
            {"role": "system", "content": "你是一个助手，请简短回答问题。"},
            {"role": "user", "content": "根据以下信息回答问题：\n\n上下文：不安全图片集路径：/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_NSFW\n\n问题：本地模型路径是什么？"}
        ]
        
        print("   发送消息到LLM...")
        print(f"   消息内容: {messages[1]['content'][:100]}...")
        
        # 设置合理的配置
        import copy
        temp_model = copy.deepcopy(model)
        temp_model.config_params = {'max_tokens': 200}
        
        try:
            response = await llm_service._call_llm_api(
                llm_model=temp_model,
                messages=messages,
                stream=False
            )
            
            print(f"✅ LLM响应成功")
            print(f"   响应内容: {response}")
            
        except Exception as e:
            print(f"❌ LLM调用失败: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_llm_direct())
