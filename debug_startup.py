#!/usr/bin/env python3
"""
调试启动问题的脚本
"""
import sys
import os

def check_imports():
    """检查关键模块导入"""
    print("🔍 检查模块导入...")
    
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ FastAPI导入失败: {e}")
        return False
    
    try:
        import uvicorn
        print(f"✅ Uvicorn: {uvicorn.__version__}")
    except ImportError as e:
        print(f"❌ Uvicorn导入失败: {e}")
        return False
    
    try:
        import sqlalchemy
        print(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
    except ImportError as e:
        print(f"❌ SQLAlchemy导入失败: {e}")
        return False
    
    try:
        from app.core.config import settings
        print(f"✅ 配置加载成功: {settings.PROJECT_NAME}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    try:
        from app.db.session import get_sync_db
        print("✅ 数据库会话配置成功")
    except Exception as e:
        print(f"❌ 数据库会话配置失败: {e}")
        return False
    
    return True

def check_database():
    """检查数据库连接"""
    print("\n🔍 检查数据库连接...")
    
    try:
        from app.db.session import SyncSessionLocal
        from sqlalchemy import text
        
        db = SyncSessionLocal()
        result = db.execute(text("SELECT 1"))
        db.close()
        print("✅ 数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_app_creation():
    """检查应用创建"""
    print("\n🔍 检查应用创建...")
    
    try:
        # 设置环境变量避免下载模型
        os.environ['TRANSFORMERS_OFFLINE'] = '1'
        os.environ['HF_HUB_OFFLINE'] = '1'
        
        from app.main import app
        print("✅ FastAPI应用创建成功")
        print(f"✅ 应用标题: {app.title}")
        return True
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("🔧 AI安全系统启动诊断")
    print("=" * 60)
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查各个组件
    checks = [
        ("模块导入", check_imports),
        ("数据库连接", check_database),
        ("应用创建", check_app_creation)
    ]
    
    all_passed = True
    for name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {name}检查异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查通过！系统应该可以正常启动")
        print("💡 尝试运行: python3 simple_start_backend.py")
    else:
        print("⚠️ 发现问题，请根据上述错误信息进行修复")
    print("=" * 60)

if __name__ == "__main__":
    main()