#!/usr/bin/env python3
"""
登录API测试脚本
测试登录端点是否正常工作
"""
import requests
import json
import sys

def test_login_api():
    """测试登录API"""
    base_url = "http://localhost:8000"
    login_url = f"{base_url}/api/v1/login/access-token"
    
    print("🔐 测试登录API...")
    print(f"登录URL: {login_url}")
    
    # 测试数据
    test_users = [
        {"username": "admin", "password": "admin123"},
        {"username": "user1", "password": "user123"},
        {"username": "admin", "password": "wrong_password"},  # 错误密码测试
    ]
    
    for i, user_data in enumerate(test_users, 1):
        print(f"\n=== 测试 {i}: {user_data['username']} ===")
        
        try:
            # 发送登录请求
            response = requests.post(
                login_url,
                data={
                    "username": user_data["username"],
                    "password": user_data["password"]
                },
                headers={
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 登录成功!")
                print(f"   访问令牌: {data.get('access_token', 'N/A')[:50]}...")
                print(f"   令牌类型: {data.get('token_type', 'N/A')}")
                
                if 'user' in data:
                    user_info = data['user']
                    print(f"   用户ID: {user_info.get('id')}")
                    print(f"   用户名: {user_info.get('username')}")
                    print(f"   邮箱: {user_info.get('email')}")
                    print(f"   角色ID: {user_info.get('role_id')}")
                    print(f"   激活状态: {user_info.get('is_active')}")
                    print(f"   超级用户: {user_info.get('is_superuser')}")
                
                # 测试使用令牌访问受保护的端点
                if i == 1:  # 只对第一个成功的登录测试
                    test_protected_endpoint(data.get('access_token'), base_url)
                    
            elif response.status_code == 401:
                print("❌ 认证失败 - 用户名或密码错误")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data.get('detail', 'N/A')}")
                except:
                    print(f"   响应内容: {response.text}")
            else:
                print(f"❌ 请求失败")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data}")
                except:
                    print(f"   响应内容: {response.text}")
                    
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - 请确保后端服务正在运行")
            print("   提示: 运行 'python3 simple_start.py' 启动后端服务")
            return False
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return True

def test_protected_endpoint(token, base_url):
    """测试受保护的端点"""
    print("\n--- 测试受保护端点 ---")
    
    # 测试获取当前用户信息
    auth_url = f"{base_url}/api/v1/auth/me"
    
    try:
        response = requests.get(
            auth_url,
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            timeout=10
        )
        
        print(f"获取用户信息状态码: {response.status_code}")
        
        if response.status_code == 200:
            user_data = response.json()
            print("✅ 令牌验证成功!")
            print(f"   当前用户: {user_data.get('username')}")
        else:
            print("❌ 令牌验证失败")
            try:
                error_data = response.json()
                print(f"   错误: {error_data}")
            except:
                print(f"   响应: {response.text}")
                
    except Exception as e:
        print(f"❌ 测试受保护端点失败: {e}")

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务器运行正常")
            print(f"   状态: {data.get('status')}")
            print(f"   版本: {data.get('version')}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("   请运行: python3 simple_start.py")
        return False
    except Exception as e:
        print(f"❌ 检查服务器状态失败: {e}")
        return False

def main():
    print("🚀 开始登录功能测试...")
    
    # 1. 检查服务器状态
    if not check_server_status():
        print("\n❌ 服务器未运行，无法进行登录测试")
        print("请先启动后端服务:")
        print("   python3 simple_start.py")
        sys.exit(1)
    
    # 2. 测试登录API
    print("\n" + "="*50)
    if test_login_api():
        print("\n✅ 登录功能测试完成!")
        print("\n📝 测试总结:")
        print("   - 数据库连接正常")
        print("   - 用户认证功能正常")
        print("   - JWT令牌生成正常")
        print("   - 受保护端点访问正常")
        print("\n🎉 登录系统工作正常!")
    else:
        print("\n❌ 登录功能测试失败")

if __name__ == "__main__":
    main()