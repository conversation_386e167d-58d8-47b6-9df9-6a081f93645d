#!/usr/bin/env python3
"""
登录问题诊断脚本
检查数据库状态和用户数据
"""
import os
import sys
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from app.db.session import SyncSessionLocal
from app.core.security import verify_password

def check_database_exists():
    """检查数据库文件是否存在"""
    db_files = [
        "ai_security.db",
        "ai_security_system.db", 
        "security_system.db",
        "app/ai_security.db"
    ]
    
    print("=== 数据库文件检查 ===")
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"✅ 找到数据库: {db_file} (大小: {size} bytes)")
            return True
        else:
            print(f"❌ 未找到: {db_file}")
    
    return False

def check_tables():
    """检查数据库表结构"""
    print("\n=== 数据库表结构检查 ===")
    db = SyncSessionLocal()
    try:
        # 检查表是否存在
        tables_to_check = ['users', 'roles', 'levels']
        
        for table in tables_to_check:
            try:
                result = db.execute(text(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'"))
                if result.fetchone():
                    print(f"✅ 表 {table} 存在")
                    
                    # 检查表结构
                    result = db.execute(text(f"PRAGMA table_info({table})"))
                    columns = result.fetchall()
                    print(f"   列: {[col[1] for col in columns]}")
                else:
                    print(f"❌ 表 {table} 不存在")
            except Exception as e:
                print(f"❌ 检查表 {table} 时出错: {e}")
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
    finally:
        db.close()

def check_users():
    """检查用户数据"""
    print("\n=== 用户数据检查 ===")
    db = SyncSessionLocal()
    try:
        # 检查用户表数据
        result = db.execute(text("SELECT id, username, email, hashed_password, role_id, is_active FROM users"))
        users = result.fetchall()
        
        if not users:
            print("❌ 用户表为空")
            return False
        
        print(f"✅ 找到 {len(users)} 个用户:")
        for user in users:
            user_id, username, email, hashed_password, role_id, is_active = user
            print(f"   ID: {user_id}, 用户名: {username}, 邮箱: {email}")
            print(f"   角色ID: {role_id}, 激活状态: {is_active}")
            print(f"   密码哈希: {'已设置' if hashed_password else '❌ 未设置'}")
            
            # 检查角色信息
            try:
                role_result = db.execute(text("SELECT name FROM roles WHERE id = :role_id"), {"role_id": role_id})
                role = role_result.fetchone()
                if role:
                    print(f"   角色名称: {role[0]}")
                else:
                    print(f"   ❌ 角色ID {role_id} 不存在")
            except Exception as e:
                print(f"   ❌ 查询角色失败: {e}")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 查询用户数据失败: {e}")
        return False
    finally:
        db.close()

def test_password_verification():
    """测试密码验证"""
    print("\n=== 密码验证测试 ===")
    db = SyncSessionLocal()
    try:
        # 获取admin用户
        result = db.execute(text("SELECT username, hashed_password FROM users WHERE username = 'admin'"))
        admin_user = result.fetchone()
        
        if not admin_user:
            print("❌ 未找到admin用户")
            return False
        
        username, hashed_password = admin_user
        if not hashed_password:
            print("❌ admin用户密码哈希为空")
            return False
        
        # 测试密码验证
        test_passwords = ["admin123", "admin", "password"]
        for password in test_passwords:
            try:
                is_valid = verify_password(password, hashed_password)
                print(f"   密码 '{password}': {'✅ 正确' if is_valid else '❌ 错误'}")
                if is_valid:
                    return True
            except Exception as e:
                print(f"   密码 '{password}': ❌ 验证失败 - {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ 密码验证测试失败: {e}")
        return False
    finally:
        db.close()

def check_crud_authentication():
    """测试CRUD认证功能"""
    print("\n=== CRUD认证功能测试 ===")
    try:
        from app.crud import user_crud
        
        db = SyncSessionLocal()
        try:
            # 测试认证
            user = user_crud.authenticate(db, "admin", "admin123")
            if user:
                print("✅ CRUD认证成功")
                print(f"   用户: {user.username}, ID: {user.id}")
                return True
            else:
                print("❌ CRUD认证失败")
                return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ CRUD认证测试失败: {e}")
        return False

def main():
    print("🔍 开始登录问题诊断...")
    
    # 1. 检查数据库文件
    if not check_database_exists():
        print("\n❌ 未找到数据库文件，需要初始化数据库")
        return
    
    # 2. 检查表结构
    check_tables()
    
    # 3. 检查用户数据
    if not check_users():
        print("\n❌ 用户数据有问题，需要重新初始化")
        return
    
    # 4. 测试密码验证
    if not test_password_verification():
        print("\n❌ 密码验证失败，可能需要重新设置密码")
        return
    
    # 5. 测试CRUD认证
    if not check_crud_authentication():
        print("\n❌ CRUD认证失败，可能有代码问题")
        return
    
    print("\n✅ 所有检查通过，登录功能应该正常工作")
    print("\n建议测试登录:")
    print("   用户名: admin")
    print("   密码: admin123")

if __name__ == "__main__":
    main()