#!/usr/bin/env python3
"""
列出所有模型
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

def list_all_models():
    """列出所有模型"""
    print("📋 列出所有模型...")
    
    db = SyncSessionLocal()
    
    try:
        # 查询所有模型
        all_models = db.query(LLMModel).order_by(LLMModel.id).all()
        
        print(f"总模型数: {len(all_models)}")
        print()
        
        for model in all_models:
            status = "✅ 激活" if model.is_active else "❌ 未激活"
            print(f"🔹 模型 {model.id}: {model.name}")
            print(f"   状态: {status}")
            print(f"   API URL: {model.api_url}")
            
            if model.config_params:
                api_format = model.config_params.get('api_format', 'openai')
                model_name = model.config_params.get('model', 'unknown')
                print(f"   格式: {api_format}")
                print(f"   模型名: {model_name}")
                print(f"   配置: {model.config_params}")
            else:
                print(f"   配置: 无")
            
            # 判断是否为远程模型
            if 'localhost' not in model.api_url and '127.0.0.1' not in model.api_url:
                print(f"   类型: 🌐 远程模型")
            else:
                print(f"   类型: 🏠 本地模型")
            
            print()
        
        # 统计
        active_count = sum(1 for m in all_models if m.is_active)
        remote_count = sum(1 for m in all_models if 'localhost' not in m.api_url and '127.0.0.1' not in m.api_url)
        local_count = len(all_models) - remote_count
        
        print(f"📊 统计:")
        print(f"   激活模型: {active_count}/{len(all_models)}")
        print(f"   远程模型: {remote_count}")
        print(f"   本地模型: {local_count}")
        
    except Exception as e:
        print(f"❌ 查询失败: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    list_all_models()
