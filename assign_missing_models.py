#!/usr/bin/env python3
"""
为admin用户分配缺失的模型
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def assign_missing_models():
    """为admin用户分配缺失的模型"""
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 获取当前用户信息
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取用户信息失败: {response.status_code}")
        return
    
    user = response.json()
    user_id = user['id']
    print(f"✅ 当前用户: {user['username']} (ID: {user_id})")
    
    # 3. 获取所有激活的模型
    response = requests.get(f"{BASE_URL}/admin/llm-models/", headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取模型列表失败: {response.status_code}")
        return
    
    all_models = response.json()
    active_models = [m for m in all_models if m.get('is_active', False)]
    active_model_ids = [m['id'] for m in active_models]
    
    print(f"✅ 找到 {len(active_models)} 个激活模型:")
    for model in active_models:
        print(f"  - {model['name']} (ID: {model['id']})")
    
    # 4. 为用户分配所有激活模型
    print(f"\n🔧 为用户分配所有激活模型...")
    
    assignment_data = {
        "model_ids": active_model_ids,
        "default_model_id": 13,  # 设置阿里云通义千问-Plus为默认模型
        "replace_existing": False  # 不替换现有分配，只添加新的
    }
    
    response = requests.post(
        f"{BASE_URL}/admin/model-assignments/users/{user_id}/assign-models",
        headers=headers,
        json=assignment_data
    )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 模型分配成功!")
        print(f"   消息: {result.get('message', '')}")
        print(f"   影响的分配: {result.get('affected_assignments', 0)}")
    else:
        print(f"❌ 模型分配失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return
    
    # 5. 验证分配结果
    print(f"\n🔍 验证分配结果...")
    
    response = requests.get(f"{BASE_URL}/llm-models/my-available", headers=headers)
    if response.status_code == 200:
        available_models = response.json()
        print(f"✅ 用户现在可用的模型数量: {len(available_models)}")
        for i, model in enumerate(available_models, 1):
            print(f"  {i}. {model['name']} (ID: {model['id']})")
        
        if len(available_models) == len(active_models):
            print("\n🎉 成功! 现在会话管理中应该能看到所有激活的模型了!")
        else:
            print(f"\n⚠️ 注意: 可用模型数量({len(available_models)})仍然少于激活模型数量({len(active_models)})")
    else:
        print(f"❌ 验证失败: {response.status_code}")

if __name__ == "__main__":
    assign_missing_models()
