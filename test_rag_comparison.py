#!/usr/bin/env python3
"""
对比知识库服务和RAG API的搜索结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import requests
from app.db.session import SyncSessionLocal
from app.services.knowledge_base_service import knowledge_base_service

async def test_search_comparison():
    """对比搜索结果"""
    print("🔍 对比知识库服务和RAG API的搜索结果")
    print("=" * 60)
    
    # 测试不同的查询语句
    test_queries = [
        "本地模型路径是什么？",
        "本地模型路径",
        "模型路径",
        "CLIP模型路径"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. 测试查询: '{query}'")
        print("-" * 40)
        
        # 直接知识库服务搜索
        print("   知识库服务搜索...")
        db = SyncSessionLocal()
        
        try:
            kb_results = await knowledge_base_service.search_knowledge_base(
                db=db,
                query=query,
                user_id=1,
                top_k=10,
                threshold=0.1
            )
            
            print(f"     结果数量: {len(kb_results)}")
            kb_doc2_count = 0
            for result in kb_results:
                if result.get('document_id') == 2:
                    kb_doc2_count += 1
                    similarity = result.get('similarity', 0)
                    if hasattr(similarity, 'item'):
                        similarity = similarity.item()
                    elif hasattr(similarity, '__iter__') and not isinstance(similarity, str):
                        similarity = float(similarity[0]) if len(similarity) > 0 else 0.0
                    print(f"       文档2: {result.get('chunk_id')} - {similarity:.4f}")
            
            print(f"     文档2数量: {kb_doc2_count}")
            
        except Exception as e:
            print(f"     知识库服务搜索失败: {e}")
            kb_doc2_count = 0
        finally:
            db.close()
        
        # RAG API搜索
        print("   RAG API搜索...")
        
        try:
            # 先登录获取token
            login_response = requests.post(
                "http://localhost:8000/api/v1/login/access-token",
                data={
                    "username": "admin",
                    "password": "admin123"
                }
            )
            
            if login_response.status_code == 200:
                token = login_response.json()["access_token"]
                headers = {"Authorization": f"Bearer {token}"}
                
                # 调用RAG API
                rag_response = requests.post(
                    "http://localhost:8000/api/v1/rag/query",
                    json={
                        "query": query,
                        "top_k": 10,
                        "temperature": 0.7,
                        "max_tokens": 1000,
                        "include_sources": True
                    },
                    headers=headers
                )
                
                if rag_response.status_code == 200:
                    rag_data = rag_response.json()
                    sources = rag_data.get("sources", [])
                    
                    print(f"     结果数量: {len(sources)}")
                    rag_doc2_count = 0
                    for source in sources:
                        if source.get('document_id') == 2:
                            rag_doc2_count += 1
                            similarity = source.get('similarity', 0)
                            print(f"       文档2: {source.get('chunk_id')} - {similarity:.4f}")
                    
                    print(f"     文档2数量: {rag_doc2_count}")
                    
                    # 对比结果
                    print(f"     对比: KB={kb_doc2_count}, RAG={rag_doc2_count}, 一致={kb_doc2_count == rag_doc2_count}")
                    
                else:
                    print(f"     RAG API调用失败: {rag_response.status_code}")
            else:
                print(f"     登录失败: {login_response.status_code}")
                
        except Exception as e:
            print(f"     RAG API测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_search_comparison())
