# 知识库管理菜单结构演示

## 🎯 新的菜单结构

### 📋 一级导航：知识库管理
现在知识库相关功能被整合到一个独立的一级导航菜单中，提供更清晰的功能组织。

### 📂 二级导航结构

```
🏠 知识库管理
├── 💬 知识库问答      (需要 RAG_CHAT 权限)
├── 📄 文档管理        (需要 KNOWLEDGE_BASE 权限)  
└── ⚙️ 模型管理        (需要 KNOWLEDGE_BASE 权限)
```

## 🔑 权限控制

### 权限模块
- **RAG_CHAT**: 控制知识库问答功能的访问
- **KNOWLEDGE_BASE**: 控制文档管理和模型管理功能的访问

### 权限组合示例

| 用户类型 | RAG_CHAT | KNOWLEDGE_BASE | 可见菜单 |
|---------|----------|----------------|----------|
| 管理员 | ✅ | ✅ | 知识库问答 + 文档管理 + 模型管理 |
| 问答用户 | ✅ | ❌ | 仅知识库问答 |
| 管理用户 | ❌ | ✅ | 仅文档管理 + 模型管理 |
| 受限用户 | ❌ | ❌ | 无知识库菜单 |

## 🛠️ 功能说明

### 💬 知识库问答
- **路由**: `/knowledge-base/chat`
- **功能**: RAG问答对话界面
- **权限**: 需要 `RAG_CHAT` 权限
- **特性**: 
  - 基于知识库的智能问答
  - 实时对话界面
  - 参考文档来源展示
  - 使用统计查看

### 📄 文档管理  
- **路由**: `/knowledge-base/documents`
- **功能**: 知识库文档管理
- **权限**: 需要 `KNOWLEDGE_BASE` 权限
- **特性**:
  - 文档上传和管理
  - 文档搜索和预览
  - 数据分类分级
  - 统计信息查看

### ⚙️ 模型管理
- **路由**: `/knowledge-base/models`  
- **功能**: 知识库模型配置管理
- **权限**: 需要 `KNOWLEDGE_BASE` 权限
- **特性**:
  - 模型配置创建和编辑
  - 多层级配置支持
  - 参数调优
  - 权限控制

## 🔄 兼容性

### 原有路由保留
为了保持向后兼容，原有的路由仍然可用：
- `/rag-chat` → 知识库问答 (原路由)
- `/admin/knowledge-base` → 文档管理 (原路由)

### 新路由结构
新的路由结构更加清晰和语义化：
- `/knowledge-base/chat` → 知识库问答
- `/knowledge-base/documents` → 文档管理  
- `/knowledge-base/models` → 模型管理

## 🎨 用户体验改进

### 1. 更清晰的功能分组
- 所有知识库相关功能集中在一个菜单下
- 避免功能分散在不同的菜单中

### 2. 更精细的权限控制
- 可以单独控制问答和管理权限
- 支持不同用户角色的差异化访问

### 3. 更好的导航体验
- 层级结构清晰
- 功能定位更容易

## 📱 移动端适配
新的菜单结构在移动端也能很好地工作，折叠菜单会自动适应屏幕尺寸。

## 🚀 未来扩展
这种菜单结构为未来添加更多知识库相关功能提供了良好的基础，比如：
- 知识图谱管理
- 向量数据库管理  
- 知识库分析报告
- 等等...
