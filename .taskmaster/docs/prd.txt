# AI安全系统 - 分层权限管理功能扩展

## 项目概述
在现有AI安全系统基础上，增加分层权限管理功能，允许上级管理员对下级用户进行权限分配和LLM模型指定。这是对现有系统的功能扩展，不改变现有架构。

## 现有系统状况
- 技术栈：Python FastAPI + SQLAlchemy + SQLite + React + TypeScript
- 已有功能：用户管理、角色级别、LLM模型管理、会话对话、脱敏规则、关键词过滤等
- 现有表：users, roles, levels, llm_models, sessions, messages, desensitization_rules 等
- 当前权限：主要分为管理员和普通用户两级

## 功能扩展需求

### 1. 用户层级关系管理

#### 1.1 数据库扩展
- 在users表添加manager_id字段，指向直接上级用户
- 新增user_subordinates视图，方便查询下级关系
- 支持多层级管理关系（A管理B，B管理C，则A也能管理C）

#### 1.2 层级权限规则
- 超级管理员：可以管理所有用户
- 上级管理员：可以管理其直接下级和间接下级用户
- 普通用户：只能查看自己的信息
- 不能管理同级或上级用户

### 2. 权限分配功能扩展

#### 2.1 角色权限分配
- 上级管理员可以为下级用户分配角色（从现有roles表选择）
- 分配的角色权限不能超过分配者自身的权限
- 保留现有的角色权限体系，在此基础上增加分层管理

#### 2.2 功能权限细化
- 基于现有功能模块，定义可分配的具体权限
- 如：脱敏规则管理权限、关键词管理权限、会话查看权限等
- 上级可以精细化控制下级用户的功能访问权限

### 3. LLM模型分配管理

#### 3.1 模型分配表设计
- 新增user_model_assignments表
- 字段：user_id, model_id, is_default, assigned_by, created_at
- 关联现有llm_models表

#### 3.2 模型分配功能
- 上级管理员可以为下级用户指定可用的LLM模型
- 支持为用户分配多个模型
- 必须为每个用户设置一个默认模型
- 用户创建会话时默认使用指定的默认模型
- 用户可以在被分配的模型范围内切换

#### 3.3 模型权限控制
- 修改现有会话创建逻辑，检查用户是否有权限使用选择的模型
- 在现有LLM模型选择界面，只显示用户被分配的模型
- 模型分配变更立即生效

### 4. API接口扩展

#### 4.1 用户管理API扩展
- GET /api/v1/admin/users/{user_id}/subordinates - 获取下级用户列表
- PUT /api/v1/admin/users/{user_id}/manager - 设置用户的直接上级
- GET /api/v1/admin/users/{user_id}/hierarchy - 获取用户的层级关系

#### 4.2 权限管理API
- GET /api/v1/admin/users/{user_id}/permissions - 获取用户详细权限
- PUT /api/v1/admin/users/{user_id}/role - 为下级用户分配角色
- GET /api/v1/admin/manageable-roles - 获取当前用户可分配的角色列表

#### 4.3 模型分配API
- GET /api/v1/admin/users/{user_id}/assigned-models - 获取用户被分配的模型
- PUT /api/v1/admin/users/{user_id}/models - 为用户分配模型
- PUT /api/v1/admin/users/{user_id}/default-model - 设置用户默认模型
- GET /api/v1/users/my-available-models - 获取当前用户可用模型

### 5. 前端界面扩展

#### 5.1 用户管理界面改造
- 在现有用户列表页面增加层级关系展示
- 增加"管理下级"功能入口
- 增加组织架构树形视图
- 增加上级设置功能

#### 5.2 权限分配界面
- 为下级用户分配角色的弹窗界面
- 权限继承关系可视化展示
- 权限分配操作的确认和日志记录

#### 5.3 模型分配界面
- 模型分配管理页面
- 拖拽式模型分配操作
- 默认模型设置界面
- 模型分配历史查看

#### 5.4 现有界面改造
- 修改会话创建页面，只显示用户可用的模型
- 修改用户个人中心，显示当前权限和可用模型
- 在用户列表中显示层级关系标识

### 6. 数据库迁移

#### 6.1 表结构修改
```sql
-- 为users表添加manager_id字段
ALTER TABLE users ADD COLUMN manager_id INTEGER REFERENCES users(id);

-- 创建用户模型分配表
CREATE TABLE user_model_assignments (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    model_id INTEGER NOT NULL REFERENCES llm_models(id),
    is_default BOOLEAN DEFAULT FALSE,
    assigned_by INTEGER NOT NULL REFERENCES users(id),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建操作日志表（如果不存在）
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY,
    operator_id INTEGER NOT NULL REFERENCES users(id),
    target_user_id INTEGER REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL,
    operation_details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.2 数据迁移策略
- 为现有用户分配默认的可用模型（所有激活的模型）
- 设置admin用户为所有用户的默认上级（临时）
- 保持现有用户的角色和权限不变

### 7. 业务规则

#### 7.1 权限分配规则
- 只能为直接或间接下级分配权限
- 分配的权限不能超过分配者的权限
- 不能修改同级或上级用户的权限
- 权限变更需要记录操作日志

#### 7.2 模型分配规则
- 只能分配自己有权限使用的模型给下级
- 每个用户必须至少有一个可用模型
- 默认模型必须在可用模型范围内
- 超级管理员拥有所有模型的分配权限

#### 7.3 层级关系规则
- 不能形成循环层级关系（A管理B，B不能管理A）
- 不能设置自己为自己的上级
- 层级关系变更会影响权限继承

### 8. 兼容性保证
- 现有API接口保持不变，只增加新接口
- 现有用户权限和功能不受影响
- 逐步迁移，支持新老功能并存
- 现有数据完整性保证

### 9. 实施计划

#### 阶段1：数据库扩展
- 添加manager_id字段到users表
- 创建user_model_assignments表
- 创建必要的索引和约束

#### 阶段2：后端API开发
- 实现层级关系管理API
- 实现权限分配API
- 实现模型分配API
- 增加权限检查中间件

#### 阶段3：前端界面开发
- 组织架构树组件开发
- 权限分配界面开发
- 模型分配界面开发
- 现有界面改造

#### 阶段4：集成测试和部署
- 功能集成测试
- 权限验证测试
- 数据迁移测试
- 生产环境部署

## 验收标准

### 核心功能验收
1. 上级管理员能够设置和管理下级用户的层级关系
2. 上级管理员能够为下级用户分配角色和权限（不超过自身权限）
3. 上级管理员能够为下级用户分配LLM模型和设置默认模型
4. 用户只能使用被分配的模型进行对话
5. 所有权限和模型分配操作有完整的审计日志

### 兼容性验收
1. 现有功能完全正常，无回归问题
2. 现有用户权限体系保持稳定
3. 现有API接口正常工作
4. 数据完整性和一致性保证

### 用户体验验收
1. 层级关系设置简单直观
2. 权限分配操作清晰明确
3. 模型分配界面友好易用
4. 错误提示准确有效 