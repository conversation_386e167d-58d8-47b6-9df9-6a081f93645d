# Task ID: 3
# Title: User Management API
# Status: done
# Dependencies: 1, 2
# Priority: high
# Description: Develop CRUD API endpoints for user management, including user creation, modification, deletion, and role/level assignment.
# Details:
Create RESTful API endpoints for user management operations. Implement input validation using Pydantic models. Include endpoints for: 1) User creation with role and level assignment, 2) User profile retrieval, 3) User modification, 4) User activation/deactivation, 5) User deletion (soft delete). Ensure proper error handling and response formatting. Implement pagination for user listing.

# Test Strategy:
Unit test each endpoint with valid and invalid inputs. Test authorization rules to ensure only authorized users can perform management operations. Verify database operations correctly update the User table. Test pagination and filtering functionality.
