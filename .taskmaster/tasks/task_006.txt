# Task ID: 6
# Title: Regex Pattern Management for Malicious Intent Detection
# Status: done
# Dependencies: 1, 4
# Priority: high
# Description: Enhance the existing basic malicious intent detection logic in messages.py by implementing a comprehensive regex pattern management system for detecting malicious intent in user messages.
# Details:
Create a RegexRule model to store and manage regex patterns. Develop API endpoints for regex pattern management (CRUD operations). Implement pattern categorization (e.g., privilege escalation, privacy extraction). Support role-based pattern assignments. Implement efficient regex matching with timeout protection to prevent regex DoS attacks. Create a management interface for testing and administering regex patterns. Support pattern activation/deactivation. Implement pattern versioning for audit purposes. Integrate with the existing basic keyword checking in messages.py.

# Test Strategy:
Test RegexRule model creation and validation. Test regex pattern CRUD operations through API endpoints. Verify pattern matching against sample texts. Test performance with complex patterns. Verify timeout protection prevents regex DoS. Test role-based pattern assignments. Verify categorization functionality. Ensure backward compatibility with existing keyword checking logic.

# Subtasks:
## 6.1. Create RegexRule data model [done]
### Dependencies: None
### Description: Design and implement the RegexRule model to store regex patterns, categories, roles, activation status, and versioning information.
### Details:
Created complete RegexRule SQLAlchemy model in app/db/models/regex_rule.py. Implemented IntentCategory enumeration (privilege escalation, privacy extraction, data poisoning, etc.) and RuleSeverity enumeration (low, medium, high, critical). Added key methods for pattern compilation, match testing, and management statistics. Implemented role associations, version control, and timeout protection. Updated Role model relationship mappings. Created comprehensive Pydantic schemas in app/schemas/regex_rule.py and updated relevant import files.

## 6.2. Develop API endpoints for regex pattern management [done]
### Dependencies: None
### Description: Create REST API endpoints for CRUD operations on regex patterns, including filtering by category and role.
### Details:
Created complete RegexRule CRUD API endpoints in app/api/v1/endpoints/admin/regex_rules.py. Implemented all basic operations: list/query, create, read, update, delete. Added advanced filtering by category, severity, role, activation status, and keyword search. Implemented special function endpoints: /test for testing regex rules, /batch-check for batch text matching, /stats for rule statistics, /{rule_id}/false-positive for marking false positives, /{rule_id}/activate and /{rule_id}/deactivate for rule activation management. Updated API route configuration in app/api/v1/api.py with /admin/regex-rules path. Created database migration file (alembic/versions/20250609_add_regex_rules_table.py). Implemented comprehensive input validation, error handling, and permission controls.

## 6.3. Implement regex matching engine with timeout protection [done]
### Dependencies: None
### Description: Enhance the existing detection logic in messages.py to use the RegexRule patterns with proper timeout protection to prevent regex DoS attacks.
### Details:


## 6.4. Create management interface for regex patterns [done]
### Dependencies: None
### Description: Develop a user interface for administrators to create, test, activate/deactivate, and manage regex patterns.
### Details:


## 6.5. Implement pattern versioning and audit logging [done]
### Dependencies: None
### Description: Add versioning support for regex patterns to track changes and maintain an audit trail of pattern modifications.
### Details:


