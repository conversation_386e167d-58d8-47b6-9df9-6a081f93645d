# Task ID: 22
# Title: 角色权限管理前端界面
# Status: done
# Dependencies: 4, 15
# Priority: high
# Description: 创建角色权限管理前端界面，包括角色列表、创建角色、编辑角色、权限分配等功能。
# Details:
开发完整的角色权限管理UI界面(/admin/roles)，包括：1. 角色列表显示(分页、搜索) 2. 新建角色表单(角色名称、描述、权限选择) 3. 编辑角色功能 4. 删除角色功能 5. 权限分配界面(树形结构或复选框列表) 6. 角色用户关联查看。需要调用已有的角色管理API端点(/api/v1/admin/roles/)实现完整的CRUD操作。使用Ant Design组件库。

# Test Strategy:
测试角色列表显示和搜索功能。验证新建角色表单提交和验证。测试权限分配界面功能。测试编辑角色功能。验证删除角色时的依赖检查。验证权限控制确保只有管理员可以访问。
