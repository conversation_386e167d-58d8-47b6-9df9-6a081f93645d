# Task ID: 7
# Title: Data Desensitization Engine
# Status: done
# Dependencies: 1, 4
# Priority: high
# Description: Enhance the existing data desensitization detection logic to include a complete desensitization engine that can mask sensitive information in model outputs based on configurable rules and levels.
# Details:
Build upon the existing sensitive_patterns check in messages.py to create a comprehensive desensitization system. Create a DesensitizationRule model to store and manage rules. Implement different masking levels (high, medium, low) with appropriate configurations. Develop the actual data masking functionality to replace detected sensitive information. Support pattern-based detection using regex. Enable customizable masking characters and length. Create API endpoints for desensitization rule management. Implement role-based rule assignments. Support rule testing against sample text.

# Test Strategy:
Test detection accuracy for various sensitive data types. Verify different masking levels work correctly. Test performance with large text blocks. Verify role-based rule assignments. Test edge cases like partial matches and special formats. Ensure backward compatibility with existing sensitive_patterns implementation.

# Subtasks:
## 7.1. Create DesensitizationRule model [done]
### Dependencies: None
### Description: Design and implement the DesensitizationRule model to store rule configurations
### Details:


## 7.2. Implement masking levels [done]
### Dependencies: None
### Description: Configure different desensitization levels (high, medium, low) with appropriate rule sets for each
### Details:


## 7.3. Develop masking functionality [done]
### Dependencies: None
### Description: Implement the actual data masking logic to replace detected sensitive information with appropriate masked characters
### Details:


## 7.4. Integrate with existing detection logic [done]
### Dependencies: None
### Description: Ensure the new desensitization system works with the existing sensitive_patterns check in messages.py
### Details:


## 7.5. Create rule management API [done]
### Dependencies: None
### Description: Develop API endpoints for creating, updating, and managing desensitization rules
### Details:


