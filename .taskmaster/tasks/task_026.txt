# Task ID: 26
# Title: 系统监控面板前端界面
# Status: pending
# Dependencies: 18, 15
# Priority: low
# Description: 创建系统监控面板前端界面，包括实时监控数据、统计图表、告警信息等功能。
# Details:
开发系统监控面板UI界面(/admin/monitoring)，包括：1. 实时系统状态显示(CPU、内存、数据库连接数等) 2. 安全威胁统计图表(关键词匹配、恶意检测、脱敏操作等) 3. 用户活动统计 4. 系统告警信息显示 5. 数据刷新控制。使用图表库(如echarts或recharts)展示数据。需要调用系统监控API端点获取监控数据。

# Test Strategy:
测试实时数据显示和刷新功能。验证统计图表准确性和交互性。测试告警信息显示。验证数据刷新控制功能。测试在不同数据量下的性能表现。
