# Task ID: 4
# Title: Role and Permission System
# Status: done
# Dependencies: 1, 2, 3
# Priority: high
# Description: Implement role-based access control system with customizable roles, levels, and permission assignments.
# Details:
Create a flexible RBAC system using the Role and Level tables. Implement API endpoints for role and level management. Create a permission system that maps roles to specific system functions. Support hierarchical roles with inheritance. Implement middleware for permission checking on protected routes. Support custom role creation (e.g., doctor roles with different levels). Implement caching for permission checks to improve performance.

# Test Strategy:
Test role creation, modification, and deletion. Verify permission assignments work correctly. Test role hierarchy and inheritance. Create integration tests with different user roles accessing protected resources. Verify custom roles can be created and assigned appropriate permissions.
