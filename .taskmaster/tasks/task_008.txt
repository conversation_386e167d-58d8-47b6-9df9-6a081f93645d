# Task ID: 8
# Title: Session Management System
# Status: done
# Dependencies: 1, 2, 3
# Priority: medium
# Description: Implement the session management system for user conversations with AI models, including session creation, retrieval, and deletion.
# Details:
Create API endpoints for session management. Support session creation with model selection. Implement session listing with pagination and filtering. Support session deletion (with message cascade). Implement session title updates. Ensure sessions are user-scoped (users can only access their own sessions). Optimize queries for efficient session retrieval with message counts.

# Test Strategy:
Test session CRUD operations. Verify user-scoping works correctly. Test pagination and filtering. Verify cascade deletion works properly. Test performance with sessions containing many messages.
