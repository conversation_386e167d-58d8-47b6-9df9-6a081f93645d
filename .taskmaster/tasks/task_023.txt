# Task ID: 23
# Title: 正则规则管理前端界面
# Status: done
# Dependencies: 6, 15
# Priority: high
# Description: 创建正则规则管理前端界面，包括规则列表、创建规则、编辑规则、测试规则等功能。
# Details:
开发完整的正则规则管理UI界面(/admin/regex-rules)，包括：1. 规则列表显示(分页、搜索、按类别过滤) 2. 新建规则表单(规则名称、正则表达式、类别、严重程度) 3. 编辑规则功能 4. 删除规则功能 5. 规则测试功能(输入文本测试匹配) 6. 规则启用/禁用状态管理 7. 规则统计信息显示。需要调用已有的正则规则API端点(/api/v1/admin/regex-rules/)实现完整的CRUD操作。

# Test Strategy:
测试规则列表显示和过滤功能。验证新建规则表单提交和正则表达式验证。测试规则测试功能的准确性。测试编辑和删除规则功能。验证规则启用/禁用功能。测试规则统计信息准确性。
