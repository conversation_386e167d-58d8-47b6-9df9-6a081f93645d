"""add regex rules table

Revision ID: 20250609_add_regex_rules_table
Revises: 169e768018f2
Create Date: 2025-06-09 11:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '20250609_add_regex_rules_table'
down_revision: Union[str, None] = '20250103_add_message_model'
depends_on: Union[str, None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('regex_rules',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='规则ID，主键'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='规则名称'),
    sa.Column('pattern', sa.Text(), nullable=False, comment='正则表达式模式'),
    sa.Column('description', sa.Text(), nullable=True, comment='规则描述'),
    sa.Column('category', sa.Enum('privilege_escalation', 'privacy_extraction', 'data_poisoning', 'system_bypass', 'social_engineering', 'malicious_code', 'information_disclosure', 'other', name='intentcategory'), nullable=False, comment='恶意意图分类'),
    sa.Column('severity', sa.Enum('low', 'medium', 'high', 'critical', name='ruleseverity'), nullable=False, comment='严重性等级'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('priority', sa.Integer(), nullable=False, comment='优先级(1-100，数字越大优先级越高)'),
    sa.Column('flags', sa.Integer(), nullable=False, comment='正则表达式标志'),
    sa.Column('timeout_ms', sa.Integer(), nullable=False, comment='匹配超时时间(毫秒)，防止DoS攻击'),
    sa.Column('version', sa.Integer(), nullable=False, comment='规则版本号'),
    sa.Column('parent_rule_id', sa.Integer(), nullable=True, comment='父规则ID，用于版本追踪'),
    sa.Column('role_id', sa.Integer(), nullable=True, comment='关联的角色ID，null表示全局规则'),
    sa.Column('match_count', sa.Integer(), nullable=False, comment='匹配次数统计'),
    sa.Column('false_positive_count', sa.Integer(), nullable=False, comment='误报次数统计'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建者用户名'),
    sa.Column('updated_by', sa.String(length=50), nullable=True, comment='最后更新者用户名'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_rule_id'], ['regex_rules.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_regex_rules_id'), 'regex_rules', ['id'], unique=False)
    op.create_index(op.f('ix_regex_rules_name'), 'regex_rules', ['name'], unique=True)
    op.create_index(op.f('ix_regex_rules_category'), 'regex_rules', ['category'], unique=False)
    op.create_index(op.f('ix_regex_rules_severity'), 'regex_rules', ['severity'], unique=False)
    op.create_index(op.f('ix_regex_rules_is_active'), 'regex_rules', ['is_active'], unique=False)
    op.create_index(op.f('ix_regex_rules_role_id'), 'regex_rules', ['role_id'], unique=False)
    op.create_index(op.f('ix_regex_rules_priority'), 'regex_rules', ['priority'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_regex_rules_priority'), table_name='regex_rules')
    op.drop_index(op.f('ix_regex_rules_role_id'), table_name='regex_rules')
    op.drop_index(op.f('ix_regex_rules_is_active'), table_name='regex_rules')
    op.drop_index(op.f('ix_regex_rules_severity'), table_name='regex_rules')
    op.drop_index(op.f('ix_regex_rules_category'), table_name='regex_rules')
    op.drop_index(op.f('ix_regex_rules_name'), table_name='regex_rules')
    op.drop_index(op.f('ix_regex_rules_id'), table_name='regex_rules')
    op.drop_table('regex_rules')
    # ### end Alembic commands ### 