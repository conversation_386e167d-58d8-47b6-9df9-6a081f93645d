"""add message model

Revision ID: 20250103_add_message_model
Revises: 
Create Date: 2025-01-03 15:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '20250103_add_message_model'
down_revision: Union[str, None] = '169e768018f2'
depends_on: Union[str, None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('messages',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='消息ID，主键'),
    sa.Column('session_id', sa.Integer(), nullable=False, comment='所属会话ID'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='消息发送用户ID'),
    sa.Column('content', sa.Text(), nullable=False, comment='消息内容'),
    sa.Column('sender_type', sa.String(length=10), nullable=False, comment='发送者类型: user/ai/system'),
    sa.Column('is_blocked', sa.Boolean(), nullable=False, comment='是否被安全系统拦截'),
    sa.Column('block_reason', sa.String(length=500), nullable=True, comment='拦截原因'),
    sa.Column('security_check_passed', sa.Boolean(), nullable=False, comment='安全检查是否通过'),
    sa.Column('security_check_details', sa.JSON(), nullable=True, comment='安全检查详细结果，包含各项检查的具体信息'),
    sa.Column('ai_model_used', sa.String(length=100), nullable=True, comment='使用的AI模型名称'),
    sa.Column('ai_response_time', sa.Float(), nullable=True, comment='AI响应时间（秒）'),
    sa.Column('ai_token_count', sa.Integer(), nullable=True, comment='AI响应使用的token数量'),
    sa.Column('is_deleted', sa.Boolean(), nullable=False, comment='是否已删除（软删除）'),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, comment='删除时间'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['session_id'], ['sessions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messages_id'), 'messages', ['id'], unique=False)
    op.create_index(op.f('ix_messages_session_id'), 'messages', ['session_id'], unique=False)
    op.create_index(op.f('ix_messages_user_id'), 'messages', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_messages_user_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_session_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_id'), table_name='messages')
    op.drop_table('messages')
    # ### end Alembic commands ### 