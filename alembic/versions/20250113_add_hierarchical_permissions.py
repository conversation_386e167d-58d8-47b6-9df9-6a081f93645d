"""add hierarchical permissions support

Revision ID: 20250113_add_hierarchical_permissions
Revises: 20250609_121717_add_desensitization_rules_table
Create Date: 2025-01-13 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '20250113_add_hierarchical_permissions'
down_revision: Union[str, None] = '20250609_121717_add_desensitization_rules_table'
depends_on: Union[str, None] = None


def upgrade() -> None:
    # ### 添加用户层级管理支持 ###
    
    # 1. 为users表添加manager_id索引（字段已存在）
    try:
        op.create_index('ix_users_manager_id', 'users', ['manager_id'])
    except Exception:
        # 索引可能已存在，忽略错误
        pass
    
    # 2. 创建用户模型分配表
    op.create_table('user_model_assignments',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='分配记录ID，主键'),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('model_id', sa.Integer(), nullable=False, comment='模型ID'),
        sa.Column('is_default', sa.Boolean(), nullable=False, server_default='false', comment='是否为默认模型'),
        sa.Column('assigned_by', sa.Integer(), nullable=False, comment='分配者用户ID'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='分配时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['model_id'], ['llm_models.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        # SQLite兼容的唯一约束：一个用户对一个模型只能有一条分配记录
        sa.UniqueConstraint('user_id', 'model_id', name='uq_user_model_assignment')
    )
    op.create_index('ix_user_model_assignments_id', 'user_model_assignments', ['id'])
    op.create_index('ix_user_model_assignments_user_id', 'user_model_assignments', ['user_id'])
    op.create_index('ix_user_model_assignments_model_id', 'user_model_assignments', ['model_id'])
    op.create_index('ix_user_model_assignments_assigned_by', 'user_model_assignments', ['assigned_by'])
    
    # 3. 创建操作审计日志表
    op.create_table('operation_logs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='日志ID，主键'),
        sa.Column('operator_id', sa.Integer(), nullable=False, comment='操作者用户ID'),
        sa.Column('target_user_id', sa.Integer(), nullable=True, comment='被操作的用户ID'),
        sa.Column('operation_type', sa.String(length=50), nullable=False, comment='操作类型'),
        sa.Column('operation_details', sa.Text(), nullable=True, comment='操作详情'),
        sa.Column('ip_address', sa.String(length=45), nullable=True, comment='操作IP地址'),
        sa.Column('user_agent', sa.String(length=500), nullable=True, comment='用户代理'),
        sa.Column('success', sa.Boolean(), nullable=False, server_default='true', comment='操作是否成功'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='操作时间'),
        sa.ForeignKeyConstraint(['operator_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_user_id'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_operation_logs_id', 'operation_logs', ['id'])
    op.create_index('ix_operation_logs_operator_id', 'operation_logs', ['operator_id'])
    op.create_index('ix_operation_logs_target_user_id', 'operation_logs', ['target_user_id'])
    op.create_index('ix_operation_logs_operation_type', 'operation_logs', ['operation_type'])
    op.create_index('ix_operation_logs_created_at', 'operation_logs', ['created_at'])
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### 回滚操作 ###
    
    # 3. 删除操作审计日志表
    op.drop_index('ix_operation_logs_created_at', table_name='operation_logs')
    op.drop_index('ix_operation_logs_operation_type', table_name='operation_logs')
    op.drop_index('ix_operation_logs_target_user_id', table_name='operation_logs')
    op.drop_index('ix_operation_logs_operator_id', table_name='operation_logs')
    op.drop_index('ix_operation_logs_id', table_name='operation_logs')
    op.drop_table('operation_logs')
    
    # 2. 删除用户模型分配表
    op.drop_index('ix_user_model_assignments_assigned_by', table_name='user_model_assignments')
    op.drop_index('ix_user_model_assignments_model_id', table_name='user_model_assignments')
    op.drop_index('ix_user_model_assignments_user_id', table_name='user_model_assignments')
    op.drop_index('ix_user_model_assignments_id', table_name='user_model_assignments')
    op.drop_table('user_model_assignments')
    
    # 1. 删除users表的manager_id字段
    op.drop_index('ix_users_manager_id', table_name='users')
    op.drop_column('users', 'manager_id')
    
    # ### end Alembic commands ### 