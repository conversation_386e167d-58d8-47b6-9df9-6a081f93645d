"""Add role feature permissions table

Revision ID: 20250708_add_role_feature_permissions
Revises: 20250626_171623_add_knowledge_base_model_config_table
Create Date: 2025-07-08 13:04:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250708_add_role_feature_permissions'
down_revision = '20250626_171623_add_knowledge_base_model_config_table'
branch_labels = None
depends_on = None


def upgrade():
    # 创建角色功能权限表
    op.create_table('role_feature_permissions',
        sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID (外键)'),
        sa.Column('module_name', sa.String(length=100), nullable=False, comment='模块名称 (例如: KNOWLEDGE_BASE)'),
        sa.Column('feature_name', sa.String(length=100), nullable=False, comment='功能名称 (例如: CHAT, DOCUMENTS, MODELS, USER_PERMISSIONS)'),
        sa.Column('can_access', sa.Bo<PERSON>(), nullable=False, comment='是否可访问'),
        sa.Column('can_create', sa.Boolean(), nullable=False, comment='是否可创建'),
        sa.Column('can_read', sa.Boolean(), nullable=False, comment='是否可读取'),
        sa.Column('can_update', sa.Boolean(), nullable=False, comment='是否可更新'),
        sa.Column('can_delete', sa.Boolean(), nullable=False, comment='是否可删除'),
        sa.Column('description', sa.Text(), nullable=True, comment='权限描述'),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('role_id', 'module_name', 'feature_name')
    )

    # 插入知识库管理的默认功能权限配置
    # 这些是系统预定义的功能，管理员可以为不同角色配置这些功能的权限
    
    # 获取所有现有角色
    connection = op.get_bind()
    roles_result = connection.execute(sa.text("SELECT id FROM roles"))
    role_ids = [row[0] for row in roles_result.fetchall()]
    
    # 知识库管理的4个功能
    knowledge_base_features = [
        {
            'feature_name': 'CHAT',
            'description': '知识库问答功能 - 允许用户与知识库进行对话'
        },
        {
            'feature_name': 'DOCUMENTS', 
            'description': '文档管理功能 - 管理知识库中的文档'
        },
        {
            'feature_name': 'MODELS',
            'description': '模型管理功能 - 配置知识库使用的AI模型'
        },
        {
            'feature_name': 'USER_PERMISSIONS',
            'description': '用户文档权限管理功能 - 管理用户对特定文档的访问权限'
        }
    ]
    
    # 为每个角色创建默认的功能权限记录（默认都是False，需要管理员手动配置）
    for role_id in role_ids:
        for feature in knowledge_base_features:
            op.execute(
                sa.text("""
                    INSERT INTO role_feature_permissions 
                    (role_id, module_name, feature_name, can_access, can_create, can_read, can_update, can_delete, description)
                    VALUES 
                    (:role_id, 'KNOWLEDGE_BASE', :feature_name, false, false, false, false, false, :description)
                """),
                {
                    'role_id': role_id,
                    'feature_name': feature['feature_name'],
                    'description': feature['description']
                }
            )


def downgrade():
    # 删除角色功能权限表
    op.drop_table('role_feature_permissions')