"""add knowledge base model config table

Revision ID: 20250626_171623
Revises: 20250609_add_regex_rules_table
Create Date: 2025-06-26 17:16:23.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision = '20250626_171623'
down_revision = '20250609_add_regex_rules_table'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('knowledge_base_model_configs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='配置ID，主键'),
    sa.Column('config_type', sa.String(length=20), nullable=False, comment='配置类型'),
    sa.Column('model_id', sa.Integer(), nullable=False, comment='关联的LLM模型ID'),
    sa.Column('user_id', sa.Integer(), nullable=True, comment='用户ID（用户自定义配置）'),
    sa.Column('category_id', sa.Integer(), nullable=True, comment='数据分类ID（按分类配置）'),
    sa.Column('grade_id', sa.Integer(), nullable=True, comment='数据分级ID（按分级配置）'),
    sa.Column('config_name', sa.String(length=100), nullable=False, comment='配置名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='配置描述'),
    sa.Column('model_params', sa.JSON(), nullable=True, comment='模型特定参数'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('priority', sa.Integer(), nullable=False, comment='优先级'),
    sa.Column('created_by', sa.Integer(), nullable=False, comment='创建者ID'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['llm_models.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_base_model_configs_config_type'), 'knowledge_base_model_configs', ['config_type'], unique=False)
    op.create_index(op.f('ix_knowledge_base_model_configs_id'), 'knowledge_base_model_configs', ['id'], unique=False)
    
    # 创建默认全局配置
    # 首先查找第一个可用的模型ID
    op.execute("""
    INSERT INTO knowledge_base_model_configs 
    (config_type, model_id, config_name, description, is_active, priority, created_by, model_params)
    SELECT 
        'global' as config_type,
        (SELECT id FROM llm_models WHERE is_active = 1 LIMIT 1) as model_id,
        '默认知识库模型' as config_name,
        '知识库问答功能的默认模型配置' as description,
        1 as is_active,
        100 as priority,
        (SELECT id FROM users WHERE username = 'admin' LIMIT 1) as created_by,
        '{"temperature": 0.7, "max_tokens": 1000}' as model_params
    WHERE (SELECT COUNT(*) FROM llm_models WHERE is_active = 1) > 0
      AND (SELECT COUNT(*) FROM users WHERE username = 'admin') > 0;
    """)
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_knowledge_base_model_configs_id'), table_name='knowledge_base_model_configs')
    op.drop_index(op.f('ix_knowledge_base_model_configs_config_type'), table_name='knowledge_base_model_configs')
    op.drop_table('knowledge_base_model_configs')
    # ### end Alembic commands ###