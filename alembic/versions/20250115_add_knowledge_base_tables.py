"""add knowledge base tables

Revision ID: add_knowledge_base_tables
Revises: 20250115_add_level_data_access_table
Create Date: 2025-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_knowledge_base_tables'
down_revision = '20250115_add_level_data_access'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create knowledge_documents table
    op.create_table('knowledge_documents',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='文档ID'),
        sa.Column('title', sa.String(length=255), nullable=False, comment='文档标题'),
        sa.Column('description', sa.Text(), nullable=True, comment='文档描述'),
        sa.Column('file_name', sa.String(length=255), nullable=False, comment='原始文件名'),
        sa.Column('file_path', sa.String(length=500), nullable=False, comment='文件存储路径'),
        sa.Column('file_size', sa.Integer(), nullable=False, comment='文件大小（字节）'),
        sa.Column('file_hash', sa.String(length=64), nullable=False, comment='文件哈希值'),
        sa.Column('document_type', sa.Enum('pdf', 'word', 'excel', 'ppt', 'txt', 'markdown', 'html', 'other', name='documenttype'), nullable=False, comment='文档类型'),
        sa.Column('category_id', sa.Integer(), nullable=True, comment='数据分类ID'),
        sa.Column('grade_id', sa.Integer(), nullable=True, comment='数据分级ID'),
        sa.Column('status', sa.Enum('pending', 'processing', 'completed', 'failed', 'archived', name='documentstatus'), nullable=False, server_default='pending', comment='文档状态'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('is_vectorized', sa.Boolean(), nullable=False, server_default='0', comment='是否已向量化'),
        sa.Column('chunk_count', sa.Integer(), nullable=False, server_default='0', comment='文档块数量'),
        sa.Column('embedding_model', sa.String(length=100), nullable=True, comment='使用的嵌入模型'),
        sa.Column('doc_metadata', sa.JSON(), nullable=True, comment='文档元数据'),
        sa.Column('tags', sa.JSON(), nullable=True, comment='文档标签'),
        sa.Column('is_reviewed', sa.Boolean(), nullable=False, server_default='0', comment='是否已审核'),
        sa.Column('reviewed_by', sa.Integer(), nullable=True, comment='审核人ID'),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True, comment='审核时间'),
        sa.Column('uploaded_by', sa.Integer(), nullable=False, comment='上传用户ID'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True, comment='处理完成时间'),
        sa.ForeignKeyConstraint(['category_id'], ['data_source_categories.id'], ),
        sa.ForeignKeyConstraint(['grade_id'], ['data_source_grades.id'], ),
        sa.ForeignKeyConstraint(['reviewed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['uploaded_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_documents_id'), 'knowledge_documents', ['id'], unique=False)
    op.create_index(op.f('ix_knowledge_documents_file_hash'), 'knowledge_documents', ['file_hash'], unique=True)
    
    # Create knowledge_chunks table
    op.create_table('knowledge_chunks',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='文档块ID'),
        sa.Column('document_id', sa.Integer(), nullable=False, comment='所属文档ID'),
        sa.Column('content', sa.Text(), nullable=False, comment='文档块内容'),
        sa.Column('chunk_index', sa.Integer(), nullable=False, comment='块在文档中的索引'),
        sa.Column('start_char', sa.Integer(), nullable=False, comment='在原文档中的起始字符位置'),
        sa.Column('end_char', sa.Integer(), nullable=False, comment='在原文档中的结束字符位置'),
        sa.Column('vector_id', sa.String(length=255), nullable=True, comment='向量数据库中的ID'),
        sa.Column('embedding_dimension', sa.Integer(), nullable=True, comment='嵌入向量维度'),
        sa.Column('chunk_metadata', sa.JSON(), nullable=True, comment='块元数据'),
        sa.Column('page_number', sa.Integer(), nullable=True, comment='所在页码（如果适用）'),
        sa.Column('section_title', sa.String(length=255), nullable=True, comment='所在章节标题'),
        sa.Column('relevance_score', sa.Float(), nullable=True, comment='搜索相关性分数（临时）'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
        sa.ForeignKeyConstraint(['document_id'], ['knowledge_documents.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_chunks_id'), 'knowledge_chunks', ['id'], unique=False)
    op.create_index(op.f('ix_knowledge_chunks_vector_id'), 'knowledge_chunks', ['vector_id'], unique=True)
    op.create_index(op.f('ix_knowledge_chunks_document_id'), 'knowledge_chunks', ['document_id'], unique=False)
    
    # Create knowledge_versions table
    op.create_table('knowledge_versions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='版本ID'),
        sa.Column('document_id', sa.Integer(), nullable=False, comment='所属文档ID'),
        sa.Column('version_number', sa.Integer(), nullable=False, comment='版本号'),
        sa.Column('title', sa.String(length=255), nullable=False, comment='版本标题'),
        sa.Column('description', sa.Text(), nullable=True, comment='版本描述'),
        sa.Column('change_summary', sa.Text(), nullable=True, comment='变更摘要'),
        sa.Column('file_path', sa.String(length=500), nullable=False, comment='版本文件路径'),
        sa.Column('file_size', sa.Integer(), nullable=False, comment='文件大小（字节）'),
        sa.Column('file_hash', sa.String(length=64), nullable=False, comment='文件哈希值'),
        sa.Column('changes', sa.JSON(), nullable=True, comment='详细变更记录'),
        sa.Column('created_by', sa.Integer(), nullable=False, comment='创建人ID'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['document_id'], ['knowledge_documents.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_versions_id'), 'knowledge_versions', ['id'], unique=False)
    op.create_index(op.f('ix_knowledge_versions_document_id'), 'knowledge_versions', ['document_id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_knowledge_versions_document_id'), table_name='knowledge_versions')
    op.drop_index(op.f('ix_knowledge_versions_id'), table_name='knowledge_versions')
    op.drop_table('knowledge_versions')
    
    op.drop_index(op.f('ix_knowledge_chunks_document_id'), table_name='knowledge_chunks')
    op.drop_index(op.f('ix_knowledge_chunks_vector_id'), table_name='knowledge_chunks')
    op.drop_index(op.f('ix_knowledge_chunks_id'), table_name='knowledge_chunks')
    op.drop_table('knowledge_chunks')
    
    op.drop_index(op.f('ix_knowledge_documents_file_hash'), table_name='knowledge_documents')
    op.drop_index(op.f('ix_knowledge_documents_id'), table_name='knowledge_documents')
    op.drop_table('knowledge_documents')
    
    # Drop enums
    sa.Enum(name='documentstatus').drop(op.get_bind())
    sa.Enum(name='documenttype').drop(op.get_bind()) 