"""Add level data access table

Revision ID: 20250115_add_level_data_access
Revises: 20250113_add_hierarchical_permissions
Create Date: 2025-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = '20250115_add_level_data_access'
down_revision = '20250113_add_hierarchical_permissions'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('level_data_access',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='权限ID'),
    sa.Column('level_id', sa.Integer(), nullable=False, comment='级别ID'),
    sa.Column('sensitivity_level', sa.Integer(), nullable=False, comment='数据敏感度级别 (1=高级敏感, 2=中级敏感, 3=初级敏感, 4=完全开放)'),
    sa.Column('can_access', sa.<PERSON>(), nullable=False, comment='是否允许访问'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['level_id'], ['levels.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('level_id', 'sensitivity_level', name='uk_level_sensitivity')
    )
    op.create_index(op.f('ix_level_data_access_id'), 'level_data_access', ['id'], unique=False)
    
    # 初始化数据：为现有级别设置默认的数据访问权限
    # 获取当前所有级别
    connection = op.get_bind()
    levels_result = connection.execute(sa.text("SELECT id, rank_value FROM levels"))
    levels = levels_result.fetchall()
    
    # 为每个级别设置默认权限
    for level in levels:
        level_id, rank_value = level
        
        # 根据rank_value确定默认可访问的敏感度级别
        if rank_value <= 1:  # 最高级别
            accessible_levels = [1, 2, 3, 4]
        elif rank_value <= 2:  # 中级
            accessible_levels = [2, 3, 4]
        elif rank_value <= 3:  # 高级普通用户
            accessible_levels = [3, 4]
        else:  # 初级用户
            accessible_levels = [4]
        
        # 插入权限记录
        for sensitivity_level in accessible_levels:
            connection.execute(sa.text("""
                INSERT INTO level_data_access (level_id, sensitivity_level, can_access, created_at, updated_at)
                VALUES (:level_id, :sensitivity_level, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """), {
                'level_id': level_id,
                'sensitivity_level': sensitivity_level
            })
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_level_data_access_id'), table_name='level_data_access')
    op.drop_table('level_data_access')
    # ### end Alembic commands ### 