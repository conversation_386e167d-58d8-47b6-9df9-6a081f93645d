"""add desensitization rules table

Revision ID: 20250609_121717_add_desensitization_rules_table
Revises: 20250609_add_regex_rules_table
Create Date: 2025-06-09 12:17:17.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '20250609_121717_add_desensitization_rules_table'
down_revision: Union[str, None] = '20250609_add_regex_rules_table'
depends_on: Union[str, None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('desensitization_rules',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='脱敏规则ID，主键'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='规则名称'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='规则描述'),
    sa.Column('data_type', sa.Enum('identity_card', 'credit_card', 'phone', 'email', 'ip_address', 'url', 'address', 'bank_account', 'password', 'username', 'social_security', 'custom', name='datatype'), nullable=False, comment='敏感数据类型'),
    sa.Column('pattern', sa.Text(), nullable=False, comment='检测正则表达式模式'),
    sa.Column('masking_level', sa.Enum('low', 'medium', 'high', 'complete', name='maskinglevel'), nullable=False, comment='脱敏级别'),
    sa.Column('masking_strategy', sa.Enum('asterisk', 'x_mark', 'hash', 'replacement', 'partial_show', 'format_preserve', name='maskingstrategy'), nullable=False, comment='脱敏策略'),
    sa.Column('mask_char', sa.String(length=5), nullable=False, comment='脱敏字符'),
    sa.Column('replacement_text', sa.String(length=100), nullable=True, comment='替换文本（用于replacement策略）'),
    sa.Column('show_first', sa.Integer(), nullable=False, comment='显示前几位'),
    sa.Column('show_last', sa.Integer(), nullable=False, comment='显示后几位'),
    sa.Column('min_mask_length', sa.Integer(), nullable=False, comment='最小脱敏长度'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('priority', sa.Integer(), nullable=False, comment='优先级，数字越大优先级越高'),
    sa.Column('flags', sa.Integer(), nullable=False, comment='正则表达式标志'),
    sa.Column('confidence_threshold', sa.Float(), nullable=False, comment='置信度阈值'),
    sa.Column('role_id', sa.Integer(), nullable=True, comment='关联角色ID，为空表示全局规则'),
    sa.Column('match_count', sa.Integer(), nullable=False, comment='匹配次数统计'),
    sa.Column('mask_count', sa.Integer(), nullable=False, comment='脱敏执行次数统计'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建者'),
    sa.Column('updated_by', sa.String(length=100), nullable=True, comment='最后更新者'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_desensitization_rules_id'), 'desensitization_rules', ['id'], unique=False)
    op.create_index(op.f('ix_desensitization_rules_name'), 'desensitization_rules', ['name'], unique=True)
    op.create_index(op.f('ix_desensitization_rules_data_type'), 'desensitization_rules', ['data_type'], unique=False)
    op.create_index(op.f('ix_desensitization_rules_masking_level'), 'desensitization_rules', ['masking_level'], unique=False)
    op.create_index(op.f('ix_desensitization_rules_is_active'), 'desensitization_rules', ['is_active'], unique=False)
    op.create_index(op.f('ix_desensitization_rules_role_id'), 'desensitization_rules', ['role_id'], unique=False)
    op.create_index(op.f('ix_desensitization_rules_priority'), 'desensitization_rules', ['priority'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_desensitization_rules_priority'), table_name='desensitization_rules')
    op.drop_index(op.f('ix_desensitization_rules_role_id'), table_name='desensitization_rules')
    op.drop_index(op.f('ix_desensitization_rules_is_active'), table_name='desensitization_rules')
    op.drop_index(op.f('ix_desensitization_rules_masking_level'), table_name='desensitization_rules')
    op.drop_index(op.f('ix_desensitization_rules_data_type'), table_name='desensitization_rules')
    op.drop_index(op.f('ix_desensitization_rules_name'), table_name='desensitization_rules')
    op.drop_index(op.f('ix_desensitization_rules_id'), table_name='desensitization_rules')
    op.drop_table('desensitization_rules')
    # ### end Alembic commands ### 