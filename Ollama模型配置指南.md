# Ollama 模型配置指南

## 📝 概述

系统现已支持 **Ollama** 格式的模型接入！Ollama 是一个本地运行大语言模型的工具，支持多种开源模型。

## 🚀 配置步骤

### 1. 在前端界面配置

1. 登录管理员账号
2. 进入"系统管理" → "模型管理"
3. 点击"新建模型"
4. 填写配置信息：

   | 字段 | 值 | 说明 |
   |------|-----|------|
   | **模型名称** | Qwen 1.8B (Ollama) | 自定义名称 |
   | **API格式** | Ollama | 必须选择 Ollama |
   | **模型标识** | qwen:1.8b | Ollama 中的模型名称（必填） |
   | **API地址** | http://*************:11434/api/generate | Ollama 的 generate 端点 |
   | **API密钥** | （留空） | Ollama 不需要密钥 |
   | **Temperature** | 0.7 | 可选，控制随机性 |
   | **最大Token数** | （可留空） | Ollama 可能不支持此参数 |

### 2. 使用测试脚本

```bash
python test_ollama_model.py
```

该脚本会：
- 自动创建 Ollama 模型配置
- 测试模型连接
- 创建会话并发送测试消息

## 🔧 技术细节

### API 格式差异

#### OpenAI 格式（默认）
```json
{
  "messages": [
    {"role": "system", "content": "..."},
    {"role": "user", "content": "..."}
  ],
  "temperature": 0.7
}
```

#### Ollama 格式
```json
{
  "model": "qwen:1.8b",
  "prompt": "用户: 你好\n助手:",
  "stream": false,
  "temperature": 0.7
}
```

### 系统自动转换

当您配置 `api_format: "ollama"` 时，系统会自动：
1. 将对话历史转换为单个 prompt 字符串
2. 添加必需的 `model` 字段
3. 解析 Ollama 格式的响应

## 📌 常见问题

### 1. 必须指定模型标识
Ollama 格式**必须**在"模型标识"字段中填写模型名称，如：
- `qwen:1.8b`
- `llama2:7b`
- `mistral:latest`

### 2. API 地址格式
Ollama 的 API 地址通常是：
```
http://[服务器IP]:11434/api/generate
```

### 3. 支持的模型
Ollama 支持多种模型，可通过以下命令查看：
```bash
curl http://*************:11434/api/tags
```

## 🎯 测试您的配置

### 方法1：直接测试 API
```bash
curl http://*************:11434/api/generate -d '{
  "model": "qwen:1.8b",
  "prompt": "Hello",
  "stream": false
}'
```

### 方法2：使用系统测试
1. 创建模型配置后
2. 创建新会话，选择该模型
3. 发送测试消息

## 💡 提示

1. **流式响应**：Ollama 支持流式响应，但目前系统暂未实现
2. **性能**：本地模型响应速度取决于服务器性能
3. **兼容性**：系统会自动处理格式转换，您只需正确配置即可

## 🔗 相关资源

- [Ollama 官网](https://ollama.ai/)
- [Ollama GitHub](https://github.com/jmorganca/ollama)
- [支持的模型列表](https://ollama.ai/library) 