#!/usr/bin/env python3
import requests

# 登录
login_data = {"username": "admin", "password": "admin123"}
response = requests.post("http://localhost:8000/api/v1/login/access-token", data=login_data)
token = response.json()["access_token"]

headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

# 获取所有关键词
response = requests.get("http://localhost:8000/api/v1/admin/keywords/", headers=headers)
if response.status_code == 200:
    keywords = response.json()
    print("更新关键词匹配类型为 partial...")
    
    for keyword in keywords['items']:
        if keyword['match_type'] == 'exact':
            update_data = {"match_type": "partial"}
            update_response = requests.put(
                f"http://localhost:8000/api/v1/admin/keywords/{keyword['id']}", 
                headers=headers, 
                json=update_data
            )
            
            if update_response.status_code == 200:
                print(f"  ✅ {keyword['word']} - 更新成功")
            else:
                print(f"  ❌ {keyword['word']} - 更新失败: {update_response.status_code}")
    
    print("\n验证更新结果:")
    # 重新获取关键词验证
    response = requests.get("http://localhost:8000/api/v1/admin/keywords/", headers=headers)
    keywords = response.json()
    for k in keywords['items']:
        print(f"  {k['word']} - {k['match_type']} - 分组: {k['keyword_group_name']}")
else:
    print(f"获取关键词失败: {response.status_code}") 