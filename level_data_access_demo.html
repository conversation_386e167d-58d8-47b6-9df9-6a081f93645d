<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>级别数据访问权限管理演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .level-card {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 10px 0;
        }
        .permission-item {
            padding: 8px;
            text-align: center;
            border-radius: 4px;
            font-size: 12px;
        }
        .permission-allowed {
            background: #4CAF50;
            color: white;
        }
        .permission-denied {
            background: #f44336;
            color: white;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn-secondary {
            background: #2196F3;
        }
        .btn-secondary:hover {
            background: #1976D2;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .edit-form {
            display: none;
            background: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .checkbox-group {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>级别数据访问权限管理系统</h1>
        
        <div class="section">
            <h2>系统说明</h2>
            <p><strong>功能概述：</strong>本系统实现了可配置的用户级别数据访问权限管理，替代了原有的硬编码规则。</p>
            <ul>
                <li><strong>灵活配置：</strong>管理员可以通过界面动态调整不同级别用户的数据访问权限</li>
                <li><strong>向后兼容：</strong>未配置的级别自动使用默认规则</li>
                <li><strong>实时生效：</strong>权限修改立即生效，无需重启系统</li>
                <li><strong>安全控制：</strong>支持4个敏感度级别：高级敏感(1)、中级敏感(2)、初级敏感(3)、完全开放(4)</li>
            </ul>
        </div>

        <div class="section">
            <h2>当前权限配置</h2>
            <button class="btn" onclick="loadPermissions()">刷新权限数据</button>
            <div id="permissionsStatus" class="status" style="display:none;"></div>
            <div id="permissionsData"></div>
        </div>

        <div class="section">
            <h2>权限管理操作</h2>
            <button class="btn btn-secondary" onclick="showEditForm()">修改权限配置</button>
            <button class="btn" onclick="resetToDefault()">重置为默认规则</button>
            
            <div id="editForm" class="edit-form">
                <h3>修改级别权限</h3>
                <div>
                    <label>选择级别：</label>
                    <select id="levelSelect">
                        <option value="">请选择级别</option>
                    </select>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="level1" value="1">
                        <label for="level1">高级敏感</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="level2" value="2">
                        <label for="level2">中级敏感</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="level3" value="3">
                        <label for="level3">初级敏感</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="level4" value="4">
                        <label for="level4">完全开放</label>
                    </div>
                </div>
                <button class="btn" onclick="savePermissions()">保存修改</button>
                <button class="btn btn-secondary" onclick="hideEditForm()">取消</button>
            </div>
        </div>

        <div class="section">
            <h2>API测试</h2>
            <p>以下API端点已实现并可用于前端集成：</p>
            <ul>
                <li><code>GET /api/v1/admin/level-data-access/levels/data-access/summary</code> - 获取所有级别权限汇总</li>
                <li><code>GET /api/v1/admin/level-data-access/levels/{level_id}/data-access</code> - 获取特定级别权限</li>
                <li><code>PUT /api/v1/admin/level-data-access/levels/{level_id}/data-access</code> - 更新级别权限</li>
                <li><code>POST /api/v1/admin/level-data-access/levels/{level_id}/data-access/reset-default</code> - 重置为默认</li>
                <li><code>GET /api/v1/data-classification/accessible-grades</code> - 获取用户可访问数据</li>
            </ul>
        </div>
    </div>

    <script>
        let authToken = '';
        let levelsData = [];

        // 获取认证令牌
        async function getAuthToken() {
            try {
                const response = await fetch('/api/v1/login/access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=admin&password=admin123'
                });
                const data = await response.json();
                authToken = data.access_token;
                return true;
            } catch (error) {
                showStatus('登录失败: ' + error.message, 'error');
                return false;
            }
        }

        // 加载权限数据
        async function loadPermissions() {
            if (!authToken && !await getAuthToken()) {
                return;
            }

            try {
                const response = await fetch('/api/v1/admin/level-data-access/levels/data-access/summary', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                displayPermissions(data);
                populateLevelSelect(data.summary);
                showStatus('权限数据加载成功', 'success');
            } catch (error) {
                showStatus('加载权限数据失败: ' + error.message, 'error');
            }
        }

        // 显示权限数据
        function displayPermissions(data) {
            const container = document.getElementById('permissionsData');
            let html = `<h3>共 ${data.total_levels} 个级别</h3>`;
            
            data.summary.forEach(level => {
                html += `
                    <div class="level-card">
                        <h4>${level.level_name} (排序值: ${level.rank_value})</h4>
                        <p>可访问数据类型: ${level.accessible_count}/4</p>
                        <div class="permission-grid">
                `;
                
                Object.entries(level.permissions).forEach(([name, allowed]) => {
                    const className = allowed ? 'permission-allowed' : 'permission-denied';
                    const status = allowed ? '✓' : '✗';
                    html += `<div class="${className} permission-item">${status} ${name}</div>`;
                });
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 填充级别选择框
        function populateLevelSelect(levels) {
            const select = document.getElementById('levelSelect');
            select.innerHTML = '<option value="">请选择级别</option>';
            
            levels.forEach(level => {
                const option = document.createElement('option');
                option.value = level.level_id;
                option.textContent = level.level_name;
                select.appendChild(option);
            });
            
            levelsData = levels;
        }

        // 显示编辑表单
        function showEditForm() {
            document.getElementById('editForm').style.display = 'block';
        }

        // 隐藏编辑表单
        function hideEditForm() {
            document.getElementById('editForm').style.display = 'none';
        }

        // 保存权限修改
        async function savePermissions() {
            const levelId = document.getElementById('levelSelect').value;
            if (!levelId) {
                showStatus('请选择要修改的级别', 'error');
                return;
            }

            const accessRules = [];
            for (let i = 1; i <= 4; i++) {
                const checkbox = document.getElementById(`level${i}`);
                if (checkbox.checked) {
                    accessRules.push({
                        sensitivity_level: i,
                        can_access: true
                    });
                }
            }

            try {
                const response = await fetch(`/api/v1/admin/level-data-access/levels/${levelId}/data-access`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(accessRules)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const result = await response.json();
                showStatus(result.message, 'success');
                hideEditForm();
                loadPermissions(); // 重新加载数据
            } catch (error) {
                showStatus('保存失败: ' + error.message, 'error');
            }
        }

        // 重置为默认规则
        async function resetToDefault() {
            const levelId = document.getElementById('levelSelect').value;
            if (!levelId) {
                showStatus('请选择要重置的级别', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/v1/admin/level-data-access/levels/${levelId}/data-access/reset-default`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const result = await response.json();
                showStatus(result.message, 'success');
                loadPermissions(); // 重新加载数据
            } catch (error) {
                showStatus('重置失败: ' + error.message, 'error');
            }
        }

        // 显示状态消息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('permissionsStatus');
            statusDiv.className = `status status-${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        // 页面加载时自动加载权限数据
        window.addEventListener('load', loadPermissions);
    </script>
</body>
</html> 