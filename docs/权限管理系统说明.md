# AI安全系统 - 权限管理系统说明文档

> **版本**: v1.0  
> **更新时间**: 2025年6月  
> **适用范围**: 开发者、系统管理员、产品经理

---

## 📋 目录

1. [系统概述](#系统概述)
2. [核心概念](#核心概念)
3. [权限体系架构](#权限体系架构)
4. [数据模型设计](#数据模型设计)
5. [权限分配逻辑](#权限分配逻辑)
6. [功能模块详解](#功能模块详解)
7. [API接口说明](#api接口说明)
8. [实际应用场景](#实际应用场景)
9. [最佳实践](#最佳实践)
10. [故障排查](#故障排查)

---

## 🎯 系统概述

AI安全系统采用**三层权限控制模型**：**用户 → 角色 → 级别**，实现细粒度的权限管理和安全控制。

### 设计目标
- ✅ **细粒度控制**: 模块权限、数据分级、功能限制
- ✅ **层级管理**: 支持组织架构的上下级关系
- ✅ **灵活配置**: 可动态调整权限分配
- ✅ **安全审计**: 完整的操作日志追踪
- ✅ **易于扩展**: 支持新模块和权限类型

### 核心特性
- 🛡️ **RBAC权限模型** (Role-Based Access Control)
- 📊 **数据分级访问控制** (4级敏感度)
- 🔍 **内容过滤权限管理** (关键词组分配)
- 📋 **全面审计日志** (操作追踪)
- 🏗️ **层级组织管理** (上下级关系)

---

## 🧩 核心概念

### 1. 用户 (User)
**定义**: 系统的使用者，每个独立的账户实体。

**主要属性**:
```json
{
  "id": 1,
  "username": "user1",
  "email": "<EMAIL>",
  "role_id": 2,        // 角色ID - 决定功能权限
  "level_id": 2,       // 级别ID - 决定权限深度
  "manager_id": 1,     // 上级ID - 层级关系
  "is_active": true    // 账户状态
}
```

### 2. 角色 (Role)
**定义**: 功能性权限的集合，定义"用户能做什么"。

**当前角色**:
- `管理员` (role_id=1): 系统管理、用户管理、配置管理
- `普通用户` (role_id=2): 基础聊天、个人设置

**角色权限包含**:
- **模块权限**: 可访问的系统功能模块
- **关键词组**: 可使用的内容过滤规则
- **数据分级访问**: 可访问的敏感度数据

### 3. 权限级别 (Level)
**定义**: 角色内部的等级划分，定义"能做到什么程度"。

**级别层次** (rank_value数值越小，级别越高):
```
超级管理员 (rank_value: 1) - 最高权限
├── 高级用户 (rank_value: 3) - 高级功能
├── 中级用户 (rank_value: 2) - 中级功能  
└── 初级用户 (rank_value: 1) - 基础功能
```

---

## 🏗️ 权限体系架构

### 整体架构图
```mermaid
graph TB
    subgraph "用户层"
        A[用户 User]
    end
    
    subgraph "角色层"
        B[角色 Role]
        C[模块权限]
        D[关键词组权限]
        E[数据分级权限]
    end
    
    subgraph "级别层"
        F[权限级别 Level]
        G[等级排序]
        H[功能范围限制]
    end
    
    subgraph "权限决策"
        I[最终权限 = 角色权限 ∩ 级别限制]
    end
    
    A --> B
    A --> F
    B --> C
    B --> D
    B --> E
    F --> G
    F --> H
    C --> I
    D --> I
    E --> I
    G --> I
    H --> I
```

### 权限决策流程
```mermaid
flowchart TD
    A[用户请求访问资源] --> B{验证用户身份}
    B -->|失败| C[拒绝访问]
    B -->|成功| D{检查角色权限}
    D -->|无权限| C
    D -->|有权限| E{检查级别限制}
    E -->|不满足| C
    E -->|满足| F{检查层级关系}
    F -->|无权限| C
    F -->|有权限| G[允许访问]
    
    style C fill:#ffebee,color:#c62828
    style G fill:#e8f5e8,color:#2e7d32
```

---

## 📊 数据模型设计

### 核心表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100),
    hashed_password VARCHAR(255) NOT NULL,
    role_id INTEGER REFERENCES roles(id),
    level_id INTEGER REFERENCES levels(id),
    manager_id INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 角色表 (roles)
```sql
CREATE TABLE roles (
    id INTEGER PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 权限级别表 (levels)
```sql
CREATE TABLE levels (
    id INTEGER PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    rank_value INTEGER NOT NULL,
    description TEXT,
    role_id INTEGER REFERENCES roles(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 权限管理表

#### 4. 角色模块权限表 (role_module_permissions)
```sql
CREATE TABLE role_module_permissions (
    id INTEGER PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id),
    module_name VARCHAR(100) NOT NULL,
    can_access BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, module_name)
);
```

#### 5. 角色关键词组分配表 (role_keyword_group_assignments)
```sql
CREATE TABLE role_keyword_group_assignments (
    id INTEGER PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id),
    keyword_group_id INTEGER REFERENCES keyword_groups(id),
    assigned BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 6. 数据源分类表 (data_source_categories)
```sql
CREATE TABLE data_source_categories (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 7. 数据源分级表 (data_source_grades)
```sql
CREATE TABLE data_source_grades (
    id INTEGER PRIMARY KEY,
    category_id INTEGER REFERENCES data_source_categories(id),
    name VARCHAR(100) NOT NULL,
    sensitivity_level INTEGER NOT NULL CHECK (sensitivity_level IN (1,2,3,4)),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 8. 角色数据分级访问表 (role_data_grade_access)
```sql
CREATE TABLE role_data_grade_access (
    id INTEGER PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id),
    data_source_grade_id INTEGER REFERENCES data_source_grades(id),
    access_allowed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 9. 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action_type VARCHAR(100) NOT NULL,
    target_entity VARCHAR(100),
    target_id INTEGER,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    result VARCHAR(20) DEFAULT 'SUCCESS',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔐 权限分配逻辑

### 1. 模块权限控制

**可控制模块**:
- `USER_MANAGEMENT` - 用户管理
- `ROLE_MANAGEMENT` - 角色管理
- `KEYWORD_MANAGEMENT` - 关键词管理
- `SECURITY_POLICY` - 安全策略管理
- `SESSION_MANAGEMENT` - 会话管理
- `AUDIT_LOGS` - 审计日志
- `SYSTEM_CONFIG` - 系统配置
- `MESSAGE_MONITORING` - 消息监控
- `DESENSITIZATION` - 数据脱敏管理
- `LLM_MODEL_MANAGEMENT` - LLM模型管理
- `KNOWLEDGE_BASE` - 知识库管理
- `RAG_CHAT` - 知识库问答

**权限检查逻辑**:
```python
def check_module_permission(user, module_name):
    # 1. 获取用户角色
    role = user.role
    
    # 2. 查询角色模块权限
    permission = db.query(RoleModulePermission).filter(
        RoleModulePermission.role_id == role.id,
        RoleModulePermission.module_name == module_name
    ).first()
    
    # 3. 返回权限结果
    return permission.can_access if permission else False
```

### 2. 数据分级访问控制

**敏感度分级**:
1. **高级敏感** (sensitivity_level=1): 核心机密数据
2. **中级敏感** (sensitivity_level=2): 重要业务数据
3. **初级敏感** (sensitivity_level=3): 一般敏感数据
4. **完全开放** (sensitivity_level=4): 公开数据

**数据分类**:
- **用户信息**: 个人隐私、联系方式等
- **业务数据**: 核心业务相关信息
- **系统日志**: 操作日志和审计信息
- **配置信息**: 系统配置和安全策略

### 3. 层级管理权限

**管理规则**:
```python
def can_manage_user(manager, target_user):
    # 1. 检查是否有管理权限
    if not manager.has_management_permission():
        return False
    
    # 2. 检查级别关系 (只能管理比自己级别低的用户)
    if manager.level.rank_value >= target_user.level.rank_value:
        return False
    
    # 3. 检查层级关系 (直接或间接下属)
    return is_subordinate(target_user, manager)
```

---

## 🚀 功能模块详解

### 1. 角色权限配置

**访问路径**: 系统管理 → 分层权限管理 → 角色权限配置

**功能特性**:
- 🎯 **角色选择**: 下拉选择要配置的角色
- ⚙️ **模块权限**: 开关控制各模块访问权限
- 🏷️ **关键词组**: 分配内容过滤规则
- 📊 **数据分级**: 控制敏感数据访问权限
- 💾 **实时保存**: 权限变更即时生效

**使用场景**:
- 新角色权限初始化
- 权限策略调整
- 安全合规配置

### 2. 数据分类管理

**访问路径**: 系统管理 → 分层权限管理 → 数据分类管理

**功能特性**:
- 📂 **分类管理**: CRUD操作数据分类
- 🔒 **分级配置**: 4级敏感度管理
- 📈 **统计面板**: 分类分布概览
- 🔍 **详情查看**: 完整分级信息

**数据分类示例**:
```json
{
  "用户信息": {
    "高级敏感": "身份证号、密码哈希",
    "中级敏感": "手机号、邮箱地址", 
    "初级敏感": "用户名、昵称",
    "完全开放": "头像、公开资料"
  }
}
```

### 3. 审计日志管理

**访问路径**: 系统管理 → 分层权限管理 → 操作日志

**功能特性**:
- 📊 **统计面板**: 日志概览、成功/失败统计
- 🔍 **高级筛选**: 时间范围、操作类型、结果状态
- 📝 **详细查看**: 完整操作信息
- 🎨 **可视化**: 彩色标签和状态指示器

**日志类型**:
- `LOGIN/LOGOUT` - 登录登出
- `CREATE/UPDATE/DELETE_USER` - 用户操作
- `SEND_MESSAGE` - 消息发送
- `MESSAGE_BLOCKED` - 消息拦截
- `PERMISSION_DENIED` - 权限拒绝

---

## 🔌 API接口说明

### 权限管理API

#### 获取角色权限
```http
GET /api/v1/admin/permissions/roles/{role_id}/permissions
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "role_id": 2,
  "role_name": "普通用户",
  "module_permissions": [
    {"module_name": "SESSION_MANAGEMENT", "can_access": true},
    {"module_name": "USER_MANAGEMENT", "can_access": false}
  ],
  "keyword_group_assignments": [1, 3],
  "data_grade_accesses": [3, 4]
}
```

#### 更新模块权限
```http
PUT /api/v1/admin/permissions/roles/{role_id}/module-permissions
Content-Type: application/json
Authorization: Bearer {token}

[
  {"module_name": "SESSION_MANAGEMENT", "can_access": true},
  {"module_name": "USER_MANAGEMENT", "can_access": false}
]
```

### 数据分类API

#### 获取数据分类
```http
GET /api/v1/admin/data-classification/categories
Authorization: Bearer {token}
```

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "用户信息",
    "description": "用户个人信息、联系方式等敏感数据",
    "grades": [
      {
        "id": 1,
        "name": "高级敏感",
        "sensitivity_level": 1,
        "description": "最高级别敏感数据"
      }
    ]
  }
]
```

### 审计日志API

#### 获取审计日志
```http
GET /api/v1/admin/audit/logs?limit=50&action_type=LOGIN
Authorization: Bearer {token}
```

**响应示例**:
```json
[
  {
    "id": 1,
    "user_id": 1,
    "username": "admin",
    "action_type": "LOGIN",
    "result": "SUCCESS",
    "ip_address": "*************",
    "created_at": "2025-06-12T10:30:00Z"
  }
]
```

---

## 🎭 实际应用场景

### 场景1: 新员工入职

**步骤**:
1. **创建用户账户**
   ```bash
   POST /api/v1/admin/users/
   {
     "username": "newuser",
     "email": "<EMAIL>",
     "role_id": 2,
     "level_id": 2,
     "manager_id": 1
   }
   ```

2. **配置角色权限**
   - 访问: 角色权限配置页面
   - 选择: 普通用户角色
   - 配置: 基础模块权限

3. **分配数据访问权限**
   - 初级敏感数据: ✅
   - 完全开放数据: ✅
   - 高级敏感数据: ❌

### 场景2: 权限升级

**业务需求**: 用户表现优秀，需要提升权限级别

**操作流程**:
1. **更新用户级别**
   ```bash
   PUT /api/v1/admin/users/2
   {"level_id": 4}  # 升级为高级用户
   ```

2. **权限自动生效**
   - 可访问更多数据分级
   - 获得高级功能权限
   - 可能获得管理权限

### 场景3: 安全事件响应

**场景**: 发现用户异常操作，需要紧急处理

**应急流程**:
1. **查看审计日志**
   - 筛选用户操作记录
   - 分析异常行为模式

2. **权限临时限制**
   ```bash
   PUT /api/v1/admin/users/2
   {"is_active": false}  # 临时禁用账户
   ```

3. **详细调查**
   - 分析操作轨迹
   - 评估安全影响

---

## 💡 最佳实践

### 1. 权限设计原则

**最小权限原则**:
- 默认拒绝，显式授权
- 只给必需的最小权限
- 定期审查权限分配

**职责分离**:
- 关键操作需要多人协作
- 避免权限过度集中
- 建立审批流程

**深度防御**:
- 多层权限验证
- 异常行为监控
- 定期安全审计

### 2. 配置建议

**角色划分**:
```yaml
管理员角色:
  - 系统配置管理
  - 用户权限管理
  - 安全策略制定
  
业务用户角色:
  - 基础聊天功能
  - 个人信息管理
  - 有限数据访问
  
审计员角色:
  - 日志查看权限
  - 安全监控功能
  - 合规报告生成
```

**级别设置**:
```yaml
级别层次:
  超级管理员: 无限制权限
  高级用户: 高级功能 + 管理权限
  中级用户: 扩展功能
  初级用户: 基础功能
```

### 3. 监控策略

**关键指标**:
- 权限变更频率
- 异常访问模式
- 失败操作统计
- 敏感数据访问

**告警规则**:
- 连续权限拒绝
- 异常时间访问
- 批量数据操作
- 权限升级事件

---

## 🔧 故障排查

### 常见问题

#### 1. 用户无法访问功能模块

**排查步骤**:
1. 检查用户角色分配
   ```bash
   GET /api/v1/admin/users/{user_id}
   ```

2. 验证角色模块权限
   ```bash
   GET /api/v1/admin/permissions/roles/{role_id}/permissions
   ```

3. 确认用户账户状态
   ```sql
   SELECT is_active FROM users WHERE id = {user_id};
   ```

#### 2. 权限配置不生效

**可能原因**:
- 前端缓存未刷新
- 后端权限缓存未更新
- 数据库事务未提交

**解决方案**:
- 强制刷新页面 (Ctrl+F5)
- 重启后端服务
- 检查数据库连接

#### 3. 审计日志丢失

**检查项目**:
- 数据库存储空间
- 日志记录配置
- API调用链路

### 调试工具

#### 权限检查工具
```python
def debug_user_permissions(user_id):
    user = get_user_by_id(user_id)
    print(f"用户: {user.username}")
    print(f"角色: {user.role.name}")
    print(f"级别: {user.level.name if user.level else 'None'}")
    
    # 检查模块权限
    permissions = get_role_permissions(user.role_id)
    for perm in permissions.module_permissions:
        status = "✅" if perm.can_access else "❌"
        print(f"{status} {perm.module_name}")
```

#### 日志分析命令
```bash
# 查看最近的权限拒绝
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/admin/audit/logs?result=FAILED&limit=10"

# 统计操作类型分布
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/admin/audit/stats"
```

---

## 📚 附录

### 数据库初始化脚本

```sql
-- 创建默认角色
INSERT INTO roles (name, description) VALUES 
('管理员', '系统管理员角色'),
('普通用户', '基础用户角色');

-- 创建默认权限级别  
INSERT INTO levels (name, rank_value, role_id, description) VALUES
('超级管理员', 1, 1, '最高级别管理员'),
('初级用户', 1, 2, '初级普通用户'),
('中级用户', 2, 2, '中级普通用户'),
('高级用户', 3, 2, '高级普通用户');

-- 创建数据分类
INSERT INTO data_source_categories (name, description) VALUES
('用户信息', '用户个人信息、联系方式等敏感数据'),
('业务数据', '核心业务相关的敏感信息'),
('系统日志', '系统操作日志和审计信息'),
('配置信息', '系统配置和安全策略信息');
```

### 配置文件示例

```yaml
# 权限配置
permissions:
  default_role_id: 2
  default_level_id: 2
  session_timeout: 3600
  max_login_attempts: 5

# 审计配置  
audit:
  enabled: true
  retention_days: 90
  log_level: INFO
  
# 安全配置
security:
  password_min_length: 8
  require_special_chars: true
  token_expiry_hours: 24
```

---

## 📞 技术支持

如有问题或建议，请联系：

- **开发团队**: <EMAIL>
- **技术文档**: https://docs.aisecurity.com
- **GitHub仓库**: https://github.com/company/ai-security-system

---

*本文档将持续更新，请关注最新版本。* 