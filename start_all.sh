#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "========================================"
echo "   AI Security System - Start Script"
echo "========================================"
echo

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo -e "${BLUE}[1/2] Starting backend service...${NC}"

# 启动后端服务（后台运行）
echo -e "${GREEN}      Backend service starting...${NC}"
nohup bash -c "cd '$SCRIPT_DIR' && TRANSFORMERS_OFFLINE=1 HF_HUB_OFFLINE=1 python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000" > backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端启动
echo -e "${YELLOW}      Waiting for backend to start...${NC}"
BACKEND_READY=false
for i in {1..30}; do
    if lsof -ti:8000 >/dev/null 2>&1; then
        # 简单的端口检查即可，不做HTTP请求
        BACKEND_READY=true
        break
    fi
    sleep 1
    echo -n "."
done
echo

if [ "$BACKEND_READY" = true ]; then
    echo -e "${GREEN}      ✓ Backend service is ready!${NC}"
    # 额外等待几秒让应用完全启动
    sleep 3
else
    echo -e "${RED}      ✗ Backend service failed to start within 30 seconds${NC}"
    echo -e "${YELLOW}      Check backend.log for errors${NC}"
fi

echo -e "${BLUE}[2/2] Starting frontend service...${NC}"

# 检查前端目录
if [ ! -d "ai-security-frontend" ]; then
    echo -e "${RED}Error: Frontend directory not found${NC}"
    exit 1
fi

# 检查前端依赖
if [ ! -d "ai-security-frontend/node_modules" ]; then
    echo -e "${YELLOW}Installing frontend dependencies...${NC}"
    cd ai-security-frontend
    npm install
    cd ..
fi

# 启动前端服务（后台运行）
echo -e "${GREEN}      Frontend service starting...${NC}"
nohup bash -c "cd '$SCRIPT_DIR/ai-security-frontend' && npm run dev" > frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
echo -e "${YELLOW}      Waiting for frontend to start...${NC}"
FRONTEND_READY=false
FRONTEND_PORT=""
for i in {1..30}; do
    if lsof -ti:5173 >/dev/null 2>&1; then
        FRONTEND_READY=true
        FRONTEND_PORT="5173"
        break
    elif lsof -ti:5174 >/dev/null 2>&1; then
        FRONTEND_READY=true
        FRONTEND_PORT="5174"
        break
    elif lsof -ti:5175 >/dev/null 2>&1; then
        FRONTEND_READY=true
        FRONTEND_PORT="5175"
        break
    fi
    sleep 1
    echo -n "."
done
echo

if [ "$FRONTEND_READY" = true ]; then
    echo -e "${GREEN}      ✓ Frontend service is ready on port $FRONTEND_PORT!${NC}"
else
    echo -e "${RED}      ✗ Frontend service failed to start within 30 seconds${NC}"
    echo -e "${YELLOW}      Check frontend.log for errors${NC}"
fi

echo
echo "========================================"
if [ "$BACKEND_READY" = true ] && [ "$FRONTEND_READY" = true ]; then
    echo "   🎉 All Services Started Successfully!"
elif [ "$BACKEND_READY" = true ]; then
    echo "   ⚠️  Backend Ready, Frontend Still Starting..."
elif [ "$FRONTEND_READY" = true ]; then
    echo "   ⚠️  Frontend Ready, Backend Still Starting..."
else
    echo "   ❌ Service Startup Issues Detected"
fi
echo "========================================"
echo
echo -e "${GREEN}Backend URL: http://localhost:8000${NC}"
echo -e "${GREEN}API Docs: http://localhost:8000/docs${NC}"

if [ "$FRONTEND_READY" = true ]; then
    echo -e "${GREEN}Frontend URL: http://localhost:${FRONTEND_PORT}${NC}"
else
    echo -e "${YELLOW}Frontend URL: http://localhost:5173 (or 5174/5175 if 5173 is occupied)${NC}"
    echo -e "${YELLOW}Note: Frontend may still be starting up...${NC}"
fi

echo
echo "Test Accounts:"
echo "  Admin: admin / admin123"
echo "  User: user1 / user123"
echo
echo -e "${YELLOW}Log Files:${NC}"
echo -e "${YELLOW}  Backend Log: backend.log${NC}"
echo -e "${YELLOW}  Frontend Log: frontend.log${NC}"
echo
echo -e "${YELLOW}Note: Both services are running in background${NC}"
echo -e "${YELLOW}Use stop_all.sh to stop all services${NC}"
echo 