#!/usr/bin/env python3
"""
修复会话模型绑定
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.session import Session
from app.db.models.llm_model import LLMModel

def fix_session_models():
    """修复会话模型绑定"""
    print("🔧 修复会话模型绑定...")
    
    db = SyncSessionLocal()
    
    try:
        # 1. 获取当前激活的模型
        active_model = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).first()
        
        if not active_model:
            print("❌ 没有找到激活的模型!")
            return
        
        print(f"✅ 找到激活模型: {active_model.name} (ID: {active_model.id})")
        
        # 2. 查询所有活跃会话
        sessions = db.query(Session).filter(
            Session.is_deleted == False
        ).all()
        
        print(f"📊 找到 {len(sessions)} 个活跃会话")
        
        # 3. 统计需要更新的会话
        sessions_to_update = []
        for session in sessions:
            if session.llm_model_id != active_model.id:
                sessions_to_update.append(session)
        
        print(f"🔄 需要更新 {len(sessions_to_update)} 个会话的模型绑定")
        
        if not sessions_to_update:
            print("✅ 所有会话的模型绑定都已正确")
            return
        
        # 4. 更新会话模型绑定
        updated_count = 0
        for session in sessions_to_update:
            old_model_id = session.llm_model_id
            session.llm_model_id = active_model.id
            updated_count += 1
            print(f"  会话 {session.id}: {old_model_id} -> {active_model.id}")
        
        # 5. 提交更改
        db.commit()
        print(f"✅ 成功更新 {updated_count} 个会话的模型绑定")
        
        # 6. 验证结果
        print("\n🔍 验证更新结果...")
        updated_sessions = db.query(Session).filter(
            Session.is_deleted == False
        ).all()
        
        correct_bindings = 0
        for session in updated_sessions:
            if session.llm_model_id == active_model.id:
                correct_bindings += 1
        
        print(f"✅ {correct_bindings}/{len(updated_sessions)} 个会话正确绑定到激活模型")
        
        if correct_bindings == len(updated_sessions):
            print("🎉 所有会话模型绑定已修复!")
        else:
            print("⚠️ 仍有会话模型绑定不正确")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        db.rollback()
        
    finally:
        db.close()

if __name__ == "__main__":
    fix_session_models()
