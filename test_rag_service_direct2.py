#!/usr/bin/env python3
"""
直接测试RAG服务的搜索方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
from app.db.session import SyncSessionLocal
from app.services.rag_service import RAGService
from app.services.knowledge_base_service import knowledge_base_service
from app.services.llm_service import llm_service
from app.services.knowledge_base_permissions import knowledge_base_permission_service
from app.services.knowledge_base_audit import knowledge_base_audit_service
from app.services.pipeline.factory import SecurityPipelineFactory

async def test_rag_service_search():
    """测试RAG服务搜索"""
    print("🔍 直接测试RAG服务搜索")
    print("=" * 60)
    
    # 创建数据库会话
    db = SyncSessionLocal()
    
    try:
        # 创建RAG服务实例
        security_pipeline_factory = SecurityPipelineFactory()
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_base_service,
            llm_service=llm_service,
            permission_service=knowledge_base_permission_service,
            audit_service=knowledge_base_audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        # 1. 测试搜索"本地模型路径是什么？"
        print("1. 测试RAG查询'本地模型路径是什么？'...")
        
        # 直接调用知识库搜索部分，跳过LLM生成
        search_results = await knowledge_base_service.search_knowledge_base(
            db=db,
            query="本地模型路径是什么？",
            user_id=1,
            top_k=5,
            threshold=0.1
        )
        
        print(f"   知识库搜索结果数量: {len(search_results)}")
        
        doc2_count = 0
        for i, result in enumerate(search_results, 1):
            document_id = result.get('document_id')
            chunk_id = result.get('chunk_id', '')
            similarity = result.get('similarity', 0)
            content = result.get('content', '')
            document_name = result.get('document_name', '')
            
            # 处理numpy数组格式的相似度
            if hasattr(similarity, 'item'):
                similarity = similarity.item()
            elif hasattr(similarity, '__iter__') and not isinstance(similarity, str):
                similarity = float(similarity[0]) if len(similarity) > 0 else 0.0
            
            print(f"     {i}. doc_id={document_id}, chunk_id={chunk_id}")
            print(f"        document_name: {document_name}")
            print(f"        相似度: {similarity:.4f}")
            print(f"        内容: {content[:100]}...")
            print()
            
            if document_id == 2:
                doc2_count += 1
        
        print(f"   文档2结果数量: {doc2_count}")
        
        # 2. 测试完整的RAG流程（但不调用LLM）
        print("\n2. 测试RAG服务内部搜索逻辑...")
        
        # 模拟RAG服务的搜索调用
        search_results2 = await rag_service.knowledge_service.search_knowledge_base(
            db=rag_service.db,
            query="本地模型路径是什么？",
            user_id=1,
            category_ids=None,
            grade_ids=None,
            top_k=5,
            threshold=0.1
        )
        
        print(f"   RAG服务搜索结果数量: {len(search_results2)}")
        
        doc2_count2 = 0
        for i, result in enumerate(search_results2, 1):
            document_id = result.get('document_id')
            chunk_id = result.get('chunk_id', '')
            similarity = result.get('similarity', 0)
            document_name = result.get('document_name', '')
            
            # 处理numpy数组格式的相似度
            if hasattr(similarity, 'item'):
                similarity = similarity.item()
            elif hasattr(similarity, '__iter__') and not isinstance(similarity, str):
                similarity = float(similarity[0]) if len(similarity) > 0 else 0.0
            
            print(f"     {i}. doc_id={document_id}, chunk_id={chunk_id}")
            print(f"        document_name: {document_name}")
            print(f"        相似度: {similarity:.4f}")
            
            if document_id == 2:
                doc2_count2 += 1
        
        print(f"   文档2结果数量: {doc2_count2}")
        
        # 3. 检查两次搜索结果是否一致
        print(f"\n3. 结果对比:")
        print(f"   直接知识库服务搜索: {len(search_results)} 个结果")
        print(f"   RAG服务内部搜索: {len(search_results2)} 个结果")
        print(f"   结果是否一致: {len(search_results) == len(search_results2)}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_rag_service_search())
