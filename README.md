# AI Security System

This project is an AI Security System designed to provide a secure layer for interactions with Large Language Models (LLMs). It includes features for user management, session handling, security policy enforcement (keyword filtering, malicious intent detection), and data desensitization.

## Project Structure

```
ai-security-system-gemini/
├── app/
│   ├── __init__.py
│   ├── main.py             # FastAPI application entry point
│   ├── core/               # Core components (config, etc.)
│   │   ├── __init__.py
│   │   └── config.py       # Application settings
│   ├── db/                 # Database related (session, models if using ORM directly)
│   │   ├── __init__.py
│   │   └── session.py      # Database session management
│   ├── models/             # Pydantic models (schemas for request/response)
│   │   └── __init__.py
│   ├── apis/               # API routers and endpoints
│   │   ├── __init__.py
│   │   └── v1/             # API version 1
│   │       ├── __init__.py
│   │       ├── api.py        # Aggregates v1 endpoints
│   │       └── endpoints/    # Specific endpoint modules
│   │           ├── __init__.py
│   │           └── users.py  # User related endpoints
│   └── services/           # Business logic services
│       └── __init__.py
├── tests/                  # Test suite
│   └── __init__.py
├── .venv/                  # Python virtual environment
├── .gitignore              # Specifies intentionally untracked files that Git should ignore
├── requirements.txt        # Project dependencies
├── database_schema.sql     # SQL schema for the database
└── README.md               # This file
```

## Technology Stack

*   **Backend**: Python, FastAPI
*   **Database**: MySQL (as per `database_schema.sql`)
*   **ASGI Server**: Uvicorn

## Setup and Installation

1.  **Clone the repository (if applicable):**
    ```bash
    git clone <repository_url>
    cd ai-security-system-gemini
    ```

2.  **Create and activate a Python virtual environment:**
    ```bash
    python -m venv .venv
    ```
    *   On Windows:
        ```bash
        .\.venv\Scripts\activate
        ```
    *   On macOS/Linux:
        ```bash
        source .venv/bin/activate
        ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Set up the database:**
    *   Ensure you have a MySQL server running.
    *   Create a database for the project.
    *   Execute the `database_schema.sql` script in your MySQL database to create the tables.
        ```bash
        # Example using mysql client
        mysql -u your_user -p your_database_name < database_schema.sql
        ```

5.  **Configure environment variables:**
    *   Create a `.env` file in the project root directory (it's ignored by git).
    *   Add the necessary environment variables, for example:
        ```env
        DATABASE_URL="mysql+mysqlclient://your_mysql_user:your_mysql_password@your_mysql_host:your_mysql_port/your_database_name"
        SECRET_KEY="your_very_strong_secret_key_for_jwt"
        # Add other variables as defined in app/core/config.py
        ```
    *   Replace placeholder values with your actual configuration.

## Running the Application

Once the setup is complete, you can run the FastAPI application using Uvicorn:

```bash
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

The API will be available at `http://127.0.0.1:8000`.
You can access the auto-generated API documentation at:
*   Swagger UI: `http://127.0.0.1:8000/docs`
*   ReDoc: `http://127.0.0.1:8000/redoc`

## Development

(Add notes about development practices, running tests, etc., as the project evolves.)

## Contributing

(Add guidelines for contributing if this is an open project.)

## License

(Specify the license for the project, e.g., MIT, Apache 2.0.)
