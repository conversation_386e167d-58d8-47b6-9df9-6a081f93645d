<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库功能权限测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        .login-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .login-form input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .login-form button {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .login-form button:hover {
            background: #40a9ff;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .status.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .permission-card {
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: #fafafa;
        }
        .permission-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .permission-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .permission-value {
            font-weight: bold;
        }
        .permission-value.true {
            color: #52c41a;
        }
        .permission-value.false {
            color: #ff4d4f;
        }
        .menu-simulation {
            margin-top: 20px;
        }
        .menu-item {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .menu-item.enabled {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .menu-item.disabled {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        .menu-item.enabled:hover {
            background: #bae7ff;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        .btn-primary:hover {
            background: #40a9ff;
        }
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        .btn-secondary:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 知识库功能权限测试系统</h1>
        
        <!-- 登录区域 -->
        <div class="section">
            <h2>1. 用户登录</h2>
            <div class="login-form">
                <input type="text" id="username" placeholder="用户名" value="admin">
                <input type="password" id="password" placeholder="密码" value="admin123">
                <button onclick="login()">登录</button>
                <button onclick="logout()" class="btn-secondary">退出</button>
            </div>
            <div id="loginStatus"></div>
        </div>

        <!-- 用户信息区域 -->
        <div class="section">
            <h2>2. 当前用户信息</h2>
            <div id="userInfo"></div>
        </div>

        <!-- 功能权限测试区域 -->
        <div class="section">
            <h2>3. 知识库功能权限检查</h2>
            <button onclick="checkFeaturePermissions()" class="btn-primary">检查功能权限</button>
            <button onclick="checkAccessibleFeatures()" class="btn-primary">获取可访问功能</button>
            <div id="permissionsResult"></div>
        </div>

        <!-- 菜单模拟区域 -->
        <div class="section">
            <h2>4. 知识库菜单模拟</h2>
            <p>基于权限动态显示菜单项：</p>
            <div id="menuSimulation" class="menu-simulation"></div>
        </div>

        <!-- API测试区域 -->
        <div class="section">
            <h2>5. API端点测试</h2>
            <button onclick="testAvailableFeatures()" class="btn-primary">测试可用功能API</button>
            <button onclick="testRolePermissions()" class="btn-primary">测试角色权限API</button>
            <div id="apiTestResult"></div>
        </div>
    </div>

    <script>
        let currentToken = null;
        let currentUser = null;

        // 登录函数
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/login/access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });

                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.access_token;
                    currentUser = data.user;
                    
                    document.getElementById('loginStatus').innerHTML = 
                        '<div class="status success">✅ 登录成功！</div>';
                    
                    updateUserInfo();
                } else {
                    document.getElementById('loginStatus').innerHTML = 
                        '<div class="status error">❌ 登录失败</div>';
                }
            } catch (error) {
                document.getElementById('loginStatus').innerHTML = 
                    '<div class="status error">❌ 连接失败: ' + error.message + '</div>';
            }
        }

        // 退出登录
        function logout() {
            currentToken = null;
            currentUser = null;
            document.getElementById('loginStatus').innerHTML = 
                '<div class="status info">已退出登录</div>';
            document.getElementById('userInfo').innerHTML = '';
            document.getElementById('permissionsResult').innerHTML = '';
            document.getElementById('menuSimulation').innerHTML = '';
            document.getElementById('apiTestResult').innerHTML = '';
        }

        // 更新用户信息显示
        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userInfo').innerHTML = `
                    <div class="status info">
                        <strong>用户ID:</strong> ${currentUser.id}<br>
                        <strong>用户名:</strong> ${currentUser.username}<br>
                        <strong>邮箱:</strong> ${currentUser.email || 'N/A'}<br>
                        <strong>角色ID:</strong> ${currentUser.role_id}<br>
                        <strong>激活状态:</strong> ${currentUser.is_active ? '是' : '否'}
                    </div>
                `;
            }
        }

        // 检查功能权限
        async function checkFeaturePermissions() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/api/v1/admin/feature-permissions/roles/${currentUser.role_id}/feature-permissions/KNOWLEDGE_BASE`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayPermissions(data);
                } else {
                    document.getElementById('permissionsResult').innerHTML = 
                        '<div class="status error">❌ 获取权限失败: ' + response.status + '</div>';
                }
            } catch (error) {
                document.getElementById('permissionsResult').innerHTML = 
                    '<div class="status error">❌ 请求失败: ' + error.message + '</div>';
            }
        }

        // 显示权限信息
        function displayPermissions(data) {
            let html = '<div class="status success">✅ 权限获取成功</div>';
            html += '<div class="permissions-grid">';
            
            data.feature_permissions.forEach(feature => {
                html += `
                    <div class="permission-card">
                        <h3>${getFeatureName(feature.feature_name)}</h3>
                        <div class="permission-item">
                            <span>访问:</span>
                            <span class="permission-value ${feature.can_access}">${feature.can_access ? '✅' : '❌'}</span>
                        </div>
                        <div class="permission-item">
                            <span>创建:</span>
                            <span class="permission-value ${feature.can_create}">${feature.can_create ? '✅' : '❌'}</span>
                        </div>
                        <div class="permission-item">
                            <span>读取:</span>
                            <span class="permission-value ${feature.can_read}">${feature.can_read ? '✅' : '❌'}</span>
                        </div>
                        <div class="permission-item">
                            <span>更新:</span>
                            <span class="permission-value ${feature.can_update}">${feature.can_update ? '✅' : '❌'}</span>
                        </div>
                        <div class="permission-item">
                            <span>删除:</span>
                            <span class="permission-value ${feature.can_delete}">${feature.can_delete ? '✅' : '❌'}</span>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('permissionsResult').innerHTML = html;
            
            // 更新菜单模拟
            updateMenuSimulation(data.feature_permissions);
        }

        // 获取功能中文名称
        function getFeatureName(featureName) {
            const names = {
                'CHAT': '知识库问答',
                'DOCUMENTS': '文档管理',
                'MODELS': '模型管理',
                'USER_PERMISSIONS': '用户文档权限管理'
            };
            return names[featureName] || featureName;
        }

        // 更新菜单模拟
        function updateMenuSimulation(permissions) {
            let html = '';
            
            permissions.forEach(feature => {
                const isEnabled = feature.can_access;
                const className = isEnabled ? 'enabled' : 'disabled';
                const icon = isEnabled ? '✅' : '❌';
                
                html += `
                    <div class="menu-item ${className}">
                        ${icon} ${getFeatureName(feature.feature_name)}
                        ${isEnabled ? '' : ' (无权限)'}
                    </div>
                `;
            });
            
            document.getElementById('menuSimulation').innerHTML = html;
        }

        // 获取可访问功能
        async function checkAccessibleFeatures() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }

            try {
                const response = await fetch('http://localhost:8000/api/v1/admin/feature-permissions/current-user/accessible-features/KNOWLEDGE_BASE', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    let html = '<div class="status success">✅ 可访问功能获取成功</div>';
                    html += '<h3>可访问的功能:</h3>';
                    
                    if (data.accessible_features.length > 0) {
                        data.accessible_features.forEach(feature => {
                            html += `<div class="menu-item enabled">✅ ${feature.name} (${feature.route})</div>`;
                        });
                    } else {
                        html += '<div class="status error">❌ 没有可访问的功能</div>';
                    }
                    
                    document.getElementById('permissionsResult').innerHTML = html;
                } else {
                    document.getElementById('permissionsResult').innerHTML = 
                        '<div class="status error">❌ 获取可访问功能失败: ' + response.status + '</div>';
                }
            } catch (error) {
                document.getElementById('permissionsResult').innerHTML = 
                    '<div class="status error">❌ 请求失败: ' + error.message + '</div>';
            }
        }

        // 测试可用功能API
        async function testAvailableFeatures() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }

            try {
                const response = await fetch('http://localhost:8000/api/v1/admin/feature-permissions/available-features/KNOWLEDGE_BASE', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    let html = '<div class="status success">✅ 可用功能API测试成功</div>';
                    html += '<h3>系统定义的功能:</h3>';
                    
                    data.features.forEach(feature => {
                        html += `
                            <div class="permission-card">
                                <h3>${feature.name}</h3>
                                <p><strong>功能名:</strong> ${feature.feature_name}</p>
                                <p><strong>描述:</strong> ${feature.description}</p>
                                <p><strong>路由:</strong> ${feature.route}</p>
                            </div>
                        `;
                    });
                    
                    document.getElementById('apiTestResult').innerHTML = html;
                } else {
                    document.getElementById('apiTestResult').innerHTML = 
                        '<div class="status error">❌ API测试失败: ' + response.status + '</div>';
                }
            } catch (error) {
                document.getElementById('apiTestResult').innerHTML = 
                    '<div class="status error">❌ 请求失败: ' + error.message + '</div>';
            }
        }

        // 测试角色权限API
        async function testRolePermissions() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/api/v1/admin/feature-permissions/roles/${currentUser.role_id}/feature-permissions/KNOWLEDGE_BASE`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.text();
                
                if (response.ok) {
                    document.getElementById('apiTestResult').innerHTML = 
                        '<div class="status success">✅ 角色权限API测试成功</div><pre>' + result + '</pre>';
                } else {
                    document.getElementById('apiTestResult').innerHTML = 
                        '<div class="status error">❌ 角色权限API测试失败: ' + response.status + '</div><pre>' + result + '</pre>';
                }
            } catch (error) {
                document.getElementById('apiTestResult').innerHTML = 
                    '<div class="status error">❌ 请求失败: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>