#!/usr/bin/env python3
"""
测试知识库菜单结构的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_knowledge_base_menu():
    """测试知识库菜单结构和权限"""
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 获取用户权限
    print("\n👤 获取用户权限...")
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if response.status_code == 200:
        user = response.json()
        print(f"✅ 当前用户: {user['username']} (角色ID: {user['role_id']})")
        
        # 获取角色权限
        response = requests.get(f"{BASE_URL}/admin/permissions/roles/{user['role_id']}/permissions", headers=headers)
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ 角色: {permissions['role_name']}")
            
            # 检查知识库相关权限
            knowledge_base_perm = next((p for p in permissions["module_permissions"] 
                                     if p["module_name"] == "KNOWLEDGE_BASE"), None)
            rag_chat_perm = next((p for p in permissions["module_permissions"] 
                                if p["module_name"] == "RAG_CHAT"), None)
            
            print("\n🔑 知识库相关权限:")
            if knowledge_base_perm and knowledge_base_perm["can_access"]:
                print("  ✅ KNOWLEDGE_BASE - 知识库管理权限")
            else:
                print("  ❌ KNOWLEDGE_BASE - 无知识库管理权限")
                
            if rag_chat_perm and rag_chat_perm["can_access"]:
                print("  ✅ RAG_CHAT - 知识库问答权限")
            else:
                print("  ❌ RAG_CHAT - 无知识库问答权限")
                
            # 3. 测试菜单可访问性
            print("\n📋 测试菜单功能可访问性:")
            
            # 测试知识库问答
            if rag_chat_perm and rag_chat_perm["can_access"]:
                print("  📱 知识库问答:")
                try:
                    response = requests.get(f"{BASE_URL}/rag/statistics", headers=headers)
                    if response.status_code == 200:
                        print("    ✅ RAG统计API可访问")
                    else:
                        print(f"    ❌ RAG统计API访问失败: {response.status_code}")
                except Exception as e:
                    print(f"    ❌ RAG API测试失败: {e}")
            else:
                print("  📱 知识库问答: ❌ 无权限")
            
            # 测试文档管理
            if knowledge_base_perm and knowledge_base_perm["can_access"]:
                print("  📁 文档管理:")
                try:
                    response = requests.get(f"{BASE_URL}/knowledge-base/documents", headers=headers)
                    if response.status_code == 200:
                        print("    ✅ 文档列表API可访问")
                    else:
                        print(f"    ❌ 文档列表API访问失败: {response.status_code}")
                except Exception as e:
                    print(f"    ❌ 文档API测试失败: {e}")
            else:
                print("  📁 文档管理: ❌ 无权限")
            
            # 测试模型管理
            if knowledge_base_perm and knowledge_base_perm["can_access"]:
                print("  🤖 模型管理:")
                try:
                    response = requests.get(f"{BASE_URL}/knowledge-base/model-configs", headers=headers)
                    if response.status_code == 200:
                        print("    ✅ 模型配置API可访问")
                    else:
                        print(f"    ❌ 模型配置API访问失败: {response.status_code}")
                except Exception as e:
                    print(f"    ❌ 模型配置API测试失败: {e}")
            else:
                print("  🤖 模型管理: ❌ 无权限")
                
            # 4. 总结菜单结构
            print("\n📊 知识库管理菜单结构总结:")
            print("  🏠 知识库管理 (一级菜单)")
            
            if rag_chat_perm and rag_chat_perm["can_access"]:
                print("    ├── 💬 知识库问答 (二级菜单) ✅")
            else:
                print("    ├── 💬 知识库问答 (二级菜单) ❌")
                
            if knowledge_base_perm and knowledge_base_perm["can_access"]:
                print("    ├── 📄 文档管理 (二级菜单) ✅")
                print("    └── ⚙️ 模型管理 (二级菜单) ✅")
            else:
                print("    ├── 📄 文档管理 (二级菜单) ❌")
                print("    └── ⚙️ 模型管理 (二级菜单) ❌")
                
        else:
            print(f"❌ 获取角色权限失败: {response.status_code}")
    else:
        print(f"❌ 获取用户信息失败: {response.status_code}")

if __name__ == "__main__":
    test_knowledge_base_menu()
