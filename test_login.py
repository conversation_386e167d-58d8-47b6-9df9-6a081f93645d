import requests
import json

# 测试不同的密码组合
test_credentials = [
    {"username": "admin", "password": "admin123"},
    {"username": "admin", "password": "adminpassword"},
    {"username": "user1", "password": "user123"},
]

print("测试登录凭据...")
print("=" * 50)

for cred in test_credentials:
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/login/access-token", 
            data=cred,
            timeout=5
        )
        
        print(f"\n用户名: {cred['username']}, 密码: {cred['password']}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 登录成功!")
            data = response.json()
            print(f"   Token: {data['access_token'][:20]}...")
            print(f"   Token类型: {data['token_type']}")
        else:
            print("❌ 登录失败")
            if response.text:
                print(f"   错误: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print(f"\n❌ 无法连接到后端服务 (http://localhost:8000)")
        print("   请确保后端服务已启动")
        break
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")

print("\n" + "=" * 50)
print("提示: 如果所有登录都失败，可能需要初始化数据库")
print("运行: python3 -m app.db.initial_data") 