#!/usr/bin/env python3
"""
修复文档10的向量化问题
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.db import session
from app.services import document_vectorizer, vector_db_simple
from sqlalchemy import text

async def fix_doc10_vectorization():
    """修复文档10的向量化问题"""
    
    print("修复文档10的向量化问题")
    print("=" * 60)
    
    # 获取数据库会话
    db = next(session.get_sync_db())
    
    try:
        # 1. 检查文档10的chunks
        print("\n1. 检查文档10的chunks...")
        chunks = db.execute(text("SELECT id, chunk_index, content FROM knowledge_chunks WHERE document_id = 10 ORDER BY chunk_index")).fetchall()
        print(f"   数据库中chunks数量: {len(chunks)}")
        
        if not chunks:
            print("   文档10没有chunks")
            return
        
        # 显示前几个chunk的内容
        for i, chunk in enumerate(chunks[:5]):
            print(f"   Chunk {chunk[1]}: {chunk[2][:100]}...")
        
        # 2. 创建向量化器实例
        print("\n2. 创建向量化器实例...")
        vectorizer = document_vectorizer.DocumentVectorizer()
        vector_db = vectorizer.vector_db
        
        print(f"   向量数据库类型: {type(vector_db)}")
        print(f"   当前向量数量: {len(vector_db.vectors)}")
        
        # 3. 检查现有的文档10向量
        print("\n3. 检查现有的文档10向量...")
        doc10_vectors = []
        for vector_id in vector_db.vectors.keys():
            if 'doc_10_' in vector_id:
                doc10_vectors.append(vector_id)
        
        print(f"   现有文档10向量数: {len(doc10_vectors)}")
        if doc10_vectors:
            print("   现有向量ID:")
            for vector_id in doc10_vectors[:5]:
                print(f"     - {vector_id}")
        
        # 4. 重新向量化文档10的所有chunks
        print("\n4. 重新向量化文档10的所有chunks...")
        
        successful_vectorizations = 0
        failed_vectorizations = 0
        
        for chunk in chunks:
            chunk_id = f"doc_10_chunk_{chunk[1]}"
            content = chunk[2].strip()
            
            if not content:
                print(f"   跳过空内容: {chunk_id}")
                continue
            
            try:
                # 生成向量
                embedding = await vectorizer.generate_embedding(content)
                
                # 存储到向量数据库
                vector_db.add_vector(
                    vector_id=chunk_id,
                    vector=embedding,
                    metadata={
                        'document_id': 10,
                        'chunk_index': chunk[1],
                        'content': content
                    }
                )
                
                print(f"   向量化成功: {chunk_id}")
                successful_vectorizations += 1
                
            except Exception as e:
                print(f"   向量化失败 {chunk_id}: {e}")
                failed_vectorizations += 1
        
        print(f"\n   向量化结果: 成功 {successful_vectorizations}, 失败 {failed_vectorizations}")
        
        # 5. 验证向量化效果
        print("\n5. 验证向量化效果...")
        
        # 重新检查文档10向量
        doc10_vectors_after = []
        for vector_id in vector_db.vectors.keys():
            if 'doc_10_' in vector_id:
                doc10_vectors_after.append(vector_id)
        
        print(f"   修复后文档10向量数: {len(doc10_vectors_after)}")
        
        # 6. 测试搜索
        print("\n6. 测试搜索...")
        
        test_queries = [
            "数据脱敏",
            "敏感数据",
            "脱敏算法",
            "水印溯源"
        ]
        
        for query in test_queries:
            try:
                # 生成查询向量
                query_embedding = await vectorizer.generate_embedding(query)
                
                # 搜索相似向量
                results = vector_db.search_by_vector(
                    query_embedding=query_embedding,
                    n_results=5
                )
                
                print(f"\n   查询 '{query}': {len(results)} 个结果")
                
                doc10_found = False
                for result in results:
                    vector_id = result.get('id', '')
                    if 'doc_10_' in vector_id:
                        doc10_found = True
                        similarity = result.get('similarity', 0)
                        print(f"     找到文档10: {vector_id}, 相似度: {similarity:.4f}")
                        print(f"        内容: {result.get('content', '')[:100]}...")
                        break
                
                if not doc10_found:
                    print(f"     未找到文档10")
                    # 显示找到的其他结果
                    for i, result in enumerate(results[:2], 1):
                        vector_id = result.get('id', '')
                        similarity = result.get('similarity', 0)
                        print(f"       {i}. {vector_id}: {similarity:.4f}")
                        
            except Exception as e:
                print(f"   搜索失败 '{query}': {e}")
        
        # 7. 测试知识库服务搜索
        print("\n7. 测试知识库服务搜索...")
        from app.services import knowledge_base_service
        
        knowledge_service = knowledge_base_service.KnowledgeBaseService()
        
        try:
            search_results = await knowledge_service.search_knowledge_base(
                db=db,
                query="数据脱敏的要求",
                user_id=1,
                top_k=5,
                threshold=0.1
            )
            
            print(f"   知识库搜索结果: {len(search_results)} 个")
            
            doc10_found_in_kb = False
            for result in search_results:
                if result.get('document_id') == 10:
                    doc10_found_in_kb = True
                    similarity = result.get('similarity', 0)
                    if hasattr(similarity, 'item'):
                        similarity = similarity.item()
                    print(f"   知识库中找到文档10，相似度: {similarity:.4f}")
                    print(f"      内容: {result.get('content', '')[:100]}...")
                    break
            
            if not doc10_found_in_kb:
                print(f"   知识库搜索中仍未找到文档10")
                
        except Exception as e:
            print(f"   知识库搜索失败: {e}")
    
    except Exception as e:
        print(f"修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(fix_doc10_vectorization())
