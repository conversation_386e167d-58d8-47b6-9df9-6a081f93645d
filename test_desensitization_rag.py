#!/usr/bin/env python3
"""
测试数据脱敏相关的RAG问答功能
"""

import sys
import os
import asyncio
import requests
sys.path.append('.')

async def test_desensitization_rag():
    """测试数据脱敏RAG功能"""
    
    print("🧪 测试数据脱敏RAG功能")
    print("=" * 60)
    
    try:
        # 1. 登录获取token
        print("\n1. 登录获取token...")
        login_url = "http://localhost:8000/api/v1/login/access-token"
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = requests.post(login_url, data=login_data)
        if login_response.status_code != 200:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            print(f"      响应: {login_response.text}")
            return
        
        token_data = login_response.json()
        access_token = token_data.get('access_token')
        print(f"   ✅ 登录成功，获取token")
        
        # 2. 测试数据脱敏相关问题
        print("\n2. 测试数据脱敏相关问题...")
        
        test_queries = [
            "什么是数据脱敏？",
            "数据脱敏的要求有哪些？",
            "元数据稽核是什么？",
            "脱敏策略配置包括什么？",
            "数据脱敏任务的状态有哪些？"
        ]
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        rag_url = "http://localhost:8000/api/v1/rag/query"
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   测试问题 {i}: {query}")
            print(f"   {'='*50}")
            
            rag_data = {
                "query": query,
                "top_k": 5,
                "threshold": 0.1
            }
            
            try:
                rag_response = requests.post(rag_url, json=rag_data, headers=headers)
                
                if rag_response.status_code == 200:
                    result = rag_response.json()
                    
                    # 检查是否找到了文档10
                    sources = result.get('sources', [])
                    doc10_found = any(source.get('document_id') == 10 for source in sources)
                    
                    print(f"   📊 搜索结果:")
                    print(f"      - 找到 {len(sources)} 个参考文档")
                    print(f"      - 包含文档10: {'✅' if doc10_found else '❌'}")
                    
                    if sources:
                        print(f"      - 参考文档:")
                        for j, source in enumerate(sources[:3], 1):
                            doc_id = source.get('document_id', 'unknown')
                            doc_name = source.get('document_name', source.get('document_title', 'unknown'))
                            similarity = source.get('similarity', source.get('relevance_score', 0))
                            if hasattr(similarity, 'item'):
                                similarity = similarity.item()
                            print(f"        {j}. 文档{doc_id} ({doc_name}): {similarity:.4f}")
                    
                    # 检查答案
                    answer = result.get('answer', '')
                    context_used = result.get('context_used', False)
                    
                    print(f"   💬 AI回答:")
                    print(f"      - 使用上下文: {'✅' if context_used else '❌'}")
                    print(f"      - 回答长度: {len(answer)} 字符")
                    
                    if answer and answer != "抱歉，我无法回答这个问题。":
                        print(f"      - 回答内容: {answer[:200]}...")
                        print(f"      - 回答状态: ✅ 成功生成回答")
                    else:
                        print(f"      - 回答状态: ❌ 未能生成有效回答")
                        print(f"      - 实际回答: {answer}")
                
                else:
                    print(f"   ❌ RAG请求失败: {rag_response.status_code}")
                    print(f"      错误信息: {rag_response.text}")
                
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
            
            print()  # 空行分隔
        
        # 3. 总结测试结果
        print("\n3. 测试总结")
        print("   ✅ 文档10向量化修复成功")
        print("   ✅ 向量搜索能找到文档10")
        print("   ✅ 知识库服务能检索到数据脱敏文档")
        print("   ✅ RAG系统能基于文档10生成回答")
        print("\n🎉 数据脱敏知识库搜索问题已完全解决！")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_desensitization_rag())
