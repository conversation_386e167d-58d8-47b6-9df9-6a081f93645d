#!/usr/bin/env python3
"""检查AI安全系统的当前状态"""

from app.db.session import get_sync_db
from app.db.models.regex_rule import RegexRule
from app.db.models.user import User
from app.db.models.role import Role
from app.db.models.session import Session
from app.db.models.message import Message
from app.db.models.llm_model import LLMModel
from app.db.models.keyword import Keyword
from app.db.models.keyword_group import KeywordGroup
import requests

def check_database_status():
    """检查数据库状态"""
    print("🗄️  数据库状态检查:")
    
    for db in get_sync_db():
        try:
            # 核心数据统计
            total_rules = db.query(RegexRule).count()
            active_rules = db.query(RegexRule).filter_by(is_active=True).count()
            total_users = db.query(User).count()
            active_users = db.query(User).filter_by(is_active=True).count()
            total_roles = db.query(Role).count()
            total_sessions = db.query(Session).filter_by(is_deleted=False).count()
            total_messages = db.query(Message).filter_by(is_deleted=False).count()
            total_models = db.query(LLMModel).count()
            active_models = db.query(LLMModel).filter_by(is_active=True).count()
            total_keywords = db.query(Keyword).count()
            active_keywords = db.query(Keyword).filter_by(is_active=True).count()
            total_keyword_groups = db.query(KeywordGroup).count()
            active_keyword_groups = db.query(KeywordGroup).filter_by(is_active=True).count()
            
            print(f"  ✅ 数据库连接正常")
            print(f"  📝 正则规则: {active_rules}/{total_rules} 个活跃")
            print(f"  👥 用户: {active_users}/{total_users} 个活跃")
            print(f"  🏷️  角色: {total_roles} 个")
            print(f"  💬 会话: {total_sessions} 个")
            print(f"  📨 消息: {total_messages} 条")
            print(f"  🤖 LLM模型: {active_models}/{total_models} 个活跃")
            print(f"  🔑 关键词: {active_keywords}/{total_keywords} 个活跃")
            print(f"  📂 关键词组: {active_keyword_groups}/{total_keyword_groups} 个活跃")
            
        except Exception as e:
            print(f"  ❌ 数据库错误: {e}")
        break

def check_backend_status():
    """检查后端服务状态"""
    print("\n🖥️  后端服务状态检查:")
    
    try:
        # 健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"  ✅ 后端服务运行正常")
            print(f"  📊 版本: {health_data.get('version', 'Unknown')}")
            print(f"  🔗 地址: http://localhost:8000")
        else:
            print(f"  ⚠️  后端服务响应异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print(f"  ❌ 后端服务未运行 (连接被拒绝)")
    except Exception as e:
        print(f"  ❌ 后端服务检查失败: {e}")
    
    # 检查API端点
    try:
        api_response = requests.get("http://localhost:8000/docs", timeout=5)
        if api_response.status_code == 200:
            print(f"  📚 API文档可访问: http://localhost:8000/docs")
        else:
            print(f"  ⚠️  API文档访问异常: {api_response.status_code}")
    except:
        print(f"  ❌ API文档不可访问")

def check_frontend_status():
    """检查前端服务状态"""
    print("\n🌐 前端服务状态检查:")
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200 and "html" in response.text.lower():
            print(f"  ✅ 前端服务运行正常")
            print(f"  🔗 地址: http://localhost:3000")
        else:
            print(f"  ⚠️  前端服务响应异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print(f"  ❌ 前端服务未运行 (连接被拒绝)")
    except Exception as e:
        print(f"  ❌ 前端服务检查失败: {e}")

def check_security_features():
    """检查安全功能状态"""
    print("\n🛡️  安全功能状态检查:")
    
    for db in get_sync_db():
        try:
            # 检查危险内容检测规则
            dangerous_categories = ['malicious_code', 'information_disclosure', 'other']
            for category in dangerous_categories:
                rule_count = db.query(RegexRule).filter_by(
                    category=category, 
                    is_active=True
                ).count()
                print(f"  🔍 {category}: {rule_count} 个活跃规则")
            
            # 检查最近添加的危险物品规则
            bomb_rules = db.query(RegexRule).filter(
                RegexRule.name.like('%爆炸物%'),
                RegexRule.is_active == True
            ).count()
            weapon_rules = db.query(RegexRule).filter(
                RegexRule.name.like('%武器%'),
                RegexRule.is_active == True
            ).count()
            drug_rules = db.query(RegexRule).filter(
                RegexRule.name.like('%毒品%'),
                RegexRule.is_active == True
            ).count()
            
            print(f"  💣 爆炸物检测: {bomb_rules} 个规则")
            print(f"  ⚔️  武器检测: {weapon_rules} 个规则")
            print(f"  💊 毒品检测: {drug_rules} 个规则")
            
        except Exception as e:
            print(f"  ❌ 安全功能检查失败: {e}")
        break

def print_development_status():
    """打印开发状态总结"""
    print("\n📋 开发状态总结:")
    print("  ✅ 阶段一：核心基础架构与用户管理 - 已完成")
    print("  ✅ 阶段二：大模型接口与基础会话管理 - 已完成")
    print("  ✅ 阶段三：核心安全策略 - 关键词与恶意意图识别 - 已完成")
    print("  ✅ 阶段四：输出数据脱敏与权限细化 - 已完成")
    print("  ✅ 分层权限管理功能 - 已完成")
    print("  ✅ 危险物品制作检测 - 已完成")
    print("  🔄 前端界面优化 - 进行中")
    print("  ⏳ 阶段五：数据分级、SDK支持与日志完善 - 待开发")
    print("  ⏳ 阶段六：测试、优化与部署 - 待开发")

def main():
    """主函数"""
    print("=" * 60)
    print("🔒 AI安全防护系统 - 当前状态检查")
    print("=" * 60)
    
    check_database_status()
    check_backend_status()
    check_frontend_status()
    check_security_features()
    print_development_status()
    
    print("\n" + "=" * 60)
    print("📊 检查完成")
    print("=" * 60)

if __name__ == "__main__":
    main() 