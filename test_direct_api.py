#!/usr/bin/env python3
"""
直接测试远程模型API
"""

import requests
import json

def test_direct_model_api():
    """直接测试远程模型API"""
    
    # 远程模型API地址
    api_url = "http://39.99.156.233:11434/api/generate"
    
    # 测试请求
    test_data = {
        "model": "qwen3:0.6b",
        "prompt": "你好，请介绍一下你自己",
        "stream": False
    }
    
    print(f"🚀 直接测试远程模型API...")
    print(f"📡 API地址: {api_url}")
    print(f"📦 请求数据: {json.dumps(test_data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=30
        )
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📝 模型回答: {result.get('response', '无回答')}")
            print(f"⏱️ 完成状态: {result.get('done', False)}")
            print(f"📊 完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def test_chat_api():
    """测试聊天API格式"""
    
    # 远程模型API地址
    api_url = "http://39.99.156.233:11434/api/chat"
    
    # 测试请求
    test_data = {
        "model": "qwen3:0.6b",
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，请友好地回答用户问题。"
            },
            {
                "role": "user", 
                "content": "你好，请介绍一下你自己"
            }
        ],
        "stream": False
    }
    
    print(f"\n🚀 测试聊天API格式...")
    print(f"📡 API地址: {api_url}")
    print(f"📦 请求数据: {json.dumps(test_data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=30
        )
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            message = result.get('message', {})
            print(f"📝 模型回答: {message.get('content', '无回答')}")
            print(f"⏱️ 完成状态: {result.get('done', False)}")
            print(f"📊 完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == "__main__":
    test_direct_model_api()
    test_chat_api()
