import requests
import json

# 获取token
login_data = {
    "username": "admin",  # 替换为你的用户名
    "password": "admin123"  # 替换为你的密码
}

# 登录
response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data)
if response.status_code == 200:
    token = response.json()["access_token"]
    user_info = response.json()["user"]
    print(f"登录成功: {user_info['username']} (角色ID: {user_info['role_id']})")
    
    # 获取角色权限
    headers = {"Authorization": f"Bearer {token}"}
    perm_response = requests.get(
        f"http://localhost:8000/api/v1/admin/permissions/roles/{user_info['role_id']}/permissions",
        headers=headers
    )
    
    if perm_response.status_code == 200:
        permissions = perm_response.json()
        print("\n模块权限:")
        for perm in permissions.get("module_permissions", []):
            print(f"  - {perm['module_name']}: {'✓' if perm['can_access'] else '✗'}")
            
        # 特别检查知识库权限
        kb_perm = next((p for p in permissions.get("module_permissions", []) 
                       if p["module_name"] == "KNOWLEDGE_BASE"), None)
        if kb_perm and kb_perm["can_access"]:
            print("\n✅ 用户有知识库管理权限，应该能看到菜单项")
        else:
            print("\n❌ 用户没有知识库管理权限，需要为角色添加此权限")
    else:
        print(f"获取权限失败: {perm_response.status_code}")
else:
    print(f"登录失败: {response.status_code}") 