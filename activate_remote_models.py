#!/usr/bin/env python3
"""
激活远程模型
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

def activate_remote_models():
    """激活远程模型"""
    print("🔧 激活远程模型...")
    
    db = SyncSessionLocal()
    
    try:
        # 激活远程模型（模型5和9）
        remote_model_ids = [5, 9]
        
        for model_id in remote_model_ids:
            model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
            if model:
                model.is_active = True
                print(f"✅ 激活远程模型: {model.name} (ID: {model.id})")
                print(f"   API URL: {model.api_url}")
                print(f"   模型: {model.config_params.get('model', 'unknown')}")
            else:
                print(f"❌ 模型不存在: ID {model_id}")
        
        # 提交更改
        db.commit()
        print(f"✅ 远程模型激活完成")
        
        # 验证结果
        print(f"\n🔍 验证激活状态...")
        active_models = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).order_by(LLMModel.id).all()
        
        print(f"当前激活的模型 ({len(active_models)} 个):")
        for model in active_models:
            model_type = "🌐 远程" if 'localhost' not in model.api_url and '127.0.0.1' not in model.api_url else "🏠 本地"
            print(f"  ✅ {model.name} (ID: {model.id}) - {model_type}")
            if model.config_params:
                model_name = model.config_params.get('model', 'unknown')
                print(f"     模型: {model_name}")
        
        print(f"\n💡 现在您可以在创建会话时选择使用远程模型了！")
        
    except Exception as e:
        print(f"❌ 激活失败: {str(e)}")
        db.rollback()
        
    finally:
        db.close()

if __name__ == "__main__":
    activate_remote_models()
