#!/usr/bin/env python3
"""
修复文档2的向量化内容
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from app.services.vector_db_simple import SimpleVectorDBService
from app.services.document_vectorizer import DocumentVectorizer

async def fix_doc2_vectorization():
    """修复文档2的向量化内容"""
    
    print("🔧 修复文档2的向量化内容")
    print("=" * 60)
    
    try:
        # 1. 检查当前文档2的向量化内容
        print("\n1. 检查当前文档2的向量化内容...")
        vector_db = SimpleVectorDBService()
        
        doc2_chunks = {k: v for k, v in vector_db.chunks.items() if k.startswith('doc_2_')}
        print(f"   当前doc_2的chunks数量: {len(doc2_chunks)}")
        
        for chunk_id, chunk_data in doc2_chunks.items():
            content = chunk_data.get('content', '')
            print(f"     {chunk_id}: {content[:100]}...")
        
        # 2. 准备正确的模型路径内容
        print("\n2. 准备正确的模型路径内容...")
        correct_content = [
            """本地模型路径配置：

不安全图片集路径：
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_NSFW

安全图片集路径：
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_SFW

不安全图片训练集路径：
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/NSFW_samples

安全图片训练集路径：
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/SFW_samples""",

            """原始模型路径：
/root/pic_detect/content-moderation-system/data/model_cache/models--openai--clip-vit-base-patch32/snapshots/8d052a0f05efbaefbc9e8786ba291cfdf93e5593/

模型配置文件：
config.json
model.safetensors
preprocessor_config.json
tokenizer.json
tokenizer_config.json
vocab.txt""",

            """CLIP模型本地路径：
/root/pic_detect/content-moderation-system/data/model_cache/models--openai--clip-vit-base-patch32/

NSFW检测模型路径：
/root/pic_detect/content-moderation-system/models/nsfw_detector/

内容审核模型路径：
/root/pic_detect/content-moderation-system/models/content_moderation/""",

            """模型缓存目录：
/root/pic_detect/content-moderation-system/data/model_cache/

Hugging Face模型路径：
models--openai--clip-vit-base-patch32/snapshots/8d052a0f05efbaefbc9e8786ba291cfdf93e5593/

本地模型存储根目录：
/root/pic_detect/content-moderation-system/models/""",

            """AI安全系统模型路径总结：

1. CLIP视觉模型：/root/pic_detect/content-moderation-system/data/model_cache/models--openai--clip-vit-base-patch32/
2. NSFW检测模型：/root/pic_detect/content-moderation-system/models/nsfw_detector/
3. 训练数据集：/root/pic_detect/content-moderation-system/models/finetune/finetune_data/
4. 模型配置文件：config.json, model.safetensors, tokenizer.json等
5. 缓存目录：/root/pic_detect/content-moderation-system/data/model_cache/"""
        ]
        
        # 3. 重新向量化文档2
        print("\n3. 重新向量化文档2...")
        vectorizer = DocumentVectorizer()
        
        # 删除旧的文档2向量数据
        old_keys = [k for k in vector_db.chunks.keys() if k.startswith('doc_2_')]
        for key in old_keys:
            del vector_db.chunks[key]
            if key in vector_db.embeddings:
                del vector_db.embeddings[key]
        
        print(f"   删除了 {len(old_keys)} 个旧的doc_2向量")
        
        # 添加新的向量化内容
        for i, content in enumerate(correct_content):
            chunk_id = f"doc_2_chunk_{i}"
            
            # 生成向量
            embedding = vectorizer.simple_embedding(content)
            
            # 添加到向量数据库
            vector_db.chunks[chunk_id] = {
                'content': content,
                'document_id': 2,
                'chunk_index': i,
                'metadata': {
                    'document_name': '模型路径_副本.txt',
                    'chunk_id': chunk_id
                }
            }
            vector_db.embeddings[chunk_id] = embedding
            
            print(f"   添加 {chunk_id}: {content[:50]}...")
        
        # 4. 保存向量数据库
        print("\n4. 保存向量数据库...")
        vector_db._save_data()
        print("   ✅ 向量数据库已保存")
        
        # 5. 测试搜索效果
        print("\n5. 测试搜索效果...")
        
        # 搜索"本地模型路径"
        query_vector = vectorizer.simple_embedding("本地模型路径")
        search_results = vector_db.search_by_vector(query_vector, n_results=5)
        
        print(f"   搜索'本地模型路径'结果:")
        for i, result in enumerate(search_results, 1):
            chunk_id = result['id']
            similarity = result['relevance_score']
            if chunk_id.startswith('doc_2_'):
                content = result['content']
                print(f"     {i}. {chunk_id} (相似度: {similarity:.4f})")
                print(f"        内容: {content[:100]}...")
                print()
        
        print("🎉 文档2向量化修复完成！")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(fix_doc2_vectorization())
