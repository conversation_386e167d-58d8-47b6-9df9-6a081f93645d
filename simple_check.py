import sys
import os
sys.path.append(os.path.abspath('.'))

try:
    from app.db.database import SessionLocal
    from app.db.models.llm_model import LLMModel
    
    db = SessionLocal()
    models = db.query(LLMModel).filter(LLMModel.is_active == True).all()
    
    print("=== LLM模型检查结果 ===")
    print(f"活跃模型数量: {len(models)}")
    
    if models:
        for i, model in enumerate(models, 1):
            print(f"\n{i}. {model.name}")
            print(f"   提供商: {model.provider}")
            print(f"   API地址: {model.api_url}")
            print(f"   API密钥: {'已配置' if model.api_key_encrypted else '未配置'}")
            print(f"   配置参数: {model.config_params}")
    else:
        print("\n❌ 没有找到活跃的LLM模型！")
        print("   这意味着AI响应将使用备用响应机制。")
        
    db.close()
    
except Exception as e:
    print(f"检查失败: {e}")
    import traceback
    traceback.print_exc() 