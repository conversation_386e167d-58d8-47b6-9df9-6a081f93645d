fastapi>=0.100.0
uvicorn[standard]>=0.20.0
python-dotenv>=0.20.0  # 用于管理环境变量
SQLAlchemy>=2.0.0     # ORM，后续会用到
# psycopg2-binary       # PostgreSQL驱动，如果用PG
# mysqlclient>=1.4.0           # MySQL驱动，如果用MySQL（项目使用SQLite，不需要）
passlib[bcrypt]       # 密码哈希，后续用户认证会用到
bcrypt>=4.0.1  # 使用与chromadb兼容的bcrypt版本
python-jose[cryptography] # JWT令牌处理，后续用户认证会用到
email-validator         # For Pydantic EmailStr validation
python-multipart        # For FastAPI form data parsing
aiohttp>=3.9.0          # 异步HTTP客户端，用于调用外部API

# 知识库RAG相关依赖
chromadb>=0.4.0         # 向量数据库
langchain>=0.1.0        # 文档处理和向量化工具链
langchain-community>=0.0.10  # LangChain社区组件
pypdf>=3.17.0           # PDF文档解析
python-docx>=1.0.0      # Word文档解析
openpyxl>=3.1.0         # Excel文档解析
python-pptx>=0.6.0      # PPT文档解析
unstructured>=0.10.0    # 通用文档解析器
tiktoken>=0.5.0         # 文本分词器
sentence-transformers>=2.2.0  # 本地嵌入模型（可选）

# OCR相关依赖
pillow>=9.0.0           # 图像处理
pdf2image>=1.16.0       # PDF转图像
easyocr>=1.7.0          # OCR文字识别
opencv-python>=4.8.0    # 图像处理
pytesseract>=0.3.10     # Tesseract OCR接口
