#!/usr/bin/env python3
"""
检查数据库表结构的脚本
"""

import sqlite3
import os

def check_database_tables():
    """检查数据库中的表"""
    # 尝试多个可能的数据库文件
    possible_db_paths = [
        '/Users/<USER>/Desktop/project/ai-security-system-gemini/ai_security_system.db',
        '/Users/<USER>/Desktop/project/ai-security-system-gemini/ai_security.db',
        '/Users/<USER>/Desktop/project/ai-security-system-gemini/ai-security.db',
        '/Users/<USER>/Desktop/project/ai-security-system-gemini/security_system.db',
        '/Users/<USER>/Desktop/project/ai-security-system-gemini/app.db'
    ]
    
    for db_path in possible_db_paths:
        if os.path.exists(db_path):
            print(f"\n📁 检查数据库: {db_path}")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 获取所有表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                print(f"  📋 表数量: {len(tables)}")
                for table in tables:
                    table_name = table[0]
                    print(f"    - {table_name}")
                    
                    # 如果是roles表，显示详细信息
                    if table_name == 'roles':
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"      记录数: {count}")
                        
                        if count > 0:
                            cursor.execute(f"SELECT id, name FROM {table_name} LIMIT 5")
                            roles = cursor.fetchall()
                            print(f"      示例角色:")
                            for role in roles:
                                print(f"        ID: {role[0]}, 名称: {role[1]}")
                
                conn.close()
                
            except Exception as e:
                print(f"  ❌ 检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database_tables()