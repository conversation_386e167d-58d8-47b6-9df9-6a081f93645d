#!/usr/bin/env python3
"""
检查会话绑定的模型
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.session import Session
from app.db.models.llm_model import LLMModel

def check_session_models():
    """检查会话绑定的模型"""
    print("🔍 检查会话绑定的模型...")
    
    db = SyncSessionLocal()
    
    try:
        # 查询所有会话
        sessions = db.query(Session).filter(
            Session.is_deleted == False
        ).all()
        
        print(f"活跃会话数: {len(sessions)}")
        
        # 统计模型绑定情况
        model_usage = {}
        sessions_without_model = []
        
        for session in sessions:
            if session.llm_model_id:
                if session.llm_model_id not in model_usage:
                    model_usage[session.llm_model_id] = []
                model_usage[session.llm_model_id].append(session.id)
            else:
                sessions_without_model.append(session.id)
        
        print(f"\n📊 模型绑定统计:")
        print(f"  未绑定模型的会话: {len(sessions_without_model)} 个")
        if sessions_without_model:
            print(f"    会话ID: {sessions_without_model}")
        
        print(f"  绑定模型的会话: {len(sessions) - len(sessions_without_model)} 个")
        
        # 检查每个绑定的模型状态
        for model_id, session_ids in model_usage.items():
            model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
            if model:
                status = "✅ 激活" if model.is_active else "❌ 未激活"
                print(f"    模型 {model_id} ({model.name}) - {status}")
                print(f"      绑定会话: {len(session_ids)} 个 (ID: {session_ids[:5]}{'...' if len(session_ids) > 5 else ''})")
            else:
                print(f"    模型 {model_id} - ❌ 不存在")
                print(f"      绑定会话: {len(session_ids)} 个 (ID: {session_ids[:5]}{'...' if len(session_ids) > 5 else ''})")
        
        # 查询当前激活的模型
        active_model = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).first()
        
        if active_model:
            print(f"\n🎯 当前激活模型: {active_model.name} (ID: {active_model.id})")
        else:
            print(f"\n❌ 没有激活的模型!")
        
        # 建议修复方案
        print(f"\n🛠️ 修复建议:")
        if active_model:
            inactive_sessions = []
            for model_id, session_ids in model_usage.items():
                model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
                if not model or not model.is_active:
                    inactive_sessions.extend(session_ids)
            
            if inactive_sessions:
                print(f"  需要更新 {len(inactive_sessions)} 个会话的模型绑定到激活模型 {active_model.id}")
                print(f"  受影响的会话ID: {inactive_sessions[:10]}{'...' if len(inactive_sessions) > 10 else ''}")
            else:
                print(f"  所有会话的模型绑定都正常")
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    check_session_models()
