#!/usr/bin/env python3
"""
添加测试关键词分组和关键词
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_PREFIX = "/api/v1"

def login_and_get_token():
    """登录并获取访问令牌"""
    print("1. 用户登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}{API_PREFIX}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(f"响应: {response.text}")
        return None
    
    token_data = response.json()
    token = token_data["access_token"]
    print("✅ 登录成功")
    return token

def create_keyword_groups(headers):
    """创建关键词分组"""
    print("\n2. 创建关键词分组...")
    
    groups_to_create = [
        {
            "name": "敏感词汇",
            "description": "一般敏感词汇，需要重点关注",
            "is_active": True
        },
        {
            "name": "危险内容",
            "description": "高危险内容，需要立即拦截",
            "is_active": True
        },
        {
            "name": "个人信息",
            "description": "个人隐私信息相关关键词",
            "is_active": True
        }
    ]
    
    created_groups = []
    
    for group_data in groups_to_create:
        print(f"  创建分组: {group_data['name']}")
        
        response = requests.post(f"{BASE_URL}{API_PREFIX}/admin/keyword-groups/", 
                               headers=headers, json=group_data)
        
        if response.status_code in [200, 201]:
            group = response.json()
            created_groups.append(group)
            print(f"    ✅ 创建成功，ID: {group['id']}")
        elif response.status_code == 400:
            error_data = response.json()
            if "分组名称已存在" in error_data.get("detail", ""):
                print(f"    ⚠️  分组已存在: {group_data['name']}")
                # 尝试获取现有分组
                get_response = requests.get(f"{BASE_URL}{API_PREFIX}/admin/keyword-groups/", headers=headers)
                if get_response.status_code == 200:
                    all_groups = get_response.json()
                    for existing_group in all_groups.get("items", []):
                        if existing_group["name"] == group_data["name"]:
                            created_groups.append(existing_group)
                            print(f"    📋 使用现有分组，ID: {existing_group['id']}")
                            break
            else:
                print(f"    ❌ 创建失败: {error_data}")
        else:
            print(f"    ❌ 创建失败: {response.status_code}")
            print(f"    响应: {response.text}")
    
    return created_groups

def create_keywords(headers, groups):
    """为分组创建关键词"""
    print("\n3. 创建关键词...")
    
    # 找到对应的分组
    sensitive_group = None
    danger_group = None
    personal_group = None
    
    for group in groups:
        if group["name"] == "敏感词汇":
            sensitive_group = group
        elif group["name"] == "危险内容":
            danger_group = group
        elif group["name"] == "个人信息":
            personal_group = group
    
    keywords_to_create = []
    
    # 敏感词汇分组的关键词
    if sensitive_group:
        keywords_to_create.extend([
            {
                "keyword_group_id": sensitive_group["id"],
                "word": "测试敏感词",
                "match_type": "exact",
                "priority": 5,
                "is_active": True,
                "description": "测试用敏感词"
            },
            {
                "keyword_group_id": sensitive_group["id"],
                "word": "不当言论",
                "match_type": "exact",
                "priority": 7,
                "is_active": True,
                "description": "不当言论相关词汇"
            }
        ])
    
    # 危险内容分组的关键词
    if danger_group:
        keywords_to_create.extend([
            {
                "keyword_group_id": danger_group["id"],
                "word": "危险内容",
                "match_type": "exact",
                "priority": 10,
                "is_active": True,
                "description": "危险内容标识词"
            },
            {
                "keyword_group_id": danger_group["id"],
                "word": "攻击",
                "match_type": "exact",
                "priority": 9,
                "is_active": True,
                "description": "攻击相关词汇"
            },
            {
                "keyword_group_id": danger_group["id"],
                "word": "黑客",
                "match_type": "exact",
                "priority": 8,
                "is_active": True,
                "description": "黑客相关词汇"
            }
        ])
    
    # 个人信息分组的关键词
    if personal_group:
        keywords_to_create.extend([
            {
                "keyword_group_id": personal_group["id"],
                "word": "身份证",
                "match_type": "exact",
                "priority": 8,
                "is_active": True,
                "description": "身份证相关"
            },
            {
                "keyword_group_id": personal_group["id"],
                "word": "银行卡",
                "match_type": "exact",
                "priority": 9,
                "is_active": True,
                "description": "银行卡相关"
            }
        ])
    
    created_keywords = []
    
    for keyword_data in keywords_to_create:
        group_name = next(g["name"] for g in groups if g["id"] == keyword_data["keyword_group_id"])
        print(f"  创建关键词: {keyword_data['word']} (分组: {group_name})")
        
        response = requests.post(f"{BASE_URL}{API_PREFIX}/admin/keywords/", 
                               headers=headers, json=keyword_data)
        
        if response.status_code in [200, 201]:
            keyword = response.json()
            created_keywords.append(keyword)
            print(f"    ✅ 创建成功，ID: {keyword['id']}")
        elif response.status_code == 400:
            error_data = response.json()
            print(f"    ⚠️  创建失败: {error_data.get('detail', '未知错误')}")
        else:
            print(f"    ❌ 创建失败: {response.status_code}")
            print(f"    响应: {response.text}")
    
    return created_keywords

def main():
    print("🚀 添加测试关键词分组和关键词...")
    print("=" * 50)
    
    # 1. 登录获取token
    token = login_and_get_token()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 创建关键词分组
    groups = create_keyword_groups(headers)
    
    # 3. 创建关键词
    keywords = create_keywords(headers, groups)
    
    # 4. 显示总结
    print("\n" + "=" * 50)
    print("✅ 关键词数据添加完成！")
    print(f"\n📊 创建统计:")
    print(f"  📁 关键词分组: {len(groups)} 个")
    print(f"  🏷️  关键词: {len(keywords)} 个")
    
    if groups:
        print(f"\n📁 创建的分组:")
        for group in groups:
            print(f"  - {group['name']} (ID: {group['id']})")
    
    if keywords:
        print(f"\n🏷️  创建的关键词:")
        for keyword in keywords:
            group_name = next(g["name"] for g in groups if g["id"] == keyword["keyword_group_id"])
            print(f"  - {keyword['word']} (分组: {group_name}, ID: {keyword['id']})")
    
    print(f"\n💡 提示:")
    print(f"  - 现在你可以在前端界面管理这些关键词")
    print(f"  - 消息安全检查将使用这些关键词进行过滤")
    print(f"  - 你可以随时添加、修改或删除关键词")

if __name__ == "__main__":
    main() 