@echo off
chcp 65001 > nul
echo ========================================
echo   AI Security System - Start Script
echo ========================================
echo.

:: Start backend service
echo [1/2] Starting backend service...
start "AI Security Backend" cmd /k "cd /d %~dp0 && .venv\Scripts\activate && python start_backend.py"
echo      Backend service starting...
timeout /t 3 /nobreak > nul

:: Start frontend service
echo [2/2] Starting frontend service...
start "AI Security Frontend" cmd /k "cd /d %~dp0ai-security-frontend && npm run dev"
echo      Frontend service starting...

echo.
echo ========================================
echo   Startup Complete!
echo ========================================
echo.
echo Backend URL: http://localhost:8000
echo API Docs: http://localhost:8000/docs
echo Frontend URL: http://localhost:5173
echo.
echo Test Accounts:
echo   Admin: admin / admin123
echo   User: user1 / user123
echo.
echo Press any key to close this window...
pause > nul