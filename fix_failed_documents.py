#!/usr/bin/env python3
"""
自动修复失败文档的脚本
解决 "Document is already being processed" 错误
"""

import sys
import os
import sqlite3
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_failed_documents():
    """修复所有失败的文档"""
    
    # 连接数据库
    db_path = './ai_security.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔍 搜索失败的文档...")
        
        # 查找失败的文档
        cursor.execute('''
            SELECT id, title, file_path, error_message
            FROM knowledge_documents 
            WHERE status = 'FAILED' 
            AND (error_message LIKE '%already being processed%' 
                 OR error_message LIKE '%coroutine%'
                 OR error_message LIKE '%AsyncSession%')
        ''')
        
        failed_docs = cursor.fetchall()
        
        if not failed_docs:
            print("✅ 没有需要修复的文档")
            return
        
        print(f"📋 找到 {len(failed_docs)} 个需要修复的文档:")
        
        fixed_count = 0
        
        for doc_id, title, file_path, error_message in failed_docs:
            print(f"\n📄 处理文档 ID {doc_id}: {title}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"   ❌ 文件不存在: {file_path}")
                continue
            
            try:
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"   📝 文件内容长度: {len(content)} 字符")
                
                # 简单分块（每1000字符一块）
                chunk_size = 1000
                chunks = []
                
                for i in range(0, len(content), chunk_size):
                    chunk_content = content[i:i+chunk_size]
                    chunks.append({
                        'content': chunk_content,
                        'start_char': i,
                        'end_char': min(i + chunk_size, len(content)),
                        'chunk_index': len(chunks)
                    })
                
                # 删除现有的分块
                cursor.execute('DELETE FROM knowledge_chunks WHERE document_id = ?', (doc_id,))
                
                # 创建新分块
                for chunk_data in chunks:
                    cursor.execute('''
                        INSERT INTO knowledge_chunks 
                        (document_id, content, chunk_index, start_char, end_char, vector_id, chunk_metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        doc_id,
                        chunk_data['content'],
                        chunk_data['chunk_index'],
                        chunk_data['start_char'],
                        chunk_data['end_char'],
                        f'fixed_chunk_{doc_id}_{chunk_data["chunk_index"]}',
                        '{"auto_fixed": true}'
                    ))
                
                # 更新文档状态
                cursor.execute('''
                    UPDATE knowledge_documents 
                    SET status = 'COMPLETED',
                        error_message = NULL,
                        chunk_count = ?,
                        is_vectorized = 0,
                        processed_at = ?
                    WHERE id = ?
                ''', (len(chunks), datetime.utcnow().isoformat(), doc_id))
                
                print(f"   ✅ 修复成功! 创建了 {len(chunks)} 个分块")
                fixed_count += 1
                
            except Exception as e:
                print(f"   ❌ 修复失败: {str(e)}")
                continue
        
        # 提交所有更改
        conn.commit()
        
        print(f"\n🎉 修复完成!")
        print(f"✅ 成功修复: {fixed_count} 个文档")
        print(f"❌ 修复失败: {len(failed_docs) - fixed_count} 个文档")
        
    except Exception as e:
        print(f"❌ 修复过程出错: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

def reset_processing_documents():
    """重置卡在处理中状态的文档"""
    
    db_path = './ai_security.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔄 重置处理中状态的文档...")
        
        # 查找处理中的文档
        cursor.execute('''
            SELECT id, title 
            FROM knowledge_documents 
            WHERE status = 'PROCESSING'
        ''')
        
        processing_docs = cursor.fetchall()
        
        if processing_docs:
            print(f"📋 找到 {len(processing_docs)} 个处理中的文档:")
            for doc_id, title in processing_docs:
                print(f"   - ID {doc_id}: {title}")
            
            # 重置为PENDING状态
            cursor.execute('''
                UPDATE knowledge_documents 
                SET status = 'PENDING', error_message = NULL
                WHERE status = 'PROCESSING'
            ''')
            
            conn.commit()
            print(f"✅ 已重置 {len(processing_docs)} 个文档为PENDING状态")
        else:
            print("✅ 没有卡在处理中的文档")
            
    except Exception as e:
        print(f"❌ 重置过程出错: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("=" * 50)
    print("    AI Security System - 文档修复工具")
    print("=" * 50)
    
    # 重置处理中的文档
    reset_processing_documents()
    
    print()
    
    # 修复失败的文档
    fix_failed_documents()
    
    print("\n" + "=" * 50)
    print("修复完成! 请刷新前端页面查看结果")
    print("=" * 50) 