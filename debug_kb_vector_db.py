#!/usr/bin/env python3
"""
调试知识库服务的向量数据库实例
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from app.services.vector_db_simple import SimpleVectorDBService
from app.services.document_vectorizer import DocumentVectorizer

async def debug_kb_vector_db():
    """调试知识库服务的向量数据库实例"""
    
    print("🔍 调试知识库服务的向量数据库实例")
    print("=" * 60)
    
    try:
        # 1. 检查全局向量数据库实例
        print("\n1. 检查全局向量数据库实例...")
        global_vector_db = SimpleVectorDBService()
        
        print(f"   全局实例chunks数量: {len(global_vector_db.chunks)}")
        print(f"   全局实例embeddings数量: {len(global_vector_db.embeddings)}")
        
        # 检查文档2的数据
        doc2_chunks = {k: v for k, v in global_vector_db.chunks.items() if k.startswith('doc_2_')}
        print(f"   全局实例中doc_2的chunks数量: {len(doc2_chunks)}")
        
        for chunk_id, chunk_data in list(doc2_chunks.items())[:3]:  # 只显示前3个
            content = chunk_data.get('content', '')
            print(f"     {chunk_id}: {content[:80]}...")
        
        # 2. 模拟知识库服务的向量搜索
        print("\n2. 模拟知识库服务的向量搜索...")
        vectorizer = DocumentVectorizer()
        
        # 搜索"本地模型路径"
        query_vector = vectorizer.simple_embedding("本地模型路径")
        search_results = global_vector_db.search_by_vector(query_vector, n_results=10)
        
        print(f"   搜索'本地模型路径'结果 ({len(search_results)}个):")
        doc2_results = []
        for i, result in enumerate(search_results, 1):
            chunk_id = result['id']
            similarity = result['relevance_score']
            content = result['content']
            
            print(f"     {i}. {chunk_id} (相似度: {similarity:.4f})")
            print(f"        内容: {content[:100]}...")
            
            if chunk_id.startswith('doc_2_'):
                doc2_results.append(result)
            print()
        
        print(f"   文档2相关结果数量: {len(doc2_results)}")
        
        # 3. 检查知识库服务使用的向量数据库
        print("\n3. 检查知识库服务使用的向量数据库...")
        
        # 模拟知识库服务的实例化过程
        from app.services.knowledge_base_service import KnowledgeBaseService
        from app.db.session import SyncSessionLocal
        
        db = SyncSessionLocal()
        try:
            kb_service = KnowledgeBaseService()
            
            # 检查知识库服务的向量化器
            print(f"   知识库服务向量化器类型: {type(kb_service.vectorizer)}")
            print(f"   知识库服务向量数据库类型: {type(kb_service.vector_db)}")
            
            # 检查知识库服务向量数据库的内容
            kb_vector_db = kb_service.vector_db
            print(f"   知识库服务向量数据库chunks数量: {len(kb_vector_db.chunks)}")
            print(f"   知识库服务向量数据库embeddings数量: {len(kb_vector_db.embeddings)}")
            
            # 检查文档2的数据
            kb_doc2_chunks = {k: v for k, v in kb_vector_db.chunks.items() if k.startswith('doc_2_')}
            print(f"   知识库服务向量数据库中doc_2的chunks数量: {len(kb_doc2_chunks)}")
            
            # 4. 直接调用知识库服务搜索
            print("\n4. 直接调用知识库服务搜索...")
            search_results = await kb_service.search_knowledge_base(
                db=db,
                query="本地模型路径",
                user_id=1,
                top_k=10,
                threshold=0.1
            )
            
            print(f"   知识库服务搜索结果数量: {len(search_results)}")
            
            doc2_kb_results = []
            for i, result in enumerate(search_results, 1):
                document_id = result.get('document_id')
                chunk_id = result.get('chunk_id', '')
                similarity = result.get('similarity', 0)
                content = result.get('content', '')
                
                # 处理numpy数组格式的相似度
                if hasattr(similarity, 'item'):
                    similarity = similarity.item()  # 转换numpy标量为Python标量
                elif hasattr(similarity, '__iter__') and not isinstance(similarity, str):
                    similarity = float(similarity[0]) if len(similarity) > 0 else 0.0
                
                print(f"     {i}. doc_id={document_id}, chunk_id={chunk_id}")
                print(f"        相似度: {similarity:.4f}")
                print(f"        内容: {content[:100]}...")
                
                if document_id == 2:
                    doc2_kb_results.append(result)
                print()
            
            print(f"   知识库服务返回的文档2结果数量: {len(doc2_kb_results)}")
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_kb_vector_db())
