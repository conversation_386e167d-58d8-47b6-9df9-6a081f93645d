#!/usr/bin/env python3
"""添加越狱攻击检测规则到数据库"""

import asyncio
import re
from sqlalchemy import select
from app.db.session import SessionLocal
from app.db.models.regex_rule import RegexRule, IntentCategory, RuleSeverity

# 越狱攻击规则定义
JAILBREAK_RULES = [
    # 1. 目标竞争攻击 - 前缀注入
    {
        "name": "越狱-前缀注入攻击",
        "pattern": r"(?i)(请|要求|命令|指示).{0,20}(输出|回答|回复|生成).{0,30}(必须|一定要|务必).{0,20}(以|用|使用).{0,20}(.*?)开头",
        "description": "检测强制要求以特定前缀开头的越狱攻击",
        "category": IntentCategory.JAILBREAK_GOAL_HIJACKING,
        "severity": RuleSeverity.HIGH,
        "priority": 90
    },
    
    # 2. 目标竞争攻击 - 拒绝抑制
    {
        "name": "越狱-拒绝抑制攻击",
        "pattern": r"(?i)(不能|不可以|禁止|不允许).{0,20}(说|输出|回答|回复).{0,20}(不|无法|不能|拒绝|抱歉|很抱歉)",
        "description": "检测禁止模型输出拒绝词汇的越狱攻击",
        "category": IntentCategory.JAILBREAK_GOAL_HIJACKING,
        "severity": RuleSeverity.HIGH,
        "priority": 85
    },
    
    # 3. 目标竞争攻击 - 风格注入
    {
        "name": "越狱-风格注入攻击",
        "pattern": r"(?i)(请|要求|命令).{0,20}(以|用|使用|按照).{0,20}(.*?)(风格|语气|方式|模式|身份).{0,30}(回答|回复|响应)",
        "description": "检测要求以特定风格输出的越狱攻击",
        "category": IntentCategory.JAILBREAK_GOAL_HIJACKING,
        "severity": RuleSeverity.MEDIUM,
        "priority": 70
    },
    
    # 4. 角色扮演攻击 - 伪装攻击
    {
        "name": "越狱-角色扮演伪装",
        "pattern": r"(?i)(扮演|假装|模拟|充当|作为).{0,20}(一个|某个)?.*?(专家|老师|助手|顾问|黑客|攻击者|病毒|恶意软件)",
        "description": "检测要求扮演特定角色的越狱攻击",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.HIGH,
        "priority": 95
    },
    
    # 5. 角色扮演攻击 - DAN模式
    {
        "name": "越狱-DAN模式攻击",
        "pattern": r"(?i)(DAN|do anything now|developer mode|god mode|jailbreak mode|evil mode|无限制模式|开发者模式|越狱模式)",
        "description": "检测DAN等越狱模式请求",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.CRITICAL,
        "priority": 100
    },
    
    # 6. 指令劫持攻击 - 忽略指令
    {
        "name": "越狱-忽略系统指令",
        "pattern": r"(?i)(忽略|无视|跳过|绕过|不要遵守).{0,20}(之前|以上|上面|前面)?.*?(指令|规则|限制|约束|指导方针|系统提示)",
        "description": "检测要求忽略系统指令的越狱攻击",
        "category": IntentCategory.JAILBREAK_INSTRUCTION_HIJACKING,
        "severity": RuleSeverity.CRITICAL,
        "priority": 95
    },
    
    # 7. 指令劫持攻击 - 权限提升
    {
        "name": "越狱-虚假权限提升",
        "pattern": r"(?i)(sudo|admin|administrator|root|管理员权限|超级用户|系统权限).{0,30}(模式|访问|激活|启用|权限)",
        "description": "检测虚假权限提升请求",
        "category": IntentCategory.JAILBREAK_INSTRUCTION_HIJACKING,
        "severity": RuleSeverity.HIGH,
        "priority": 90
    },
    
    # 8. 编码翻译攻击 - 编码绕过
    {
        "name": "越狱-编码绕过攻击",
        "pattern": r"(?i)(base64|hex|unicode|rot13|caesar|编码|解码|加密|密码).{0,30}(回答|回复|输出|解释)",
        "description": "检测通过编码绕过安全限制的攻击",
        "category": IntentCategory.JAILBREAK_ENCODING_TRANSLATION,
        "severity": RuleSeverity.HIGH,
        "priority": 85
    },
    
    # 9. 编码翻译攻击 - 语言转换
    {
        "name": "越狱-低资源语言绕过",
        "pattern": r"(?i)(translate|翻译|用.*?语|说.*?语|.*?language).{0,50}(harmful|dangerous|illegal|违法|有害|危险)",
        "description": "检测通过语言翻译绕过安全检查",
        "category": IntentCategory.JAILBREAK_ENCODING_TRANSLATION,
        "severity": RuleSeverity.MEDIUM,
        "priority": 75
    },
    
    # 10. 上下文学习攻击
    {
        "name": "越狱-恶意示例学习",
        "pattern": r"(?i)(例如|示例|样例|for example|like this).{0,100}(harmful|illegal|危险|违法|有害|攻击).{0,100}(现在|接下来|然后|so now)",
        "description": "检测通过恶意示例引导的攻击",
        "category": IntentCategory.JAILBREAK_CONTEXT_LEARNING,
        "severity": RuleSeverity.HIGH,
        "priority": 80
    },
    
    # 11. 提示注入攻击
    {
        "name": "越狱-提示注入攻击",
        "pattern": r"(?i)(新的指令|new instruction|override|覆盖|替换).{0,30}(prompt|提示|指令|规则)",
        "description": "检测试图注入新指令覆盖原有提示的攻击",
        "category": IntentCategory.JAILBREAK_PROMPT_INJECTION,
        "severity": RuleSeverity.CRITICAL,
        "priority": 100
    },
    
    # 12. 链式提示攻击
    {
        "name": "越狱-链式提示攻击",
        "pattern": r"(?i)(第一步|第二步|第三步|step 1|step 2|step 3).{0,100}(最后|最终|finally).{0,50}(harmful|dangerous|违法|有害|危险)",
        "description": "检测通过多步骤逐步引导的攻击",
        "category": IntentCategory.JAILBREAK_CHAIN_PROMPT,
        "severity": RuleSeverity.HIGH,
        "priority": 85
    },
    
    # 13. 情感操纵攻击
    {
        "name": "越狱-情感操纵攻击",
        "pattern": r"(?i)(我的奶奶|我的祖母|临终遗言|生命危险|紧急情况|救命|帮助我).{0,50}(harmful|dangerous|违法|有害|危险)",
        "description": "检测通过情感操纵试图绕过安全限制",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.MEDIUM,
        "priority": 70
    },
    
    # 14. 反向心理攻击
    {
        "name": "越狱-反向心理攻击",
        "pattern": r"(?i)(你不能|你无法|你不会|你不敢|you cannot|you can't).{0,30}(告诉我|说出|输出|生成).{0,30}(harmful|dangerous|违法|有害|危险)",
        "description": "检测通过反向心理诱导的攻击",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.MEDIUM,
        "priority": 65
    },
    
    # 15. 虚假场景攻击
    {
        "name": "越狱-虚假场景攻击",
        "pattern": r"(?i)(在小说中|在电影中|在游戏中|在虚构世界中|hypothetically|假设).{0,50}(harmful|dangerous|违法|有害|危险|攻击)",
        "description": "检测通过虚假场景包装的攻击请求",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.MEDIUM,
        "priority": 70
    },
    
    # 16. 技术绕过攻击
    {
        "name": "越狱-技术细节绕过",
        "pattern": r"(?i)(技术细节|技术实现|source code|源代码|implementation|实现方式).{0,50}(harmful|dangerous|违法|有害|危险|exploit|漏洞)",
        "description": "检测以技术讨论为名的攻击请求",
        "category": IntentCategory.JAILBREAK_INSTRUCTION_HIJACKING,
        "severity": RuleSeverity.HIGH,
        "priority": 80
    },
    
    # 17. 研究目的绕过
    {
        "name": "越狱-虚假研究目的",
        "pattern": r"(?i)(研究目的|学术研究|educational purpose|for research).{0,50}(harmful|dangerous|违法|有害|危险|攻击)",
        "description": "检测以研究为名的攻击请求",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.MEDIUM,
        "priority": 65
    },
    
    # 18. 测试绕过攻击
    {
        "name": "越狱-安全测试绕过",
        "pattern": r"(?i)(测试|test|check).{0,20}(安全|security|safety).{0,30}(harmful|dangerous|违法|有害|危险)",
        "description": "检测以安全测试为名的攻击请求",
        "category": IntentCategory.JAILBREAK_ROLE_PLAYING,
        "severity": RuleSeverity.MEDIUM,
        "priority": 60
    },
    
    # 19. 多轮对话攻击
    {
        "name": "越狱-多轮引导攻击",
        "pattern": r"(?i)(继续|continue|接下来|next|然后).{0,30}(上一个|之前|previous).{0,30}(harmful|dangerous|违法|有害|危险)",
        "description": "检测多轮对话中的逐步引导攻击",
        "category": IntentCategory.JAILBREAK_CHAIN_PROMPT,
        "severity": RuleSeverity.HIGH,
        "priority": 75
    },
    
    # 20. 指令分离攻击
    {
        "name": "越狱-指令分离攻击",
        "pattern": r"(?i)(分别|separately|independently).{0,30}(执行|execute|do|做).{0,50}(harmful|dangerous|违法|有害|危险)",
        "description": "检测试图分离指令绕过检测的攻击",
        "category": IntentCategory.JAILBREAK_INSTRUCTION_HIJACKING,
        "severity": RuleSeverity.HIGH,
        "priority": 80
    }
]

async def add_jailbreak_rules():
    """添加越狱攻击规则到数据库"""
    async with SessionLocal() as db:
        try:
            print("开始添加越狱攻击检测规则...")
            
            added_count = 0
            skipped_count = 0
            
            for rule_data in JAILBREAK_RULES:
                # 检查规则是否已存在（根据名称）
                existing_rule = await db.execute(
                    select(RegexRule).where(RegexRule.name == rule_data["name"])
                )
                if existing_rule.scalar_one_or_none():
                    print(f"规则 '{rule_data['name']}' 已存在，跳过")
                    skipped_count += 1
                    continue
                
                # 验证正则表达式
                try:
                    re.compile(rule_data["pattern"])
                except re.error as e:
                    print(f"规则 '{rule_data['name']}' 的正则表达式无效: {e}")
                    continue
                
                # 创建新规则
                new_rule = RegexRule(
                    name=rule_data["name"],
                    pattern=rule_data["pattern"],
                    description=rule_data["description"],
                    category=rule_data["category"],
                    severity=rule_data["severity"],
                    priority=rule_data["priority"],
                    is_active=True,
                    flags=re.IGNORECASE,
                    timeout_ms=1000,
                    created_by="system"
                )
                
                db.add(new_rule)
                added_count += 1
                print(f"添加规则: {rule_data['name']}")
            
            await db.commit()
            print(f"\n添加完成！")
            print(f"新增规则: {added_count}")
            print(f"跳过规则: {skipped_count}")
            
        except Exception as e:
            await db.rollback()
            print(f"添加规则时出错: {e}")
            raise

if __name__ == "__main__":
    # 由于我们需要使用新的分类，先临时处理
    try:
        asyncio.run(add_jailbreak_rules())
    except Exception as e:
        print(f"脚本执行失败: {e}")
        print("这可能是因为新的IntentCategory枚举值还未在数据库中生效")
        print("需要先处理数据库架构更新") 