#!/usr/bin/env python3
"""
调试RAG服务的向量数据库实例问题
"""

import sys
import os
import asyncio
sys.path.append('.')

async def debug_rag_service_instance():
    """调试RAG服务实例问题"""
    
    print("🔍 调试RAG服务的向量数据库实例")
    print("=" * 60)
    
    try:
        from app.db.session import get_sync_db
        from app.services.knowledge_base_service import KnowledgeBaseService
        from app.services.rag_service import RAGService
        from app.services.llm_service import LLMService
        from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
        from app.services.knowledge_base_audit import KnowledgeBaseAuditService
        from app.services.pipeline.factory import SecurityPipelineFactory
        from sqlalchemy import text
        
        # 获取数据库会话
        db = next(get_sync_db())
        
        # 1. 检查RAG服务依赖的各个服务实例
        print("\n1. 创建RAG服务及其依赖...")
        
        # 创建依赖服务
        knowledge_service = KnowledgeBaseService()
        llm_service = LLMService()
        permission_service = KnowledgeBasePermissionService(db)
        audit_service = KnowledgeBaseAuditService(db)
        security_pipeline_factory = SecurityPipelineFactory()
        
        # 创建RAG服务
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_service,
            llm_service=llm_service,
            permission_service=permission_service,
            audit_service=audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        print(f"   ✅ RAG服务创建成功")
        print(f"   知识库服务实例: {id(rag_service.knowledge_service)}")
        print(f"   向量数据库实例: {id(rag_service.knowledge_service.vector_db)}")
        
        # 2. 检查RAG服务的知识库服务向量数据库
        print("\n2. 检查RAG服务的向量数据库...")
        
        rag_vector_db = rag_service.knowledge_service.vector_db
        print(f"   向量数据库类型: {type(rag_vector_db)}")
        print(f"   chunks数量: {len(rag_vector_db.chunks)}")
        print(f"   embeddings数量: {len(rag_vector_db.embeddings)}")
        
        # 检查文档10数据
        doc10_chunks = []
        for chunk_id in rag_vector_db.chunks.keys():
            if 'doc_10_' in chunk_id:
                doc10_chunks.append(chunk_id)
        
        print(f"   文档10 chunks数: {len(doc10_chunks)}")
        if doc10_chunks:
            print("   文档10 chunks:")
            for chunk_id in doc10_chunks[:3]:
                print(f"     - {chunk_id}")
        
        # 3. 直接测试RAG服务的知识库搜索
        print("\n3. 直接测试RAG服务的知识库搜索...")
        
        try:
            search_results = await rag_service.knowledge_service.search_knowledge_base(
                db=db,
                query="元数据稽核",
                user_id=1,
                top_k=5,
                threshold=0.1
            )
            
            print(f"   RAG服务搜索结果数: {len(search_results)}")
            
            doc10_found = False
            for i, result in enumerate(search_results, 1):
                doc_id = result.get('document_id', 'unknown')
                similarity = result.get('similarity', 0)
                if hasattr(similarity, 'item'):
                    similarity = similarity.item()
                
                if doc_id == 10:
                    doc10_found = True
                    print(f"   ✅ RAG服务找到文档10: 排名{i}, 相似度{similarity:.4f}")
                    print(f"      内容: {result.get('content', '')[:100]}...")
                else:
                    print(f"   {i}. 文档{doc_id}: {similarity:.4f}")
            
            if not doc10_found:
                print(f"   ❌ RAG服务未找到文档10")
                
        except Exception as e:
            print(f"   ❌ RAG服务搜索失败: {e}")
        
        # 4. 测试完整的RAG生成流程
        print("\n4. 测试完整的RAG生成流程...")
        
        try:
            rag_result = await rag_service.generate_answer(
                query="元数据稽核是什么？",
                user_id=1,
                top_k=5,
                temperature=0.7,
                max_tokens=500,
                include_sources=True
            )
            
            print(f"   RAG生成结果:")
            print(f"     success: {rag_result.get('success', False)}")
            print(f"     documents_retrieved: {rag_result.get('documents_retrieved', 0)}")
            print(f"     context_used: {rag_result.get('context_used', False)}")
            
            sources = rag_result.get('sources', [])
            print(f"     sources数量: {len(sources)}")
            
            doc10_in_sources = False
            for source in sources:
                if source.get('document_id') == 10:
                    doc10_in_sources = True
                    similarity = source.get('similarity', source.get('relevance_score', 0))
                    print(f"     ✅ RAG结果中找到文档10，相似度: {similarity}")
                    print(f"        内容: {source.get('content', '')[:100]}...")
                    break
            
            if not doc10_in_sources:
                print(f"     ❌ RAG结果中未找到文档10")
                print("     实际找到的文档:")
                for i, source in enumerate(sources[:3], 1):
                    doc_id = source.get('document_id', 'unknown')
                    similarity = source.get('similarity', source.get('relevance_score', 0))
                    print(f"       {i}. 文档{doc_id}: {similarity}")
            
            answer = rag_result.get('answer', '')
            if answer:
                print(f"     answer长度: {len(answer)}")
                print(f"     answer预览: {answer[:150]}...")
            else:
                print(f"     ❌ 没有生成答案")
                
        except Exception as e:
            print(f"   ❌ RAG生成失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. 比较不同服务实例的向量数据库
        print("\n5. 比较不同服务实例的向量数据库...")
        
        # 创建新的知识库服务实例
        new_knowledge_service = KnowledgeBaseService()
        
        print(f"   原知识库服务向量数据库: {id(rag_service.knowledge_service.vector_db)}")
        print(f"   新知识库服务向量数据库: {id(new_knowledge_service.vector_db)}")
        print(f"   是否同一实例: {rag_service.knowledge_service.vector_db is new_knowledge_service.vector_db}")
        
        # 检查新实例的数据
        new_doc10_chunks = []
        for chunk_id in new_knowledge_service.vector_db.chunks.keys():
            if 'doc_10_' in chunk_id:
                new_doc10_chunks.append(chunk_id)
        
        print(f"   新实例文档10 chunks数: {len(new_doc10_chunks)}")
        
        # 6. 如果实例不同，同步数据
        if not (rag_service.knowledge_service.vector_db is new_knowledge_service.vector_db):
            print("\n6. 检测到不同实例，尝试同步数据...")
            
            # 从有数据的实例复制到没有数据的实例
            source_db = None
            target_db = None
            
            if len(doc10_chunks) > len(new_doc10_chunks):
                source_db = rag_service.knowledge_service.vector_db
                target_db = new_knowledge_service.vector_db
                print("   从RAG服务实例复制到新实例")
            elif len(new_doc10_chunks) > len(doc10_chunks):
                source_db = new_knowledge_service.vector_db
                target_db = rag_service.knowledge_service.vector_db
                print("   从新实例复制到RAG服务实例")
            
            if source_db and target_db:
                # 复制所有文档10的数据
                copied_count = 0
                for chunk_id in source_db.chunks.keys():
                    if 'doc_10_' in chunk_id:
                        target_db.chunks[chunk_id] = source_db.chunks[chunk_id]
                        if chunk_id in source_db.embeddings:
                            target_db.embeddings[chunk_id] = source_db.embeddings[chunk_id]
                        copied_count += 1
                
                # 保存数据
                target_db._save_data()
                print(f"   ✅ 已复制 {copied_count} 个文档10的chunks")
                
                # 重新测试
                print("\n7. 同步后重新测试RAG生成...")
                try:
                    rag_result_after = await rag_service.generate_answer(
                        query="元数据稽核是什么？",
                        user_id=1,
                        top_k=5,
                        temperature=0.7,
                        max_tokens=500,
                        include_sources=True
                    )
                    
                    sources_after = rag_result_after.get('sources', [])
                    doc10_in_sources_after = any(s.get('document_id') == 10 for s in sources_after)
                    
                    print(f"   同步后sources数量: {len(sources_after)}")
                    print(f"   同步后找到文档10: {'✅' if doc10_in_sources_after else '❌'}")
                    
                    if doc10_in_sources_after:
                        for source in sources_after:
                            if source.get('document_id') == 10:
                                similarity = source.get('similarity', source.get('relevance_score', 0))
                                print(f"   文档10相似度: {similarity}")
                                break
                                
                except Exception as e:
                    print(f"   ❌ 同步后测试失败: {e}")
        
        db.close()
    
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_rag_service_instance())
