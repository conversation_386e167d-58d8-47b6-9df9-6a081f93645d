#!/usr/bin/env python3
"""
调试RAG权限和安全管道问题
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.db.session import get_sync_db
from app.db.models.user import User as UserModel
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.pipeline.factory import SecurityPipelineFactory
from app.services.pipeline.context import PipelineContext

async def debug_rag_permissions():
    """调试RAG权限问题"""
    
    print("🔍 调试RAG权限和安全管道")
    print("=" * 60)
    
    # 获取数据库会话
    db = next(get_sync_db())
    
    try:
        # 1. 检查admin用户
        print("\n1. 检查admin用户...")
        user = db.query(UserModel).filter(UserModel.username == "admin").first()
        if not user:
            print("❌ admin用户不存在")
            return
        
        print(f"✅ 找到用户: {user.username} (ID: {user.id})")
        print(f"   角色ID: {user.role_id}")
        print(f"   等级ID: {user.level_id}")
        print(f"   激活状态: {user.is_active}")
        
        # 2. 检查知识库权限
        print("\n2. 检查知识库权限...")
        permission_service = KnowledgeBasePermissionService(db)
        
        has_access = permission_service.check_module_access(user)
        print(f"   模块访问权限: {has_access}")
        
        if not has_access:
            print("❌ 用户没有知识库访问权限")
            # 检查具体原因
            print(f"   用户激活状态: {user.is_active}")
            print(f"   用户角色: {user.role}")
            print(f"   用户等级: {user.level}")
        
        # 3. 测试安全管道
        print("\n3. 测试安全管道...")
        security_factory = SecurityPipelineFactory()
        security_pipeline = security_factory.create_default_pipeline()
        
        test_query = "本地模型路径是什么？"
        security_context = PipelineContext(
            content=test_query,
            user_id=user.id,
            message_type="text"
        )
        
        security_result = security_pipeline.execute(security_context, db)
        print(f"   查询: {test_query}")
        print(f"   安全检查结果: {'通过' if not security_result.is_blocked else '被阻止'}")
        
        if security_result.is_blocked:
            print(f"   阻止原因: {security_result.termination_reason}")
        
        # 4. 测试知识库搜索
        print("\n4. 测试知识库搜索...")
        from app.services.knowledge_base_service import KnowledgeBaseService
        
        knowledge_service = KnowledgeBaseService()
        search_results = await knowledge_service.search_knowledge_base(
            db=db,
            query=test_query,
            user_id=user.id,
            top_k=3,
            threshold=0.1
        )
        
        print(f"   搜索结果数量: {len(search_results)}")
        for i, result in enumerate(search_results[:2], 1):
            similarity = result.get('similarity', 0)
            if hasattr(similarity, 'item'):
                similarity = similarity.item()
            print(f"   {i}. 文档: {result.get('document_name', '未知')}")
            print(f"      相似度: {similarity:.4f}")
            print(f"      内容: {result.get('content', '')[:100]}...")
        
        # 5. 检查LLM模型
        print("\n5. 检查LLM模型...")
        from app.db.models.llm_model import LLMModel
        
        models = db.query(LLMModel).filter(LLMModel.is_active == True).all()
        print(f"   活跃模型数量: {len(models)}")
        
        for model in models[:3]:
            print(f"   - {model.name} (ID: {model.id})")
            print(f"     API地址: {model.api_url}")
            print(f"     模型名: {getattr(model, 'model_name', 'N/A')}")
        
        if not models:
            print("❌ 没有活跃的LLM模型")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_rag_permissions())
