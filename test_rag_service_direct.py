#!/usr/bin/env python3
"""
直接测试RAG服务
"""

import sys
import os
import asyncio
import json
sys.path.append('.')

from app.db.session import get_sync_db
from app.db.models.user import User as UserModel
from app.services.rag_service import RAGService
from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.llm_service import LLMService
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.knowledge_base_audit import KnowledgeBaseAuditService
from app.services.pipeline.factory import SecurityPipelineFactory

async def test_rag_service_direct():
    """直接测试RAG服务"""
    
    print("🤖 直接测试RAG服务")
    print("=" * 60)
    
    # 获取数据库会话
    db = next(get_sync_db())
    
    try:
        # 1. 获取admin用户
        print("\n1. 获取admin用户...")
        user = db.query(UserModel).filter(UserModel.username == "admin").first()
        if not user:
            print("❌ admin用户不存在")
            return
        
        print(f"✅ 找到用户: {user.username} (ID: {user.id})")
        
        # 2. 初始化服务
        print("\n2. 初始化服务...")
        knowledge_service = KnowledgeBaseService()
        llm_service = LLMService()
        permission_service = KnowledgeBasePermissionService(db=db)
        
        from app.core.audit import AuditLogger
        audit_logger = AuditLogger()
        audit_service = KnowledgeBaseAuditService()
        
        security_pipeline_factory = SecurityPipelineFactory
        
        # 创建RAG服务实例
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_service,
            llm_service=llm_service,
            permission_service=permission_service,
            audit_service=audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        print("✅ 服务初始化完成")
        
        # 3. 测试RAG查询
        print("\n3. 测试RAG查询...")
        test_query = "本地模型路径是什么？"
        print(f"   查询: {test_query}")
        
        try:
            result = await rag_service.generate_answer(
                query=test_query,
                user_id=user.id,
                model_id=12,  # 使用本地模型
                top_k=5,
                temperature=0.7,
                max_tokens=400,
                include_sources=True
            )
            
            print(f"✅ RAG查询完成")
            print(f"   成功: {result.get('success', False)}")
            print(f"   回答: {result.get('answer', 'None')}")
            print(f"   使用上下文: {result.get('context_used', False)}")
            print(f"   检索文档数: {result.get('documents_retrieved', 0)}")
            print(f"   模型ID: {result.get('model_id', None)}")
            print(f"   错误: {result.get('error', None)}")
            
            sources = result.get('sources', [])
            print(f"   参考文档数量: {len(sources)}")
            
            for i, source in enumerate(sources[:2], 1):
                similarity = source.get('similarity', 0)
                if hasattr(similarity, 'item'):
                    similarity = similarity.item()
                
                print(f"   {i}. 文档: {source.get('document_name', '未知')}")
                print(f"      相似度: {similarity:.1%}")
                print(f"      内容: {source.get('content', '')[:100]}...")
            
        except Exception as e:
            print(f"❌ RAG查询失败: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_rag_service_direct())
