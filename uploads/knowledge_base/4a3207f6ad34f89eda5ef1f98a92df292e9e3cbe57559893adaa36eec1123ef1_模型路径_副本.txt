不安全图片集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_NSFW
安全图片集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_SFW

不安全图片训练集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/NSFW_samples
安全图片训练集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/SFW_samples

原始模型路径
/root/pic_detect/content-moderation-system/data/model_cache/models--openai--clip-vit-base-patch32/snapshots/8d052a0f05efbaefbc9e8786ba291cfdf93e5593/

config.json
preprocessor_config.json
pytorch_model.bin

现在我需要对open-clip的预测准确性进行优化，我希望开发一个优化程序
1、程序能根据训练集图片的特征自动优化提示词
2、智能寻找最优提示词和阈值的组合，直到模型的F1分数达到99%以上
3、程序每次优化都将误识别的图片自动加入训练集对应的路径，覆盖原有图片
4、验证集图片路径如下
不安全图片集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_NSFW
安全图片集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_SFW
5、训练集图片（误识别图片）路径如下
不安全图片训练集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/NSFW_samples
安全图片训练集
/root/pic_detect/content-moderation-system/models/finetune/finetune_data/SFW_samples6、
6、原始模型本地缓存路径
/root/pic_detect/content-moderation-system/data/model_cache/models--openai--clip-vit-base-patch32/snapshots/8d052a0f05efbaefbc9e8786ba291cfdf93e5593/
7、优化程序独立执行不能修改原有模型以及相关程序
8、优化后的配置不能直接应用到生产环境，必须经过人工确认