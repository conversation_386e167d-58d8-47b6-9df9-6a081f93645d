#!/usr/bin/env python3
"""
创建样本数据分级数据
"""

import os
import sys

# 确保从正确的目录运行
sys.path.insert(0, os.path.abspath('.'))

# 导入必要的模块
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import sqlite3
from datetime import datetime

def create_sample_data():
    """创建样本数据分级"""
    
    # 样本数据分级
    sample_grades = [
        {
            "name": "用户敏感信息",
            "description": "包含用户个人隐私、身份证号、手机号等敏感信息",
            "sensitivity_level": 1
        },
        {
            "name": "用户基本信息", 
            "description": "包含用户姓名、邮箱等基本信息",
            "sensitivity_level": 2
        },
        {
            "name": "业务操作记录",
            "description": "用户的业务操作记录，包含一定敏感性",
            "sensitivity_level": 2
        },
        {
            "name": "系统日志信息",
            "description": "系统运行日志，包含部分敏感信息",
            "sensitivity_level": 3
        },
        {
            "name": "统计分析数据",
            "description": "经过脱敏处理的统计分析数据",
            "sensitivity_level": 3
        },
        {
            "name": "公开文档资料",
            "description": "完全公开的文档和资料",
            "sensitivity_level": 4
        },
        {
            "name": "产品介绍信息",
            "description": "产品的公开介绍和说明信息",
            "sensitivity_level": 4
        }
    ]
    
    try:
        # 连接SQLite数据库
        conn = sqlite3.connect('app/security_audit.db')
        cursor = conn.cursor()
        
        print("开始创建数据分级...")
        
        created_count = 0
        for grade_data in sample_grades:
            # 检查是否已存在同名分级
            cursor.execute(
                "SELECT id FROM data_classification_grades WHERE name = ?",
                (grade_data["name"],)
            )
            existing = cursor.fetchone()
            
            if existing:
                print(f"跳过已存在的分级: {grade_data['name']}")
                continue
            
            # 创建新的数据分级
            now = datetime.utcnow().isoformat()
            cursor.execute("""
                INSERT INTO data_classification_grades 
                (name, description, sensitivity_level, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                grade_data["name"],
                grade_data["description"],
                grade_data["sensitivity_level"],
                now,
                now
            ))
            
            created_count += 1
            print(f"创建数据分级: {grade_data['name']} (敏感级别: {grade_data['sensitivity_level']})")
        
        # 提交事务
        conn.commit()
        print(f"\n✅ 成功创建 {created_count} 个数据分级!")
        
        # 显示所有分级
        cursor.execute("SELECT id, name, sensitivity_level FROM data_classification_grades ORDER BY sensitivity_level")
        all_grades = cursor.fetchall()
        
        print(f"\n📊 当前共有 {len(all_grades)} 个数据分级:")
        for grade in all_grades:
            print(f"  ID {grade[0]}: {grade[1]} (级别 {grade[2]})")
            
    except Exception as e:
        print(f"❌ 创建数据分级失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    create_sample_data() 