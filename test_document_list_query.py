#!/usr/bin/env python3
"""
测试文档列表查询
"""

import requests
import json

def test_document_list_query():
    base_url = "http://localhost:8000/api/v1"
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = requests.post(
        f"{base_url}/login/access-token",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"响应: {login_response.text}")
        return
    
    login_result = login_response.json()
    token = login_result["access_token"]
    print(f"✅ 登录成功，用户: {login_result['user']['username']}")
    
    # 2. 测试文档列表查询
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    test_queries = [
        "知识库有什么文档",
        "文档列表",
        "包含哪些文档",
        "都有什么文档",
        "有哪些文档"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: {query}")
        
        rag_data = {
            "query": query,
            "top_k": 5,
            "temperature": 0.5,
            "max_tokens": 300
        }
        
        rag_response = requests.post(
            f"{base_url}/rag/query",
            json=rag_data,
            headers=headers
        )
        
        print(f"📊 响应状态: {rag_response.status_code}")
        
        if rag_response.status_code == 200:
            try:
                result = rag_response.json()
                print(f"✅ 成功: {result.get('success', False)}")
                print(f"📄 答案: {result.get('answer', 'N/A')[:200]}...")
                print(f"📊 检索文档数: {result.get('documents_retrieved', 0)}")
                print(f"❌ 错误: {result.get('error', 'N/A')}")
            except Exception as e:
                print(f"❌ 解析响应失败: {e}")
                print(f"原始响应: {rag_response.text}")
        else:
            print(f"❌ 请求失败: {rag_response.text}")

if __name__ == "__main__":
    test_document_list_query()
