#!/usr/bin/env python3
"""
直接测试API调用
"""

import requests
import json

def test_ai_generate_stream():
    """测试AI流式生成接口"""
    
    # 首先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 登录获取token...")
    login_response = requests.post(
        "http://localhost:8000/api/v1/login/access-token",
        data=login_data
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()["access_token"]
    print(f"✅ 登录成功，获取到token")
    
    # 创建新会话
    print("📋 创建新会话...")
    headers = {"Authorization": f"Bearer {token}"}
    create_session_response = requests.post(
        "http://localhost:8000/api/v1/sessions/",
        headers=headers,
        json={
            "title": "测试新会话",
            "llm_model_id": 5  # 使用qwen3:0.6b模型
        }
    )

    if create_session_response.status_code in [200, 201]:
        session_data = create_session_response.json()
        session_id = session_data["id"]
        print(f"✅ 创建新会话成功，会话ID: {session_id}")
    else:
        print(f"❌ 创建会话失败: {create_session_response.status_code}")
        print(f"错误信息: {create_session_response.text}")
        return
    
    # 测试AI流式生成
    print("🚀 测试AI流式生成...")
    test_data = {
        "session_id": session_id,
        "user_message": "详细介绍你自己",
        "stream": True
    }
    
    response = requests.post(
        "http://localhost:8000/api/v1/messages/ai-generate-stream",
        headers=headers,
        json=test_data,
        stream=True
    )
    
    print(f"📡 HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 请求成功，开始接收流式数据...")
        for line in response.iter_lines():
            if line:
                line_text = line.decode('utf-8')
                print(f"📦 收到数据: {line_text}")
                if line_text.startswith('data: '):
                    try:
                        data = json.loads(line_text[6:])
                        print(f"   解析后: {data}")
                    except:
                        pass
    else:
        print(f"❌ 请求失败: {response.text}")

if __name__ == "__main__":
    test_ai_generate_stream()
