#!/usr/bin/env python3
"""
直接创建功能权限表的脚本
"""

import sqlite3
import os

def create_feature_permissions_table():
    """创建功能权限表"""
    # 使用主数据库文件
    db_path = '/Users/<USER>/Desktop/project/ai-security-system-gemini/ai_security.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"✅ 使用数据库文件: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建角色功能权限表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS role_feature_permissions (
            role_id INTEGER NOT NULL,
            module_name VARCHAR(100) NOT NULL,
            feature_name VARCHAR(100) NOT NULL,
            can_access BOOLEAN NOT NULL DEFAULT 0,
            can_create BOOLEAN NOT NULL DEFAULT 0,
            can_read BO<PERSON><PERSON>N NOT NULL DEFAULT 0,
            can_update BO<PERSON>EAN NOT NULL DEFAULT 0,
            can_delete BOOLEAN NOT NULL DEFAULT 0,
            description TEXT,
            PRIMARY KEY (role_id, module_name, feature_name),
            FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
        );
        """
        
        cursor.execute(create_table_sql)
        print("✅ 成功创建 role_feature_permissions 表")
        
        # 获取所有现有角色
        cursor.execute("SELECT id FROM roles")
        role_ids = [row[0] for row in cursor.fetchall()]
        print(f"📋 找到 {len(role_ids)} 个角色")
        
        # 知识库管理的4个功能
        knowledge_base_features = [
            ('CHAT', '知识库问答功能 - 允许用户与知识库进行对话'),
            ('DOCUMENTS', '文档管理功能 - 管理知识库中的文档'),
            ('MODELS', '模型管理功能 - 配置知识库使用的AI模型'),
            ('USER_PERMISSIONS', '用户文档权限管理功能 - 管理用户对特定文档的访问权限')
        ]
        
        # 为每个角色创建默认的功能权限记录
        for role_id in role_ids:
            for feature_name, description in knowledge_base_features:
                # 检查是否已存在
                cursor.execute("""
                    SELECT COUNT(*) FROM role_feature_permissions 
                    WHERE role_id = ? AND module_name = 'KNOWLEDGE_BASE' AND feature_name = ?
                """, (role_id, feature_name))
                
                if cursor.fetchone()[0] == 0:
                    cursor.execute("""
                        INSERT INTO role_feature_permissions 
                        (role_id, module_name, feature_name, can_access, can_create, can_read, can_update, can_delete, description)
                        VALUES (?, 'KNOWLEDGE_BASE', ?, 0, 0, 0, 0, 0, ?)
                    """, (role_id, feature_name, description))
                    print(f"  ➕ 为角色 {role_id} 添加功能权限: {feature_name}")
        
        conn.commit()
        print("✅ 成功插入默认功能权限数据")
        
        # 验证创建结果
        cursor.execute("SELECT COUNT(*) FROM role_feature_permissions")
        count = cursor.fetchone()[0]
        print(f"📊 功能权限表中共有 {count} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建表时出错: {e}")
        return False

if __name__ == "__main__":
    success = create_feature_permissions_table()
    if success:
        print("\n🎉 功能权限表创建完成！")
        print("现在可以使用细粒度的知识库功能权限控制了。")
    else:
        print("\n💥 创建失败，请检查错误信息。")