-- AI 安全防护系统数据库 Schema
-- 版本: 1.0
-- 数据库类型: MySQL

-- 清理已存在的表 (可选, 开发初期方便重置)
-- SET FOREIGN_KEY_CHECKS = 0;
-- DROP TABLE IF EXISTS audit_logs, role_data_grade_access, role_desensitization_rule_assignments, role_malicious_intent_category_assignments, role_keyword_group_assignments, role_module_permissions, data_source_grades, data_source_categories, desensitization_rules, sensitive_info_types, malicious_intent_rules, malicious_intent_categories, keywords, keyword_groups, messages, sessions, llm_models, levels, roles, users;
-- SET FOREIGN_KEY_CHECKS = 1;

-- 1. 用户与权限管理
CREATE TABLE roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '角色信息表';

CREATE TABLE levels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '级别ID',
    role_id BIGINT UNSIGNED NOT NULL COMMENT '所属角色ID (外键)',
    name VARCHAR(100) NOT NULL COMMENT '级别名称 (例如: 主任医师, 医师)',
    rank_value INT NOT NULL COMMENT '级别排序值 (例如: 主任医师为1, 医师为2)',
    description TEXT COMMENT '级别描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_role_level_name (role_id, name) COMMENT '同一角色下级别名称唯一',
    UNIQUE KEY uk_role_level_rank (role_id, rank_value) COMMENT '同一角色下级别排序值唯一',
    CONSTRAINT fk_levels_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE COMMENT '级别所属角色外键'
) COMMENT '级别信息表';

CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    email VARCHAR(255) UNIQUE COMMENT '邮箱',
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID (外键)',
    level_id BIGINT UNSIGNED COMMENT '级别ID (外键, 可为空)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    CONSTRAINT fk_users_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE RESTRICT COMMENT '用户角色外键',
    CONSTRAINT fk_users_level FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE SET NULL COMMENT '用户级别外键'
) COMMENT '用户信息表';

-- 2. 会话管理
CREATE TABLE llm_models (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '模型ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '模型名称',
    api_url VARCHAR(512) NOT NULL COMMENT 'API接口地址',
    api_key_encrypted VARCHAR(512) COMMENT '加密后的API密钥',
    config_params JSON COMMENT '模型特定配置参数 (例如: temperature, max_tokens)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '大语言模型接口表';

CREATE TABLE sessions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID (外键)',
    llm_model_id BIGINT UNSIGNED NOT NULL COMMENT '使用的大模型ID (外键)',
    title VARCHAR(255) COMMENT '会话标题 (可选, 用户可设或自动生成)',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除 (软删除)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间 (用于排序)',
    INDEX idx_sessions_user_id (user_id),
    INDEX idx_sessions_last_activity (last_activity_at),
    CONSTRAINT fk_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE COMMENT '会话用户外键',
    CONSTRAINT fk_sessions_llm_model FOREIGN KEY (llm_model_id) REFERENCES llm_models(id) ON DELETE RESTRICT COMMENT '会话使用模型外键'
) COMMENT '会话信息表';

CREATE TABLE messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    session_id BIGINT UNSIGNED NOT NULL COMMENT '所属会话ID (外键)',
    sender_id BIGINT UNSIGNED COMMENT '发送者ID (如果发送者类型是AI或SYSTEM则为空, 外键)',
    sender_type ENUM('USER', 'AI', 'SYSTEM') NOT NULL COMMENT '发送者类型 (用户, AI, 系统)',
    content_type ENUM('TEXT', 'IMAGE_URL') DEFAULT 'TEXT' COMMENT '内容类型 (文本, 图片URL)',
    content TEXT NOT NULL COMMENT '消息内容 (若是图片URL, 则存储URL)',
    metadata JSON COMMENT '附加信息 (例如: 图片尺寸, AI处理时间)',
    is_blocked BOOLEAN DEFAULT FALSE COMMENT '是否被拦截',
    block_reason TEXT COMMENT '拦截原因 (如果消息被安全策略拦截)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_messages_session_id (session_id),
    INDEX idx_messages_created_at (created_at),
    CONSTRAINT fk_messages_session FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE COMMENT '消息所属会话外键',
    CONSTRAINT fk_messages_sender FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL COMMENT '消息发送者外键 (用户删除则设为NULL)'
) COMMENT '消息记录表';

-- 3. 安全策略管理
CREATE TABLE keyword_groups (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '关键词组ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '关键词组名称',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '关键词组表';

CREATE TABLE keywords (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '关键词ID',
    group_id BIGINT UNSIGNED NOT NULL COMMENT '所属关键词组ID (外键)',
    term VARCHAR(255) NOT NULL COMMENT '关键词条目',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_group_term (group_id, term) COMMENT '同一组内关键词唯一',
    CONSTRAINT fk_keywords_group FOREIGN KEY (group_id) REFERENCES keyword_groups(id) ON DELETE CASCADE COMMENT '关键词所属组外键'
) COMMENT '关键词表';

CREATE TABLE malicious_intent_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '恶意意图分类ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '恶意意图分类表';

CREATE TABLE malicious_intent_rules (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '恶意意图规则ID',
    category_id BIGINT UNSIGNED NOT NULL COMMENT '所属分类ID (外键)',
    name VARCHAR(255) NOT NULL COMMENT '规则名称',
    pattern TEXT NOT NULL COMMENT '正则表达式模式',
    description TEXT COMMENT '规则描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_mir_category_id (category_id),
    CONSTRAINT fk_mir_category FOREIGN KEY (category_id) REFERENCES malicious_intent_categories(id) ON DELETE CASCADE COMMENT '恶意意图规则所属分类外键'
) COMMENT '恶意意图规则表';

CREATE TABLE sensitive_info_types (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '敏感信息类型ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '类型名称',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '敏感信息类型表';

CREATE TABLE desensitization_rules (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '脱敏规则ID',
    sensitive_info_type_id BIGINT UNSIGNED NOT NULL COMMENT '关联的敏感信息类型ID (外键)',
    name VARCHAR(255) NOT NULL UNIQUE COMMENT '规则名称',
    level ENUM('高级', '中级', '初级', '自定义') NOT NULL COMMENT '脱敏级别',
    detection_pattern TEXT COMMENT '用于检测敏感信息的正则表达式',
    desensitize_config JSON COMMENT '脱敏配置 (例如: {"method": "mask_all", "mask_char": "*"})',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_dr_sensitive_info_type_id (sensitive_info_type_id),
    CONSTRAINT fk_dr_sensitive_info_type FOREIGN KEY (sensitive_info_type_id) REFERENCES sensitive_info_types(id) ON DELETE CASCADE COMMENT '脱敏规则关联敏感信息类型外键'
) COMMENT '脱敏规则表';

-- 4. 数据分级分类管理
CREATE TABLE data_source_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '数据源分类ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '数据源分类表';

CREATE TABLE data_source_grades (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '数据源分级ID',
    category_id BIGINT UNSIGNED NOT NULL COMMENT '所属分类ID (外键)',
    name ENUM('高级敏感', '中级敏感', '初级敏感', '完全开放') NOT NULL COMMENT '分级名称',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_dsg_category_name (category_id, name) COMMENT '同一分类下分级名称唯一',
    CONSTRAINT fk_dsg_category FOREIGN KEY (category_id) REFERENCES data_source_categories(id) ON DELETE CASCADE COMMENT '数据源分级所属分类外键'
) COMMENT '数据源分级表';

-- 5. 权限关联表
CREATE TABLE role_module_permissions (
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID (外键)',
    module_name VARCHAR(100) NOT NULL COMMENT '模块名称 (例如: USER_MANAGEMENT, SESSION_HISTORY)',
    can_access BOOLEAN DEFAULT FALSE COMMENT '是否可访问',
    PRIMARY KEY (role_id, module_name),
    CONSTRAINT fk_rmp_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE COMMENT '角色模块权限的角色外键'
) COMMENT '角色模块权限表';

CREATE TABLE role_keyword_group_assignments (
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID (外键)',
    keyword_group_id BIGINT UNSIGNED NOT NULL COMMENT '关键词组ID (外键)',
    PRIMARY KEY (role_id, keyword_group_id),
    CONSTRAINT fk_rkga_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE COMMENT '角色关键词组分配的角色外键',
    CONSTRAINT fk_rkga_keyword_group FOREIGN KEY (keyword_group_id) REFERENCES keyword_groups(id) ON DELETE CASCADE COMMENT '角色关键词组分配的关键词组外键'
) COMMENT '角色关键词组分配表';

CREATE TABLE role_malicious_intent_category_assignments (
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID (外键)',
    malicious_intent_category_id BIGINT UNSIGNED NOT NULL COMMENT '恶意意图分类ID (外键)',
    PRIMARY KEY (role_id, malicious_intent_category_id),
    CONSTRAINT fk_rmica_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE COMMENT '角色恶意意图分类分配的角色外键',
    CONSTRAINT fk_rmica_mic FOREIGN KEY (malicious_intent_category_id) REFERENCES malicious_intent_categories(id) ON DELETE CASCADE COMMENT '角色恶意意图分类分配的分类外键'
) COMMENT '角色恶意意图分类分配表';

CREATE TABLE role_desensitization_rule_assignments (
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID (外键)',
    desensitization_rule_id BIGINT UNSIGNED NOT NULL COMMENT '脱敏规则ID (外键)',
    PRIMARY KEY (role_id, desensitization_rule_id),
    CONSTRAINT fk_rdra_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE COMMENT '角色脱敏规则分配的角色外键',
    CONSTRAINT fk_rdra_dr FOREIGN KEY (desensitization_rule_id) REFERENCES desensitization_rules(id) ON DELETE CASCADE COMMENT '角色脱敏规则分配的规则外键'
) COMMENT '角色脱敏规则分配表';

CREATE TABLE role_data_grade_access (
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID (外键)',
    data_source_grade_id BIGINT UNSIGNED NOT NULL COMMENT '数据源分级ID (外键)',
    access_allowed BOOLEAN DEFAULT FALSE COMMENT '是否允许访问 (或特定访问类型如 READ_ONLY)',
    PRIMARY KEY (role_id, data_source_grade_id),
    CONSTRAINT fk_rdga_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE COMMENT '角色数据分级访问的角色外键',
    CONSTRAINT fk_rdga_dsg FOREIGN KEY (data_source_grade_id) REFERENCES data_source_grades(id) ON DELETE CASCADE COMMENT '角色数据分级访问的分级外键'
) COMMENT '角色数据分级访问表';

-- 6. 日志管理
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT UNSIGNED COMMENT '操作用户ID (系统操作可为空, 外键)',
    action_type VARCHAR(100) NOT NULL COMMENT '行为类型 (例如: LOGIN, CREATE_USER, SEND_MESSAGE_ATTEMPT)',
    target_entity VARCHAR(100) COMMENT '目标实体 (例如: users, sessions, keywords)',
    target_id BIGINT UNSIGNED COMMENT '目标实体ID',
    details TEXT COMMENT '行为详情 (JSON或文本)',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_audit_logs_user_id (user_id),
    INDEX idx_audit_logs_action_type (action_type),
    INDEX idx_audit_logs_created_at (created_at),
    CONSTRAINT fk_audit_logs_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL COMMENT '审计日志用户外键 (用户删除则设为NULL)'
) COMMENT '审计日志表';

-- 插入一些初始数据 (可选)
-- 例如, 创建一个默认管理员角色和一个普通用户角色
INSERT INTO roles (name, description) VALUES ('admin', '系统管理员');
INSERT INTO roles (name, description) VALUES ('user', '普通用户');

-- 可以在这里添加更多的初始数据，例如默认的恶意意图分类、敏感信息类型等。

-- 提示: 实际部署时，密码哈希应通过应用逻辑生成并存储。
-- INSERT INTO users (username, password_hash, email, role_id) VALUES ('admin', 'hashed_admin_password', '<EMAIL>', (SELECT id FROM roles WHERE name = 'admin'));
