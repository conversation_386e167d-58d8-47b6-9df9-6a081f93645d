#!/usr/bin/env python3
"""
修复向量数据库搜索问题
"""

import sys
import os
sys.path.append('.')

from app.services.vector_db_simple import SimpleVectorDBService
import re

def improved_text_similarity(query: str, content: str) -> float:
    """
    改进的文本相似度计算，支持中文字符级匹配
    """
    # 将中文按字符分割，英文按词分割
    def tokenize_text(text):
        # 移除标点符号和特殊字符
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        tokens = set()
        
        # 分割中文字符和英文单词
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 中文字符
                tokens.add(char)
            elif char.isalnum():
                tokens.add(char)
        
        # 同时也按空格分割获取完整词汇
        words = text.split()
        for word in words:
            if word.strip():
                tokens.add(word.strip())
        
        return tokens
    
    query_tokens = tokenize_text(query)
    content_tokens = tokenize_text(content)
    
    if not query_tokens or not content_tokens:
        return 0.0
    
    # 计算交集和并集
    intersection = query_tokens.intersection(content_tokens)
    union = query_tokens.union(content_tokens)
    
    # Jaccard相似度
    jaccard_sim = len(intersection) / len(union) if union else 0.0
    
    # 额外加权：如果查询词作为子字符串出现在内容中
    substring_bonus = 0.0
    if query.lower() in content.lower():
        substring_bonus = 0.3
    
    # 组合相似度
    final_similarity = min(1.0, jaccard_sim + substring_bonus)
    
    return final_similarity

def test_and_fix_search():
    """测试并修复搜索功能"""
    
    print("=== 修复向量数据库搜索问题 ===\n")
    
    # 创建向量数据库实例
    vector_db = SimpleVectorDBService()
    
    # 替换相似度计算方法
    vector_db._compute_text_similarity = improved_text_similarity
    
    print("1. 测试改进后的相似度算法...")
    
    # 测试数据
    query = "本地模型路径"
    test_content = "本地模型路径配置：\n\n不安全图片集路径：\n/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_NSFW"
    
    similarity = improved_text_similarity(query, test_content)
    print(f"查询: {query}")
    print(f"内容: {test_content[:50]}...")
    print(f"改进后的相似度: {similarity:.3f}")
    
    print("\n2. 测试搜索功能...")
    
    # 测试搜索
    search_results = vector_db.search(query, n_results=10)
    print(f"搜索'{query}'的结果数量: {len(search_results)}")
    
    for i, result in enumerate(search_results):
        print(f"结果{i+1}: 文档{result['document_id']}, 相似度: {result['relevance_score']:.3f}")
        print(f"  内容: {result['content'][:100]}...")
        print()
    
    # 查找文档2的结果
    doc2_results = [r for r in search_results if r['document_id'] == 2]
    print(f"文档2的搜索结果数量: {len(doc2_results)}")
    
    if doc2_results:
        print("✅ 成功找到文档2的相关内容！")
        for i, result in enumerate(doc2_results):
            print(f"文档2结果{i+1}: 相似度 {result['relevance_score']:.3f}")
            print(f"  内容: {result['content'][:150]}...")
    else:
        print("❌ 仍然没有找到文档2的内容")
    
    return len(doc2_results) > 0

if __name__ == "__main__":
    success = test_and_fix_search()
    if success:
        print("\n🎉 搜索问题已修复！")
    else:
        print("\n⚠️ 搜索问题仍需进一步调试")