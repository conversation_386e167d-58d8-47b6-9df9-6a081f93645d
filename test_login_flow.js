// 测试前端登录流程
const axios = require('axios');

// 创建axios实例，模拟前端配置
const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

async function testLogin() {
  console.log('开始测试登录流程...\n');
  
  try {
    // 1. 测试健康检查
    console.log('1. 测试后端健康检查...');
    const healthResponse = await axios.get('http://localhost:8000/health');
    console.log('✅ 后端健康检查成功:', healthResponse.status);
    
    // 2. 测试登录API
    console.log('\n2. 测试登录API...');
    const params = new URLSearchParams();
    params.append('username', 'admin');
    params.append('password', 'admin123');
    
    const loginResponse = await axios.post('http://localhost:8000/api/v1/login/access-token', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    console.log('✅ 登录API成功:', loginResponse.status);
    console.log('📄 响应数据:', JSON.stringify(loginResponse.data, null, 2));
    
    // 3. 测试带token的API调用
    console.log('\n3. 测试带token的API调用...');
    const token = loginResponse.data.access_token;
    
    const userResponse = await axios.get('http://localhost:8000/api/v1/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    
    console.log('✅ 用户信息获取成功:', userResponse.status);
    console.log('👤 用户信息:', JSON.stringify(userResponse.data, null, 2));
    
    // 4. 测试前端代理
    console.log('\n4. 测试前端代理...');
    try {
      const proxyResponse = await axios.post('http://localhost:5173/api/v1/login/access-token', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      console.log('✅ 前端代理测试成功:', proxyResponse.status);
    } catch (proxyError) {
      console.log('❌ 前端代理测试失败:', proxyError.message);
      if (proxyError.response) {
        console.log('   状态码:', proxyError.response.status);
        console.log('   错误信息:', proxyError.response.data);
      }
    }
    
    console.log('\n🎉 登录流程测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误信息:', error.response.data);
    }
    console.error('   完整错误:', error);
  }
}

// 运行测试
testLogin();
