#!/usr/bin/env python3
"""
修复版后端启动脚本
解决Python模块路径问题
"""
import sys
import os
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent.absolute()
print(f"项目根目录: {project_root}")

# 将项目根目录添加到Python路径
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
    print(f"已添加到Python路径: {project_root}")

# 设置环境变量
os.environ['PYTHONPATH'] = str(project_root)

try:
    # 现在导入应该可以工作了
    from app.main import app
    import uvicorn
    
    print("正在启动AI安全系统后端服务...")
    print("服务地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    
    # 启动服务
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8000, 
        log_level="info",
        reload=False  # 生产环境建议设为False
    )
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"启动失败: {e}")
    sys.exit(1) 