#!/usr/bin/env python3
"""
测试会话类型过滤修复
"""

import requests
import sqlite3

def test_session_filter():
    """测试会话类型过滤"""
    print("=== 测试会话类型过滤修复 ===\n")
    
    # 1. 首先检查数据库中的实际数据
    print("1. 数据库中的会话类型分布:")
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT session_type, COUNT(*) as count
        FROM sessions 
        WHERE is_deleted = 0
        GROUP BY session_type
        ORDER BY count DESC
    """)
    
    session_types = cursor.fetchall()
    total_sessions = sum(count for _, count in session_types)
    
    for session_type, count in session_types:
        session_type_display = session_type if session_type else "NULL"
        print(f"   {session_type_display}: {count} 个会话")
    print(f"   总计: {total_sessions} 个会话\n")
    
    # 2. 检查最近的会话示例
    print("2. 最近的会话示例:")
    cursor.execute("""
        SELECT id, title, session_type, created_at
        FROM sessions 
        WHERE is_deleted = 0
        ORDER BY created_at DESC
        LIMIT 10
    """)
    
    recent_sessions = cursor.fetchall()
    for session_id, title, session_type, created_at in recent_sessions:
        session_type_display = session_type if session_type else "NULL"
        print(f"   ID:{session_id} | {title[:30]}... | 类型:{session_type_display} | {created_at}")
    
    conn.close()
    print()
    
    # 3. 测试API过滤效果
    print("3. 测试API过滤效果:")
    
    # 登录获取token
    login_data = {
        "username": "admin", 
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(
            "http://localhost:8000/api/v1/login/access-token",
            data=login_data
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print(f"   ✅ 登录成功")
        else:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            return
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # 测试修复前的行为（应该只返回chat类型的会话）
        print("\n   测试普通会话管理API (应该只返回chat类型):")
        sessions_response = requests.get(
            "http://localhost:8000/api/v1/sessions/",
            headers=headers
        )
        
        if sessions_response.status_code == 200:
            sessions_data = sessions_response.json()
            sessions_list = sessions_data if isinstance(sessions_data, list) else sessions_data.get('items', [])
            
            print(f"   API返回会话数量: {len(sessions_list)}")
            
            # 检查返回的会话类型
            chat_count = 0
            rag_count = 0
            null_count = 0
            
            print("   返回的会话类型分布:")
            for session in sessions_list[:10]:  # 只显示前10个
                session_type = session.get('session_type')
                title = session.get('title', '无标题')
                session_id = session.get('id')
                
                if session_type == 'chat':
                    chat_count += 1
                elif session_type == 'rag':
                    rag_count += 1
                else:
                    null_count += 1
                
                session_type_display = session_type if session_type else "NULL"
                print(f"     ID:{session_id} | {title[:25]}... | 类型:{session_type_display}")
            
            print(f"\n   会话类型统计:")
            print(f"     chat类型: {chat_count} 个")
            print(f"     rag类型: {rag_count} 个 {'✅ 正确过滤' if rag_count == 0 else '❌ 过滤失败'}")
            print(f"     NULL类型: {null_count} 个")
            
            if rag_count == 0:
                print("   ✅ 修复成功：RAG会话已被正确过滤")
            else:
                print("   ❌ 修复失败：仍有RAG会话显示在普通会话管理中")
                
        else:
            print(f"   ❌ 获取会话列表失败: {sessions_response.status_code}")
        
        # 测试RAG会话API（应该只返回rag类型的会话）
        print("\n   测试RAG会话管理API (应该只返回rag类型):")
        rag_sessions_response = requests.get(
            "http://localhost:8000/api/v1/rag-sessions/",
            headers=headers
        )
        
        if rag_sessions_response.status_code == 200:
            rag_sessions_data = rag_sessions_response.json()
            rag_sessions_list = rag_sessions_data if isinstance(rag_sessions_data, list) else rag_sessions_data.get('items', [])
            
            print(f"   RAG API返回会话数量: {len(rag_sessions_list)}")
            
            # 检查是否都是rag类型
            rag_sessions_correct = all(s.get('session_type') == 'rag' for s in rag_sessions_list)
            
            if rag_sessions_list and rag_sessions_correct:
                print("   ✅ RAG会话API正确返回rag类型会话")
            elif not rag_sessions_list:
                print("   ⚠️  RAG会话API没有返回任何会话")
            else:
                print("   ❌ RAG会话API返回了非rag类型的会话")
                
        else:
            print(f"   ❌ 获取RAG会话列表失败: {rag_sessions_response.status_code}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

def test_expected_behavior():
    """说明期望的行为"""
    print("\n=== 期望的行为 ===")
    print("修复后的预期效果:")
    print("1. 普通会话管理页面 (/sessions/):")
    print("   - 只显示 session_type='chat' 的会话")
    print("   - 包含旧数据 (session_type=NULL)")
    print("   - 不显示 session_type='rag' 的会话")
    print()
    print("2. 知识库问答会话管理 (/rag-sessions/):")
    print("   - 只显示 session_type='rag' 的会话")
    print("   - 用于管理知识库问答的对话记录")
    print()
    print("3. 用户体验:")
    print("   - 普通AI聊天和知识库问答分开管理")
    print("   - 避免混淆不同类型的对话")
    print("   - 更清晰的会话组织结构")

if __name__ == "__main__":
    print("🔧 开始测试会话类型过滤修复\n")
    
    test_session_filter()
    test_expected_behavior()
    
    print("\n🎉 测试完成！如果修复成功，普通会话管理将不再显示知识库问答记录")