#!/usr/bin/env python3
"""
最终RAG功能测试
"""

import sys
import os
import asyncio
import requests
import json
sys.path.append('.')

async def test_final_rag():
    """测试完整的RAG问答功能"""
    
    print("🧪 测试完整的RAG问答功能")
    print("=" * 60)
    
    # 测试查询
    queries = [
        "本地模型路径是什么？",
        "NSFW图片训练集在哪里？",
        "clip-vit模型的路径是什么？",
        "有哪些训练数据路径？"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n🔍 测试查询 {i}: {query}")
        print("-" * 40)
        
        try:
            # 调用RAG API
            response = requests.post(
                "http://localhost:8000/api/v1/rag/query",
                json={
                    "query": query,
                    "model_id": 1  # 使用默认模型
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ 状态: 成功")
                print(f"📝 回答: {result.get('answer', '无回答')[:200]}...")
                print(f"🔗 使用上下文: {result.get('context_used', False)}")
                
                sources = result.get('sources', [])
                print(f"📚 参考文档数量: {len(sources)}")
                
                for j, source in enumerate(sources[:3], 1):  # 只显示前3个
                    similarity = source.get('similarity', 0)
                    if hasattr(similarity, 'item'):
                        similarity = similarity.item()
                    
                    print(f"  {j}. 文档: {source.get('document_name', '未知')}")
                    print(f"     相似度: {similarity:.1%}")
                    print(f"     内容: {source.get('content', '')[:100]}...")
                    
            elif response.status_code == 401:
                print("❌ 认证失败 - 需要登录")
                break
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("- 如果看到'使用上下文: True'和参考文档，说明RAG功能正常")
    print("- 如果看到模型路径相关内容，说明问题已解决")
    print("- 前端地址: http://localhost:5174/")
    print("- 可以在'知识库问答'页面进行交互测试")

if __name__ == "__main__":
    asyncio.run(test_final_rag())
