#!/usr/bin/env python3
"""
配置本地LLM模型脚本
用于将系统切换到本地Ollama模型
"""

import sqlite3
import json
import subprocess
import sys

def check_ollama():
    """检查Ollama是否安装和运行"""
    try:
        result = subprocess.run(['curl', '-s', 'http://localhost:11434/api/tags'], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            models = json.loads(result.stdout)
            return True, models.get('models', [])
    except:
        pass
    return False, []

def add_local_model(model_name="qwen:0.5b"):
    """添加本地模型到数据库"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 检查是否已存在本地模型配置
    cursor.execute("""
        SELECT id FROM llm_models 
        WHERE api_url = 'http://localhost:11434/api/generate'
    """)
    existing = cursor.fetchone()
    
    if existing:
        print(f"✅ 更新现有本地模型配置 (ID: {existing[0]})")
        # 更新配置
        config_params = json.dumps({
            "api_format": "ollama",
            "model": model_name,
            "temperature": 0.7,
            "top_p": 0.9
        })
        cursor.execute("""
            UPDATE llm_models 
            SET name = ?, config_params = ?, is_active = 1
            WHERE id = ?
        """, (f"本地 {model_name}", config_params, existing[0]))
    else:
        print("✅ 创建新的本地模型配置")
        # 插入新配置
        config_params = json.dumps({
            "api_format": "ollama",
            "model": model_name,
            "temperature": 0.7,
            "top_p": 0.9
        })
        cursor.execute("""
            INSERT INTO llm_models (name, api_url, config_params, is_active)
            VALUES (?, ?, ?, 1)
        """, (f"本地 {model_name}", "http://localhost:11434/api/generate", config_params))
    
    # 停用远程模型
    cursor.execute("""
        UPDATE llm_models 
        SET is_active = 0
        WHERE api_url LIKE '%*************%'
    """)
    print("✅ 已停用远程模型")
    
    conn.commit()
    conn.close()
    print("✅ 本地模型配置完成！")

def main():
    print("🔍 检查Ollama状态...")
    is_running, models = check_ollama()
    
    if not is_running:
        print("❌ Ollama未运行！")
        print("\n请先:")
        print("1. 安装Ollama: brew install ollama")
        print("2. 启动服务: ollama serve")
        print("3. 下载模型: ollama pull qwen:0.5b")
        sys.exit(1)
    
    print("✅ Ollama正在运行")
    if models:
        print("\n📋 可用的本地模型:")
        for m in models:
            print(f"  - {m['name']} ({m['size'] / 1024 / 1024:.1f} MB)")
        
        # 选择第一个可用模型
        model_name = models[0]['name']
        print(f"\n🚀 使用模型: {model_name}")
    else:
        print("⚠️  没有找到本地模型，将使用默认配置")
        model_name = "qwen:0.5b"
    
    add_local_model(model_name)
    print("\n✨ 配置完成！现在可以使用本地模型进行快速响应了。")

if __name__ == "__main__":
    main() 
"""
配置本地LLM模型脚本
用于将系统切换到本地Ollama模型
"""

import sqlite3
import json
import subprocess
import sys

def check_ollama():
    """检查Ollama是否安装和运行"""
    try:
        result = subprocess.run(['curl', '-s', 'http://localhost:11434/api/tags'], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            models = json.loads(result.stdout)
            return True, models.get('models', [])
    except:
        pass
    return False, []

def add_local_model(model_name="qwen:0.5b"):
    """添加本地模型到数据库"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 检查是否已存在本地模型配置
    cursor.execute("""
        SELECT id FROM llm_models 
        WHERE api_url = 'http://localhost:11434/api/generate'
    """)
    existing = cursor.fetchone()
    
    if existing:
        print(f"✅ 更新现有本地模型配置 (ID: {existing[0]})")
        # 更新配置
        config_params = json.dumps({
            "api_format": "ollama",
            "model": model_name,
            "temperature": 0.7,
            "top_p": 0.9
        })
        cursor.execute("""
            UPDATE llm_models 
            SET name = ?, config_params = ?, is_active = 1
            WHERE id = ?
        """, (f"本地 {model_name}", config_params, existing[0]))
    else:
        print("✅ 创建新的本地模型配置")
        # 插入新配置
        config_params = json.dumps({
            "api_format": "ollama",
            "model": model_name,
            "temperature": 0.7,
            "top_p": 0.9
        })
        cursor.execute("""
            INSERT INTO llm_models (name, api_url, config_params, is_active)
            VALUES (?, ?, ?, 1)
        """, (f"本地 {model_name}", "http://localhost:11434/api/generate", config_params))
    
    # 停用远程模型
    cursor.execute("""
        UPDATE llm_models 
        SET is_active = 0
        WHERE api_url LIKE '%*************%'
    """)
    print("✅ 已停用远程模型")
    
    conn.commit()
    conn.close()
    print("✅ 本地模型配置完成！")

def main():
    print("🔍 检查Ollama状态...")
    is_running, models = check_ollama()
    
    if not is_running:
        print("❌ Ollama未运行！")
        print("\n请先:")
        print("1. 安装Ollama: brew install ollama")
        print("2. 启动服务: ollama serve")
        print("3. 下载模型: ollama pull qwen:0.5b")
        sys.exit(1)
    
    print("✅ Ollama正在运行")
    if models:
        print("\n📋 可用的本地模型:")
        for m in models:
            print(f"  - {m['name']} ({m['size'] / 1024 / 1024:.1f} MB)")
        
        # 选择第一个可用模型
        model_name = models[0]['name']
        print(f"\n🚀 使用模型: {model_name}")
    else:
        print("⚠️  没有找到本地模型，将使用默认配置")
        model_name = "qwen:0.5b"
    
    add_local_model(model_name)
    print("\n✨ 配置完成！现在可以使用本地模型进行快速响应了。")

if __name__ == "__main__":
    main() 