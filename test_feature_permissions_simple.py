#!/usr/bin/env python3
"""
简单的功能权限测试脚本
"""

import sqlite3
import os

def test_feature_permissions_database():
    """测试功能权限数据库"""
    db_path = '/Users/<USER>/Desktop/project/ai-security-system-gemini/ai_security.db'
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查功能权限表
        cursor.execute("SELECT COUNT(*) FROM role_feature_permissions")
        count = cursor.fetchone()[0]
        print(f"✅ 功能权限表中有 {count} 条记录")
        
        # 查看一些示例数据
        cursor.execute("""
            SELECT role_id, module_name, feature_name, can_access 
            FROM role_feature_permissions 
            LIMIT 5
        """)
        records = cursor.fetchall()
        
        print("📋 示例功能权限记录:")
        for record in records:
            role_id, module_name, feature_name, can_access = record
            print(f"  角色{role_id}: {module_name}.{feature_name} = {bool(can_access)}")
        
        # 测试为管理员角色启用一些权限
        print("\n🔧 为管理员角色启用功能权限...")
        admin_role_id = 1  # 管理员角色
        
        features_to_enable = ['CHAT', 'DOCUMENTS', 'MODELS', 'USER_PERMISSIONS']
        for feature in features_to_enable:
            cursor.execute("""
                UPDATE role_feature_permissions 
                SET can_access = 1, can_create = 1, can_read = 1, can_update = 1, can_delete = 1
                WHERE role_id = ? AND module_name = 'KNOWLEDGE_BASE' AND feature_name = ?
            """, (admin_role_id, feature))
            print(f"  ✅ 启用 {feature} 功能权限")
        
        conn.commit()
        
        # 验证更新
        cursor.execute("""
            SELECT feature_name, can_access, can_create, can_read, can_update, can_delete
            FROM role_feature_permissions 
            WHERE role_id = ? AND module_name = 'KNOWLEDGE_BASE'
        """, (admin_role_id,))
        
        updated_records = cursor.fetchall()
        print(f"\n📊 管理员角色的功能权限:")
        for record in updated_records:
            feature_name, can_access, can_create, can_read, can_update, can_delete = record
            print(f"  {feature_name}: 访问={bool(can_access)}, 创建={bool(can_create)}, 读取={bool(can_read)}, 更新={bool(can_update)}, 删除={bool(can_delete)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False

def test_service_import():
    """测试服务导入"""
    try:
        from app.services.knowledge_base_feature_permissions import KnowledgeBaseFeaturePermissionService
        print("✅ 功能权限服务导入成功")
        
        # 测试功能定义
        features = KnowledgeBaseFeaturePermissionService.get_all_features()
        print(f"📋 知识库功能定义 ({len(features)} 个):")
        for feature_name, feature_info in features.items():
            print(f"  - {feature_name}: {feature_info['name']}")
        
        return True
    except Exception as e:
        print(f"❌ 服务导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始简单功能权限测试...")
    
    print("\n1. 测试数据库功能权限...")
    db_success = test_feature_permissions_database()
    
    print("\n2. 测试服务导入...")
    service_success = test_service_import()
    
    if db_success and service_success:
        print("\n🎉 功能权限系统基础测试通过！")
        print("数据库表已创建，权限记录已初始化，服务可以正常导入。")
    else:
        print("\n❌ 功能权限系统测试失败")