#!/usr/bin/env python3
"""
调试文档内容问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.knowledge_base_service import KnowledgeBaseService
from app.db.session import SyncSessionLocal
from app.db.models.knowledge_document import KnowledgeDocument
from app.db.models.knowledge_chunk import KnowledgeChunk

async def debug_document_content():
    """调试文档内容问题"""
    print("🔍 调试文档内容问题...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 1. 检查数据库中的文档
        print("📊 检查数据库中的文档...")
        documents = db.query(KnowledgeDocument).all()
        
        print(f"总文档数: {len(documents)}")
        
        for doc in documents:
            print(f"\n📄 文档ID: {doc.id}")
            print(f"   标题: {doc.title}")
            print(f"   状态: {doc.status}")
            print(f"   类型: {doc.document_type}")
            print(f"   文件路径: {doc.file_path}")
            print(f"   创建时间: {doc.created_at}")
            
            # 检查文档的分块
            chunks = db.query(KnowledgeChunk).filter(
                KnowledgeChunk.document_id == doc.id
            ).all()
            
            print(f"   分块数量: {len(chunks)}")
            
            if chunks:
                for i, chunk in enumerate(chunks[:3]):  # 只显示前3个分块
                    print(f"   分块 {i+1}:")
                    print(f"     ID: {chunk.id}")
                    print(f"     索引: {chunk.chunk_index}")
                    print(f"     内容长度: {len(chunk.content) if chunk.content else 0}")
                    print(f"     内容预览: {chunk.content[:100] if chunk.content else 'None'}...")
                    print(f"     向量ID: {chunk.vector_id}")
            else:
                print("   ❌ 没有找到分块")
        
        # 2. 测试知识库搜索
        print(f"\n🔍 测试知识库搜索...")
        knowledge_service = KnowledgeBaseService()
        
        test_query = "产品清单"
        search_results = await knowledge_service.search_knowledge_base(
            db=db,
            query=test_query,
            user_id=1,
            top_k=5,
            threshold=0.1
        )
        
        print(f"搜索查询: {test_query}")
        print(f"搜索结果数量: {len(search_results)}")
        
        for i, result in enumerate(search_results):
            print(f"\n搜索结果 {i+1}:")
            print(f"  文档ID: {result.get('document_id', 'N/A')}")
            print(f"  文档名: {result.get('document_name', 'N/A')}")
            print(f"  相似度: {result.get('similarity', 0.0):.3f}")
            print(f"  内容长度: {len(result.get('content', ''))}")
            print(f"  内容: {result.get('content', 'N/A')[:200]}...")
        
        # 3. 检查向量数据库
        print(f"\n🔍 检查向量数据库...")
        try:
            from app.services.vector_db_service import VectorDBService
            vector_service = VectorDBService()
            
            # 获取所有向量
            all_vectors = vector_service.collection.get()
            print(f"向量数据库中的记录数: {len(all_vectors['ids']) if all_vectors['ids'] else 0}")
            
            if all_vectors['ids']:
                print("前5个向量记录:")
                for i in range(min(5, len(all_vectors['ids']))):
                    vector_id = all_vectors['ids'][i]
                    metadata = all_vectors['metadatas'][i] if all_vectors['metadatas'] else {}
                    document = all_vectors['documents'][i] if all_vectors['documents'] else ""
                    
                    print(f"  {i+1}. ID: {vector_id}")
                    print(f"     元数据: {metadata}")
                    print(f"     文档内容: {document[:100]}...")
            
            # 测试向量搜索
            vector_results = vector_service.search(test_query, n_results=3)
            print(f"\n向量搜索结果数量: {len(vector_results)}")
            
            for i, result in enumerate(vector_results):
                print(f"  {i+1}. ID: {result.get('id', 'N/A')}")
                print(f"     距离: {result.get('distance', 'N/A')}")
                print(f"     内容: {result.get('content', 'N/A')[:100]}...")
                
        except Exception as e:
            print(f"❌ 向量数据库检查失败: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_document_content())
