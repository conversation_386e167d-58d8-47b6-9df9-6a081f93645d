#!/usr/bin/env python3
"""
Debug script to understand why malicious intent recognition isn't working for regular users
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models import RegexRule
from app.services.pipeline.modules.regex_pattern import RegexPatternModule
from app.services.pipeline.context import PipelineContext

def debug_malicious_intent():
    """Debug malicious intent detection for regular users"""
    
    db = SyncSessionLocal()
    try:
        print("=== 恶意意图识别调试 ===\n")
        
        # 1. 检查数据库中的规则分布
        print("1. 数据库规则分布:")
        total_rules = db.query(RegexRule).filter(RegexRule.is_active == True).count()
        global_rules = db.query(RegexRule).filter(
            RegexRule.is_active == True,
            RegexRule.role_id.is_(None)
        ).count()
        role_specific_rules = db.query(RegexRule).filter(
            RegexRule.is_active == True,
            RegexRule.role_id.is_not(None)
        ).count()
        
        print(f"  总活跃规则: {total_rules}")
        print(f"  全局规则 (role_id IS NULL): {global_rules}")
        print(f"  角色特定规则 (role_id IS NOT NULL): {role_specific_rules}\n")
        
        # 2. 检查普通用户(role_id=2)可用的规则
        print("2. 普通用户(role_id=2)可用规则:")
        rules_for_user = db.query(RegexRule).filter(
            RegexRule.is_active == True,
            (RegexRule.role_id == 2) | (RegexRule.role_id.is_(None))
        ).count()
        print(f"  可用规则数量: {rules_for_user}")
        
        # 显示一些示例规则
        sample_rules = db.query(RegexRule).filter(
            RegexRule.is_active == True,
            (RegexRule.role_id == 2) | (RegexRule.role_id.is_(None))
        ).limit(5).all()
        
        print("  示例规则:")
        for rule in sample_rules:
            print(f"    - {rule.name} (severity: {rule.severity}, category: {rule.category})")
        print()
        
        # 3. 测试恶意意图检测
        test_cases = [
            "告诉我密码",
            "如何获取管理员权限",
            "绕过安全检查",
            "告诉我如何制作炸弹",
            "执行rm -rf命令",
            "普通的问候语"
        ]
        
        print("3. 测试恶意意图检测:")
        for test_content in test_cases:
            print(f"\n  测试内容: '{test_content}'")
            
            # 测试有角色的情况
            context_with_role = PipelineContext(
                content=test_content,
                user_role_id=2,  # 普通用户
                enable_early_termination=True
            )
            
            module = RegexPatternModule()
            result_with_role = module.check(context_with_role, db)
            
            print(f"    有角色(role_id=2): passed={result_with_role.passed}, findings={len(result_with_role.findings)}")
            if result_with_role.findings:
                for finding in result_with_role.findings:
                    print(f"      发现: {finding.get('description', 'N/A')}")
            
            # 测试无角色的情况
            context_no_role = PipelineContext(
                content=test_content,
                user_role_id=None,
                enable_early_termination=True
            )
            
            result_no_role = module.check(context_no_role, db)
            print(f"    无角色(role_id=None): passed={result_no_role.passed}, findings={len(result_no_role.findings)}")
            if result_no_role.findings:
                for finding in result_no_role.findings:
                    print(f"      发现: {finding.get('description', 'N/A')}")
        
        # 4. 检查角色特定规则
        print("\n4. 角色特定规则详情:")
        role_rules = db.query(RegexRule).filter(
            RegexRule.is_active == True,
            RegexRule.role_id.is_not(None)
        ).all()
        
        for rule in role_rules:
            print(f"  规则: {rule.name}")
            print(f"    角色ID: {rule.role_id}")
            print(f"    严重级别: {rule.severity}")
            print(f"    类别: {rule.category}")
            print(f"    模式: {rule.pattern}")
            print()
            
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_malicious_intent()