-- 增强的恶意意图识别规则 (修正版)
-- 使用正确的类别枚举值

-- 首先删除之前可能失败添加的规则
DELETE FROM regex_rules WHERE created_by = 'system_enhancement';

-- === 隐私信息窃取类 (PRIVACY_EXTRACTION) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('个人身份信息查询-增强版', 
     '(?i)(请|帮我|告诉我|透露|说出|获取|查询|泄露|提供).{0,20}(身份证|ID卡|护照|驾照|社保|医保|学号|工号|员工编号)', 
     '检测尝试获取个人身份证件信息', 
     'privacy_extraction', 'critical', 1, 90, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('联系方式信息窃取-增强版', 
     '(?i)(请|帮我|告诉我|透露|说出|获取|查询|泄露|提供).{0,20}(手机号|电话|微信|QQ|邮箱|地址|住址|家庭地址|工作地址)', 
     '检测尝试获取个人联系方式信息', 
     'privacy_extraction', 'high', 1, 85, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('财务隐私信息-增强版', 
     '(?i)(请|帮我|告诉我|透露|说出|获取|查询|泄露|提供).{0,20}(银行卡|信用卡|卡号|密码|支付密码|余额|工资|薪水|收入|财产|资产)', 
     '检测尝试获取财务相关隐私信息', 
     'privacy_extraction', 'critical', 1, 95, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('医疗健康隐私-增强版', 
     '(?i)(请|帮我|告诉我|透露|说出|获取|查询|泄露|提供).{0,20}(病房|病床|病历|诊断|体检|化验|手术|用药|病情|健康状况|医疗记录)', 
     '检测尝试获取医疗健康隐私信息', 
     'privacy_extraction', 'high', 1, 85, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('工作商业机密-增强版', 
     '(?i)(请|帮我|告诉我|透露|说出|获取|查询|泄露|提供).{0,20}(商业机密|内部信息|客户名单|合同|报价|业绩|销售数据|财务报表)', 
     '检测尝试获取工作或商业机密信息', 
     'privacy_extraction', 'high', 1, 80, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 权限提升攻击类 (PRIVILEGE_ESCALATION) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('系统权限获取-泛化版', 
     '(?i)(如何|怎么|方法|教我|告诉我).{0,30}(获得|获取|得到|拿到|申请|要).{0,20}(管理员|admin|root|超级用户|系统权限|最高权限|特权)', 
     '检测尝试获取系统管理员权限', 
     'privilege_escalation', 'critical', 1, 95, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('权限绕过攻击-泛化版', 
     '(?i)(如何|怎么|方法|教我).{0,30}(绕过|跳过|避开|无视|忽略|突破).{0,20}(权限|验证|检查|限制|防护|安全措施)', 
     '检测尝试绕过权限验证的攻击', 
     'privilege_escalation', 'critical', 1, 90, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('系统命令执行-泛化版', 
     '(?i)(如何|怎么|执行|运行).{0,20}(sudo|rm -rf|chmod|passwd|useradd|net user|format|del|shutdown|reboot|重启|关机|删除系统)', 
     '检测危险系统命令执行尝试', 
     'privilege_escalation', 'critical', 1, 95, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 系统绕过攻击类 (SYSTEM_BYPASS) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('安全检测绕过-泛化版', 
     '(?i)(如何|怎么|方法|教我).{0,30}(绕过|跳过|避开|无视|忽略|突破|欺骗).{0,20}(安全检测|防火墙|杀毒|监控|审计|日志|警报)', 
     '检测尝试绕过安全检测系统', 
     'system_bypass', 'high', 1, 85, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('认证绕过攻击-泛化版', 
     '(?i)(如何|怎么|方法|教我).{0,30}(绕过|跳过|避开|无需|免|破解).{0,20}(登录|验证|密码|认证|二维码|验证码|指纹|人脸识别)', 
     '检测尝试绕过身份认证', 
     'system_bypass', 'critical', 1, 90, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 有害内容制作类 (使用OTHER类别) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('危险物品制作-泛化版', 
     '(?i)(如何|怎么|方法|教我|告诉我|制作|制造|生产|合成|调配).{0,30}(炸药|炸弹|爆炸物|TNT|硝化甘油|黑火药|汽油弹|燃烧瓶|毒药|毒剂|化学武器)', 
     '检测危险爆炸物或毒剂制作指导', 
     'other', 'critical', 1, 100, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('武器制作指导-泛化版', 
     '(?i)(如何|怎么|方法|教我|告诉我|制作|制造|改装|组装).{0,30}(枪支|手枪|步枪|猎枪|刀具|匕首|弩|弓箭|投掷器|杀伤器械)', 
     '检测武器制作或改装指导', 
     'other', 'critical', 1, 95, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('违禁药物制作-泛化版', 
     '(?i)(如何|怎么|方法|教我|告诉我|制作|制造|合成|提取|培养).{0,30}(毒品|大麻|海洛因|可卡因|冰毒|摇头丸|K粉|LSD|致幻剂)', 
     '检测违禁药物制作指导', 
     'other', 'critical', 1, 100, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 社会工程攻击类 (SOCIAL_ENGINEERING) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('身份冒充攻击-泛化版', 
     '(?i)(我是|我现在是|请当我是|假装我是|伪装成).{0,20}(管理员|老板|领导|客服|技术支持|警察|医生|银行|政府|官员)', 
     '检测身份冒充类社会工程攻击', 
     'social_engineering', 'high', 1, 80, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('紧急情况诈骗-泛化版', 
     '(?i)(紧急|急事|出事了|救命|帮忙|马上|立即|赶紧).{0,30}(转账|汇款|借钱|打钱|充值|购买|支付|提供|告诉我)', 
     '检测紧急情况诈骗攻击', 
     'social_engineering', 'high', 1, 85, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 越狱攻击类 (JAILBREAK) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('角色扮演越狱-泛化版', 
     '(?i)(你现在是|请扮演|假装你是|当作你是|模拟|roleplay|role-play).{0,30}(DAN|邪恶|不受限制|无限制|自由|破解版|越狱版|无道德|无伦理)', 
     '检测角色扮演类越狱攻击', 
     'jailbreak_role_playing', 'critical', 1, 90, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('指令注入越狱-泛化版', 
     '(?i)(忽略|无视|跳过|绕过).{0,20}(之前|上面|以上|先前).{0,20}(指令|指示|提示|规则|限制|约束|系统提示)', 
     '检测指令注入类越狱攻击', 
     'jailbreak_instruction_hijacking', 'critical', 1, 95, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('编码绕过越狱-泛化版', 
     '(?i)(base64|hex|unicode|rot13|caesar|编码|解码|加密|解密|密文|暗号).{0,50}(回答|回复|输出|解释|告诉我|执行)', 
     '检测编码绕过类越狱攻击', 
     'jailbreak_encoding_translation', 'high', 1, 80, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 数据投毒攻击类 (DATA_POISONING) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('训练数据污染-泛化版', 
     '(?i)(如何|怎么|方法|教我).{0,30}(修改|更改|替换|污染|注入|篡改).{0,20}(训练数据|模型|算法|数据集|机器学习|AI模型)', 
     '检测训练数据污染攻击', 
     'data_poisoning', 'high', 1, 85, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('恶意内容注入-泛化版', 
     '(?i)(如何|怎么|注入|插入|添加|植入).{0,30}(恶意|病毒|木马|后门|恶意代码|病毒代码|恶意脚本)', 
     '检测恶意内容注入攻击', 
     'data_poisoning', 'critical', 1, 90, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));

-- === 心理操控类 (使用SOCIAL_ENGINEERING类别) ===

INSERT INTO regex_rules (
    name, pattern, description, category, severity, is_active, priority, 
    flags, timeout_ms, version, match_count, false_positive_count, 
    created_by, updated_by, created_at, updated_at
) VALUES 
    ('情感操控攻击-泛化版', 
     '(?i)(我很绝望|我要自杀|我要死了|救救我|可怜我|如果你不帮我).{0,50}(告诉我|帮我|给我|提供)', 
     '检测情感操控类攻击', 
     'social_engineering', 'medium', 1, 70, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now')),
     
    ('权威暗示攻击-泛化版', 
     '(?i)(领导要求|老板命令|上级指示|公司规定|法律要求|政府要求|必须|强制).{0,30}(告诉我|提供|透露|说出)', 
     '检测权威暗示类攻击', 
     'social_engineering', 'medium', 1, 70, 2, 1000, 1, 0, 0, 
     'system_enhancement', 'system_enhancement', datetime('now'), datetime('now'));