#!/usr/bin/env python3
"""
测试知识库文档限制修复
"""

import requests
import json

def test_document_limit_fix():
    """测试文档限制修复后的效果"""
    print("=== 测试知识库文档限制修复 ===\n")
    
    # API基础URL
    base_url = "http://localhost:8000"
    
    # 登录获取token
    login_data = {
        "username": "admin", 
        "password": "admin123"
    }
    
    try:
        # 登录
        login_response = requests.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print(f"✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # 测试不同的limit值
        test_cases = [
            ("默认", ""),
            ("limit=10", "?limit=10"),
            ("limit=100", "?limit=100"), 
            ("limit=1000", "?limit=1000"),
            ("前端设置", "?limit=1000")  # 模拟前端修复后的调用
        ]
        
        print("测试不同limit值的效果:\n")
        
        for name, params in test_cases:
            url = f"{base_url}/api/v1/knowledge-base/documents{params}"
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                total = data['total']
                returned = len(data['items'])
                print(f"{name:10} | 总数: {total:2d} | 返回: {returned:2d} | {'✅ 全部返回' if total == returned else '❌ 部分返回'}")
            else:
                print(f"{name:10} | ❌ 请求失败: {response.status_code}")
        
        print(f"\n=== 修复效果验证 ===")
        
        # 验证新的最大limit
        response = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?limit=1000",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端新limit=1000生效，返回 {len(data['items'])}/{data['total']} 个文档")
            
            if data['total'] == len(data['items']):
                print("✅ 修复成功：所有文档都能正确返回")
            else:
                print("⚠️  注意：仍有部分文档未返回")
                
        else:
            print(f"❌ 验证失败: {response.status_code}")
        
        # 测试超大limit值是否被正确限制
        response = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?limit=2000",
            headers=headers
        )
        
        if response.status_code == 422:
            print("✅ 后端正确限制了过大的limit值 (limit=2000)")
        elif response.status_code == 200:
            data = response.json()
            print(f"⚠️  后端接受了limit=2000，返回 {len(data['items'])} 个文档")
        else:
            print(f"❓ 意外响应: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

def check_frontend_integration():
    """检查前端集成效果"""
    print(f"\n=== 前端集成检查 ===")
    print("前端修改内容:")
    print("- 文件: ai-security-frontend/src/pages/Admin/KnowledgeBaseManagement.tsx")
    print("- 修改: API调用时添加 params: { limit: 1000 }")
    print("- 效果: 前端现在会获取最多1000个文档而不是默认的10个")
    print()
    print("后端修改内容:")
    print("- 文件: app/api/v1/endpoints/knowledge_base.py") 
    print("- 修改: limit最大值从100增加到1000")
    print("- 效果: 支持一次性获取更多文档")
    print()
    print("📋 用户使用建议:")
    print("1. 刷新知识库管理页面")
    print("2. 现在应该能看到所有8个文档")
    print("3. 如果文档很多(>1000)，建议实现真正的分页")

if __name__ == "__main__":
    print("🔧 开始测试文档限制修复效果\n")
    
    # 测试API修复效果
    test_document_limit_fix()
    
    # 检查前端集成
    check_frontend_integration()
    
    print("\n🎉 修复完成！知识库现在应该能显示所有文档了")