#!/usr/bin/env python3
"""
测试功能权限API的脚本
"""

import requests
import json

def test_feature_permissions():
    """测试功能权限API"""
    base_url = "http://localhost:8000/api/v1"
    
    # 测试登录获取token
    print("🔐 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(
            f"{base_url}/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get("access_token")
            print(f"✅ 登录成功，获取到token")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 测试获取角色功能权限
            print("\n📋 测试获取角色功能权限...")
            role_id = 1  # 管理员角色
            module_name = "KNOWLEDGE_BASE"
            
            perm_response = requests.get(
                f"{base_url}/admin/feature-permissions/roles/{role_id}/feature-permissions/{module_name}",
                headers=headers
            )
            
            if perm_response.status_code == 200:
                permissions = perm_response.json()
                print(f"✅ 成功获取角色 {role_id} 的功能权限")
                print(f"角色名称: {permissions['role_name']}")
                print(f"模块名称: {permissions['module_name']}")
                print("功能权限:")
                for feature in permissions['feature_permissions']:
                    print(f"  - {feature['feature_name']}: 访问={feature['can_access']}")
            else:
                print(f"❌ 获取功能权限失败: {perm_response.status_code}")
                print(perm_response.text)
            
            # 测试更新功能权限
            print("\n🔧 测试更新功能权限...")
            update_data = [
                {
                    "feature_name": "CHAT",
                    "can_access": True,
                    "can_create": True,
                    "can_read": True,
                    "can_update": False,
                    "can_delete": False
                },
                {
                    "feature_name": "DOCUMENTS",
                    "can_access": True,
                    "can_create": True,
                    "can_read": True,
                    "can_update": True,
                    "can_delete": True
                }
            ]
            
            update_response = requests.put(
                f"{base_url}/admin/feature-permissions/roles/{role_id}/feature-permissions/{module_name}",
                headers=headers,
                json=update_data
            )
            
            if update_response.status_code == 200:
                print("✅ 功能权限更新成功")
            else:
                print(f"❌ 更新功能权限失败: {update_response.status_code}")
                print(update_response.text)
            
            # 测试获取可用功能列表
            print("\n📝 测试获取可用功能列表...")
            features_response = requests.get(
                f"{base_url}/admin/feature-permissions/available-features/{module_name}",
                headers=headers
            )
            
            if features_response.status_code == 200:
                features = features_response.json()
                print(f"✅ 成功获取 {module_name} 模块的可用功能")
                for feature in features['features']:
                    print(f"  - {feature['feature_name']}: {feature['name']}")
            else:
                print(f"❌ 获取可用功能失败: {features_response.status_code}")
                print(features_response.text)
            
            # 测试获取当前用户可访问功能
            print("\n👤 测试获取当前用户可访问功能...")
            accessible_response = requests.get(
                f"{base_url}/admin/feature-permissions/current-user/accessible-features/{module_name}",
                headers=headers
            )
            
            if accessible_response.status_code == 200:
                accessible = accessible_response.json()
                print(f"✅ 成功获取当前用户可访问功能")
                print(f"用户ID: {accessible['user_id']}")
                print("可访问功能:")
                for feature in accessible['accessible_features']:
                    print(f"  - {feature['feature_name']}: {feature['name']}")
                    print(f"    路由: {feature['route']}")
            else:
                print(f"❌ 获取可访问功能失败: {accessible_response.status_code}")
                print(accessible_response.text)
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(login_response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    print("🧪 开始测试功能权限API...")
    test_feature_permissions()
    print("\n✨ 测试完成")