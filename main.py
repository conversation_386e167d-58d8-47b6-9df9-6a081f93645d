# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.api import api_router as api_v1_router
from app.core.config import settings
# from app.db.session import engine # Uncomment if you need to interact with engine directly here
# from app.db.base_class import Base # Uncomment for creating tables directly (not recommended if using Alembic)

# Optional: Function to create database tables. 
# This is generally handled by Alembic migrations in a production setup.
# def create_db_and_tables():
#     Base.metadata.create_all(bind=engine)

# Call this on startup if you're not using Alembic and want to create tables:
# @app.on_event("startup")
# def on_startup():
#     create_db_and_tables()

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json", # Standard OpenAPI doc path
    description="AI Security System API"
)

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS, # Use the list directly
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_v1_router, prefix=settings.API_V1_STR)

# You can add other global dependencies, event handlers, or middleware here
# For example, a root path for health check:
@app.get("/", tags=["Health Check"])
async def read_root():
    return {"message": f"Welcome to {settings.PROJECT_NAME} v{settings.PROJECT_VERSION}"}


if __name__ == "__main__":
    import uvicorn
    # This is for development purposes. 
    # For production, use a process manager like Gunicorn with Uvicorn workers.
    # Example: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
