#!/usr/bin/env python3
"""
测试RAG服务构建的prompt
"""

import sys
import os
import asyncio
import json
import aiohttp
sys.path.append('.')

from app.db.session import get_sync_db
from app.db.models.user import User as UserModel
from app.db.models.llm_model import LLMModel
from app.services.knowledge_base_service import KnowledgeBaseService

async def test_rag_prompt():
    """测试RAG服务构建的prompt"""
    
    print("📝 测试RAG服务构建的prompt")
    print("=" * 60)
    
    # 获取数据库会话
    db = next(get_sync_db())
    
    try:
        # 1. 获取知识库搜索结果
        print("\n1. 获取知识库搜索结果...")
        knowledge_service = KnowledgeBaseService()
        
        query = "本地模型路径是什么？"
        search_results = await knowledge_service.search_knowledge_base(
            db=db,
            query=query,
            user_id=1,
            top_k=3,
            threshold=0.1
        )
        
        print(f"   搜索结果数量: {len(search_results)}")
        
        # 2. 构建上下文
        print("\n2. 构建上下文...")
        context_chunks = []
        for result in search_results:
            chunk_content = result.get("content", "")
            context_chunks.append(chunk_content)
        
        MAX_CONTEXT_LENGTH = 800
        context_text = "\n\n".join(context_chunks)
        if len(context_text) > MAX_CONTEXT_LENGTH:
            context_text = context_text[:MAX_CONTEXT_LENGTH] + "..."
        
        print(f"   上下文长度: {len(context_text)}")
        print(f"   上下文内容: {context_text[:200]}...")
        
        # 3. 构建增强提示
        print("\n3. 构建增强提示...")
        enhanced_prompt = f"""请基于以下上下文信息回答问题。如果上下文中没有相关信息，请明确说明。

上下文信息：
{context_text}

问题：{query}

请提供准确、简洁的回答："""
        
        print(f"   增强提示长度: {len(enhanced_prompt)}")
        print(f"   增强提示: {enhanced_prompt[:300]}...")
        
        # 4. 构建消息
        print("\n4. 构建消息...")
        messages = [
            {"role": "system", "content": "你是一个基于知识库的问答助手。请根据提供的上下文准确回答问题。"},
            {"role": "user", "content": enhanced_prompt}
        ]
        
        # 5. 转换为Ollama格式
        print("\n5. 转换为Ollama格式...")
        def messages_to_prompt(messages):
            prompt_parts = []
            for msg in messages:
                role = msg.get("role", "")
                content = msg.get("content", "")
                
                if role == "system":
                    prompt_parts.append(f"System: {content}")
                elif role == "user":
                    prompt_parts.append(f"User: {content}")
                elif role == "assistant":
                    prompt_parts.append(f"Assistant: {content}")
            
            prompt_parts.append("Assistant:")
            return "\n\n".join(prompt_parts)
        
        ollama_prompt = messages_to_prompt(messages)
        print(f"   Ollama提示长度: {len(ollama_prompt)}")
        print(f"   Ollama提示: {ollama_prompt[:400]}...")
        
        # 6. 直接测试Ollama API
        print("\n6. 直接测试Ollama API...")
        request_body = {
            "model": "qwen:1.8b",
            "prompt": ollama_prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "num_predict": 400
            }
        }
        
        print(f"   请求体大小: {len(json.dumps(request_body))} 字节")
        
        async with aiohttp.ClientSession() as session:
            try:
                print("   发送请求到Ollama...")
                async with session.post(
                    "http://localhost:11434/api/generate",
                    json=request_body,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    print(f"   响应状态: {response.status}")
                    
                    if response.status == 200:
                        response_json = await response.json()
                        answer = response_json.get("response", "")
                        print(f"✅ 成功获取回答")
                        print(f"   回答: {answer}")
                    else:
                        error_text = await response.text()
                        print(f"❌ API错误: {error_text}")
                        
            except asyncio.TimeoutError:
                print("❌ 请求超时")
            except Exception as e:
                print(f"❌ 请求失败: {e}")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_rag_prompt())
