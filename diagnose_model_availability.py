#!/usr/bin/env python3
"""
诊断模型可用性问题的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def diagnose_model_availability():
    """诊断为什么模型管理中有3个激活模型，但会话管理中只有2个可选"""
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 获取当前用户信息
    print("\n👤 获取当前用户信息...")
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if response.status_code == 200:
        user = response.json()
        print(f"✅ 当前用户: {user['username']} (ID: {user['id']}, 角色ID: {user['role_id']})")
    else:
        print(f"❌ 获取用户信息失败: {response.status_code}")
        return
    
    # 3. 检查模型管理中的所有激活模型
    print("\n🤖 检查模型管理中的激活模型...")
    try:
        response = requests.get(f"{BASE_URL}/admin/llm-models/", headers=headers)
        if response.status_code == 200:
            all_models = response.json()
            active_models = [m for m in all_models if m.get('is_active', False)]
            print(f"✅ 模型管理中共有 {len(active_models)} 个激活模型:")
            for i, model in enumerate(active_models, 1):
                print(f"  {i}. {model['name']} (ID: {model['id']})")
                print(f"     API URL: {model['api_url']}")
                print(f"     有API密钥: {model.get('has_api_key', 'Unknown')}")
                print(f"     配置参数: {model.get('config_params', {})}")
                print()
        else:
            print(f"❌ 获取模型管理列表失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取模型管理列表异常: {e}")
        return
    
    # 4. 检查用户可用的模型（会话管理使用的API）
    print("📋 检查用户可用的模型（会话管理使用）...")
    try:
        response = requests.get(f"{BASE_URL}/llm-models/my-available", headers=headers)
        if response.status_code == 200:
            available_models = response.json()
            print(f"✅ 用户可用模型共有 {len(available_models)} 个:")
            for i, model in enumerate(available_models, 1):
                print(f"  {i}. {model['name']} (ID: {model['id']})")
                print(f"     API URL: {model['api_url']}")
                print(f"     有API密钥: {model.get('has_api_key', 'Unknown')}")
                print(f"     配置参数: {model.get('config_params', {})}")
                print()
        else:
            print(f"❌ 获取用户可用模型失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 获取用户可用模型异常: {e}")
    
    # 5. 检查用户模型分配情况
    print("🔗 检查用户模型分配情况...")
    try:
        response = requests.get(f"{BASE_URL}/admin/model-assignments/users/{user['id']}/models", headers=headers)
        if response.status_code == 200:
            assignments = response.json()
            print(f"✅ 用户模型分配情况:")
            if assignments:
                for assignment in assignments:
                    status = "✓ 已分配" if assignment.get('is_assigned', False) else "✗ 未分配"
                    default = " (默认)" if assignment.get('is_default', False) else ""
                    print(f"  - {assignment['name']} (ID: {assignment['id']}): {status}{default}")
            else:
                print("  ❌ 用户没有任何模型分配")
        else:
            print(f"❌ 获取用户模型分配失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 获取用户模型分配异常: {e}")
    
    # 6. 分析问题原因
    print("\n🔍 问题分析:")
    print("=" * 50)
    
    if len(active_models) > len(available_models):
        print("❌ 问题确认: 模型管理中的激活模型数量 > 用户可用模型数量")
        print("\n可能的原因:")
        print("1. 🔑 用户模型分配问题:")
        print("   - 用户可能没有被分配所有激活的模型")
        print("   - 会话管理使用 /llm-models/my-available 端点")
        print("   - 该端点只返回用户被明确分配的模型")
        print()
        print("2. 🔧 API密钥问题:")
        print("   - 某些模型可能缺少API密钥")
        print("   - 没有API密钥的模型可能被过滤掉")
        print()
        print("3. 📊 权限控制:")
        print("   - 系统启用了严格的模型分配权限控制")
        print("   - 管理员需要为用户明确分配模型权限")
        
        print("\n💡 解决方案:")
        print("1. 为用户分配所有需要的模型:")
        print("   - 进入 系统管理 → 模型分配管理")
        print("   - 为用户分配缺失的模型")
        print()
        print("2. 检查模型API密钥配置:")
        print("   - 确保所有激活模型都有有效的API密钥")
        print()
        print("3. 如果希望所有激活模型对所有用户可用:")
        print("   - 可以修改 /llm-models/my-available 端点逻辑")
        print("   - 或者批量为所有用户分配所有激活模型")
    else:
        print("✅ 模型数量一致，可能是其他原因")

if __name__ == "__main__":
    diagnose_model_availability()
