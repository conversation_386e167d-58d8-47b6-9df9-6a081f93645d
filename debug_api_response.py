#!/usr/bin/env python3
"""
调试API响应详情
"""

import requests
import json

def debug_api_response():
    """调试API响应详情"""
    
    print("🔍 调试API响应详情")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_response = requests.post(
        f"{base_url}/api/v1/login/access-token",
        data={
            "username": "admin",
            "password": "admin123"
        }
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 发送RAG查询
    print("\n2. 发送RAG查询...")
    query_data = {
        "query": "本地模型路径是什么？",
        "model_id": 12,
        "top_k": 5,
        "temperature": 0.7,
        "max_tokens": 400,
        "include_sources": True
    }
    
    print(f"   请求数据: {json.dumps(query_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/rag/query",
            headers=headers,
            json=query_data,
            timeout=60
        )
        
        print(f"   响应状态: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                # 详细分析响应
                print(f"\n3. 响应分析:")
                print(f"   success: {result.get('success')}")
                print(f"   answer: {result.get('answer')}")
                print(f"   context_used: {result.get('context_used')}")
                print(f"   documents_retrieved: {result.get('documents_retrieved')}")
                print(f"   model_id: {result.get('model_id')}")
                print(f"   error: {result.get('error')}")
                
                sources = result.get('sources', [])
                print(f"   sources数量: {len(sources)}")
                
                if sources:
                    print(f"   第一个source: {json.dumps(sources[0], ensure_ascii=False, indent=4)}")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
        else:
            print(f"❌ API调用失败")
            print(f"   错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    debug_api_response()
