#!/usr/bin/env python3
"""
添加qwen:1.8b模型到数据库
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

def add_qwen_1_8b_model():
    """添加qwen:1.8b模型"""
    print("🔧 添加qwen:1.8b模型到数据库...")
    
    db = SyncSessionLocal()
    
    try:
        # 检查是否已存在
        existing_model = db.query(LLMModel).filter(
            LLMModel.name == "本地 qwen:1.8b (增强版)"
        ).first()
        
        if existing_model:
            print("✅ qwen:1.8b模型已存在")
            print(f"   模型ID: {existing_model.id}")
            print(f"   是否激活: {existing_model.is_active}")
            
            # 激活这个模型，停用其他模型
            db.query(LLMModel).update({"is_active": False})
            existing_model.is_active = True
            db.commit()
            print("✅ 已激活qwen:1.8b模型")
            return
        
        # 停用所有现有模型
        db.query(LLMModel).update({"is_active": False})
        
        # 创建新模型
        new_model = LLMModel(
            name="本地 qwen:1.8b (增强版)",
            api_url="http://localhost:11434/api/generate",
            api_key_encrypted=None,  # Ollama不需要API key
            config_params={
                "model": "qwen:1.8b",
                "api_format": "ollama",
                "temperature": 0.7,
                "top_p": 0.9,
                "max_tokens": 500
            },
            is_active=True
        )
        
        db.add(new_model)
        db.commit()
        
        print("✅ 成功添加qwen:1.8b模型")
        print(f"   模型ID: {new_model.id}")
        print(f"   模型名称: {new_model.name}")
        print(f"   API URL: {new_model.api_url}")
        print(f"   配置: {new_model.config_params}")
        
    except Exception as e:
        print(f"❌ 添加模型失败: {str(e)}")
        db.rollback()
        
    finally:
        db.close()

if __name__ == "__main__":
    add_qwen_1_8b_model()
