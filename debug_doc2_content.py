#!/usr/bin/env python3
"""
调试文档2的内容和向量化状态
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

try:
    from app.db.database import get_db
    from app.db.models.document import Document
    from app.db.models.document_chunk import DocumentChunk
    from app.services.vector_db_simple import SimpleVectorDBService
    from app.services.document_vectorizer import DocumentVectorizer
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

async def debug_doc2():
    """调试文档2的内容"""
    
    print("🔍 调试文档2内容和向量化状态")
    print("=" * 60)
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 1. 检查文档2的基本信息
        print("\n1. 检查文档2基本信息...")
        doc2 = db.query(Document).filter(Document.id == 2).first()
        
        if doc2:
            print(f"   ✅ 文档2存在:")
            print(f"     ID: {doc2.id}")
            print(f"     文件名: {doc2.filename}")
            print(f"     标题: {doc2.title}")
            print(f"     向量化状态: {doc2.is_vectorized}")
            print(f"     创建时间: {doc2.created_at}")
        else:
            print("   ❌ 文档2不存在")
            return
        
        # 2. 检查文档2的chunks
        print("\n2. 检查文档2的chunks...")
        chunks = db.query(DocumentChunk).filter(
            DocumentChunk.document_id == 2
        ).order_by(DocumentChunk.chunk_index).all()
        
        print(f"   找到 {len(chunks)} 个chunks:")
        for chunk in chunks:
            print(f"     Chunk {chunk.chunk_index}:")
            print(f"       ID: {chunk.id}")
            print(f"       内容长度: {len(chunk.content)}")
            print(f"       内容预览: {chunk.content[:100]}...")
            print()
        
        # 3. 检查向量数据库中的文档2数据
        print("\n3. 检查向量数据库中的文档2数据...")
        vector_db = SimpleVectorDBService()
        
        # 检查chunks
        doc2_chunks = {k: v for k, v in vector_db.chunks.items() if k.startswith('doc_2_')}
        print(f"   向量数据库中doc_2的chunks数量: {len(doc2_chunks)}")
        
        for chunk_id, chunk_data in doc2_chunks.items():
            print(f"     {chunk_id}:")
            print(f"       内容长度: {len(chunk_data.get('content', ''))}")
            print(f"       内容预览: {chunk_data.get('content', '')[:100]}...")
            print()
        
        # 检查embeddings
        doc2_embeddings = {k: v for k, v in vector_db.embeddings.items() if k.startswith('doc_2_')}
        print(f"   向量数据库中doc_2的embeddings数量: {len(doc2_embeddings)}")
        
        # 4. 测试向量搜索
        print("\n4. 测试向量搜索...")
        vectorizer = DocumentVectorizer()
        
        # 搜索"模型路径"
        query_vector = vectorizer.simple_embedding("模型路径")
        search_results = vector_db.search_by_vector(query_vector, top_k=10)
        
        print(f"   搜索'模型路径'结果 ({len(search_results)}个):")
        for i, (chunk_id, similarity) in enumerate(search_results, 1):
            chunk_data = vector_db.chunks.get(chunk_id, {})
            content = chunk_data.get('content', '')
            print(f"     {i}. {chunk_id} (相似度: {similarity:.4f})")
            print(f"        内容: {content[:150]}...")
            print()
        
        # 5. 搜索"本地路径"
        query_vector2 = vectorizer.simple_embedding("本地路径")
        search_results2 = vector_db.search_by_vector(query_vector2, top_k=10)
        
        print(f"   搜索'本地路径'结果 ({len(search_results2)}个):")
        for i, (chunk_id, similarity) in enumerate(search_results2, 1):
            chunk_data = vector_db.chunks.get(chunk_id, {})
            content = chunk_data.get('content', '')
            print(f"     {i}. {chunk_id} (相似度: {similarity:.4f})")
            print(f"        内容: {content[:150]}...")
            print()
            
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_doc2())
