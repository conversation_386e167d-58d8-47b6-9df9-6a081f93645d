#!/usr/bin/env python3
"""
使用真实内容测试RAG
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.rag_service import RAGService
from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.llm_service import LLMService
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.knowledge_base_audit import KnowledgeBaseAuditService
from app.services.pipeline.factory import SecurityPipelineFactory
from app.db.session import SyncSessionLocal

async def test_rag_with_real_content():
    """使用真实内容测试RAG"""
    print("🔍 使用真实内容测试RAG...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 初始化服务
        knowledge_service = KnowledgeBaseService()
        llm_service = LLMService()
        permission_service = KnowledgeBasePermissionService(db=db)
        
        from app.core.audit import AuditLogger
        audit_logger = AuditLogger()
        audit_service = KnowledgeBaseAuditService()
        
        security_pipeline_factory = SecurityPipelineFactory
        
        # 创建RAG服务实例
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_service,
            llm_service=llm_service,
            permission_service=permission_service,
            audit_service=audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        # 测试查询
        query = "产品清单有什么产品"
        user_id = 1
        
        print(f"📝 测试查询: {query}")
        
        # 1. 先测试知识库搜索
        print("\n1️⃣ 测试知识库搜索...")
        search_results = await knowledge_service.search_knowledge_base(
            db=db,
            query=query,
            user_id=user_id,
            top_k=3,
            threshold=0.1
        )
        
        print(f"搜索结果数量: {len(search_results)}")
        
        # 构建上下文
        context_parts = []
        for result in search_results:
            context_parts.append(result.get('content', ''))
        
        context = '\n\n'.join(context_parts)
        print(f"上下文长度: {len(context)} 字符")
        print(f"上下文内容预览: {context[:300]}...")
        
        # 2. 测试提示词构建
        print("\n2️⃣ 测试提示词构建...")
        prompt = rag_service._build_enhanced_prompt(query, context)
        print(f"提示词长度: {len(prompt)} 字符")
        print(f"提示词内容:")
        print("-" * 50)
        print(prompt)
        print("-" * 50)
        
        # 3. 测试LLM调用
        print("\n3️⃣ 测试LLM调用...")
        
        from app.db.models.llm_model import LLMModel
        llm_model = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).first()
        
        if not llm_model:
            print("❌ 没有找到激活的LLM模型")
            return
        
        print(f"使用模型: {llm_model.name}")
        
        # 构建消息
        messages = [
            {"role": "system", "content": "你是一个智能的知识库问答助手。"},
            {"role": "user", "content": prompt}
        ]
        
        # 创建模型副本
        import copy
        temp_model = copy.deepcopy(llm_model)
        if temp_model.config_params:
            temp_model.config_params['max_tokens'] = 300
        else:
            temp_model.config_params = {'max_tokens': 300}
        
        print(f"模型配置: {temp_model.config_params}")
        
        try:
            print("🚀 调用LLM API...")
            llm_response = await llm_service._call_llm_api(
                llm_model=temp_model,
                messages=messages,
                stream=False
            )
            
            print("✅ LLM调用成功")
            print(f"LLM响应: {llm_response}")
            
        except Exception as e:
            print("❌ LLM调用失败")
            print(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return
        
        # 4. 测试完整RAG流程
        print("\n4️⃣ 测试完整RAG流程...")
        
        try:
            result = await rag_service.generate_answer(
                query=query,
                user_id=user_id,
                model_id=None,
                top_k=3,
                temperature=0.7,
                max_tokens=300,
                include_sources=True
            )
            
            print("✅ RAG流程完成")
            print(f"成功: {result.get('success', False)}")
            print(f"答案: {result.get('answer', 'N/A')}")
            print(f"检索文档数: {result.get('documents_retrieved', 0)}")
            print(f"错误: {result.get('error', 'N/A')}")
            
            if result.get('sources'):
                print(f"来源数量: {len(result['sources'])}")
                for i, source in enumerate(result['sources'][:3]):
                    print(f"  {i+1}. 文档: {source.get('document_title', 'N/A')}")
            
        except Exception as e:
            print("❌ RAG流程失败")
            print(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_rag_with_real_content())
