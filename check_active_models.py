#!/usr/bin/env python3
"""
检查当前激活的模型
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

def check_active_models():
    """检查当前激活的模型"""
    print("🔍 检查当前激活的模型...")
    
    db = SyncSessionLocal()
    
    try:
        # 查询所有模型
        all_models = db.query(LLMModel).all()
        print(f"总模型数: {len(all_models)}")
        
        # 查询激活的模型
        active_models = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).all()
        
        print(f"激活的模型数: {len(active_models)}")
        
        print("\n📋 所有模型状态:")
        for model in all_models:
            status = "✅ 激活" if model.is_active else "❌ 未激活"
            print(f"  {model.id}. {model.name} - {status}")
            if model.config_params:
                print(f"     配置: {model.config_params}")
        
        if active_models:
            print(f"\n🎯 当前激活的模型:")
            for model in active_models:
                print(f"  ID: {model.id}")
                print(f"  名称: {model.name}")
                print(f"  API URL: {model.api_url}")
                print(f"  配置: {model.config_params}")
                print()
        else:
            print("\n❌ 没有激活的模型!")
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    check_active_models()
