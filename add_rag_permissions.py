#!/usr/bin/env python3
"""
为管理员角色添加RAG问答权限
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def add_rag_permissions():
    """为管理员角色添加RAG问答权限"""
    
    # 1. 登录获取token
    print("🔐 正在登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 获取管理员角色的当前权限
    print("\n📋 获取管理员角色当前权限...")
    role_id = 1  # 管理员角色ID
    response = requests.get(f"{BASE_URL}/admin/permissions/roles/{role_id}/permissions", headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取角色权限失败: {response.status_code}")
        print(response.text)
        return
    
    current_permissions = response.json()
    print(f"✅ 当前权限配置获取成功")
    
    # 3. 检查是否已有RAG_CHAT权限
    existing_rag_perm = next((p for p in current_permissions["module_permissions"] 
                             if p["module_name"] == "RAG_CHAT"), None)
    
    if existing_rag_perm and existing_rag_perm["can_access"]:
        print("✅ 管理员角色已有RAG问答权限")
        return
    
    # 4. 添加RAG_CHAT权限
    print("\n🔧 添加RAG问答权限...")
    
    # 构建新的权限列表
    new_permissions = []
    for perm in current_permissions["module_permissions"]:
        new_permissions.append({
            "module_name": perm["module_name"],
            "can_access": perm["can_access"]
        })
    
    # 添加或更新RAG_CHAT权限
    if existing_rag_perm:
        # 更新现有权限
        for perm in new_permissions:
            if perm["module_name"] == "RAG_CHAT":
                perm["can_access"] = True
    else:
        # 添加新权限
        new_permissions.append({
            "module_name": "RAG_CHAT",
            "can_access": True
        })
    
    # 5. 更新权限
    response = requests.put(
        f"{BASE_URL}/admin/permissions/roles/{role_id}/module-permissions",
        headers=headers,
        json=new_permissions
    )
    
    if response.status_code == 200:
        print("✅ RAG问答权限添加成功")
        
        # 6. 验证权限更新
        print("\n🔍 验证权限更新...")
        response = requests.get(f"{BASE_URL}/admin/permissions/roles/{role_id}/permissions", headers=headers)
        if response.status_code == 200:
            updated_permissions = response.json()
            rag_perm = next((p for p in updated_permissions["module_permissions"] 
                           if p["module_name"] == "RAG_CHAT"), None)
            if rag_perm and rag_perm["can_access"]:
                print("✅ 权限验证成功 - 管理员现在有RAG问答权限")
            else:
                print("❌ 权限验证失败 - RAG问答权限未生效")
        else:
            print(f"❌ 权限验证失败: {response.status_code}")
    else:
        print(f"❌ 权限更新失败: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    add_rag_permissions()
