// API响应基础类型
export interface ApiError {
  detail: string
  status_code: number
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  role_id: number
  level_id: number
  is_active: boolean
  is_superuser: boolean
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
  user: User
}

// 会话相关类型
export interface Session {
  id: number
  title: string
  name?: string // 兼容字段
  description?: string // 兼容字段
  llm_model_id: number
  user_id: number
  is_active: boolean
  is_deleted: boolean
  created_at: string
  updated_at: string
  deleted_at?: string
  message_count?: number
  last_message_at?: string
}

export interface CreateSessionRequest {
  title: string
  llm_model_id: number
}

export interface UpdateSessionRequest {
  title?: string
  llm_model_id?: number
  is_active?: boolean
}

export interface SessionListResponse {
  items: Session[]
  total: number
  skip: number
  limit: number
}

export interface SessionDetailResponse {
  session: Session
  messages: Message[]
}

// 消息相关类型
export interface Message {
  id: number
  session_id: number
  content: string
  sender_type: 'user' | 'ai' | 'system'
  is_blocked: boolean
  block_reason?: string
  security_check_passed: boolean
  security_check_details?: any
  created_at: string
  updated_at: string
}

export interface CreateMessageRequest {
  content: string
  sender_type?: 'user' | 'ai' | 'system'
}

// AI消息生成相关类型
export interface AIMessageGenerateRequest {
  session_id: number
  user_message: string
  llm_model_id?: number
  stream?: boolean
}

export interface SecurityCheckResult {
  passed: boolean
  score: number
  keyword_check: boolean
  malicious_intent_check: boolean
  data_sensitivity_check: boolean
  blocked_keywords: string[]
  risk_level: string
}

export interface AIMessageGenerateResponse {
  user_message: Message
  ai_message: Message
  security_check_result: SecurityCheckResult
  generation_time: number
}

// LLM模型相关类型
export interface LLMModel {
  id: number
  name: string
  provider: string
  model_name: string
  api_endpoint: string
  is_active: boolean
  config_params: Record<string, any>
  created_at: string
  updated_at: string
}

// 角色和级别类型
export interface Role {
  id: number
  name: string
  description?: string
  is_active: boolean
  is_system: boolean
  created_at: string
  updated_at: string
}

export interface Level {
  id: number
  name: string
  description?: string
  role_id: number
  rank_value: number
  created_at: string
  updated_at: string
} 