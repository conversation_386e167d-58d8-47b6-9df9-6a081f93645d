import React, { useState, useEffect } from 'react'
import { Layout, Menu, Avatar, Dropdown, Space, Typography, Button } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  MessageOutlined,
  UserOutlined,
  LogoutOutlined,
  SafetyCertificateOutlined,
  SettingOutlined,
  AppstoreOutlined,
  ApiOutlined,
  TagsOutlined,
  SecurityScanOutlined,
  SafetyOutlined,
  CodeOutlined,
  FolderOutlined,
  NodeIndexOutlined,
  DatabaseOutlined,
} from '@ant-design/icons'
import { useAuthStore } from '../../store/authStore'

const { Header, Sider } = Layout
const { Text } = Typography

interface MainLayoutProps {
  children: React.ReactNode
}

interface MenuItem {
  key: string
  icon?: React.ReactNode
  label?: string
  type?: 'divider'
  onClick?: () => void
  children?: MenuItem[]
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const [userPermissions, setUserPermissions] = useState<any>(null)
  const [userFeaturePermissions, setUserFeaturePermissions] = useState<any>(null)

  // 获取用户权限
  useEffect(() => {
    const fetchUserPermissions = async () => {
      if (!user?.role_id) return
      
      try {
        const getAuthToken = () => {
          try {
            const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}');
            return authData?.state?.token || null;
          } catch {
            return null;
          }
        };
        
        const token = getAuthToken();
        
        // 获取模块权限
        const moduleResponse = await fetch(`/api/v1/admin/permissions/roles/${user.role_id}/permissions`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (moduleResponse.ok) {
          const permissions = await moduleResponse.json();
          setUserPermissions(permissions);
        }
        
        // 获取功能权限
        const featureResponse = await fetch(`/api/v1/admin/feature-permissions/roles/${user.role_id}/feature-permissions/KNOWLEDGE_BASE`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (featureResponse.ok) {
          const featurePermissions = await featureResponse.json();
          setUserFeaturePermissions(featurePermissions);
        }
      } catch (error) {
        console.error('获取用户权限失败:', error);
      }
    };
    
    fetchUserPermissions();
  }, [user?.role_id]);

  // 检查用户是否有特定模块权限
  const hasPermission = (moduleName: string): boolean => {
    if (!userPermissions) return false;
    const permission = userPermissions.module_permissions?.find(
      (p: any) => p.module_name === moduleName
    );
    return permission?.can_access || false;
  };

  // 检查用户是否有特定功能权限
  const hasFeaturePermission = (moduleName: string, featureName: string): boolean => {
    if (!userFeaturePermissions || userFeaturePermissions.module_name !== moduleName) return false;
    const feature = userFeaturePermissions.feature_permissions?.find(
      (f: any) => f.feature_name === featureName
    );
    return feature?.can_access || false;
  };

  const handleLogout = () => {
    logout()
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ]

  const sidebarMenuItems: MenuItem[] = [
    {
      key: '/sessions',
      icon: <MessageOutlined />,
      label: '会话管理',
      onClick: () => navigate('/sessions'),
    },
    // 移除原来的RAG问答菜单项，将其整合到知识库管理中
    {
      key: '/profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
  ]

  // 构建知识库管理子菜单 - 使用新的功能权限系统
  const buildKnowledgeBaseMenu = (): MenuItem[] => {
    const items: MenuItem[] = [];

    // 知识库问答 - 检查CHAT功能权限
    if (hasFeaturePermission('KNOWLEDGE_BASE', 'CHAT')) {
      items.push({
        key: '/knowledge-base/chat',
        icon: <MessageOutlined />,
        label: '知识库问答',
        onClick: () => navigate('/knowledge-base/chat'),
      });
    }

    // 文档管理 - 检查DOCUMENTS功能权限
    if (hasFeaturePermission('KNOWLEDGE_BASE', 'DOCUMENTS')) {
      items.push({
        key: '/knowledge-base/documents',
        icon: <FolderOutlined />,
        label: '文档管理',
        onClick: () => navigate('/knowledge-base/documents'),
      });
    }

    // 模型管理 - 检查MODELS功能权限
    if (hasFeaturePermission('KNOWLEDGE_BASE', 'MODELS')) {
      items.push({
        key: '/knowledge-base/models',
        icon: <ApiOutlined />,
        label: '模型管理',
        onClick: () => navigate('/knowledge-base/models'),
      });
    }

    // 用户文档权限管理 - 检查USER_PERMISSIONS功能权限
    if (hasFeaturePermission('KNOWLEDGE_BASE', 'USER_PERMISSIONS')) {
      items.push({
        key: '/admin/user-document-permissions',
        icon: <SecurityScanOutlined />,
        label: '用户文档权限管理',
        onClick: () => navigate('/admin/user-document-permissions'),
      });
    }

    return items;
  };

  // 构建安全审查管理子菜单
  const buildSecurityReviewMenu = (): MenuItem[] => {
    const items: MenuItem[] = [];
    
    if (hasPermission('KEYWORD_MANAGEMENT')) {
      items.push(
        {
          key: '/admin/keyword-groups',
          icon: <FolderOutlined />,
          label: '关键词分组管理',
          onClick: () => navigate('/admin/keyword-groups'),
        },
        {
          key: '/admin/keywords',
          icon: <TagsOutlined />,
          label: '关键词管理',
          onClick: () => navigate('/admin/keywords'),
        }
      );
    }
    
    if (hasPermission('DESENSITIZATION')) {
      items.push({
        key: '/admin/desensitization',
        icon: <SafetyOutlined />,
        label: '脱敏规则管理',
        onClick: () => navigate('/admin/desensitization'),
      });
    }
    
    if (hasPermission('SECURITY_POLICY')) {
      items.push({
        key: '/admin/regex-rules',
        icon: <CodeOutlined />,
        label: '恶意意图识别管理',
        onClick: () => navigate('/admin/regex-rules'),
      });
    }
    
    // 移除原来的知识库管理菜单项，将其整合到独立的知识库管理中
    
    return items;
  };

  // 构建系统管理子菜单
  const buildSystemManagementMenu = (): MenuItem[] => {
    const items: MenuItem[] = [];
    
    if (hasPermission('LLM_MODEL_MANAGEMENT')) {
      items.push({
        key: '/admin/models',
        icon: <ApiOutlined />,
        label: '模型管理',
        onClick: () => navigate('/admin/models'),
      });
    }
    
    if (hasPermission('USER_MANAGEMENT')) {
      items.push({
        key: '/admin/users',
        icon: <UserOutlined />,
        label: '用户管理',
        onClick: () => navigate('/admin/users'),
      });
    }
    
    if (hasPermission('ROLE_MANAGEMENT')) {
      items.push({
        key: '/admin/roles',
        icon: <SafetyCertificateOutlined />,
        label: '角色管理',
        onClick: () => navigate('/admin/roles'),
      });
    }
    
    if (hasPermission('SYSTEM_CONFIG')) {
      items.push(
        {
          key: '/admin/levels',
          icon: <SettingOutlined />,
          label: '权限级别管理',
          onClick: () => navigate('/admin/levels'),
        },
        {
          key: '/admin/hierarchy',
          icon: <NodeIndexOutlined />,
          label: '分层权限管理',
          onClick: () => navigate('/admin/hierarchy'),
        },
        {
          key: '/admin/level-data-access',
          icon: <SecurityScanOutlined />,
          label: '数据访问权限管理',
          onClick: () => navigate('/admin/level-data-access'),
        }
      );
    }
    
    return items;
  };

  // 动态构建管理菜单
  const buildAdminMenuItems = (): MenuItem[] => {
    if (!userPermissions) return [];

    const menuItems: MenuItem[] = [];
    const knowledgeBaseItems = buildKnowledgeBaseMenu();
    const securityItems = buildSecurityReviewMenu();
    const systemItems = buildSystemManagementMenu();

    // 如果有任何管理权限，添加分隔线
    if (knowledgeBaseItems.length > 0 || securityItems.length > 0 || systemItems.length > 0) {
      menuItems.push({
        key: 'admin-divider',
        type: 'divider',
      });
    }

    // 知识库管理
    if (knowledgeBaseItems.length > 0) {
      menuItems.push({
        key: 'knowledge-base-submenu',
        icon: <DatabaseOutlined />,
        label: '知识库管理',
        children: knowledgeBaseItems,
      });
    }
    
    // 安全审查管理
    if (securityItems.length > 0) {
      menuItems.push({
        key: 'security-review-submenu',
        icon: <SecurityScanOutlined />,
        label: '安全审查管理',
        children: securityItems,
      });
    }
    
    // 系统管理
    if (systemItems.length > 0) {
      menuItems.push({
        key: 'system-submenu',
        icon: <AppstoreOutlined />,
        label: '系统管理',
        children: systemItems,
      });
    }
    
    return menuItems;
  };

  const adminMenuItems: MenuItem[] = buildAdminMenuItems();

  const allMenuItems: MenuItem[] = [...sidebarMenuItems, ...adminMenuItems]

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        padding: '0 24px',
      }}>
        {/* Logo */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <SafetyCertificateOutlined 
            style={{ 
              fontSize: 24, 
              color: '#1890ff',
              marginRight: 12 
            }} 
          />
          <Typography.Title level={4} style={{ margin: 0, color: '#1890ff' }}>
            AI安全系统
          </Typography.Title>
        </div>

        {/* User Menu */}
        <Space>
          <Text type="secondary">
            欢迎，{user?.username}
          </Text>
          <Dropdown 
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <Button 
              type="text" 
              style={{ 
                display: 'flex', 
                alignItems: 'center',
                height: 40,
              }}
            >
              <Avatar 
                size="small" 
                icon={<UserOutlined />} 
                style={{ marginRight: 8 }}
              />
              <span>{user?.username}</span>
            </Button>
          </Dropdown>
        </Space>
      </Header>

      <Layout>
        <Sider 
          width={240}
          style={{
            background: '#fff',
            borderRight: '1px solid #f0f0f0',
          }}
        >
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            style={{
              height: '100%',
              borderRight: 0,
              paddingTop: 16,
            }}
            items={allMenuItems.map(item => {
              if (item.type === 'divider') {
                return { type: 'divider', key: item.key }
              }
              if (item.children) {
                return {
                  key: item.key,
                  icon: item.icon,
                  label: item.label,
                  children: item.children.map(child => ({
                    key: child.key,
                    icon: child.icon,
                    label: child.label,
                    onClick: child.onClick,
                  })),
                }
              }
              return {
                key: item.key,
                icon: item.icon,
                label: item.label,
                onClick: item.onClick,
              }
            })}
          />
        </Sider>

        <Layout style={{ padding: 0 }}>
          {children}
        </Layout>
      </Layout>
    </Layout>
  )
}

export default MainLayout 