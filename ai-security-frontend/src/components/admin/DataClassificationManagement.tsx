import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  Tag,
  message,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Divider,
  Descriptions,
  Tooltip,
  Alert,
  Badge
} from 'antd';
import {
  DatabaseOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  SecurityScanOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';
import api from '../../services/api';
import { useAuthStore } from '../../store/authStore';

const { TextArea } = Input;

// 数据结构类型定义（基于真实API）
interface DataSourceGrade {
  id: number;
  name: string;
  description?: string;
  sensitivity_level: number;
  category_name?: string;
  accessible?: boolean;
}

interface DataSourceCategory {
  id: number;
  name: string;
  description?: string;
  grades: DataSourceGrade[];
  created_at: string;
  updated_at: string;
}

interface UserAccessibleGradesResponse {
  user_level_info: {
    user_id: number;
    username: string;
    level_name: string;
    rank_value: number;
    accessible_sensitivity_levels: number[];
  };
  total_grades: number;
  accessible_count: number;
  grades: DataSourceGrade[];
}

const DataClassificationManagement: React.FC = () => {
  const [categories, setCategories] = useState<DataSourceCategory[]>([]);
  const [accessibleGrades, setAccessibleGrades] = useState<DataSourceGrade[]>([]);
  const [userLevelInfo, setUserLevelInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<DataSourceCategory | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<DataSourceCategory | null>(null);
  const [categoryForm] = Form.useForm();
  
  const { user } = useAuthStore();

  // 获取用户可访问的数据分级
  const fetchAccessibleGrades = async () => {
    try {
      setLoading(true);
      const response = await api.get('/data-classification/accessible-grades');
      const data: UserAccessibleGradesResponse = response.data;
      setAccessibleGrades(data.grades || []);
      setUserLevelInfo(data.user_level_info);
    } catch (error) {
      console.error('获取可访问数据分级失败:', error);
      message.error('获取可访问数据分级失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有数据分类（管理员权限）
  const fetchAllCategories = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/data-classification/categories');
      setCategories(response.data || []);
    } catch (error) {
      console.error('获取数据分类失败:', error);
      message.error('获取数据分类失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccessibleGrades();
    if (user?.is_superuser) {
      fetchAllCategories();
    }
  }, [user]);

  // 获取敏感级别标签
  const getSensitivityTag = (level: number) => {
    const configs = {
      1: { color: 'red', text: '高级敏感' },
      2: { color: 'orange', text: '中级敏感' },
      3: { color: 'blue', text: '初级敏感' },
      4: { color: 'green', text: '完全开放' }
    };
    const config = configs[level as keyof typeof configs] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 检查用户是否可以访问某个敏感级别
  const canAccessLevel = (sensitivityLevel: number) => {
    return userLevelInfo?.accessible_sensitivity_levels?.includes(sensitivityLevel) || false;
  };

  // 编辑数据分类
  const handleEditCategory = (category?: DataSourceCategory) => {
    setEditingCategory(category || null);
    if (category) {
      categoryForm.setFieldsValue({
        name: category.name,
        description: category.description
      });
    } else {
      categoryForm.resetFields();
    }
    setCategoryModalVisible(true);
  };

  // 保存数据分类
  const handleSaveCategory = async () => {
    try {
      const values = await categoryForm.validateFields();
      
      if (editingCategory) {
        await api.put(`/admin/data-classification/categories/${editingCategory.id}`, values);
        message.success('数据分类更新成功');
      } else {
        await api.post('/admin/data-classification/categories', values);
        message.success('数据分类创建成功');
      }
      
      setCategoryModalVisible(false);
      fetchAllCategories();
      fetchAccessibleGrades();
    } catch (error) {
      console.error('保存数据分类失败:', error);
      message.error('保存数据分类失败');
    }
  };

  // 删除数据分类
  const handleDeleteCategory = async (id: number) => {
    try {
      await api.delete(`/admin/data-classification/categories/${id}`);
      message.success('数据分类删除成功');
      fetchAllCategories();
      fetchAccessibleGrades();
    } catch (error) {
      console.error('删除数据分类失败:', error);
      message.error('删除数据分类失败');
    }
  };

  // 查看详情
  const handleViewDetail = (category: DataSourceCategory) => {
    setSelectedCategory(category);
    setDetailModalVisible(true);
  };

  // 数据分类表格列定义
  const categoryColumns: ColumnsType<DataSourceCategory> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '分级数量',
      key: 'grade_count',
      width: 100,
      render: (_, record) => (
        <Tag color="blue">{record.grades?.length || 0} 个</Tag>
      ),
    },
    {
      title: '敏感度分布',
      key: 'sensitivity_distribution',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {[1, 2, 3, 4].map(level => {
            const count = record.grades?.filter(g => g.sensitivity_level === level).length || 0;
            return count > 0 ? (
              <Tag key={level} color={
                level === 1 ? 'red' : level === 2 ? 'orange' : level === 3 ? 'blue' : 'green'
              }>
                {count}
              </Tag>
            ) : null;
          })}
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {user?.is_superuser && (
            <>
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEditCategory(record)}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个数据分类吗？"
                description="删除后将同时删除该分类下的所有分级，此操作不可恢复。"
                onConfirm={() => handleDeleteCategory(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  // 数据分级表格列定义
  const gradeColumns: ColumnsType<DataSourceGrade> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '分级名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '所属分类',
      dataIndex: 'category_name',
      key: 'category_name',
      ellipsis: true,
    },
    {
      title: '敏感级别',
      dataIndex: 'sensitivity_level',
      key: 'sensitivity_level',
      width: 120,
      render: (level: number) => getSensitivityTag(level),
    },
    {
      title: '可访问性',
      key: 'accessibility',
      width: 100,
      render: (_, record) => {
        const accessible = record.accessible || canAccessLevel(record.sensitivity_level);
        return (
          <Badge 
            status={accessible ? "success" : "error"} 
            text={accessible ? "可访问" : "受限"} 
          />
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
  ];

  // 计算统计数据
  const totalCategories = categories.length;
  const allGrades = categories.reduce((acc, cat) => [...acc, ...cat.grades], [] as DataSourceGrade[]);
  const accessibleCount = accessibleGrades.filter(g => g.accessible).length;
  const highestAccessibleLevel = Math.min(...(userLevelInfo?.accessible_sensitivity_levels || [4]));

  return (
    <div style={{ padding: '24px' }}>
      {/* 权限提示 */}
      {userLevelInfo && (
        <Alert
          message={`当前用户: ${userLevelInfo.username} (${userLevelInfo.level_name}), 级别值: ${userLevelInfo.rank_value}, 可访问敏感级别: ${userLevelInfo.accessible_sensitivity_levels.join(', ')}`}
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="可访问数据分级"
              value={accessibleCount}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="全部数据分类"
              value={totalCategories}
              prefix={<SecurityScanOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="全部数据分级"
              value={allGrades.length}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="最高可访问级别"
              value={highestAccessibleLevel}
              prefix={<SecurityScanOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 可访问的数据分级 */}
      <Card
        title={
          <Space>
            <DatabaseOutlined />
            我可访问的数据分级
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={fetchAccessibleGrades}
            loading={loading}
          >
            刷新
          </Button>
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          columns={gradeColumns}
          dataSource={accessibleGrades}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* 全部数据分类（管理员可见） */}
      {user?.is_superuser && (
        <Card
          title={
            <Space>
              <SecurityScanOutlined />
              全部数据分类管理
            </Space>
          }
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleEditCategory()}
              >
                新建分类
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchAllCategories}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          }
        >
          <Table
            columns={categoryColumns}
            dataSource={categories}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        </Card>
      )}

      {/* 数据分类编辑Modal */}
      <Modal
        title={editingCategory ? '编辑数据分类' : '新建数据分类'}
        open={categoryModalVisible}
        onOk={handleSaveCategory}
        onCancel={() => setCategoryModalVisible(false)}
        destroyOnClose
      >
        <Form
          form={categoryForm}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            label="分类名称"
            name="name"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea
              rows={4}
              placeholder="请输入分类描述"
            />
          </Form.Item>
        </Form>
        
        {!editingCategory && (
          <div style={{ padding: '16px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
            <Space direction="vertical" size="small">
              <div style={{ fontWeight: 'bold', color: '#389e0d' }}>系统提示：</div>
              <div>创建数据分类后，系统将自动生成4个标准敏感度分级：</div>
              <div style={{ marginLeft: '16px' }}>
                • <Tag color="red">高级敏感</Tag> - 最高级别敏感数据<br/>
                • <Tag color="orange">中级敏感</Tag> - 中等级别敏感数据<br/>
                • <Tag color="blue">初级敏感</Tag> - 低级别敏感数据<br/>
                • <Tag color="green">完全开放</Tag> - 公开数据
              </div>
            </Space>
          </div>
        )}
      </Modal>

      {/* 分类详情Modal */}
      <Modal
        title="数据分类详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedCategory && (
          <div>
            <Descriptions bordered column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="分类名称">{selectedCategory.name}</Descriptions.Item>
              <Descriptions.Item label="分级数量">{selectedCategory.grades?.length || 0} 个</Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {dayjs(selectedCategory.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间" span={2}>
                {dayjs(selectedCategory.updated_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {selectedCategory.description || '无'}
              </Descriptions.Item>
            </Descriptions>

            <Divider orientation="left">数据分级详情</Divider>
            <Table
              columns={[
                {
                  title: '分级名称',
                  dataIndex: 'name',
                  key: 'name',
                  render: (name: string, record) => (
                    <Space>
                      {getSensitivityTag(record.sensitivity_level)}
                      <span>{name}</span>
                    </Space>
                  )
                },
                {
                  title: '敏感度等级',
                  dataIndex: 'sensitivity_level',
                  key: 'sensitivity_level',
                  width: 120,
                  render: (level: number) => (
                    <Tag color={level === 1 ? 'red' : level === 2 ? 'orange' : level === 3 ? 'blue' : 'green'}>
                      级别 {level}
                    </Tag>
                  )
                },
                {
                  title: '描述',
                  dataIndex: 'description',
                  key: 'description'
                }
              ]}
              dataSource={selectedCategory.grades || []}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DataClassificationManagement; 