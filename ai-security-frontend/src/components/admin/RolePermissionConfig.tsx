import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Switch,
  Button,
  Table,
  Tabs,
  Space,
  Tag,
  message,
  Checkbox,
  Row,
  Col,
  Descriptions,
  Divider,
  Tooltip
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  SecurityScanOutlined,
  TagsOutlined,
  DatabaseOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { TabPane } = Tabs;

interface Role {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

interface ModulePermission {
  module_name: string;
  can_access: boolean;
}

interface KeywordGroup {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

interface DataGrade {
  id: number;
  name: string;
  category_name: string;
  sensitivity_level: number;
  description: string;
}

interface FeaturePermission {
  feature_name: string;
  can_access: boolean;
  can_create: boolean;
  can_read: boolean;
  can_update: boolean;
  can_delete: boolean;
  description: string;
}

interface RolePermissions {
  role_id: number;
  role_name: string;
  module_permissions: ModulePermission[];
  keyword_group_assignments: number[];
  data_grade_accesses: number[];
}

interface KnowledgeBaseFeaturePermissions {
  role_id: number;
  role_name: string;
  module_name: string;
  feature_permissions: FeaturePermission[];
}

interface AvailableModule {
  name: string;
  description: string;
}

// 真实API调用
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// 获取认证token的工具函数
const getAuthToken = (): string | null => {
  try {
    const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}');
    return authData?.state?.token || null;
  } catch {
    return null;
  }
};

const permissionApi = {
  getRoles: async (): Promise<Role[]> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/roles/`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) {
      throw new Error('获取角色列表失败');
    }
    return await response.json();
  },

  getRolePermissions: async (roleId: number): Promise<RolePermissions> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/permissions/roles/${roleId}/permissions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) {
      throw new Error('获取角色权限失败');
    }
    return await response.json();
  },

  getAvailableModules: async (): Promise<AvailableModule[]> => {
    try {
      // 从后端API获取可用模块列表
      const token = getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/v1/admin/permissions/available-modules`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const data = await response.json();
        return data.modules.map((module: any) => ({
          name: module.name,
          description: module.description
        }));
      }
    } catch (error) {
      console.error('获取可用模块列表失败:', error);
    }
    
    // 如果API调用失败，返回默认的模块列表作为备选
    // 注意：知识库相关权限已迁移到功能权限系统，不再使用模块权限
    return [
      { name: 'USER_MANAGEMENT', description: '用户管理' },
      { name: 'ROLE_MANAGEMENT', description: '角色管理' },
      { name: 'KEYWORD_MANAGEMENT', description: '关键词管理' },
      { name: 'SECURITY_POLICY', description: '安全策略管理' },
      { name: 'SESSION_MANAGEMENT', description: '会话管理' },
      { name: 'AUDIT_LOGS', description: '审计日志' },
      { name: 'SYSTEM_CONFIG', description: '系统配置' },
      { name: 'MESSAGE_MONITORING', description: '消息监控' },
      { name: 'DESENSITIZATION', description: '数据脱敏管理' },
      { name: 'LLM_MODEL_MANAGEMENT', description: 'LLM模型管理' }
    ];
  },

  // 获取知识库功能权限
  getKnowledgeBaseFeaturePermissions: async (roleId: number): Promise<any> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/feature-permissions/roles/${roleId}/feature-permissions/KNOWLEDGE_BASE`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) {
      throw new Error('获取知识库功能权限失败');
    }
    return await response.json();
  },

  // 更新知识库功能权限
  updateKnowledgeBaseFeaturePermissions: async (roleId: number, permissions: any): Promise<void> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/feature-permissions/roles/${roleId}/feature-permissions/KNOWLEDGE_BASE`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(permissions)
    });
    if (!response.ok) {
      throw new Error('更新知识库功能权限失败');
    }
  },

  getKeywordGroups: async (): Promise<KeywordGroup[]> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/keyword-groups/`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) {
      throw new Error('获取关键词组失败');
    }
    const data = await response.json();
    // 处理后端返回的分页格式
    return data.items || data;
  },

  getDataGrades: async (): Promise<DataGrade[]> => {
    try {
      const token = getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/v1/admin/data-classification/categories`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('获取数据分类失败');
      }
      const categories = await response.json();
      
      // 从分类中提取所有分级
      const grades: DataGrade[] = [];
      categories.forEach((category: any) => {
        if (category.grades && Array.isArray(category.grades)) {
          category.grades.forEach((grade: any) => {
            grades.push({
              id: grade.id,
              name: grade.name,
              category_name: category.name,
              sensitivity_level: grade.sensitivity_level,
              description: grade.description || ''
            });
          });
        }
      });
      
      return grades;
    } catch (error) {
      console.warn('获取数据分级失败，使用默认数据:', error);
      // 如果API失败，返回默认数据
      return [
        { id: 1, name: '高级敏感', category_name: '用户信息', sensitivity_level: 1, description: '最高级别敏感数据' },
        { id: 2, name: '中级敏感', category_name: '用户信息', sensitivity_level: 2, description: '中等级别敏感数据' },
        { id: 3, name: '初级敏感', category_name: '用户信息', sensitivity_level: 3, description: '低级别敏感数据' },
        { id: 4, name: '完全开放', category_name: '用户信息', sensitivity_level: 4, description: '公开数据' }
      ];
    }
  },

  updateModulePermissions: async (roleId: number, permissions: ModulePermission[]): Promise<void> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/permissions/roles/${roleId}/module-permissions`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(permissions)
    });
    if (!response.ok) {
      throw new Error('更新模块权限失败');
    }
  },

  updateKeywordGroups: async (roleId: number, assignments: Array<{keyword_group_id: number, assigned: boolean}>): Promise<void> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/permissions/roles/${roleId}/keyword-groups`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assignments)
    });
    if (!response.ok) {
      throw new Error('更新关键词组分配失败');
    }
  },

  updateDataGradeAccess: async (roleId: number, accesses: Array<{data_source_grade_id: number, access_allowed: boolean}>): Promise<void> => {
    const token = getAuthToken();
    const response = await fetch(`${API_BASE_URL}/api/v1/admin/permissions/roles/${roleId}/data-grade-access`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(accesses)
    });
    if (!response.ok) {
      throw new Error('更新数据分级访问失败');
    }
  }
};

const RolePermissionConfig: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRoleId, setSelectedRoleId] = useState<number | undefined>();
  const [rolePermissions, setRolePermissions] = useState<RolePermissions | null>(null);
  const [availableModules, setAvailableModules] = useState<AvailableModule[]>([]);
  const [keywordGroups, setKeywordGroups] = useState<KeywordGroup[]>([]);
  const [dataGrades, setDataGrades] = useState<DataGrade[]>([]);
  const [knowledgeBaseFeaturePermissions, setKnowledgeBaseFeaturePermissions] = useState<KnowledgeBaseFeaturePermissions | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const data = await permissionApi.getRoles();
      setRoles(data.filter((role: any) => role.is_active));
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    }
  };

  // 获取可用模块列表
  const fetchAvailableModules = async () => {
    try {
      const data = await permissionApi.getAvailableModules();
      setAvailableModules(data);
    } catch (error) {
      console.error('获取模块列表失败:', error);
    }
  };

  // 获取关键词组列表
  const fetchKeywordGroups = async () => {
    try {
      const data = await permissionApi.getKeywordGroups();
      setKeywordGroups(data.filter((group: any) => group.is_active));
    } catch (error) {
      console.error('获取关键词组失败:', error);
    }
  };

  // 获取数据分级列表
  const fetchDataGrades = async () => {
    try {
      const data = await permissionApi.getDataGrades();
      setDataGrades(data);
    } catch (error) {
      console.error('获取数据分级失败:', error);
    }
  };

  // 获取角色权限
  const fetchRolePermissions = async (roleId: number) => {
    try {
      setLoading(true);
      const data = await permissionApi.getRolePermissions(roleId);
      setRolePermissions(data);
    } catch (error) {
      console.error('获取角色权限失败:', error);
      message.error('获取角色权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取知识库功能权限
  const fetchKnowledgeBaseFeaturePermissions = async (roleId: number) => {
    try {
      const data = await permissionApi.getKnowledgeBaseFeaturePermissions(roleId);
      setKnowledgeBaseFeaturePermissions(data);
    } catch (error) {
      console.error('获取知识库功能权限失败:', error);
      // 如果获取失败，设置默认的功能权限
      setKnowledgeBaseFeaturePermissions({
        role_id: roleId,
        role_name: roles.find(r => r.id === roleId)?.name || '',
        module_name: 'KNOWLEDGE_BASE',
        feature_permissions: [
          {
            feature_name: 'CHAT',
            can_access: false,
            can_create: false,
            can_read: false,
            can_update: false,
            can_delete: false,
            description: '知识库问答功能'
          },
          {
            feature_name: 'DOCUMENTS',
            can_access: false,
            can_create: false,
            can_read: false,
            can_update: false,
            can_delete: false,
            description: '文档管理功能'
          },
          {
            feature_name: 'MODELS',
            can_access: false,
            can_create: false,
            can_read: false,
            can_update: false,
            can_delete: false,
            description: '模型管理功能'
          },
          {
            feature_name: 'USER_PERMISSIONS',
            can_access: false,
            can_create: false,
            can_read: false,
            can_update: false,
            can_delete: false,
            description: '用户文档权限管理功能'
          }
        ]
      });
    }
  };

  useEffect(() => {
    fetchRoles();
    fetchAvailableModules();
    fetchKeywordGroups();
    fetchDataGrades();
  }, []);

  useEffect(() => {
    if (selectedRoleId) {
      fetchRolePermissions(selectedRoleId);
      fetchKnowledgeBaseFeaturePermissions(selectedRoleId);
    }
  }, [selectedRoleId]);

  // 更新模块权限
  const handleModulePermissionChange = (moduleName: string, canAccess: boolean) => {
    if (!rolePermissions) return;

    const updatedPermissions = rolePermissions.module_permissions.map(perm => 
      perm.module_name === moduleName ? { ...perm, can_access: canAccess } : perm
    );

    setRolePermissions({
      ...rolePermissions,
      module_permissions: updatedPermissions
    });
  };

  // 更新关键词组分配
  const handleKeywordGroupChange = (groupId: number, assigned: boolean) => {
    if (!rolePermissions) return;

    let updatedAssignments;
    if (assigned) {
      updatedAssignments = [...rolePermissions.keyword_group_assignments, groupId];
    } else {
      updatedAssignments = rolePermissions.keyword_group_assignments.filter(id => id !== groupId);
    }

    setRolePermissions({
      ...rolePermissions,
      keyword_group_assignments: updatedAssignments
    });
  };

  // 更新数据分级访问
  const handleDataGradeChange = (gradeId: number, allowed: boolean) => {
    if (!rolePermissions) return;

    let updatedAccesses;
    if (allowed) {
      updatedAccesses = [...rolePermissions.data_grade_accesses, gradeId];
    } else {
      updatedAccesses = rolePermissions.data_grade_accesses.filter(id => id !== gradeId);
    }

    setRolePermissions({
      ...rolePermissions,
      data_grade_accesses: updatedAccesses
    });
  };

  // 更新知识库功能权限
  const handleFeaturePermissionChange = (featureName: string, field: string, value: boolean) => {
    if (!knowledgeBaseFeaturePermissions) return;

    const updatedFeaturePermissions = knowledgeBaseFeaturePermissions.feature_permissions.map(feature => {
      if (feature.feature_name === featureName) {
        return {
          ...feature,
          [field]: value
        };
      }
      return feature;
    });

    setKnowledgeBaseFeaturePermissions({
      ...knowledgeBaseFeaturePermissions,
      feature_permissions: updatedFeaturePermissions
    });
  };

  // 保存权限配置
  const handleSave = async () => {
    if (!rolePermissions || !selectedRoleId) {
      message.warning('请选择角色');
      return;
    }

    try {
      setSaving(true);

      // 保存模块权限
      await permissionApi.updateModulePermissions(selectedRoleId, rolePermissions.module_permissions);

      // 保存关键词组分配
      const keywordAssignments = keywordGroups.map(group => ({
        keyword_group_id: group.id,
        assigned: rolePermissions.keyword_group_assignments.includes(group.id)
      }));
      await permissionApi.updateKeywordGroups(selectedRoleId, keywordAssignments);

      // 保存数据分级访问
      const dataAccesses = dataGrades.map(grade => ({
        data_source_grade_id: grade.id,
        access_allowed: rolePermissions.data_grade_accesses.includes(grade.id)
      }));
      await permissionApi.updateDataGradeAccess(selectedRoleId, dataAccesses);

      // 保存知识库功能权限
      if (knowledgeBaseFeaturePermissions) {
        const featurePermissionsData = knowledgeBaseFeaturePermissions.feature_permissions.map(feature => ({
          feature_name: feature.feature_name,
          can_access: feature.can_access,
          can_create: feature.can_create,
          can_read: feature.can_read,
          can_update: feature.can_update,
          can_delete: feature.can_delete
        }));
        
        try {
          await permissionApi.updateKnowledgeBaseFeaturePermissions(selectedRoleId, featurePermissionsData);
        } catch (error) {
          console.warn('保存知识库功能权限失败，但其他权限已保存:', error);
        }
      }

      message.success('权限配置保存成功');
    } catch (error) {
      console.error('保存权限配置失败:', error);
      message.error('保存权限配置失败');
    } finally {
      setSaving(false);
    }
  };

  // 获取敏感度标签
  const getSensitivityTag = (level: number) => {
    const tagProps = {
      1: { color: 'red', text: '高级敏感' },
      2: { color: 'orange', text: '中级敏感' },
      3: { color: 'blue', text: '初级敏感' },
      4: { color: 'green', text: '完全开放' }
    };
    
    const props = tagProps[level as keyof typeof tagProps] || { color: 'default', text: '未知' };
    return <Tag color={props.color}>{props.text}</Tag>;
  };

  // 模块权限表格列
  const moduleColumns: ColumnsType<ModulePermission & AvailableModule> = [
    {
      title: '模块名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          <SecurityScanOutlined />
          <span>{record.description}</span>
          <Tag>{name}</Tag>
        </Space>
      )
    },
    {
      title: '访问权限',
      dataIndex: 'can_access',
      key: 'can_access',
      width: 120,
      render: (canAccess: boolean, record) => (
        <Switch
          checked={canAccess}
          onChange={(checked) => handleModulePermissionChange(record.module_name, checked)}
          checkedChildren="允许"
          unCheckedChildren="禁止"
        />
      )
    }
  ];

  // 关键词组表格列
  const keywordColumns: ColumnsType<KeywordGroup> = [
    {
      title: '关键词组',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          <TagsOutlined />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '分配状态',
      key: 'assigned',
      width: 120,
      render: (_, record) => (
        <Switch
          checked={rolePermissions?.keyword_group_assignments.includes(record.id) || false}
          onChange={(checked) => handleKeywordGroupChange(record.id, checked)}
          checkedChildren="已分配"
          unCheckedChildren="未分配"
        />
      )
    }
  ];

  // 知识库功能权限表格列
  const featurePermissionColumns: ColumnsType<FeaturePermission> = [
    {
      title: '功能名称',
      dataIndex: 'feature_name',
      key: 'feature_name',
      render: (featureName: string, record) => {
        const featureNameMap: { [key: string]: string } = {
          'CHAT': '知识库问答',
          'DOCUMENTS': '文档管理',
          'MODELS': '模型管理',
          'USER_PERMISSIONS': '用户文档权限管理'
        };
        return (
          <Space>
            <SecurityScanOutlined />
            <span>{featureNameMap[featureName] || featureName}</span>
            <Tag>{featureName}</Tag>
          </Space>
        );
      }
    },
    {
      title: '访问权限',
      dataIndex: 'can_access',
      key: 'can_access',
      width: 100,
      render: (canAccess: boolean, record) => (
        <Switch
          checked={canAccess}
          onChange={(checked) => handleFeaturePermissionChange(record.feature_name, 'can_access', checked)}
          checkedChildren="允许"
          unCheckedChildren="禁止"
          size="small"
        />
      )
    },
    {
      title: '创建',
      dataIndex: 'can_create',
      key: 'can_create',
      width: 80,
      render: (canCreate: boolean, record) => (
        <Switch
          checked={canCreate}
          onChange={(checked) => handleFeaturePermissionChange(record.feature_name, 'can_create', checked)}
          size="small"
        />
      )
    },
    {
      title: '查看',
      dataIndex: 'can_read',
      key: 'can_read',
      width: 80,
      render: (canRead: boolean, record) => (
        <Switch
          checked={canRead}
          onChange={(checked) => handleFeaturePermissionChange(record.feature_name, 'can_read', checked)}
          size="small"
        />
      )
    },
    {
      title: '编辑',
      dataIndex: 'can_update',
      key: 'can_update',
      width: 80,
      render: (canUpdate: boolean, record) => (
        <Switch
          checked={canUpdate}
          onChange={(checked) => handleFeaturePermissionChange(record.feature_name, 'can_update', checked)}
          size="small"
        />
      )
    },
    {
      title: '删除',
      dataIndex: 'can_delete',
      key: 'can_delete',
      width: 80,
      render: (canDelete: boolean, record) => (
        <Switch
          checked={canDelete}
          onChange={(checked) => handleFeaturePermissionChange(record.feature_name, 'can_delete', checked)}
          size="small"
        />
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    }
  ];

  // 数据分级表格列
  const dataGradeColumns: ColumnsType<DataGrade> = [
    {
      title: '数据分类',
      dataIndex: 'category_name',
      key: 'category_name',
      render: (categoryName: string) => (
        <Space>
          <DatabaseOutlined />
          <span>{categoryName}</span>
        </Space>
      )
    },
    {
      title: '敏感度等级',
      dataIndex: 'sensitivity_level',
      key: 'sensitivity_level',
      render: (level: number, record) => (
        <Space>
          {getSensitivityTag(level)}
          <span>{record.name}</span>
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '访问权限',
      key: 'access',
      width: 120,
      render: (_, record) => (
        <Switch
          checked={rolePermissions?.data_grade_accesses.includes(record.id) || false}
          onChange={(checked) => handleDataGradeChange(record.id, checked)}
          checkedChildren="允许"
          unCheckedChildren="禁止"
        />
      )
    }
  ];

  // 合并模块权限数据
  const modulePermissionData = availableModules.map(module => {
    const permission = rolePermissions?.module_permissions.find(p => p.module_name === module.name);
    return {
      ...module,
      module_name: module.name,
      can_access: permission?.can_access || false
    };
  });

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          <span>角色详细权限配置</span>
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => selectedRoleId && fetchRolePermissions(selectedRoleId)}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={saving}
            onClick={handleSave}
            disabled={!selectedRoleId}
          >
            保存配置
          </Button>
        </Space>
      }
    >
      {/* 角色选择 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card size="small" title="选择角色">
            <Select
              placeholder="请选择要配置的角色"
              style={{ width: '100%' }}
              value={selectedRoleId}
              onChange={setSelectedRoleId}
            >
              {roles.map(role => (
                <Option key={role.id} value={role.id}>
                  <Space>
                    <span>{role.name}</span>
                    <Tag>{role.description}</Tag>
                  </Space>
                </Option>
              ))}
            </Select>
          </Card>
        </Col>
        <Col span={16}>
          {rolePermissions && (
            <Card size="small" title="角色信息">
              <Descriptions size="small" column={3}>
                <Descriptions.Item label="角色名称">{rolePermissions.role_name}</Descriptions.Item>
                <Descriptions.Item label="模块权限">{rolePermissions.module_permissions.filter(p => p.can_access).length} 个</Descriptions.Item>
                <Descriptions.Item label="关键词组">{rolePermissions.keyword_group_assignments.length} 个</Descriptions.Item>
                <Descriptions.Item label="数据分级权限">{rolePermissions.data_grade_accesses.length} 个</Descriptions.Item>
              </Descriptions>
            </Card>
          )}
        </Col>
      </Row>

      {/* 权限配置标签页 */}
      {rolePermissions && (
        <Tabs defaultActiveKey="modules">
          <TabPane
            tab={
              <Space>
                <SecurityScanOutlined />
                <span>模块权限</span>
                <Tag>{rolePermissions.module_permissions.filter(p => p.can_access).length}</Tag>
              </Space>
            }
            key="modules"
          >
            <Table
              columns={moduleColumns}
              dataSource={modulePermissionData}
              rowKey="name"
              loading={loading}
              pagination={false}
              size="small"
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <TagsOutlined />
                <span>关键词组</span>
                <Tag>{rolePermissions.keyword_group_assignments.length}</Tag>
              </Space>
            }
            key="keywords"
          >
            <Table
              columns={keywordColumns}
              dataSource={keywordGroups}
              rowKey="id"
              loading={loading}
              pagination={false}
              size="small"
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <DatabaseOutlined />
                <span>数据分级权限</span>
                <Tag>{rolePermissions.data_grade_accesses.length}</Tag>
              </Space>
            }
            key="dataGrades"
          >
            <Table
              columns={dataGradeColumns}
              dataSource={dataGrades}
              rowKey="id"
              loading={loading}
              pagination={false}
              size="small"
              expandable={{
                expandedRowRender: (record) => (
                  <div style={{ margin: 0, padding: 8, backgroundColor: '#fafafa' }}>
                    <Space direction="vertical" size="small">
                      <div><strong>分类:</strong> {record.category_name}</div>
                      <div><strong>敏感度:</strong> {getSensitivityTag(record.sensitivity_level)}</div>
                      <div><strong>描述:</strong> {record.description}</div>
                    </Space>
                  </div>
                ),
                rowExpandable: (record) => !!record.description,
              }}
            />
          </TabPane>

          {/* 知识库功能权限标签页 */}
          {knowledgeBaseFeaturePermissions && (
            <TabPane
              tab={
                <Space>
                  <SecurityScanOutlined />
                  <span>知识库功能权限</span>
                  <Tag>{knowledgeBaseFeaturePermissions.feature_permissions.filter(p => p.can_access).length}</Tag>
                </Space>
              }
              key="knowledgeBaseFeatures"
            >
              <Table
                columns={featurePermissionColumns}
                dataSource={knowledgeBaseFeaturePermissions.feature_permissions}
                rowKey="feature_name"
                loading={loading}
                pagination={false}
                size="small"
              />
            </TabPane>
          )}
        </Tabs>
      )}

      {!selectedRoleId && (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <InfoCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <div>请选择角色以配置权限</div>
        </div>
      )}
    </Card>
  );
};

export default RolePermissionConfig; 