import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Card,
  message,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Tabs,
  Alert,
  Spin,
  Popconfirm,
  Badge,
  Descriptions,
  Typography,
  Switch,
  InputNumber,
  Divider,
  List,
  Collapse
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  BulbOutlined,
  ApiOutlined,
  UserOutlined,
  GlobalOutlined,
  TagOutlined,
  StarOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import api from '../../services/api';
import dayjs from 'dayjs';

const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

interface ModelConfig {
  id: number;
  config_type: 'global' | 'user' | 'category' | 'grade';
  model_id: number;
  user_id?: number;
  category_id?: number;
  grade_id?: number;
  config_name: string;
  description?: string;
  model_params: Record<string, any>;
  is_active: boolean;
  priority: number;
  created_by: number;
  created_at: string;
  updated_at: string;
  model_name?: string;
  model_api_url?: string;
  creator_name?: string;
  user_name?: string;
  category_name?: string;
  grade_name?: string;
}

interface Model {
  id: number;
  name: string;
  api_url: string;
  is_active: boolean;
  has_api_key: boolean;
}

interface ModelPermissions {
  can_view: boolean;
  can_create: boolean;
  can_update: boolean;
  can_delete: boolean;
  can_config_global: boolean;
  can_config_category: boolean;
  can_config_grade: boolean;
  accessible_models: number[];
  accessible_categories: number[];
  accessible_grades: number[];
}

interface ConfigSummary {
  total_configs: number;
  active_configs: number;
  global_configs: number;
  user_configs: number;
  category_configs: number;
  grade_configs: number;
  models_in_use: Array<{
    model_id: number;
    model_name: string;
    config_count: number;
  }>;
  recent_updates: Array<{
    config_id: number;
    config_name: string;
    config_type: string;
    model_name: string;
    updated_at: string;
    updated_by: string;
  }>;
}

const ModelConfigManagement: React.FC = () => {
  const [configs, setConfigs] = useState<ModelConfig[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [grades, setGrades] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<ModelPermissions | null>(null);
  const [summary, setSummary] = useState<ConfigSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<ModelConfig | null>(null);
  const [activeTab, setActiveTab] = useState('configs');
  
  const [configForm] = Form.useForm();

  useEffect(() => {
    fetchConfigs();
    fetchModels();
    fetchCategories();
    fetchGrades();
    fetchPermissions();
    fetchSummary();
  }, []);

  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const response = await api.get('/knowledge-base/model-configs');
      setConfigs(response.data.items || []);
    } catch (error: any) {
      message.error('获取模型配置失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  const fetchModels = async () => {
    try {
      const response = await api.get('/knowledge-base/llm-models');
      setModels(response.data || []);
    } catch (error: any) {
      message.error('获取模型列表失败: ' + (error.response?.data?.detail || error.message));
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await api.get('/knowledge-base/data-categories');
      setCategories(response.data || []);
    } catch (error: any) {
      console.error('获取分类列表失败:', error);
    }
  };

  const fetchGrades = async () => {
    try {
      const response = await api.get('/knowledge-base/data-grades');
      setGrades(response.data || []);
    } catch (error: any) {
      console.error('获取分级列表失败:', error);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await api.get('/knowledge-base/model-configs/permissions/current');
      setPermissions(response.data);
    } catch (error: any) {
      console.error('获取权限信息失败:', error);
    }
  };

  const fetchSummary = async () => {
    try {
      const response = await api.get('/knowledge-base/model-configs/summary');
      setSummary(response.data);
    } catch (error: any) {
      console.error('获取摘要信息失败:', error);
    }
  };

  const getConfigTypeColor = (type: string) => {
    switch (type) {
      case 'global':
        return 'blue';
      case 'user':
        return 'green';
      case 'category':
        return 'orange';
      case 'grade':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getConfigTypeText = (type: string) => {
    switch (type) {
      case 'global':
        return '全局配置';
      case 'user':
        return '用户配置';
      case 'category':
        return '分类配置';
      case 'grade':
        return '分级配置';
      default:
        return type;
    }
  };

  const getConfigTypeIcon = (type: string) => {
    switch (type) {
      case 'global':
        return <GlobalOutlined />;
      case 'user':
        return <UserOutlined />;
      case 'category':
        return <TagOutlined />;
      case 'grade':
        return <StarOutlined />;
      default:
        return <SettingOutlined />;
    }
  };

  const handleCreateConfig = () => {
    if (!permissions?.can_create) {
      message.error('权限不足：无法创建模型配置');
      return;
    }
    setEditingConfig(null);
    configForm.resetFields();
    setConfigModalVisible(true);
  };

  const handleEditConfig = (config: ModelConfig) => {
    if (!permissions?.can_update) {
      message.error('权限不足：无法编辑模型配置');
      return;
    }
    setEditingConfig(config);
    configForm.setFieldsValue({
      ...config,
      model_params: JSON.stringify(config.model_params, null, 2)
    });
    setConfigModalVisible(true);
  };

  const handleDeleteConfig = async (configId: number) => {
    if (!permissions?.can_delete) {
      message.error('权限不足：无法删除模型配置');
      return;
    }
    
    try {
      await api.delete(`/knowledge-base/model-configs/${configId}`);
      message.success('配置删除成功');
      fetchConfigs();
      fetchSummary();
    } catch (error: any) {
      message.error('删除失败: ' + (error.response?.data?.detail || error.message));
    }
  };

  const handleSaveConfig = async (values: any) => {
    try {
      setLoading(true);
      
      // 解析模型参数
      let modelParams = {};
      if (values.model_params) {
        try {
          modelParams = JSON.parse(values.model_params);
        } catch (e) {
          message.error('模型参数格式错误，请输入有效的JSON');
          return;
        }
      }

      const configData = {
        ...values,
        model_params: modelParams
      };

      if (editingConfig) {
        // 更新配置
        await api.put(`/knowledge-base/model-configs/${editingConfig.id}`, configData);
        message.success('配置更新成功');
      } else {
        // 创建配置
        await api.post('/knowledge-base/model-configs', configData);
        message.success('配置创建成功');
      }

      setConfigModalVisible(false);
      configForm.resetFields();
      fetchConfigs();
      fetchSummary();
    } catch (error: any) {
      message.error('保存失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  const columns: ColumnsType<ModelConfig> = [
    {
      title: '配置名称',
      dataIndex: 'config_name',
      key: 'config_name',
      width: 200,
      render: (text, record) => (
        <Space>
          {getConfigTypeIcon(record.config_type)}
          <a onClick={() => handleEditConfig(record)}>{text}</a>
        </Space>
      ),
    },
    {
      title: '配置类型',
      dataIndex: 'config_type',
      key: 'config_type',
      width: 120,
      render: (type) => (
        <Tag color={getConfigTypeColor(type)} icon={getConfigTypeIcon(type)}>
          {getConfigTypeText(type)}
        </Tag>
      ),
    },
    {
      title: '关联模型',
      dataIndex: 'model_name',
      key: 'model_name',
      width: 150,
      render: (modelName, record) => (
        <Space>
          <ApiOutlined />
          <span>{modelName || '未知模型'}</span>
        </Space>
      ),
    },
    {
      title: '适用范围',
      key: 'scope',
      width: 200,
      render: (_, record) => {
        if (record.config_type === 'global') {
          return <Tag color="blue">全局适用</Tag>;
        } else if (record.config_type === 'user') {
          return <Tag color="green">用户: {record.user_name || '未知'}</Tag>;
        } else if (record.config_type === 'category') {
          return <Tag color="orange">分类: {record.category_name || '未知'}</Tag>;
        } else if (record.config_type === 'grade') {
          return <Tag color="purple">分级: {record.grade_name || '未知'}</Tag>;
        }
        return '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive) => (
        <Switch checked={isActive} disabled size="small" />
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => <Badge count={priority} showZero />,
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditConfig(record)}
              disabled={!permissions?.can_update}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDeleteConfig(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                disabled={!permissions?.can_delete}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  if (!permissions) {
    return <Spin size="large" />;
  }

  if (!permissions.can_view) {
    return (
      <Alert
        message="权限不足"
        description="您没有查看模型配置的权限"
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="配置管理" key="configs">
          <Space style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateConfig}
              disabled={!permissions.can_create}
            >
              新建配置
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={fetchConfigs}
              loading={loading}
            >
              刷新
            </Button>
          </Space>

          <Table
            columns={columns}
            dataSource={configs}
            rowKey="id"
            loading={loading}
            scroll={{ x: 1200 }}
            pagination={{
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        </TabPane>

        <TabPane tab="配置摘要" key="summary">
          {summary && (
            <>
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="总配置数"
                      value={summary.total_configs}
                      prefix={<SettingOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="启用配置"
                      value={summary.active_configs}
                      prefix={<BulbOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="全局配置"
                      value={summary.global_configs}
                      prefix={<GlobalOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="用户配置"
                      value={summary.user_configs}
                      prefix={<UserOutlined />}
                    />
                  </Card>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Card title="使用中的模型">
                    <List
                      dataSource={summary.models_in_use}
                      renderItem={item => (
                        <List.Item>
                          <List.Item.Meta
                            avatar={<ApiOutlined />}
                            title={item.model_name}
                            description={`${item.config_count} 个配置`}
                          />
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="最近更新">
                    <List
                      dataSource={summary.recent_updates}
                      renderItem={item => (
                        <List.Item>
                          <List.Item.Meta
                            title={item.config_name}
                            description={
                              <Space>
                                <Tag color={getConfigTypeColor(item.config_type)}>
                                  {getConfigTypeText(item.config_type)}
                                </Tag>
                                <Text type="secondary">
                                  {dayjs(item.updated_at).format('MM-DD HH:mm')}
                                </Text>
                                <Text type="secondary">by {item.updated_by}</Text>
                              </Space>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>
              </Row>
            </>
          )}
        </TabPane>
      </Tabs>

      {/* 配置编辑模态框 */}
      <Modal
        title={editingConfig ? '编辑模型配置' : '新建模型配置'}
        open={configModalVisible}
        onOk={() => configForm.submit()}
        onCancel={() => {
          setConfigModalVisible(false);
          configForm.resetFields();
        }}
        confirmLoading={loading}
        width={800}
      >
        <Form
          form={configForm}
          layout="vertical"
          onFinish={handleSaveConfig}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="config_name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="请输入配置名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="config_type"
                label="配置类型"
                rules={[{ required: true, message: '请选择配置类型' }]}
              >
                <Select placeholder="请选择配置类型">
                  <Option value="global" disabled={!permissions.can_config_global}>
                    <Space>
                      <GlobalOutlined />
                      全局配置
                    </Space>
                  </Option>
                  <Option value="user">
                    <Space>
                      <UserOutlined />
                      用户配置
                    </Space>
                  </Option>
                  <Option value="category" disabled={!permissions.can_config_category}>
                    <Space>
                      <TagOutlined />
                      分类配置
                    </Space>
                  </Option>
                  <Option value="grade" disabled={!permissions.can_config_grade}>
                    <Space>
                      <StarOutlined />
                      分级配置
                    </Space>
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="model_id"
                label="关联模型"
                rules={[{ required: true, message: '请选择模型' }]}
              >
                <Select placeholder="请选择模型">
                  {models.filter(m => permissions.accessible_models.includes(m.id)).map(model => (
                    <Option key={model.id} value={model.id}>
                      <Space>
                        <ApiOutlined />
                        {model.name}
                        {!model.is_active && <Tag color="red">未激活</Tag>}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请输入优先级' }]}
                tooltip="数字越小优先级越高"
              >
                <InputNumber min={1} max={999} placeholder="100" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="配置描述"
          >
            <TextArea rows={3} placeholder="请输入配置描述（可选）" />
          </Form.Item>

          <Form.Item
            name="model_params"
            label="模型参数"
            tooltip="JSON格式的模型参数，如 temperature、max_tokens 等"
          >
            <TextArea
              rows={6}
              placeholder='{"temperature": 0.7, "max_tokens": 1000}'
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Collapse>
            <Panel header="高级配置" key="advanced">
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.config_type !== currentValues.config_type
                }
              >
                {({ getFieldValue }) => {
                  const configType = getFieldValue('config_type');
                  
                  if (configType === 'category') {
                    return (
                      <Form.Item
                        name="category_id"
                        label="数据分类"
                        rules={[{ required: true, message: '请选择数据分类' }]}
                      >
                        <Select placeholder="请选择数据分类">
                          {categories.filter(c => permissions.accessible_categories.includes(c.id)).map(category => (
                            <Option key={category.id} value={category.id}>
                              {category.name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    );
                  }
                  
                  if (configType === 'grade') {
                    return (
                      <Form.Item
                        name="grade_id"
                        label="数据分级"
                        rules={[{ required: true, message: '请选择数据分级' }]}
                      >
                        <Select placeholder="请选择数据分级">
                          {grades.filter(g => permissions.accessible_grades.includes(g.id)).map(grade => (
                            <Option key={grade.id} value={grade.id}>
                              <Tag color={grade.sensitivity_level <= 2 ? 'red' : grade.sensitivity_level <= 3 ? 'orange' : 'green'}>
                                {grade.name}
                              </Tag>
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    );
                  }
                  
                  return null;
                }}
              </Form.Item>
            </Panel>
          </Collapse>
        </Form>
      </Modal>
    </div>
  );
};

export default ModelConfigManagement;