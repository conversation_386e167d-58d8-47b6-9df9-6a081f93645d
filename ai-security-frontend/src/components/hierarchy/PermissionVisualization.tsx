import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Table,
  Tag,
  Spin,
  Row,
  Col,
  Statistic,
  Select,
  Tabs,
  Tree,
  Empty,
  message,
  Badge,
  Tooltip,
  Progress
} from 'antd';
import {
  EyeOutlined,
  ReloadOutlined,
  UserOutlined,
  SecurityScanOutlined,
  SettingOutlined,
  TeamOutlined,
  KeyOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { hierarchyService } from '../../services/hierarchyService';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface PermissionModule {
  name: string;
  description: string;
}

interface RolePermission {
  role_id: number;
  role_name: string;
  module_permissions: string[];
  keyword_groups: Array<{
    id: number;
    name: string;
    description: string;
  }>;
  data_grade_access: Array<{
    data_category: string;
    max_grade: number;
  }>;
}

interface PermissionMatrixData {
  module: string;
  description: string;
  roles: Record<string, boolean>;
}

const PermissionVisualization: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modules, setModules] = useState<PermissionModule[]>([]);
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>([]);
  const [selectedRole, setSelectedRole] = useState<number | undefined>();
  const [permissionMatrix, setPermissionMatrix] = useState<PermissionMatrixData[]>([]);

  // 获取可用模块
  const fetchAvailableModules = async () => {
    try {
      const response = await fetch('/api/v1/admin/permissions/available-modules', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const data = await response.json();
        setModules(data.modules || []);
      }
    } catch (error) {
      console.error('获取模块列表失败:', error);
    }
  };

  // 获取角色权限信息
  const fetchRolePermissions = async () => {
    try {
      // 首先获取所有角色
      const rolesResponse = await fetch('/api/v1/admin/roles/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (rolesResponse.ok) {
        const roles = await rolesResponse.json();
        const rolePermissionData: RolePermission[] = [];
        
        // 为每个角色获取权限信息
        for (const role of roles) {
          try {
            const [modulePermsResponse, keywordGroupsResponse, dataGradeResponse] = await Promise.all([
              fetch(`/api/v1/admin/permissions/roles/${role.id}/module-permissions`, {
                headers: { 'Authorization': `Bearer ${getAuthToken()}` }
              }),
              fetch(`/api/v1/admin/permissions/roles/${role.id}/keyword-groups`, {
                headers: { 'Authorization': `Bearer ${getAuthToken()}` }
              }),
              fetch(`/api/v1/admin/permissions/roles/${role.id}/data-grade-access`, {
                headers: { 'Authorization': `Bearer ${getAuthToken()}` }
              })
            ]);

            const modulePerms = modulePermsResponse.ok ? await modulePermsResponse.json() : { modules: [] };
            const keywordGroups = keywordGroupsResponse.ok ? await keywordGroupsResponse.json() : { keyword_groups: [] };
            const dataGrade = dataGradeResponse.ok ? await dataGradeResponse.json() : { data_grade_access: [] };

            rolePermissionData.push({
              role_id: role.id,
              role_name: role.name,
              module_permissions: modulePerms.modules || [],
              keyword_groups: keywordGroups.keyword_groups || [],
              data_grade_access: dataGrade.data_grade_access || []
            });
          } catch (error) {
            console.error(`获取角色 ${role.name} 权限失败:`, error);
          }
        }
        
        setRolePermissions(rolePermissionData);
        generatePermissionMatrix(rolePermissionData);
      }
    } catch (error) {
      console.error('获取角色权限失败:', error);
      message.error('获取角色权限失败');
    }
  };

  // 生成权限矩阵
  const generatePermissionMatrix = (rolePerms: RolePermission[]) => {
    const matrix: PermissionMatrixData[] = modules.map(module => {
      const roles: Record<string, boolean> = {};
      rolePerms.forEach(rolePermission => {
        roles[rolePermission.role_name] = rolePermission.module_permissions.includes(module.name);
      });
      return {
        module: module.name,
        description: module.description,
        roles
      };
    });
    setPermissionMatrix(matrix);
  };

  // 获取认证token
  const getAuthToken = () => {
    const authData = localStorage.getItem('auth-storage');
    if (authData) {
      try {
        const parsed = JSON.parse(authData);
        return parsed.state?.token || parsed.token;
      } catch (error) {
        console.error('解析认证token失败:', error);
      }
    }
    return null;
  };

  // 加载所有数据
  const loadAllData = async () => {
    setLoading(true);
    try {
      await fetchAvailableModules();
      await fetchRolePermissions();
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 计算权限统计
  const getPermissionStats = () => {
    const totalModules = modules.length;
    const totalRoles = rolePermissions.length;
    const totalAssignments = rolePermissions.reduce((sum, role) => sum + role.module_permissions.length, 0);
    const avgPermissionsPerRole = totalRoles > 0 ? Math.round(totalAssignments / totalRoles) : 0;
    
    return {
      totalModules,
      totalRoles,
      totalAssignments,
      avgPermissionsPerRole
    };
  };

  // 权限矩阵表格列
  const matrixColumns = [
    {
      title: '功能模块',
      dataIndex: 'module',
      key: 'module',
      width: 200,
      render: (text: string, record: PermissionMatrixData) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    ...rolePermissions.map(role => ({
      title: role.role_name,
      dataIndex: ['roles', role.role_name],
      key: role.role_name,
      width: 120,
      align: 'center' as const,
      render: (hasPermission: boolean) => (
        <Tag 
          icon={hasPermission ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
          color={hasPermission ? 'success' : 'default'}
        >
          {hasPermission ? '有权限' : '无权限'}
        </Tag>
      )
    }))
  ];

  // 角色详情表格列
  const roleDetailColumns = [
    {
      title: '权限类型',
      dataIndex: 'type',
      key: 'type',
      width: 120
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: boolean) => (
        <Tag color={status ? 'success' : 'default'}>
          {status ? '已授权' : '未授权'}
        </Tag>
      )
    }
  ];

  // 获取选中角色的详细数据
  const getSelectedRoleData = () => {
    if (!selectedRole) return [];
    
    const role = rolePermissions.find(r => r.role_id === selectedRole);
    if (!role) return [];

    const data: any[] = [];
    
    // 添加模块权限
    modules.forEach(module => {
      data.push({
        key: `module-${module.name}`,
        type: '功能模块',
        name: module.name,
        description: module.description,
        status: role.module_permissions.includes(module.name)
      });
    });
    
    // 添加关键词组权限
    role.keyword_groups.forEach(group => {
      data.push({
        key: `keyword-${group.id}`,
        type: '关键词组',
        name: group.name,
        description: group.description,
        status: true
      });
    });
    
    // 添加数据分级权限
    role.data_grade_access.forEach(access => {
      data.push({
        key: `data-${access.data_category}`,
        type: '数据分级',
        name: access.data_category,
        description: `最大访问等级: ${access.max_grade}`,
        status: true
      });
    });
    
    return data;
  };

  const stats = getPermissionStats();

  useEffect(() => {
    loadAllData();
  }, []);

  return (
    <div>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <EyeOutlined />
              <span>权限可视化</span>
            </Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadAllData}
              loading={loading}
            >
              刷新数据
            </Button>
          </div>
        }
      >
        <Spin spinning={loading}>
          {/* 权限统计概览 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="功能模块数"
                  value={stats.totalModules}
                  prefix={<SettingOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="角色数量"
                  value={stats.totalRoles}
                  prefix={<TeamOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="权限分配总数"
                  value={stats.totalAssignments}
                  prefix={<KeyOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="平均权限数/角色"
                  value={stats.avgPermissionsPerRole}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          <Tabs defaultActiveKey="matrix">
            <TabPane 
              tab={
                <Space>
                  <DatabaseOutlined />
                  权限矩阵
                </Space>
              } 
              key="matrix"
            >
              {permissionMatrix.length > 0 ? (
                <Table
                  columns={matrixColumns}
                  dataSource={permissionMatrix}
                  rowKey="module"
                  pagination={false}
                  scroll={{ x: true }}
                  size="middle"
                />
              ) : (
                <Empty description="暂无权限数据" />
              )}
            </TabPane>

            <TabPane 
              tab={
                <Space>
                  <UserOutlined />
                  角色详情
                </Space>
              } 
              key="role-detail"
            >
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>选择角色:</Text>
                    <Select
                      style={{ width: '100%', marginTop: 8 }}
                      placeholder="请选择角色"
                      value={selectedRole}
                      onChange={setSelectedRole}
                    >
                      {rolePermissions.map(role => (
                        <Option key={role.role_id} value={role.role_id}>
                          <Space>
                            <UserOutlined />
                            <span>{role.role_name}</span>
                            <Badge count={role.module_permissions.length} size="small" />
                          </Space>
                        </Option>
                      ))}
                    </Select>
                  </div>
                </Col>
                <Col span={16}>
                  {selectedRole ? (
                    <Table
                      columns={roleDetailColumns}
                      dataSource={getSelectedRoleData()}
                      pagination={{ pageSize: 10 }}
                      size="small"
                    />
                  ) : (
                    <Empty description="请选择角色查看详情" />
                  )}
                </Col>
              </Row>
            </TabPane>

            <TabPane 
              tab={
                <Space>
                  <SecurityScanOutlined />
                  权限覆盖率
                </Space>
              } 
              key="coverage"
            >
              <Row gutter={16}>
                {modules.map(module => {
                  const grantedRoles = rolePermissions.filter(role => 
                    role.module_permissions.includes(module.name)
                  ).length;
                  const coveragePercent = rolePermissions.length > 0 
                    ? Math.round((grantedRoles / rolePermissions.length) * 100)
                    : 0;
                  
                  return (
                    <Col span={12} key={module.name} style={{ marginBottom: 16 }}>
                      <Card size="small">
                        <div style={{ marginBottom: 8 }}>
                          <Text strong>{module.name}</Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {module.description}
                          </Text>
                        </div>
                        <Progress 
                          percent={coveragePercent}
                          format={() => `${grantedRoles}/${rolePermissions.length}`}
                          strokeColor={
                            coveragePercent >= 80 ? '#52c41a' :
                            coveragePercent >= 50 ? '#faad14' : '#f5222d'
                          }
                        />
                        <div style={{ marginTop: 8, fontSize: '12px' }}>
                          <Text type="secondary">
                            覆盖率: {coveragePercent}% ({grantedRoles}/{rolePermissions.length} 个角色)
                          </Text>
                        </div>
                      </Card>
                    </Col>
                  );
                })}
              </Row>
            </TabPane>
          </Tabs>
        </Spin>
      </Card>
    </div>
  );
};

export default PermissionVisualization; 