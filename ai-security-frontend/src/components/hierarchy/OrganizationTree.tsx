import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Tree,
  Avatar,
  Tag,
  Tooltip,
  Spin,
  Empty,
  Dropdown,
  Modal,
  Select,
  message,
  Row,
  Col,
  Badge
} from 'antd';
import {
  ReloadOutlined,
  NodeIndexOutlined,
  UserOutlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  TeamOutlined,
  CrownOutlined
} from '@ant-design/icons';
import { hierarchyService, User } from '../../services/hierarchyService';

const { Title, Text } = Typography;
const { Option } = Select;

interface OrganizationTreeProps {
  onRefresh?: () => void;
}

interface TreeNode {
  key: string;
  title: React.ReactNode;
  children?: TreeNode[];
  user: User;
}

const OrganizationTree: React.FC<OrganizationTreeProps> = ({ onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [organizationData, setOrganizationData] = useState<User[]>([]);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [managerModalVisible, setManagerModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [availableManagers, setAvailableManagers] = useState<User[]>([]);
  const [selectedManagerId, setSelectedManagerId] = useState<number | undefined>();

  // 获取组织架构数据
  const fetchOrganizationData = async () => {
    setLoading(true);
    try {
      const data = await hierarchyService.getOrganizationTree();
      setOrganizationData(data);
      convertToTreeData(data);
      
      // 默认展开所有节点
      const allKeys = extractAllKeys(data);
      setExpandedKeys(allKeys);
    } catch (error) {
      console.error('获取组织架构数据失败:', error);
      message.error('获取组织架构数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 提取所有节点的key用于展开
  const extractAllKeys = (users: User[]): string[] => {
    const keys: string[] = [];
    const traverse = (userList: User[]) => {
      userList.forEach(user => {
        keys.push(user.id.toString());
        if (user.subordinates && user.subordinates.length > 0) {
          traverse(user.subordinates);
        }
      });
    };
    traverse(users);
    return keys;
  };

  // 转换数据为Tree组件需要的格式
  const convertToTreeData = (users: User[]): void => {
    const convertUser = (user: User): TreeNode => {
      const roleColor = getRoleColor(user.role_name);
      const levelColor = getLevelColor(user.level_name);
      
      return {
        key: user.id.toString(),
        user: user,
        title: (
          <div className="tree-node-content" style={{ padding: '4px 0' }}>
            <Row align="middle" gutter={8}>
              <Col>
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  style={{ backgroundColor: user.is_active ? '#1890ff' : '#d9d9d9' }}
                />
              </Col>
              <Col flex="auto">
                <div>
                  <Space size="small">
                    <Text strong>{user.username}</Text>
                                         {!user.is_active && (
                       <Tag color="red">已禁用</Tag>
                     )}
                  </Space>
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  <Space size="small">
                    {user.email && <Text type="secondary">{user.email}</Text>}
                                         {user.role_name && (
                       <Tag color={roleColor}>{user.role_name}</Tag>
                     )}
                     {user.level_name && (
                       <Tag color={levelColor}>{user.level_name}</Tag>
                     )}
                  </Space>
                </div>
              </Col>
              <Col>
                <Space size="small">
                  {user.subordinates && user.subordinates.length > 0 && (
                    <Badge count={user.subordinates.length} size="small">
                      <TeamOutlined style={{ color: '#1890ff' }} />
                    </Badge>
                  )}
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'setManager',
                          label: '设置上级',
                          icon: <CrownOutlined />,
                          onClick: () => handleSetManager(user)
                        },
                        {
                          key: 'viewSubordinates',
                          label: '查看下属',
                          icon: <TeamOutlined />,
                          disabled: !user.subordinates || user.subordinates.length === 0
                        }
                      ]
                    }}
                    trigger={['click']}
                    placement="bottomRight"
                  >
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<SettingOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Dropdown>
                </Space>
              </Col>
            </Row>
          </div>
        ),
        children: user.subordinates && user.subordinates.length > 0 
          ? user.subordinates.map(convertUser)
          : undefined
      };
    };

    const treeNodes = users.map(convertUser);
    setTreeData(treeNodes);
  };

  // 获取角色颜色
  const getRoleColor = (roleName?: string) => {
    switch (roleName) {
      case '管理员':
        return 'red';
      case '部门经理':
        return 'orange';
      case '普通用户':
        return 'blue';
      default:
        return 'default';
    }
  };

  // 获取等级颜色  
  const getLevelColor = (levelName?: string) => {
    switch (levelName) {
      case '超级管理员':
        return 'purple';
      case '高级用户':
        return 'green';
      case '中级用户':
        return 'cyan';
      case '初级用户':
        return 'gray';
      default:
        return 'default';
    }
  };

  // 处理设置上级
  const handleSetManager = async (user: User) => {
    setSelectedUser(user);
    
    // 获取可选的上级用户（排除自己和下属）
    const flatUsers = flattenUsers(organizationData);
    const subordinateIds = getAllSubordinateIds(user, flatUsers);
    const available = flatUsers.filter(u => 
      u.id !== user.id && 
      !subordinateIds.includes(u.id)
    );
    
    setAvailableManagers(available);
    setSelectedManagerId(user.manager_id || undefined);
    setManagerModalVisible(true);
  };

  // 获取所有下属的ID（包括间接下属）
  const getAllSubordinateIds = (user: User, allUsers: User[]): number[] => {
    const ids: number[] = [];
    const getSubordinates = (userId: number) => {
      allUsers.forEach(u => {
        if (u.manager_id === userId) {
          ids.push(u.id);
          getSubordinates(u.id);
        }
      });
    };
    getSubordinates(user.id);
    return ids;
  };

  // 扁平化用户数据
  const flattenUsers = (users: User[]): User[] => {
    const result: User[] = [];
    const traverse = (userList: User[]) => {
      userList.forEach(user => {
        result.push(user);
        if (user.subordinates && user.subordinates.length > 0) {
          traverse(user.subordinates);
        }
      });
    };
    traverse(users);
    return result;
  };

  // 确认设置上级
  const handleConfirmSetManager = async () => {
    if (!selectedUser) return;

    try {
      await hierarchyService.setUserManager(selectedUser.id, {
        manager_id: selectedManagerId || null
      });
      message.success('设置上级成功');
      setManagerModalVisible(false);
      fetchOrganizationData(); // 重新加载数据
    } catch (error) {
      console.error('设置上级失败:', error);
      message.error('设置上级失败');
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchOrganizationData();
    onRefresh?.();
  };

  // 组件挂载时加载数据
  useEffect(() => {
    fetchOrganizationData();
  }, []);

  return (
    <div>
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <NodeIndexOutlined />
              <span>组织架构树</span>
              <Badge count={organizationData.length} showZero color="#108ee9" />
            </Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </div>
        }
        bodyStyle={{ padding: '16px' }}
      >
        <Spin spinning={loading}>
          {treeData.length > 0 ? (
            <Tree
              treeData={treeData}
              expandedKeys={expandedKeys}
              selectedKeys={selectedKeys}
              onExpand={(keys) => setExpandedKeys(keys as string[])}
              onSelect={(keys) => setSelectedKeys(keys as string[])}
              showLine={{ showLeafIcon: false }}
              blockNode
              style={{ fontSize: '14px' }}
            />
          ) : (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无组织架构数据"
              style={{ padding: '40px 0' }}
            >
              <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh}>
                重新加载
              </Button>
            </Empty>
          )}
        </Spin>
      </Card>

      {/* 设置上级模态框 */}
      <Modal
        title="设置上级"
        open={managerModalVisible}
        onOk={handleConfirmSetManager}
        onCancel={() => setManagerModalVisible(false)}
        okText="确认"
        cancelText="取消"
      >
        {selectedUser && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>用户: {selectedUser.username}</Text>
              {selectedUser.email && (
                <Text type="secondary" style={{ marginLeft: 8 }}>
                  ({selectedUser.email})
                </Text>
              )}
            </div>
            <div>
              <Text>选择上级:</Text>
              <Select
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择上级用户"
                value={selectedManagerId}
                onChange={setSelectedManagerId}
                allowClear
              >
                {availableManagers.map(manager => (
                  <Option key={manager.id} value={manager.id}>
                    <Space>
                      <Avatar size="small" icon={<UserOutlined />} />
                      <span>{manager.username}</span>
                                             {manager.role_name && (
                         <Tag color={getRoleColor(manager.role_name)}>
                           {manager.role_name}
                         </Tag>
                       )}
                    </Space>
                  </Option>
                ))}
              </Select>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrganizationTree; 