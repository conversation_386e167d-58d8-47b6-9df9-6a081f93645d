import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Table,
  Tag,
  Spin,
  Row,
  Col,
  Statistic,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Badge,
  Tooltip,
  Divider,
  Alert
} from 'antd';
import {
  ApiOutlined,
  ReloadOutlined,
  UserOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { hierarchyService, ModelAssignment } from '../../services/hierarchyService';

const { Title, Text } = Typography;
const { Option } = Select;

interface User {
  id: number;
  username: string;
  email: string;
  role_name?: string;
  level_name?: string;
  is_active: boolean;
}

interface LLMModel {
  id: number;
  name: string;
  provider: string;
  model_name: string;
  api_endpoint: string;
  is_active: boolean;
}

interface ModelAssignmentManagementProps {
  onRefresh?: () => void;
}

const ModelAssignmentManagement: React.FC<ModelAssignmentManagementProps> = ({ onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [models, setModels] = useState<LLMModel[]>([]);
  const [assignments, setAssignments] = useState<ModelAssignment[]>([]);
  const [selectedUser, setSelectedUser] = useState<number | undefined>();
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [selectedModels, setSelectedModels] = useState<number[]>([]);
  const [defaultModel, setDefaultModel] = useState<number | undefined>();
  const [form] = Form.useForm();

  // 获取认证token
  const getAuthToken = () => {
    const authData = localStorage.getItem('auth-storage');
    if (authData) {
      try {
        const parsed = JSON.parse(authData);
        return parsed.state?.token || parsed.token;
      } catch (error) {
        console.error('解析认证token失败:', error);
      }
    }
    return null;
  };

  // 获取所有用户
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/v1/admin/users/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    }
  };

  // 获取所有模型
  const fetchModels = async () => {
    try {
      const response = await fetch('/api/v1/admin/llm-models/', {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const data = await response.json();
        setModels(data.filter((model: LLMModel) => model.is_active));
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
    }
  };

  // 获取所有模型分配
  const fetchAssignments = async () => {
    try {
      const data = await hierarchyService.getAllModelAssignments();
      setAssignments(data);
    } catch (error) {
      console.error('获取模型分配失败:', error);
    }
  };

  // 获取用户的模型分配
  const fetchUserModels = async (userId: number) => {
    try {
      const data = await hierarchyService.getUserModels(userId);
      return data;
    } catch (error) {
      console.error('获取用户模型失败:', error);
      return [];
    }
  };

  // 加载所有数据
  const loadAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchUsers(),
        fetchModels(),
        fetchAssignments()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开分配模型对话框
  const handleAssignModels = async (userId: number) => {
    setSelectedUser(userId);
    try {
      const userModels = await fetchUserModels(userId);
      // 安全地提取模型ID，确保model对象存在
      const assignedModelIds = userModels
        .filter((assignment: any) => assignment.model && assignment.model.id)
        .map((assignment: any) => assignment.model.id);
      
      const defaultAssignment = userModels.find((assignment: any) => assignment.is_default);
      const defaultModelId = defaultAssignment?.model?.id;
      
      setSelectedModels(assignedModelIds);
      setDefaultModel(defaultModelId);
      setAssignModalVisible(true);
    } catch (error) {
      console.error('获取用户模型失败:', error);
      message.error('获取用户模型失败');
    }
  };

  // 确认分配模型
  const handleConfirmAssign = async () => {
    if (!selectedUser) return;

    try {
      await hierarchyService.assignModelsToUser(selectedUser, {
        model_ids: selectedModels,
        default_model_id: defaultModel
      });
      
      message.success('模型分配成功');
      setAssignModalVisible(false);
      await fetchAssignments();
    } catch (error) {
      console.error('分配模型失败:', error);
      message.error('分配模型失败');
    }
  };

  // 设置默认模型
  const handleSetDefault = async (userId: number, modelId: number) => {
    try {
      await hierarchyService.setUserDefaultModel(userId, modelId);
      message.success('设置默认模型成功');
      await fetchAssignments();
    } catch (error) {
      console.error('设置默认模型失败:', error);
      message.error('设置默认模型失败');
    }
  };

  // 撤销模型分配
  const handleRevokeAssignment = async (userId: number, modelId: number) => {
    try {
      await hierarchyService.revokeModelAssignment(userId, modelId);
      message.success('撤销分配成功');
      await fetchAssignments();
    } catch (error) {
      console.error('撤销分配失败:', error);
      message.error('撤销分配失败');
    }
  };

  // 计算统计数据
  const getStats = () => {
    const totalUsers = users?.length || 0;
    const totalModels = models?.length || 0;
    const totalAssignments = assignments?.length || 0;
    const usersWithModels = assignments?.length > 0 
      ? new Set(assignments.filter(a => a.user_id).map(a => a.user_id)).size 
      : 0;
    
    return {
      totalUsers,
      totalModels,
      totalAssignments,
      usersWithModels
    };
  };

  // 用户表格列
  const userColumns = [
    {
      title: '用户',
      key: 'user',
      render: (record: User) => (
        <div>
          <div>
            <Text strong>{record.username}</Text>
            {!record.is_active && <Tag color="red" style={{ marginLeft: 8 }}>已禁用</Tag>}
          </div>
          {record.email && (
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.email}</Text>
          )}
        </div>
      )
    },
    {
      title: '角色/等级',
      key: 'role',
      render: (record: User) => (
        <Space direction="vertical" size="small">
          {record.role_name && <Tag color="blue">{record.role_name}</Tag>}
          {record.level_name && <Tag color="green">{record.level_name}</Tag>}
        </Space>
      )
    },
    {
      title: '已分配模型',
      key: 'models',
      render: (record: User) => {
        const userAssignments = assignments?.filter(a => a.user_id === record.id) || [];
        return (
          <div>
            <Badge count={userAssignments.length} />
            {userAssignments.map(assignment => {
              // 确保assignment和model存在
              if (!assignment || !assignment.model) {
                return null;
              }
              
              return (
                <div key={assignment.id} style={{ margin: '2px 0' }}>
                  <Space size="small">
                    <Tag 
                      color={assignment.is_default ? 'gold' : 'default'}
                      icon={assignment.is_default ? <StarFilled /> : undefined}
                    >
                      {assignment.model?.name || '未知模型'}
                    </Tag>
                    <Tooltip title="设置为默认">
                      <Button
                        type="text"
                        size="small"
                        icon={<StarOutlined />}
                        disabled={assignment.is_default}
                        onClick={() => handleSetDefault(record.id, assignment.model_id)}
                      />
                    </Tooltip>
                    <Popconfirm
                      title="确认撤销该模型分配？"
                      onConfirm={() => handleRevokeAssignment(record.id, assignment.model_id)}
                    >
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                      />
                    </Popconfirm>
                  </Space>
                </div>
              );
            })}
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: User) => (
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={() => handleAssignModels(record.id)}
        >
          分配模型
        </Button>
      )
    }
  ];

  // 模型分配统计列
  const modelStatsColumns = [
    {
      title: '模型',
      key: 'model',
      render: (record: LLMModel) => (
        <div>
          <Text strong>{record.name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.provider} - {record.model_name}
          </Text>
        </div>
      )
    },
    {
      title: '分配用户数',
      key: 'assignments',
      render: (record: LLMModel) => {
        const modelAssignments = assignments?.filter(a => a.model_id === record.id) || [];
        const defaultAssignments = modelAssignments.filter(a => a.is_default);
        
        return (
          <Space direction="vertical" size="small">
            <Badge count={modelAssignments.length} />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {defaultAssignments.length} 个默认分配
            </Text>
          </Space>
        );
      }
    },
    {
      title: '使用率',
      key: 'usage',
      render: (record: LLMModel) => {
        const modelAssignments = assignments?.filter(a => a.model_id === record.id) || [];
        const totalUsers = users?.length || 0;
        const usagePercent = totalUsers > 0 ? Math.round((modelAssignments.length / totalUsers) * 100) : 0;
        
        return (
          <div>
            <Text>{usagePercent}%</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {modelAssignments.length}/{totalUsers} 用户
            </Text>
          </div>
        );
      }
    }
  ];

  const stats = getStats();

  useEffect(() => {
    loadAllData();
  }, []);

  return (
    <div>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <ApiOutlined />
              <span>模型分配管理</span>
            </Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadAllData}
              loading={loading}
            >
              刷新数据
            </Button>
          </div>
        }
      >
        <Spin spinning={loading}>
          {/* 统计概览 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="用户总数"
                  value={stats.totalUsers}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="可用模型数"
                  value={stats.totalModels}
                  prefix={<ApiOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="分配总数"
                  value={stats.totalAssignments}
                  prefix={<SettingOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="已分配用户"
                  value={stats.usersWithModels}
                  suffix={`/ ${stats.totalUsers}`}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={16}>
            {/* 用户模型分配 */}
            <Col span={16}>
              <Card 
                title="用户模型分配" 
                size="small"
                extra={
                  <Text type="secondary">
                    共 {users.length} 个用户
                  </Text>
                }
              >
                <Table
                  columns={userColumns}
                  dataSource={users}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                  size="small"
                />
              </Card>
            </Col>

            {/* 模型使用统计 */}
            <Col span={8}>
              <Card title="模型使用统计" size="small">
                <Table
                  columns={modelStatsColumns}
                  dataSource={models}
                  rowKey="id"
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </Spin>
      </Card>

      {/* 分配模型对话框 */}
      <Modal
        title="分配模型"
        open={assignModalVisible}
        onOk={handleConfirmAssign}
        onCancel={() => setAssignModalVisible(false)}
        okText="确认分配"
        cancelText="取消"
        width={600}
      >
        {selectedUser && (
          <Form form={form} layout="vertical">
            <Alert
              message="模型分配说明"
              description="为用户分配可访问的LLM模型，并设置默认模型。用户只能使用已分配的模型进行对话。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Form.Item label="用户">
              <Text strong>
                {users.find(u => u.id === selectedUser)?.username}
              </Text>
            </Form.Item>

            <Form.Item label="选择模型" required>
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="请选择要分配的模型"
                value={selectedModels}
                onChange={setSelectedModels}
              >
                {models.map(model => (
                  <Option key={model.id} value={model.id}>
                    <Space>
                      <Text>{model.name}</Text>
                                             <Tag>{model.provider}</Tag>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="默认模型">
              <Select
                style={{ width: '100%' }}
                placeholder="请选择默认模型"
                value={defaultModel}
                onChange={setDefaultModel}
                allowClear
              >
                {models
                  .filter(model => selectedModels.includes(model.id))
                  .map(model => (
                    <Option key={model.id} value={model.id}>
                      <Space>
                        <StarOutlined />
                        <Text>{model.name}</Text>
                      </Space>
                    </Option>
                  ))}
              </Select>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default ModelAssignmentManagement; 