import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Space,
  Button,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Tooltip,
  message,
  Drawer,
  Descriptions,
  Timeline
} from 'antd';
import {
  AuditOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  EyeOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface AuditLog {
  id: number;
  user_id?: number;
  username?: string;
  action_type: string;
  target_entity?: string;
  target_id?: number;
  details?: string;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  result?: string;
  created_at: string;
}

interface AuditLogStats {
  total_logs: number;
  today_logs: number;
  successful_actions: number;
  failed_actions: number;
  blocked_actions: number;
  top_actions: Array<{ action_type: string; count: number }>;
  top_users: Array<{ username: string; count: number }>;
}

// 模拟API调用 - 后续需要替换为真实的API
const mockAuditApi = {
  getLogs: async (params: any): Promise<AuditLog[]> => {
    // 模拟数据
    return [
      {
        id: 1,
        user_id: 1,
        username: 'admin',
        action_type: 'LOGIN',
        target_entity: undefined,
        target_id: undefined,
        details: '管理员登录系统',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        session_id: 'sess_123456',
        result: 'SUCCESS',
        created_at: dayjs().subtract(1, 'hour').toISOString()
      },
      {
        id: 2,
        user_id: 2,
        username: 'user1',
        action_type: 'UPDATE_USER',
        target_entity: 'users',
        target_id: 3,
        details: '更新用户角色权限',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        session_id: 'sess_789012',
        result: 'SUCCESS',
        created_at: dayjs().subtract(2, 'hours').toISOString()
      },
      {
        id: 3,
        user_id: 1,
        username: 'admin',
        action_type: 'MESSAGE_BLOCKED',
        target_entity: 'messages',
        target_id: 15,
        details: '检测到恶意内容，消息被阻止',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        session_id: 'sess_123456',
        result: 'BLOCKED',
        created_at: dayjs().subtract(3, 'hours').toISOString()
      }
    ];
  },

  getStats: async (): Promise<AuditLogStats> => {
    return {
      total_logs: 1247,
      today_logs: 45,
      successful_actions: 1180,
      failed_actions: 32,
      blocked_actions: 35,
      top_actions: [
        { action_type: 'LOGIN', count: 324 },
        { action_type: 'SEND_MESSAGE', count: 256 },
        { action_type: 'UPDATE_USER', count: 128 },
        { action_type: 'MESSAGE_BLOCKED', count: 89 },
        { action_type: 'PERMISSION_DENIED', count: 67 }
      ],
      top_users: [
        { username: 'admin', count: 456 },
        { username: 'user1', count: 234 },
        { username: 'user2', count: 189 },
        { username: 'manager', count: 156 },
        { username: 'operator', count: 123 }
      ]
    };
  },

  getActionTypes: async (): Promise<string[]> => {
    return [
      'LOGIN', 'LOGOUT', 'CREATE_USER', 'UPDATE_USER', 'DELETE_USER',
      'CREATE_ROLE', 'UPDATE_ROLE', 'DELETE_ROLE', 'SEND_MESSAGE',
      'MESSAGE_BLOCKED', 'KEYWORD_DETECTION', 'PERMISSION_DENIED',
      'SESSION_CREATED', 'SESSION_TERMINATED', 'SECURITY_POLICY_UPDATED'
    ];
  }
};

const PermissionAuditLog: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [stats, setStats] = useState<AuditLogStats | null>(null);
  const [actionTypes, setActionTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  
  // 筛选参数
  const [searchText, setSearchText] = useState('');
  const [actionTypeFilter, setActionTypeFilter] = useState<string | undefined>();
  const [resultFilter, setResultFilter] = useState<string | undefined>();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);

  // 获取审计日志数据
  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = {
        action_type: actionTypeFilter,
        result: resultFilter,
        start_date: dateRange?.[0]?.toISOString(),
        end_date: dateRange?.[1]?.toISOString(),
        limit: 100
      };
      
      const data = await mockAuditApi.getLogs(params);
      setLogs(data);
    } catch (error) {
      console.error('获取审计日志失败:', error);
      message.error('获取审计日志失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const data = await mockAuditApi.getStats();
      setStats(data);
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  };

  // 获取操作类型列表
  const fetchActionTypes = async () => {
    try {
      const data = await mockAuditApi.getActionTypes();
      setActionTypes(data);
    } catch (error) {
      console.error('获取操作类型失败:', error);
    }
  };

  useEffect(() => {
    fetchLogs();
    fetchStats();
    fetchActionTypes();
  }, [actionTypeFilter, resultFilter, dateRange]);

  // 获取结果状态标签
  const getResultTag = (result?: string) => {
    const tagProps = {
      SUCCESS: { color: 'success', icon: <CheckCircleOutlined /> },
      FAILED: { color: 'error', icon: <CloseCircleOutlined /> },
      BLOCKED: { color: 'warning', icon: <ExclamationCircleOutlined /> }
    };

    const props = tagProps[result as keyof typeof tagProps] || { color: 'default', icon: null };
    
    return (
      <Tag color={props.color} icon={props.icon}>
        {result || '未知'}
      </Tag>
    );
  };

  // 获取操作类型标签
  const getActionTypeTag = (actionType: string) => {
    const colorMap: Record<string, string> = {
      LOGIN: 'blue',
      LOGOUT: 'cyan',
      CREATE_USER: 'green',
      UPDATE_USER: 'orange',
      DELETE_USER: 'red',
      SEND_MESSAGE: 'purple',
      MESSAGE_BLOCKED: 'volcano',
      PERMISSION_DENIED: 'magenta'
    };
    
    return (
      <Tag color={colorMap[actionType] || 'default'}>
        {actionType}
      </Tag>
    );
  };

  // 查看日志详情
  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setDrawerVisible(true);
  };

  // 表格列定义
  const columns: ColumnsType<AuditLog> = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (time: string) => (
        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
          <Space>
            <ClockCircleOutlined />
            {dayjs(time).format('MM-DD HH:mm')}
          </Space>
        </Tooltip>
      ),
      sorter: (a, b) => dayjs(a.created_at).valueOf() - dayjs(b.created_at).valueOf(),
      defaultSortOrder: 'descend'
    },
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      render: (username?: string) => (
        <Space>
          <UserOutlined />
          {username || '系统'}
        </Space>
      )
    },
    {
      title: '操作类型',
      dataIndex: 'action_type',
      key: 'action_type',
      width: 140,
      render: (actionType: string) => getActionTypeTag(actionType)
    },
    {
      title: '目标',
      key: 'target',
      width: 120,
      render: (_, record) => {
        if (record.target_entity && record.target_id) {
          return `${record.target_entity}#${record.target_id}`;
        }
        if (record.target_entity) {
          return record.target_entity;
        }
        return '-';
      }
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 100,
      render: (result?: string) => getResultTag(result)
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address',
      width: 130,
      render: (ip?: string) => ip || '-'
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
      render: (details?: string) => details || '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetails(record)}
        >
          详情
        </Button>
      )
    }
  ];

  return (
    <div>
      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title="总日志数"
                value={stats.total_logs}
                prefix={<AuditOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title="今日日志"
                value={stats.today_logs}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title="成功操作"
                value={stats.successful_actions}
                valueStyle={{ color: '#3f8600' }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title="失败操作"
                value={stats.failed_actions}
                valueStyle={{ color: '#cf1322' }}
                prefix={<CloseCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Statistic
                title="阻止操作"
                value={stats.blocked_actions}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card size="small">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchLogs();
                  fetchStats();
                }}
                style={{ width: '100%', height: '64px' }}
              >
                刷新数据
              </Button>
            </Card>
          </Col>
        </Row>
      )}

      {/* 筛选条件 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Search
              placeholder="搜索用户名或详情"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={fetchLogs}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="操作类型"
              allowClear
              style={{ width: '100%' }}
              value={actionTypeFilter}
              onChange={setActionTypeFilter}
            >
              {actionTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="结果状态"
              allowClear
              style={{ width: '100%' }}
              value={resultFilter}
              onChange={setResultFilter}
            >
              <Option value="SUCCESS">成功</Option>
              <Option value="FAILED">失败</Option>
              <Option value="BLOCKED">阻止</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              style={{ width: '100%' }}
              showTime
              value={dateRange}
              onChange={setDateRange}
              placeholder={['开始时间', '结束时间']}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                icon={<SearchOutlined />}
                onClick={fetchLogs}
              >
                搜索
              </Button>
              <Button
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchText('');
                  setActionTypeFilter(undefined);
                  setResultFilter(undefined);
                  setDateRange(null);
                }}
              >
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 审计日志表格 */}
      <Card
        title={
          <Space>
            <AuditOutlined />
            <span>审计日志</span>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            total: logs.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 日志详情抽屉 */}
      <Drawer
        title="审计日志详情"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={500}
      >
        {selectedLog && (
          <div>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="日志ID">{selectedLog.id}</Descriptions.Item>
              <Descriptions.Item label="时间">
                {dayjs(selectedLog.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="用户">
                {selectedLog.username || '系统'}
              </Descriptions.Item>
              <Descriptions.Item label="操作类型">
                {getActionTypeTag(selectedLog.action_type)}
              </Descriptions.Item>
              <Descriptions.Item label="目标实体">
                {selectedLog.target_entity || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="目标ID">
                {selectedLog.target_id || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="结果">
                {getResultTag(selectedLog.result)}
              </Descriptions.Item>
              <Descriptions.Item label="IP地址">
                {selectedLog.ip_address || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="会话ID">
                {selectedLog.session_id || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="用户代理">
                <div style={{ wordBreak: 'break-all', fontSize: '12px' }}>
                  {selectedLog.user_agent || '-'}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="详细描述">
                {selectedLog.details || '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default PermissionAuditLog; 