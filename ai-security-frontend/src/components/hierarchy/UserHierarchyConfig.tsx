import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Table,
  Tag,
  Spin,
  Row,
  Col,
  Statistic,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Badge,
  Tooltip,
  Divider,
  Alert,
  Transfer,
  Tabs
} from 'antd';
import {
  SettingOutlined,
  ReloadOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SwapOutlined,
  BulbOutlined,
  AuditOutlined
} from '@ant-design/icons';
import type { Key } from 'react';
import { hierarchyService, User } from '../../services/hierarchyService';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface UserHierarchyConfigProps {
  onRefresh?: () => void;
}

interface HierarchyValidation {
  valid: boolean;
  issues: string[];
  total_users: number;
  users_with_managers: number;
  root_users: number;
}

interface BatchOperation {
  userIds: number[];
  managerId: number | null;
}

const UserHierarchyConfig: React.FC<UserHierarchyConfigProps> = ({ onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [validationData, setValidationData] = useState<HierarchyValidation | null>(null);
  const [selectedManager, setSelectedManager] = useState<number | null>(null);
  const [transferData, setTransferData] = useState<any[]>([]);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  // 获取认证token
  const getAuthToken = () => {
    const authData = localStorage.getItem('auth-storage');
    if (authData) {
      try {
        const parsed = JSON.parse(authData);
        return parsed.state?.token || parsed.token;
      } catch (error) {
        console.error('解析认证token失败:', error);
      }
    }
    return null;
  };

  // 获取组织架构数据
  const fetchOrganizationData = async () => {
    try {
      const data = await hierarchyService.getOrganizationTree();
      const flatUsers = flattenUsers(data);
      setUsers(flatUsers);
      
      // 准备Transfer组件数据
      const transferItems = flatUsers.map(user => ({
        key: user.id.toString(),
        title: `${user.username}${user.email ? ` (${user.email})` : ''}`,
        description: `${user.role_name || ''} - ${user.level_name || ''}`,
        disabled: !user.is_active
      }));
      setTransferData(transferItems);
    } catch (error) {
      console.error('获取组织架构数据失败:', error);
      message.error('获取组织架构数据失败');
    }
  };

  // 扁平化用户数据
  const flattenUsers = (users: User[]): User[] => {
    const result: User[] = [];
    const traverse = (userList: User[]) => {
      userList.forEach(user => {
        result.push(user);
        if (user.subordinates && user.subordinates.length > 0) {
          traverse(user.subordinates);
        }
      });
    };
    traverse(users);
    return result;
  };

  // 验证层级结构
  const validateHierarchy = async () => {
    try {
      const validation = await hierarchyService.validateHierarchy();
      setValidationData(validation);
      return validation;
    } catch (error) {
      console.error('验证层级结构失败:', error);
      message.error('验证层级结构失败');
      return null;
    }
  };

  // 批量设置上级
  const handleBatchSetManager = () => {
    if (selectedUsers.length === 0) {
      message.warning('请先选择要设置的用户');
      return;
    }
    setBatchModalVisible(true);
  };

  // 确认批量设置
  const confirmBatchOperation = async () => {
    if (selectedUsers.length === 0) return;

    try {
      setLoading(true);
      
      // 串行处理每个用户，避免并发冲突
      for (const userId of selectedUsers) {
        await hierarchyService.setUserManager(userId, {
          manager_id: selectedManager
        });
      }
      
      message.success(`成功为 ${selectedUsers.length} 个用户设置上级`);
      setBatchModalVisible(false);
      setSelectedUsers([]);
      setSelectedManager(null);
      await fetchOrganizationData();
    } catch (error) {
      console.error('批量设置失败:', error);
      message.error('批量设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取可选的上级列表（排除选中的用户及其下属）
  const getAvailableManagers = () => {
    return users.filter(user => 
      !selectedUsers.includes(user.id) && 
      user.is_active
    );
  };

  // 获取统计数据
  const getStats = () => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.is_active).length;
    const usersWithManagers = users.filter(u => u.manager_id).length;
    const rootUsers = users.filter(u => !u.manager_id).length;
    
    return {
      totalUsers,
      activeUsers,
      usersWithManagers,
      rootUsers
    };
  };

  // 用户配置表格列
  const configColumns = [
    {
      title: '用户',
      key: 'user',
      render: (record: User) => (
        <div>
          <div>
            <Text strong>{record.username}</Text>
            {!record.is_active && <Tag color="red" style={{ marginLeft: 8 }}>已禁用</Tag>}
          </div>
          {record.email && (
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.email}</Text>
          )}
        </div>
      )
    },
    {
      title: '角色/等级',
      key: 'role',
      render: (record: User) => (
        <Space direction="vertical" size="small">
          {record.role_name && <Tag color="blue">{record.role_name}</Tag>}
          {record.level_name && <Tag color="green">{record.level_name}</Tag>}
        </Space>
      )
    },
    {
      title: '上级',
      key: 'manager',
      render: (record: User) => {
        const manager = users.find(u => u.id === record.manager_id);
        return manager ? (
          <Space>
            <UserOutlined />
            <Text>{manager.username}</Text>
          </Space>
        ) : (
          <Text type="secondary">无上级</Text>
        );
      }
    },
    {
      title: '下属数量',
      key: 'subordinates',
      render: (record: User) => {
        const subordinateCount = users.filter(u => u.manager_id === record.id).length;
        return (
          <Badge count={subordinateCount} showZero />
        );
      }
    },
    {
      title: '状态',
      key: 'status',
      render: (record: User) => (
        <Tag 
          icon={record.is_active ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
          color={record.is_active ? 'success' : 'default'}
        >
          {record.is_active ? '活跃' : '已禁用'}
        </Tag>
      )
    }
  ];

  // 加载所有数据
  const loadAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOrganizationData(),
        validateHierarchy()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // Transfer组件变化处理
  const handleTransferChange = (targetKeys: Key[], direction: 'left' | 'right', moveKeys: Key[]) => {
    const stringKeys = targetKeys.map(key => key.toString());
    setTargetKeys(stringKeys);
    setSelectedUsers(stringKeys.map(key => parseInt(key)));
  };

  const stats = getStats();

  useEffect(() => {
    loadAllData();
  }, []);

  return (
    <div>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Space>
              <SettingOutlined />
              <span>用户层级配置</span>
            </Space>
            <Space>
              <Button 
                type="primary"
                icon={<BulbOutlined />}
                onClick={handleBatchSetManager}
                disabled={selectedUsers.length === 0}
              >
                批量设置上级
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadAllData}
                loading={loading}
              >
                刷新数据
              </Button>
            </Space>
          </div>
        }
      >
        <Spin spinning={loading}>
          {/* 统计概览 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="用户总数"
                  value={stats.totalUsers}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="活跃用户"
                  value={stats.activeUsers}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="有上级用户"
                  value={stats.usersWithManagers}
                  prefix={<TeamOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="根用户"
                  value={stats.rootUsers}
                  prefix={<CrownOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 层级验证状态 */}
          {validationData && (
            <Alert
              message="层级结构验证"
              description={
                <div>
                  <Text>
                    结构状态: {validationData.valid ? '正常' : '存在问题'}
                  </Text>
                  {!validationData.valid && validationData.issues.length > 0 && (
                    <div style={{ marginTop: 8 }}>
                      <Text strong>发现的问题:</Text>
                      <ul>
                        {validationData.issues.map((issue, index) => (
                          <li key={index}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              }
              type={validationData.valid ? 'success' : 'warning'}
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Tabs defaultActiveKey="batch">
            <TabPane
              tab={
                <Space>
                  <SwapOutlined />
                  批量配置
                </Space>
              }
              key="batch"
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="用户选择" size="small">
                    <Transfer
                      dataSource={transferData}
                      targetKeys={targetKeys}
                      onChange={handleTransferChange}
                      render={item => item.title}
                      titles={['可选用户', '已选用户']}
                      listStyle={{
                        width: 250,
                        height: 400,
                      }}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="操作设置" size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>已选择用户: </Text>
                        <Badge count={selectedUsers.length} />
                      </div>
                      
                      <Divider />
                      
                      <div>
                        <Text strong>选择上级管理者:</Text>
                        <Select
                          style={{ width: '100%', marginTop: 8 }}
                          placeholder="请选择上级管理者"
                          value={selectedManager}
                          onChange={setSelectedManager}
                          allowClear
                        >
                          {getAvailableManagers().map(manager => (
                            <Option key={manager.id} value={manager.id}>
                              <Space>
                                <UserOutlined />
                                <span>{manager.username}</span>
                                {manager.role_name && (
                                  <Tag color="blue">{manager.role_name}</Tag>
                                )}
                              </Space>
                            </Option>
                          ))}
                        </Select>
                      </div>
                      
                      <Divider />
                      
                      <Button
                        type="primary"
                        block
                        icon={<TeamOutlined />}
                        disabled={selectedUsers.length === 0}
                        onClick={handleBatchSetManager}
                      >
                        执行批量设置
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <AuditOutlined />
                  用户列表
                </Space>
              }
              key="list"
            >
              <Table
                columns={configColumns}
                dataSource={users}
                rowKey="id"
                pagination={{ pageSize: 15 }}
                size="small"
                rowSelection={{
                  selectedRowKeys: selectedUsers,
                  onChange: (selectedRowKeys) => {
                    setSelectedUsers(selectedRowKeys as number[]);
                    setTargetKeys(selectedRowKeys.map(key => key.toString()));
                  },
                  getCheckboxProps: (record) => ({
                    disabled: !record.is_active,
                  }),
                }}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Card>

      {/* 批量设置确认对话框 */}
      <Modal
        title="批量设置上级"
        open={batchModalVisible}
        onOk={confirmBatchOperation}
        onCancel={() => setBatchModalVisible(false)}
        okText="确认设置"
        cancelText="取消"
        width={600}
        confirmLoading={loading}
      >
        <Alert
          message="批量操作确认"
          description={`即将为 ${selectedUsers.length} 个用户设置上级管理者。此操作将覆盖现有的管理关系。`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>影响的用户:</Text>
            <div style={{ marginTop: 8, maxHeight: 200, overflowY: 'auto' }}>
              {selectedUsers.map(userId => {
                const user = users.find(u => u.id === userId);
                return user ? (
                  <Tag key={userId} style={{ margin: '2px' }}>
                    {user.username}
                  </Tag>
                ) : null;
              })}
            </div>
          </div>
          
          <div>
            <Text strong>新的上级:</Text>
            <div style={{ marginTop: 8 }}>
              {selectedManager ? (
                <Tag color="blue" icon={<UserOutlined />}>
                  {users.find(u => u.id === selectedManager)?.username}
                </Tag>
              ) : (
                <Text type="secondary">移除上级管理者</Text>
              )}
            </div>
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default UserHierarchyConfig; 