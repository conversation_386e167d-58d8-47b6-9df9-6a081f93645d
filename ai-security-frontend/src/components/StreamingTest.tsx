import React, { useState } from 'react';
import { Button, Input, Card, Typography, Space, Divider } from 'antd';
import { messagesAPI } from '../services/api';
import type { AIMessageGenerateRequest } from '../types/api';

const { TextArea } = Input;
const { Title, Paragraph } = Typography;

interface StreamingTestProps {
  sessionId?: number;
}

const StreamingTest: React.FC<StreamingTestProps> = ({ sessionId = 1 }) => {
  const [input, setInput] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedContent, setStreamedContent] = useState('');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  const handleTest = async () => {
    if (!input.trim()) return;

    setIsStreaming(true);
    setStreamedContent('');
    setLogs([]);
    
    addLog('开始流式测试...');

    try {
      const request: AIMessageGenerateRequest = {
        session_id: sessionId,
        user_message: input,
        llm_model_id: 1,
        stream: true
      };

      addLog(`发送请求: ${JSON.stringify(request)}`);

      await messagesAPI.generateAIStream(request, (chunk) => {
        addLog(`收到chunk: ${JSON.stringify(chunk)}`);
        
        if (chunk.type === 'ai_chunk') {
          if (chunk.full_content) {
            setStreamedContent(chunk.full_content);
          } else if (chunk.content) {
            setStreamedContent(prev => prev + chunk.content);
          }
        } else if (chunk.type === 'ai_complete') {
          addLog('流式传输完成');
          if (chunk.full_content) {
            setStreamedContent(chunk.full_content);
          }
        } else if (chunk.type === 'error') {
          addLog(`错误: ${chunk.error}`);
        }
      });

    } catch (error: any) {
      addLog(`异常: ${error.message}`);
    } finally {
      setIsStreaming(false);
      addLog('测试结束');
    }
  };

  const clearTest = () => {
    setStreamedContent('');
    setLogs([]);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={3}>流式消息显示测试</Title>
      
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="输入测试消息">
          <Space direction="vertical" style={{ width: '100%' }}>
            <TextArea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="输入测试消息..."
              rows={3}
              disabled={isStreaming}
            />
            <Space>
              <Button 
                type="primary" 
                onClick={handleTest}
                loading={isStreaming}
                disabled={!input.trim()}
              >
                {isStreaming ? '流式传输中...' : '开始测试'}
              </Button>
              <Button onClick={clearTest} disabled={isStreaming}>
                清空结果
              </Button>
            </Space>
          </Space>
        </Card>

        <Card title="流式内容显示" style={{ minHeight: '200px' }}>
          <div style={{ 
            border: '1px solid #d9d9d9', 
            borderRadius: '6px', 
            padding: '12px',
            minHeight: '150px',
            backgroundColor: '#fafafa',
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}>
            {streamedContent || '等待流式内容...'}
            {isStreaming && (
              <span style={{ 
                animation: 'blink 1s infinite',
                marginLeft: '2px'
              }}>|</span>
            )}
          </div>
        </Card>

        <Card title="调试日志">
          <div style={{ 
            maxHeight: '300px', 
            overflow: 'auto',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            {logs.map((log, index) => (
              <div key={index} style={{ marginBottom: '4px' }}>
                {log}
              </div>
            ))}
            {logs.length === 0 && (
              <div style={{ color: '#999' }}>暂无日志...</div>
            )}
          </div>
        </Card>
      </Space>

      <style>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  );
};

export default StreamingTest;
