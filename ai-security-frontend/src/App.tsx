import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/es/locale/zh_CN'
import { useAuthStore } from './store/authStore'
import LoginPage from './pages/Auth/LoginPage'
import SessionsPage from './pages/Sessions/SessionsPage'
import ChatPage from './pages/Chat/ChatPage'
import { ModelManagement } from './pages/Admin/ModelManagement'
import KeywordGroupManagement from './pages/Admin/KeywordGroupManagement'
import KeywordManagement from './pages/Admin/KeywordManagement'
import DesensitizationRuleManagement from './pages/Admin/DesensitizationRuleManagement'
import UserManagement from './pages/Admin/UserManagement'
import RoleManagement from './pages/Admin/RoleManagement'
import LevelManagement from './pages/Admin/LevelManagement'
import LevelDataAccessManagement from './pages/Admin/LevelDataAccessManagement'
import RegexRuleManagement from './pages/Admin/RegexRuleManagement'
import HierarchyManagement from './pages/Admin/HierarchyManagement'
import KnowledgeBaseManagement from './pages/Admin/KnowledgeBaseManagement'
import UserDocumentPermissions from './pages/Admin/UserDocumentPermissions'
import RAGChatPage from './pages/Chat/RAGChatPage'
// 新的知识库管理页面
import KnowledgeBaseChatPage from './pages/KnowledgeBase/ChatPage'
import KnowledgeBaseDocumentsPage from './pages/KnowledgeBase/DocumentsPage'
import KnowledgeBaseModelsPage from './pages/KnowledgeBase/ModelsPage'
import StreamingTest from './components/StreamingTest'
import MainLayout from './components/Layout/MainLayout'
import './App.css'

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore()
  
  // 如果正在加载，显示加载状态
  if (isLoading) {
    return <div>加载中...</div>
  }
  
  if (!isAuthenticated) {
    console.log('ProtectedRoute: 用户未认证，重定向到登录页')
    return <Navigate to="/login" replace />
  }
  
  console.log('ProtectedRoute: 用户已认证，显示受保护内容')
  return <MainLayout>{children}</MainLayout>
}

// 公共路由组件（未登录用户）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore()
  
  // 如果正在加载，显示加载状态
  if (isLoading) {
    return <div>加载中...</div>
  }
  
  if (isAuthenticated) {
    console.log('PublicRoute: 用户已认证，重定向到主页')
    return <Navigate to="/sessions" replace />
  }
  
  console.log('PublicRoute: 用户未认证，显示公共内容')
  return <>{children}</>
}

function App() {
  const { isAuthenticated, user, token } = useAuthStore()

  // 初始化应用状态
  useEffect(() => {
    // 这里可以添加应用初始化逻辑
    console.log('AI安全系统启动中...')
    console.log('初始认证状态:', { isAuthenticated, user: user?.username, hasToken: !!token })
  }, [])
  
  // 监听认证状态变化
  useEffect(() => {
    console.log('认证状态变化:', { isAuthenticated, user: user?.username, hasToken: !!token })
  }, [isAuthenticated, user, token])

  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <div className="App">
          <Routes>
            {/* 公共路由 - 登录页面 */}
            <Route 
              path="/login" 
              element={
                <PublicRoute>
                  <LoginPage />
                </PublicRoute>
              } 
            />
            
            {/* 受保护的路由 */}
            <Route 
              path="/sessions" 
              element={
                <ProtectedRoute>
                  <SessionsPage />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/profile" 
              element={
                <ProtectedRoute>
                  <div style={{ padding: 24 }}>
                    <h2>个人资料</h2>
                    <p>个人资料页面开发中...</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/settings" 
              element={
                <ProtectedRoute>
                  <div style={{ padding: 24 }}>
                    <h2>系统设置</h2>
                    <p>系统设置页面开发中...</p>
                  </div>
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 模型管理 */}
            <Route 
              path="/admin/models" 
              element={
                <ProtectedRoute>
                  <ModelManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 关键词管理 */}
            <Route 
              path="/admin/keywords" 
              element={
                <ProtectedRoute>
                  <KeywordManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 关键词分组管理 */}
            <Route 
              path="/admin/keyword-groups" 
              element={
                <ProtectedRoute>
                  <KeywordGroupManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 脱敏规则管理 */}
            <Route 
              path="/admin/desensitization" 
              element={
                <ProtectedRoute>
                  <DesensitizationRuleManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 用户管理 */}
            <Route 
              path="/admin/users" 
              element={
                <ProtectedRoute>
                  <UserManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 角色管理 */}
            <Route 
              path="/admin/roles" 
              element={
                <ProtectedRoute>
                  <RoleManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 权限级别管理 */}
            <Route 
              path="/admin/levels" 
              element={
                <ProtectedRoute>
                  <LevelManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 级别数据访问权限管理 */}
            <Route 
              path="/admin/level-data-access" 
              element={
                <ProtectedRoute>
                  <LevelDataAccessManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 恶意意图识别管理 */}
            <Route 
              path="/admin/regex-rules" 
              element={
                <ProtectedRoute>
                  <RegexRuleManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 分层权限管理 */}
            <Route 
              path="/admin/hierarchy" 
              element={
                <ProtectedRoute>
                  <HierarchyManagement />
                </ProtectedRoute>
              } 
            />

            {/* 管理员路由 - 知识库管理 */}
            <Route
              path="/admin/knowledge-base"
              element={
                <ProtectedRoute>
                  <KnowledgeBaseManagement />
                </ProtectedRoute>
              }
            />

            {/* 管理员路由 - 用户文档权限管理 */}
            <Route
              path="/admin/user-document-permissions"
              element={
                <ProtectedRoute>
                  <UserDocumentPermissions />
                </ProtectedRoute>
              }
            />

            {/* AI对话页面路由 */}
            <Route 
              path="/chat/:sessionId" 
              element={<ChatPage />}
            />

            {/* RAG知识库问答页面 - 保留原路由以兼容 */}
            <Route
              path="/rag-chat"
              element={
                <ProtectedRoute>
                  <RAGChatPage />
                </ProtectedRoute>
              }
            />

            {/* 新的知识库管理路由 */}
            <Route
              path="/knowledge-base/chat"
              element={
                <ProtectedRoute>
                  <KnowledgeBaseChatPage />
                </ProtectedRoute>
              }
            />

            <Route
              path="/knowledge-base/documents"
              element={
                <ProtectedRoute>
                  <KnowledgeBaseDocumentsPage />
                </ProtectedRoute>
              }
            />

            <Route
              path="/knowledge-base/models"
              element={
                <ProtectedRoute>
                  <KnowledgeBaseModelsPage />
                </ProtectedRoute>
              }
            />

            {/* 流式测试组件 */}
            <Route 
              path="/streaming-test" 
              element={
                <ProtectedRoute>
                  <StreamingTest />
                </ProtectedRoute>
              } 
            />
            
            {/* 默认重定向 */}
            <Route 
              path="/" 
              element={
                <Navigate 
                  to={isAuthenticated ? "/sessions" : "/login"} 
                  replace 
                />
              } 
            />
            
            {/* 404页面 */}
            <Route 
              path="*" 
              element={
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  alignItems: 'center', 
                  height: '100vh',
                  flexDirection: 'column' 
                }}>
                  <h1>404</h1>
                  <p>页面未找到</p>
                  <a href="/">返回首页</a>
                </div>
              } 
            />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  )
}

export default App
