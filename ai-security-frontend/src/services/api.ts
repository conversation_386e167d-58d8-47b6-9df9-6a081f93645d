import axios, { AxiosResponse } from 'axios'
import type {
  Session,
  Message,
  CreateSessionRequest,
  UpdateSessionRequest,
  CreateMessageRequest,
  SessionListResponse,
  SessionDetailResponse,
  LoginRequest,
  AuthResponse,
  ApiError,
  AIMessageGenerateRequest,
  AIMessageGenerateResponse,
} from '../types/api'

// 创建axios实例 - 使用相对路径通过Vite代理访问后端
const api = axios.create({
  baseURL: '/api/v1',  // 使用相对路径，通过Vite代理转发到后端
  timeout: 180000,  // 增加到180秒(3分钟)给RAG足够的处理时间
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-storage')
    if (token) {
      try {
        const authData = JSON.parse(token)
        // 处理zustand persist的多种可能格式
        let actualToken = null
        if (authData.state?.token) {
          actualToken = authData.state.token
        } else if (authData.token) {
          actualToken = authData.token
        }
        
        if (actualToken) {
          config.headers.Authorization = `Bearer ${actualToken}`
        }
        console.log('API请求添加token:', actualToken ? '已添加' : '未找到')
      } catch (error) {
        console.error('Failed to parse auth token:', error)
      }
    } else {
      console.log('localStorage中无auth-storage')
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一错误处理
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 只有在不是登录接口时才自动跳转到登录页
      if (!error.config?.url?.includes('/login/access-token')) {
        localStorage.removeItem('auth-storage')
        window.location.href = '/login'
      }
    }
    
    const apiError: ApiError = {
      detail: error.response?.data?.detail || error.message || '请求失败',
      status_code: error.response?.status || 500,
    }
    
    return Promise.reject(apiError)
  }
)

// API接口定义
export const authAPI = {
  // 登录 - 修复登录API调用
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    // 使用URLSearchParams格式发送请求，匹配后端期望的application/x-www-form-urlencoded格式
    const params = new URLSearchParams()
    params.append('username', data.username)
    params.append('password', data.password)
    
    const response = await api.post<AuthResponse>('/login/access-token', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    return response.data
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    const response = await api.get('/auth/me')
    return response.data
  },

  // 登出
  logout: async () => {
    await api.post('/auth/logout')
  },
}

export const sessionsAPI = {
  // 获取会话列表
  list: async (params?: {
    skip?: number
    limit?: number
    is_active?: boolean
  }): Promise<SessionListResponse> => {
    const response = await api.get<SessionListResponse>('/sessions/', { params })
    return response.data
  },

  // 创建会话
  create: async (data: CreateSessionRequest): Promise<Session> => {
    const response = await api.post<Session>('/sessions/', data)
    return response.data
  },

  // 获取会话详情
  get: async (id: number): Promise<SessionDetailResponse> => {
    const response = await api.get<SessionDetailResponse>(`/sessions/${id}`)
    return response.data
  },

  // 更新会话
  update: async (id: number, data: UpdateSessionRequest): Promise<Session> => {
    const response = await api.put<Session>(`/sessions/${id}`, data)
    return response.data
  },

  // 删除会话
  delete: async (id: number): Promise<void> => {
    await api.delete(`/sessions/${id}`)
  },

  // 恢复会话
  restore: async (id: number): Promise<Session> => {
    const response = await api.post<Session>(`/sessions/${id}/restore`)
    return response.data
  },
}

// 脱敏规则相关API
export const desensitizationApi = {
  // 获取脱敏规则列表
  getRules: async (params?: {
    skip?: number;
    limit?: number;
    data_type?: string;
    masking_level?: string;
    is_active?: boolean;
    role_id?: number;
    search?: string;
  }) => {
    const response = await api.get('/admin/desensitization-rules/', { params });
    return response.data;
  },

  // 创建脱敏规则
  createRule: async (data: any) => {
    const response = await api.post('/admin/desensitization-rules/', data);
    return response.data;
  },

  // 更新脱敏规则
  updateRule: async (id: number, data: any) => {
    const response = await api.put(`/admin/desensitization-rules/${id}`, data);
    return response.data;
  },

  // 删除脱敏规则
  deleteRule: async (id: number) => {
    const response = await api.delete(`/admin/desensitization-rules/${id}`);
    return response.data;
  },

  // 切换规则状态
  toggleRule: async (id: number) => {
    const response = await api.post(`/admin/desensitization-rules/toggle/${id}`);
    return response.data;
  },

  // 测试脱敏规则
  testRule: async (data: { text: string; rule_id?: number }) => {
    const response = await api.post('/admin/desensitization-rules/test', data);
    return response.data;
  },

  // 创建默认规则
  createDefaults: async () => {
    const response = await api.post('/admin/desensitization-rules/create-defaults');
    return response.data;
  },

  // 获取配置选项
  getOptions: async () => {
    const response = await api.get('/admin/desensitization-rules/options/all');
    return response.data;
  },
};

// 用户管理相关API
export const userApi = {
  // 获取用户列表
  getUsers: async (params?: {
    skip?: number;
    limit?: number;
    is_active?: boolean;
    search?: string;
  }) => {
    const response = await api.get('/admin/users/', { params });
    return response.data;
  },

  // 创建用户
  createUser: async (data: any) => {
    const response = await api.post('/admin/users/', data);
    return response.data;
  },

  // 更新用户
  updateUser: async (id: number, data: any) => {
    const response = await api.put(`/admin/users/${id}`, data);
    return response.data;
  },

  // 删除用户
  deleteUser: async (id: number) => {
    const response = await api.delete(`/admin/users/${id}`);
    return response.data;
  },

  // 获取角色列表
  getRoles: async () => {
    const response = await api.get('/admin/roles/');
    return response.data;
  },

  // 创建角色
  createRole: async (data: any) => {
    const response = await api.post('/admin/roles/', data);
    return response.data;
  },

  // 更新角色
  updateRole: async (id: number, data: any) => {
    const response = await api.put(`/admin/roles/${id}`, data);
    return response.data;
  },

  // 删除角色
  deleteRole: async (id: number) => {
    const response = await api.delete(`/admin/roles/${id}`);
    return response.data;
  },

  // 获取权限级别列表
  getLevels: async (params?: {
    skip?: number;
    limit?: number;
    is_active?: boolean;
    search?: string;
  }) => {
    const response = await api.get('/admin/levels/', { params });
    return response.data;
  },

  // 创建权限级别
  createLevel: async (data: any) => {
    const response = await api.post('/admin/levels/', data);
    return response.data;
  },

  // 获取单个权限级别
  getLevel: async (id: number) => {
    const response = await api.get(`/admin/levels/${id}`);
    return response.data;
  },

  // 更新权限级别
  updateLevel: async (id: number, data: any) => {
    const response = await api.put(`/admin/levels/${id}`, data);
    return response.data;
  },

  // 删除权限级别
  deleteLevel: async (id: number) => {
    const response = await api.delete(`/admin/levels/${id}`);
    return response.data;
  },

  // 获取激活的权限级别列表（用于下拉选择）
  getActiveLevels: async () => {
    const response = await api.get('/admin/levels/active/list');
    return response.data;
  },
};

// 用户文档权限管理相关API
export const userDocumentPermissionApi = {
  // 获取权限列表
  getPermissions: async (params?: {
    skip?: number;
    limit?: number;
  }) => {
    const response = await api.get('/user-document-permissions/list', { params });
    return response.data;
  },

  // 授予权限
  grantPermission: async (data: {
    user_id: number;
    document_id: number;
    permission_type: string;
    expires_days?: number;
    reason?: string;
  }) => {
    const response = await api.post('/user-document-permissions/grant', data);
    return response.data;
  },

  // 撤销权限
  revokePermission: async (data: {
    user_id: number;
    document_id: number;
    permission_type: string;
  }) => {
    const response = await api.post('/user-document-permissions/revoke', data);
    return response.data;
  },

  // 批量授权
  batchGrantPermissions: async (data: {
    user_ids: number[];
    document_ids: number[];
    permission_types: string[];
    expires_days?: number;
    reason?: string;
  }) => {
    const response = await api.post('/user-document-permissions/batch-grant', data);
    return response.data;
  },
};

// 知识库相关API
export const knowledgeBaseApi = {
  // 获取文档列表
  getDocuments: async (params?: {
    skip?: number;
    limit?: number;
    category_id?: number;
    grade_id?: number;
    status?: string;
    search?: string;
  }) => {
    const response = await api.get('/knowledge-base/documents', { params });
    return response.data;
  },

  // 获取文档详情
  getDocument: async (id: number) => {
    const response = await api.get(`/knowledge-base/documents/${id}`);
    return response.data;
  },

  // 上传文档
  uploadDocument: async (formData: FormData) => {
    const response = await api.post('/knowledge-base/documents', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 删除文档
  deleteDocument: async (id: number) => {
    const response = await api.delete(`/knowledge-base/documents/${id}`);
    return response.data;
  },

  // 搜索文档
  searchDocuments: async (data: {
    query: string;
    category_ids?: number[];
    grade_ids?: number[];
    top_k?: number;
    threshold?: number;
  }) => {
    const response = await api.post('/knowledge-base/search', data);
    return response.data;
  },
};

// 恶意意图识别管理相关API
export const regexRuleApi = {
  // 获取正则规则列表
  getRules: async (params?: {
    skip?: number;
    limit?: number;
    active_only?: boolean;
    category?: string;
    severity?: string;
    role_id?: number;
    search?: string;
  }) => {
    const response = await api.get('/admin/regex-rules/', { params });
    return response.data;
  },

  // 创建正则规则
  createRule: async (data: any) => {
    const response = await api.post('/admin/regex-rules/', data);
    return response.data;
  },

  // 获取单个正则规则
  getRule: async (id: number) => {
    const response = await api.get(`/admin/regex-rules/${id}`);
    return response.data;
  },

  // 更新正则规则
  updateRule: async (id: number, data: any) => {
    const response = await api.put(`/admin/regex-rules/${id}`, data);
    return response.data;
  },

  // 删除正则规则
  deleteRule: async (id: number) => {
    const response = await api.delete(`/admin/regex-rules/${id}`);
    return response.data;
  },

  // 测试正则规则
  testRule: async (data: {
    pattern: string;
    flags: number;
    test_texts: string[];
    timeout_ms: number;
  }) => {
    const response = await api.post('/admin/regex-rules/test', data);
    return response.data;
  },

  // 批量检查文本
  batchCheck: async (data: {
    texts: string[];
    category_filter?: string[];
    severity_filter?: string[];
    role_id?: number;
    only_active?: boolean;
  }) => {
    const response = await api.post('/admin/regex-rules/batch-check', data);
    return response.data;
  },

  // 获取规则统计
  getStats: async () => {
    const response = await api.get('/admin/regex-rules/stats');
    return response.data;
  },

  // 标记误报
  markFalsePositive: async (id: number) => {
    const response = await api.post(`/admin/regex-rules/${id}/false-positive`);
    return response.data;
  },

  // 激活规则
  activateRule: async (id: number) => {
    const response = await api.post(`/admin/regex-rules/${id}/activate`);
    return response.data;
  },

  // 停用规则
  deactivateRule: async (id: number) => {
    const response = await api.post(`/admin/regex-rules/${id}/deactivate`);
    return response.data;
  },
};

// 权限管理相关API
export const permissionApi = {
  // 获取角色权限配置
  getRolePermissions: async (roleId: number) => {
    const response = await api.get(`/admin/permissions/roles/${roleId}/permissions`);
    return response.data;
  },

  // 更新角色模块权限
  updateModulePermissions: async (roleId: number, permissions: Array<{module_name: string, can_access: boolean}>) => {
    const response = await api.put(`/admin/permissions/roles/${roleId}/module-permissions`, permissions);
    return response.data;
  },

  // 更新角色关键词组分配
  updateKeywordGroups: async (roleId: number, assignments: Array<{keyword_group_id: number, assigned: boolean}>) => {
    const response = await api.put(`/admin/permissions/roles/${roleId}/keyword-groups`, assignments);
    return response.data;
  },

  // 更新角色数据分级访问权限
  updateDataGradeAccess: async (roleId: number, accesses: Array<{data_source_grade_id: number, access_allowed: boolean}>) => {
    const response = await api.put(`/admin/permissions/roles/${roleId}/data-grade-access`, accesses);
    return response.data;
  },

  // 获取可用模块列表
  getAvailableModules: async () => {
    const response = await api.get('/admin/permissions/available-modules');
    return response.data;
  },
};

// 数据分类管理相关API
export const dataClassificationApi = {
  // 获取数据源分类列表
  getCategories: async () => {
    const response = await api.get('/admin/data-classification/categories');
    return response.data;
  },

  // 创建数据源分类
  createCategory: async (data: {name: string, description?: string}) => {
    const response = await api.post('/admin/data-classification/categories', data);
    return response.data;
  },

  // 更新数据源分类
  updateCategory: async (id: number, data: {name?: string, description?: string}) => {
    const response = await api.put(`/admin/data-classification/categories/${id}`, data);
    return response.data;
  },

  // 删除数据源分类
  deleteCategory: async (id: number) => {
    const response = await api.delete(`/admin/data-classification/categories/${id}`);
    return response.data;
  },

  // 获取可用数据分级列表
  getAvailableGrades: async () => {
    const response = await api.get('/admin/data-classification/available-grades');
    return response.data;
  },
};

// 审计日志相关API
export const auditLogApi = {
  // 获取审计日志列表
  getLogs: async (params?: {
    limit?: number;
    offset?: number;
    action_type?: string;
    user_id?: number;
    result?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const response = await api.get('/admin/audit/logs', { params });
    return response.data;
  },

  // 创建审计日志
  createLog: async (data: {
    action_type: string;
    target_entity?: string;
    target_id?: number;
    details?: string;
    ip_address?: string;
    user_agent?: string;
    session_id?: string;
    result?: string;
  }) => {
    const response = await api.post('/admin/audit/logs', data);
    return response.data;
  },

  // 获取审计日志统计
  getStats: async () => {
    const response = await api.get('/admin/audit/stats');
    return response.data;
  },

  // 获取可用操作类型
  getActionTypes: async () => {
    const response = await api.get('/admin/audit/action-types');
    return response.data;
  },

  // 删除审计日志
  deleteLog: async (id: number) => {
    const response = await api.delete(`/admin/audit/logs/${id}`);
    return response.data;
  },
};

// 数据分类API
export const dataClassificationAPI = {
  // 获取用户可访问的数据分级
  getAccessibleGrades: async () => {
    const response = await api.get('/data-classification/accessible-grades');
    return response.data;
  },

  // 获取所有数据分级
  getAllGrades: async () => {
    const response = await api.get('/admin/data-classification/grades');
    return response.data;
  },

  // 创建数据分级
  createGrade: async (data: {
    name: string;
    description: string;
    sensitivity_level: number;
    access_control_rules?: any;
  }) => {
    const response = await api.post('/admin/data-classification/grades', data);
    return response.data;
  },

  // 更新数据分级
  updateGrade: async (id: number, data: {
    name?: string;
    description?: string;
    sensitivity_level?: number;
    access_control_rules?: any;
  }) => {
    const response = await api.put(`/admin/data-classification/grades/${id}`, data);
    return response.data;
  },

  // 删除数据分级
  deleteGrade: async (id: number) => {
    const response = await api.delete(`/admin/data-classification/grades/${id}`);
    return response.data;
  },

  // 获取数据项列表
  getDataItems: async (params?: {
    skip?: number;
    limit?: number;
    classification_id?: number;
    sensitivity_level?: number;
  }) => {
    const response = await api.get('/admin/data-classification/items', { params });
    return response.data;
  },

  // 创建数据项
  createDataItem: async (data: {
    name: string;
    description: string;
    classification_id: number;
    metadata?: any;
  }) => {
    const response = await api.post('/admin/data-classification/items', data);
    return response.data;
  },

  // 更新数据项
  updateDataItem: async (id: number, data: {
    name?: string;
    description?: string;
    classification_id?: number;
    metadata?: any;
  }) => {
    const response = await api.put(`/admin/data-classification/items/${id}`, data);
    return response.data;
  },

  // 删除数据项
  deleteDataItem: async (id: number) => {
    const response = await api.delete(`/admin/data-classification/items/${id}`);
    return response.data;
  },
};

export const messagesAPI = {
  // 发送消息
  send: async (sessionId: number, data: CreateMessageRequest): Promise<Message> => {
    const response = await api.post<Message>(`/messages/`, data)
    return response.data
  },

  // 获取会话消息列表
  list: async (
    sessionId: number,
    params?: {
      skip?: number
      limit?: number
      sender_type?: 'user' | 'ai' | 'system'
      include_blocked?: boolean
    }
  ): Promise<Message[]> => {
    const response = await api.get<Message[]>(`/messages/session/${sessionId}`, { params })
    return response.data
  },

  // 获取消息详情
  get: async (id: number): Promise<Message> => {
    const response = await api.get<Message>(`/messages/${id}`)
    return response.data
  },

  // 拦截消息
  block: async (id: number, reason?: string): Promise<Message> => {
    const response = await api.post<Message>(`/messages/${id}/block`, { reason })
    return response.data
  },

  // 取消拦截
  unblock: async (id: number): Promise<Message> => {
    const response = await api.post<Message>(`/messages/${id}/unblock`)
    return response.data
  },

  // AI消息生成
  generateAI: async (data: AIMessageGenerateRequest): Promise<AIMessageGenerateResponse> => {
    const response = await api.post<AIMessageGenerateResponse>('/messages/ai-generate', data)
    return response.data
  },

  // 流式AI消息生成
  generateAIStream: async (data: AIMessageGenerateRequest, onChunk: (chunk: any) => void): Promise<void> => {
    const token = localStorage.getItem('auth-storage')
    let actualToken = null
    if (token) {
      try {
        const authData = JSON.parse(token)
        if (authData.state?.token) {
          actualToken = authData.state.token
        } else if (authData.token) {
          actualToken = authData.token
        }
      } catch (error) {
        console.error('Failed to parse auth token:', error)
      }
    }

    const response = await fetch('/api/v1/messages/ai-generate-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${actualToken}`,
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('Failed to get response reader')
    }

    const decoder = new TextDecoder()
    let buffer = ''
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        // 将新数据添加到缓冲区
        const chunk = decoder.decode(value, { stream: true })
        buffer += chunk
        
        // 处理缓冲区中的完整行
        let lines = buffer.split('\n')
        
        // 保留最后一个可能不完整的行
        buffer = lines.pop() || ''
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6) // 移除 'data: ' 前缀
              if (jsonStr.trim() === '[DONE]') {
                return
              }
              
              const data = JSON.parse(jsonStr)
              onChunk(data)
            } catch (error) {
              console.error('Failed to parse streaming chunk:', error, 'Line:', line)
            }
          }
        }
      }
      
      // 处理缓冲区中剩余的数据
      if (buffer.trim() && buffer.startsWith('data: ')) {
        try {
          const jsonStr = buffer.slice(6)
          if (jsonStr.trim() !== '[DONE]') {
            const data = JSON.parse(jsonStr)
            onChunk(data)
          }
        } catch (error) {
          console.error('Failed to parse final chunk:', error)
        }
      }
    } finally {
      reader.releaseLock()
    }
  },
}

// 开发模式下的模拟API
const isDev = import.meta.env.DEV

export const mockAPI = {
  // 模拟登录
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    // 简单的模拟验证
    if (data.username === 'admin' && data.password === 'admin') {
      return {
        access_token: 'mock-token-admin',
        token_type: 'bearer',
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          role_id: 1,
          level_id: 1,
          is_active: true,
          is_superuser: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      }
    } else if (data.username === 'user' && data.password === 'user') {
      return {
        access_token: 'mock-token-user',
        token_type: 'bearer',
        user: {
          id: 2,
          username: 'user',
          email: '<EMAIL>',
          role_id: 2,
          level_id: 2,
          is_active: true,
          is_superuser: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      }
    } else {
      throw new Error('用户名或密码错误')
    }
  },
}

// 根据环境选择API
export const API = isDev ? { ...authAPI, ...mockAPI } : authAPI

export default api