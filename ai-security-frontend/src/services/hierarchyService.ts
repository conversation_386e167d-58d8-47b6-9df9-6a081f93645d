import axios from 'axios';

const BASE_URL = 'http://localhost:8000/api/v1';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-storage');
    if (token) {
      try {
        const authData = JSON.parse(token);
        let actualToken = null;
        if (authData.state?.token) {
          actualToken = authData.state.token;
        } else if (authData.token) {
          actualToken = authData.token;
        }
        
        if (actualToken) {
          config.headers.Authorization = `Bearer ${actualToken}`;
        }
      } catch (error) {
        console.error('Failed to parse auth token:', error);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export interface User {
  id: number;
  username: string;
  email: string;
  role_name?: string;
  level_name?: string;
  manager_id?: number;
  is_active: boolean;
  subordinates: User[];
}

export interface ModelAssignment {
  id: number;
  user_id: number;
  model_id: number;
  is_default: boolean;
  assigned_by: number;
  created_at: string;
  updated_at: string;
  user?: {
    username: string;
    email: string;
  };
  model?: {
    name: string;
    api_url: string;
  };
  assigner?: {
    username: string;
  };
}

export interface SetManagerRequest {
  manager_id?: number | null;
}

export interface ModelAssignmentRequest {
  model_ids: number[];
  default_model_id?: number;
}

export interface OperationLog {
  id: number;
  operator_id: number;
  target_user_id?: number;
  operation_type: string;
  operation_details: string;
  ip_address?: string;
  user_agent?: string;
  success: boolean;
  error_message?: string;
  created_at: string;
  operator?: {
    username: string;
  };
  target_user?: {
    username: string;
  };
}

export const hierarchyService = {
  // 获取组织架构树
  async getOrganizationTree(): Promise<User[]> {
    const response = await api.get('/admin/hierarchy/organization-tree');
    return response.data;
  },

  // 获取用户层级信息
  async getUserHierarchy(userId: number) {
    const response = await api.get(`/admin/hierarchy/users/${userId}/hierarchy`);
    return response.data;
  },

  // 设置用户上级
  async setUserManager(userId: number, data: SetManagerRequest) {
    const response = await api.post(`/admin/hierarchy/users/${userId}/set-manager`, data);
    return response.data;
  },

  // 获取用户下属列表
  async getUserSubordinates(userId: number, includeIndirect = false) {
    const response = await api.get(`/admin/hierarchy/users/${userId}/subordinates`, {
      params: { include_indirect: includeIndirect }
    });
    return response.data;
  },

  // 验证层级结构
  async validateHierarchy() {
    const response = await api.get('/admin/hierarchy/validate-hierarchy');
    return response.data;
  },

  // 获取用户可用模型
  async getUserModels(userId: number) {
    const response = await api.get(`/admin/model-assignments/users/${userId}/models`);
    return response.data;
  },

  // 为用户分配模型
  async assignModelsToUser(userId: number, data: ModelAssignmentRequest) {
    const response = await api.post(`/admin/model-assignments/users/${userId}/assign-models`, data);
    return response.data;
  },

  // 设置用户默认模型
  async setUserDefaultModel(userId: number, modelId: number) {
    const response = await api.post(`/admin/model-assignments/users/${userId}/set-default-model`, {
      model_id: modelId
    });
    return response.data;
  },

  // 撤销用户模型分配
  async revokeModelAssignment(userId: number, modelId: number) {
    const response = await api.delete(`/admin/model-assignments/users/${userId}/models/${modelId}`);
    return response.data;
  },

  // 获取所有模型分配
  async getAllModelAssignments(): Promise<ModelAssignment[]> {
    const response = await api.get('/admin/model-assignments/assignments');
    return response.data;
  },

  // 获取操作日志
  async getOperationLogs(params?: {
    operator_id?: number;
    target_user_id?: number;
    operation_type?: string;
    start_date?: string;
    end_date?: string;
    success?: boolean;
    skip?: number;
    limit?: number;
  }): Promise<OperationLog[]> {
    const response = await api.get('/admin/hierarchy/operation-logs', { params });
    return response.data;
  },
}; 