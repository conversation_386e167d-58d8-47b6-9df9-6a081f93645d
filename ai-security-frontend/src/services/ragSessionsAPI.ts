import api from './api';
import type { Session, SessionCreate, SessionUpdate, Message } from '../types/api';

export interface RAGSessionCreate {
  title: string;
  llm_model_id?: number;
  session_type?: 'rag';
}

export interface RAGSessionUpdate extends SessionUpdate {
  session_type?: 'rag';
}

/**
 * RAG会话管理API服务
 */
export const ragSessionsAPI = {
  /**
   * 创建新的RAG会话
   */
  create: async (sessionData: RAGSessionCreate): Promise<Session> => {
    const response = await api.post('/rag-sessions/', {
      ...sessionData,
      session_type: 'rag'
    });
    return response.data;
  },

  /**
   * 获取用户的RAG会话列表
   */
  list: async (params?: { skip?: number; limit?: number }): Promise<Session[]> => {
    const response = await api.get('/rag-sessions/', { params });
    return response.data;
  },

  /**
   * 获取特定的RAG会话
   */
  get: async (sessionId: number): Promise<Session> => {
    const response = await api.get(`/rag-sessions/${sessionId}`);
    return response.data;
  },

  /**
   * 更新RAG会话
   */
  update: async (sessionId: number, sessionData: RAGSessionUpdate): Promise<Session> => {
    const response = await api.put(`/rag-sessions/${sessionId}`, sessionData);
    return response.data;
  },

  /**
   * 删除RAG会话
   */
  delete: async (sessionId: number): Promise<void> => {
    await api.delete(`/rag-sessions/${sessionId}`);
  },

  /**
   * 获取RAG会话的消息历史
   */
  getMessages: async (sessionId: number, params?: { skip?: number; limit?: number }): Promise<Message[]> => {
    const response = await api.get(`/rag-sessions/${sessionId}/messages`, { params });
    return response.data;
  },

  /**
   * 发送RAG查询并保存到会话
   */
  sendQuery: async (sessionId: number, query: string, options?: {
    model_id?: number;
    category_ids?: number[];
    grade_ids?: number[];
    top_k?: number;
    temperature?: number;
    max_tokens?: number;
    include_sources?: boolean;
  }) => {
    const response = await api.post('/rag/query', {
      query,
      session_id: sessionId,
      model_id: options?.model_id || 13, // 默认使用阿里云百炼模型
      category_ids: options?.category_ids,
      grade_ids: options?.grade_ids,
      top_k: options?.top_k || 10,
      temperature: options?.temperature || 0.7,
      max_tokens: options?.max_tokens || 800,
      include_sources: options?.include_sources !== false
    });
    return response.data;
  }
};

export default ragSessionsAPI;
