import { userApi } from './api';

export interface User {
  id: number;
  username: string;
  email: string;
  role_id?: number;
  level_id?: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  role?: {
    id: number;
    name: string;
  };
  level?: {
    id: number;
    name: string;
  };
}

export const userService = {
  // 获取用户列表
  async getUsers(params?: {
    skip?: number;
    limit?: number;
    is_active?: boolean;
    search?: string;
  }): Promise<User[]> {
    return await userApi.getUsers(params);
  },

  // 创建用户
  async createUser(data: any): Promise<User> {
    return await userApi.createUser(data);
  },

  // 更新用户
  async updateUser(id: number, data: any): Promise<User> {
    return await userApi.updateUser(id, data);
  },

  // 删除用户
  async deleteUser(id: number): Promise<void> {
    return await userApi.deleteUser(id);
  },

  // 获取角色列表
  async getRoles() {
    return await userApi.getRoles();
  },

  // 获取权限级别列表
  async getLevels(params?: {
    skip?: number;
    limit?: number;
    is_active?: boolean;
    search?: string;
  }) {
    return await userApi.getLevels(params);
  },
}; 