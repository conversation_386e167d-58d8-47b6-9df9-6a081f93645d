import React, { useEffect, useState, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Input,
  Button,
  List,
  Avatar,
  Space,
  Typography,
  Tag,
  Alert,
  Spin,
  message,
  Tooltip,
  Modal,
  Divider,
  Progress,
} from 'antd'
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  SafetyCertificateOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ArrowLeftOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { useSessionStore, useSessionMessages } from '../../store/sessionStore'
import { useAuthStore } from '../../store/authStore'
import type { Message, AIMessageGenerateRequest } from '../../types/api'
import { messagesAPI, sessionsAPI } from '../../services/api'
import dayjs from 'dayjs'

const { TextArea } = Input
const { Text, Title } = Typography

const ChatPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>()
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const { 
    currentSession, 
    setCurrentSession, 
    getMessages, 
    addMessage, 
    updateMessage,
    initializeSessions 
  } = useSessionStore()
  
  // 获取当前会话的消息
  const sessionIdNum = sessionId ? parseInt(sessionId) : 0
  const messages = useSessionMessages(sessionIdNum)
  
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [securityCheckProgress, setSecurityCheckProgress] = useState(0)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 初始化会话和加载数据
  useEffect(() => {
    const initializeSession = async () => {
      // 初始化会话数据
      initializeSessions()
      
      if (sessionId) {
        const sessionIdNum = parseInt(sessionId)
        
        // 查找现有会话
        const existingSession = useSessionStore.getState().sessions.find(s => s.id === sessionIdNum)
        
        if (existingSession) {
          setCurrentSession(existingSession)
        } else {
          try {
            // 通过API创建真实的会话
            const newSessionData = {
              title: 'AI助手对话',
              llm_model_id: 1  // 使用默认模型
            }
            
            const createdSession = await sessionsAPI.create(newSessionData)
            
            // 添加到store
            useSessionStore.getState().addSession(createdSession)
            setCurrentSession(createdSession)
            
            // 添加初始AI消息
            const initialMessage: Message = {
              id: Date.now(),
              session_id: createdSession.id,
              content: '您好！我是AI安全助手，很高兴为您服务。请问有什么可以帮助您的吗？',
              sender_type: 'ai',
              is_blocked: false,
              security_check_passed: true,
              security_check_details: {
                keyword_check: true,
                malicious_intent_check: true,
                data_sensitivity_check: true,
                score: 95,
              },
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }
            
            addMessage(createdSession.id, initialMessage)
            
            // 更新URL为新创建的会话ID
            navigate(`/chat/${createdSession.id}`, { replace: true })
            
          } catch (error: any) {
            console.error('创建会话失败:', error)
            message.error('创建会话失败，请重试')
            navigate('/sessions')
          }
        }
      }
    }
    
    initializeSession()
  }, [sessionId, user, setCurrentSession, addMessage, initializeSessions, navigate])

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 删除了模拟的安全检查和AI响应生成函数，现在使用真实的API调用

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim()) {
      message.warning('请输入消息内容')
      return
    }

    if (!currentSession) {
      message.error('会话信息不存在')
      return
    }

    setIsSending(true)
    setIsLoading(true)
    const userMessageContent = inputValue.trim()
    setInputValue('')

    try {
      // 调用后端流式AI消息生成接口
      const generateRequest: AIMessageGenerateRequest = {
        session_id: currentSession.id,
        user_message: userMessageContent,
        llm_model_id: currentSession.llm_model_id,
        stream: true
      }

      console.log('发送流式AI生成请求:', generateRequest)
      console.log('当前会话信息:', currentSession)
      
      let userMessage: Message | null = null
      let aiMessage: Message | null = null
      let aiMessageContent = ""
      let isStreamingComplete = false

      await messagesAPI.generateAIStream(generateRequest, (chunk) => {
        console.log('收到流式数据:', chunk)
        
        if (chunk.type === 'user_message') {
          // 添加用户消息
          userMessage = chunk.data
          if (userMessage) {
            addMessage(currentSession.id, userMessage)
          }
        } else if (chunk.type === 'blocked') {
          // 消息被拦截
          message.error('消息因安全原因被拦截')
          const systemMessage: Message = {
            id: Date.now() + 1,
            session_id: currentSession.id,
            content: `⚠️ 您的消息"${userMessageContent}"因包含敏感内容被安全系统拦截。请重新组织语言后再试。`,
            sender_type: 'system',
            is_blocked: false,
            security_check_passed: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
          addMessage(currentSession.id, systemMessage)
          return
        } else if (chunk.type === 'ai_chunk') {
          // AI响应片段 - 支持逐字符流式显示
          if (!isStreamingComplete) {
            // 更新完整内容
            if (chunk.full_content !== undefined) {
              aiMessageContent = chunk.full_content
            } else if (chunk.content) {
              aiMessageContent = aiMessageContent + chunk.content
            }
            
            // 如果是第一个AI片段，创建AI消息
            if (!aiMessage) {
              aiMessage = {
                id: Date.now() + 2,
                session_id: currentSession.id,
                content: aiMessageContent,
                sender_type: 'ai',
                is_blocked: false,
                security_check_passed: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              }
              addMessage(currentSession.id, aiMessage)
              console.log('创建AI消息:', aiMessage)
            } else {
              // 实时更新AI消息内容 - 实现打字机效果
              updateMessage(currentSession.id, aiMessage.id, {
                content: aiMessageContent,
                updated_at: new Date().toISOString(),
              })
              console.log('更新AI消息内容:', aiMessageContent)
            }
          }
        } else if (chunk.type === 'ai_complete') {
          // AI响应完成
          isStreamingComplete = true
          if (aiMessage) {
            // 确保最终内容正确
            const finalContent = chunk.full_content || aiMessageContent
            updateMessage(currentSession.id, aiMessage.id, {
              content: finalContent,
              updated_at: new Date().toISOString(),
            })
            console.log('AI响应完成，最终内容:', finalContent)
          }
        } else if (chunk.type === 'completed') {
          // 生成完成，更新最终的AI消息（兼容旧格式）
          if (aiMessage && chunk.ai_message) {
            updateMessage(currentSession.id, aiMessage.id, {
              content: chunk.ai_message.content,
              security_check_passed: chunk.ai_message.security_check_passed,
              security_check_details: chunk.ai_message.security_check_details,
              is_blocked: chunk.ai_message.is_blocked,
              block_reason: chunk.ai_message.block_reason,
              updated_at: chunk.ai_message.updated_at,
            })
          }
        } else if (chunk.type === 'error') {
          console.error('流式生成错误:', chunk.error)
          message.error('AI生成过程中出现错误：' + chunk.error)
          
          // 添加错误消息
          if (aiMessage) {
            updateMessage(currentSession.id, aiMessage.id, {
              content: '抱歉，生成过程中出现错误，请重试。',
              updated_at: new Date().toISOString(),
            })
          }
        }
      })

    } catch (error: any) {
      console.error('AI消息生成失败:', error)
      message.error(error.detail || '发送消息失败，请重试')
    } finally {
      setIsSending(false)
      setIsLoading(false)
      setSecurityCheckProgress(0)
    }
  }

  // 清空对话
  const handleClearChat = () => {
    if (!currentSession) return
    
    Modal.confirm({
      title: '确认清空对话',
      content: '此操作将清空当前会话的所有消息，是否继续？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        useSessionStore.getState().setMessages(currentSession.id, [])
        message.success('对话已清空')
      },
    })
  }

  // 重新生成响应
  const handleRegenerateResponse = async (messageId: number) => {
    if (!currentSession) return
    
    const messageList = getMessages(currentSession.id)
    const messageIndex = messageList.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1 || messageIndex === 0) return

    const userMessage = messageList[messageIndex - 1]
    if (userMessage.sender_type !== 'user') return

    setIsLoading(true)
    try {
      const generateRequest: AIMessageGenerateRequest = {
        session_id: currentSession.id,
        user_message: userMessage.content,
        llm_model_id: currentSession.llm_model_id,
        stream: true
      }

      const response = await messagesAPI.generateAI(generateRequest)
      
      // 更新AI消息
      updateMessage(currentSession.id, messageId, {
        content: response.ai_message.content,
        security_check_passed: response.ai_message.security_check_passed,
        security_check_details: response.ai_message.security_check_details,
        is_blocked: response.ai_message.is_blocked,
        block_reason: response.ai_message.block_reason,
        updated_at: new Date().toISOString(),
      })
      
      message.success('响应已重新生成')
    } catch (error: any) {
      console.error('重新生成失败:', error)
      message.error(error.detail || '重新生成失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 渲染安全检查详情
  const renderSecurityDetails = (details?: Message['security_check_details']) => {
    if (!details || typeof details !== 'object') return null

    const safeDetails = details as {
      keyword_check: boolean
      malicious_intent_check: boolean
      data_sensitivity_check: boolean
      score: number
    }

    return (
      <Space direction="vertical" size={4} style={{ width: '100%' }}>
        <div>
          <Text strong>安全评分：</Text>
          <Progress 
            percent={safeDetails.score} 
            size="small" 
            status={safeDetails.score >= 80 ? 'success' : safeDetails.score >= 60 ? 'normal' : 'exception'}
          />
        </div>
        <Space wrap>
          <Tag color={safeDetails.keyword_check ? 'green' : 'red'}>
            关键词检查 {safeDetails.keyword_check ? '✓' : '✗'}
          </Tag>
          <Tag color={safeDetails.malicious_intent_check ? 'green' : 'red'}>
            恶意意图检查 {safeDetails.malicious_intent_check ? '✓' : '✗'}
          </Tag>
          <Tag color={safeDetails.data_sensitivity_check ? 'green' : 'red'}>
            数据敏感性检查 {safeDetails.data_sensitivity_check ? '✓' : '✗'}
          </Tag>
        </Space>
      </Space>
    )
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <Card 
        size="small" 
        style={{ 
          borderRadius: 0,
          borderBottom: '1px solid #f0f0f0',
          flexShrink: 0,
        }}
      >
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center' 
        }}>
          <Space>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/sessions')}
            >
              返回会话列表
            </Button>
            <Divider type="vertical" />
            <Title level={4} style={{ margin: 0 }}>
              {currentSession?.name || currentSession?.title}
            </Title>
            <Tag color="blue">AI安全对话</Tag>
          </Space>
          
          <Space>
            <Button 
              type="text" 
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            >
              刷新
            </Button>
            <Button 
              type="text" 
              danger
              icon={<DeleteOutlined />}
              onClick={handleClearChat}
            >
              清空对话
            </Button>
          </Space>
        </div>
      </Card>

      {/* 消息列表 */}
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        padding: '16px',
        background: '#fafafa' 
      }}>
        <List
          dataSource={messages}
          renderItem={(message) => (
            <List.Item style={{ 
              border: 'none', 
              padding: '8px 0',
              display: 'block'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: message.sender_type === 'user' ? 'flex-end' : 'flex-start',
                marginBottom: 8,
              }}>
                <div style={{
                  maxWidth: '70%',
                  display: 'flex',
                  flexDirection: message.sender_type === 'user' ? 'row-reverse' : 'row',
                  alignItems: 'flex-start',
                  gap: 8,
                }}>
                  {/* 头像 */}
                  <Avatar 
                    icon={
                      message.sender_type === 'user' ? <UserOutlined /> :
                      message.sender_type === 'ai' ? <RobotOutlined /> :
                      <SafetyCertificateOutlined />
                    }
                    style={{
                      backgroundColor: 
                        message.sender_type === 'user' ? '#1890ff' :
                        message.sender_type === 'ai' ? '#52c41a' :
                        '#faad14'
                    }}
                  />
                  
                  {/* 消息内容 */}
                  <div style={{
                    background: message.is_blocked ? '#fff2f0' : 
                              message.sender_type === 'user' ? '#1890ff' :
                              message.sender_type === 'system' ? '#fffbe6' : '#fff',
                    color: message.sender_type === 'user' ? '#fff' : '#333',
                    padding: '12px 16px',
                    borderRadius: '12px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    position: 'relative',
                  }}>
                    {/* 被拦截的消息显示警告 */}
                    {message.is_blocked && (
                      <Alert
                        message="消息已被安全系统拦截"
                        description={message.block_reason}
                        type="error"
                        style={{ marginBottom: 8 }}
                      />
                    )}
                    
                    {/* 消息文本 */}
                    <div style={{ whiteSpace: 'pre-wrap', lineHeight: 1.6 }}>
                      {message.content}
                    </div>
                    
                    {/* 安全检查详情 */}
                    {message.security_check_details && (
                      <Tooltip 
                        title={renderSecurityDetails(message.security_check_details)}
                        overlayStyle={{ maxWidth: 400 }}
                      >
                        <div style={{ 
                          marginTop: 8, 
                          display: 'flex', 
                          alignItems: 'center',
                          gap: 4,
                          fontSize: 12,
                          opacity: 0.8,
                        }}>
                          {message.security_check_passed ? (
                            <CheckCircleOutlined style={{ color: '#52c41a' }} />
                          ) : (
                            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                          )}
                          安全评分: {(message.security_check_details as any)?.score || 0}
                        </div>
                      </Tooltip>
                    )}
                    
                    {/* 时间和操作 */}
                    <div style={{ 
                      marginTop: 8,
                      fontSize: 12,
                      opacity: 0.7,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                      <span>{dayjs(message.created_at).format('HH:mm:ss')}</span>
                      {message.sender_type === 'ai' && (
                        <Button
                          type="text"
                          size="small"
                          icon={<ReloadOutlined />}
                          onClick={() => handleRegenerateResponse(message.id)}
                          style={{ color: 'inherit', opacity: 0.6 }}
                        >
                          重新生成
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </List.Item>
          )}
        />
        
        {/* AI正在输入指示器 */}
        {isLoading && (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'flex-start',
            marginTop: 16,
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Avatar icon={<RobotOutlined />} style={{ backgroundColor: '#52c41a' }} />
              <div style={{
                background: '#fff',
                padding: '12px 16px',
                borderRadius: '12px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
              }}>
                <Space>
                  <Spin size="small" />
                  <Text type="secondary">AI正在思考中...</Text>
                </Space>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 安全检查进度 */}
      {securityCheckProgress > 0 && securityCheckProgress < 100 && (
        <div style={{ padding: '8px 16px', background: '#fff' }}>
          <Space style={{ width: '100%' }} direction="vertical" size={4}>
            <Text type="secondary">正在进行安全检查...</Text>
            <Progress percent={securityCheckProgress} size="small" />
          </Space>
        </div>
      )}

      {/* 输入区域 */}
      <Card 
        size="small" 
        style={{ 
          borderRadius: 0,
          borderTop: '1px solid #f0f0f0',
          flexShrink: 0,
        }}
      >
        <div style={{ display: 'flex', gap: 8, alignItems: 'flex-end' }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="输入您的消息... (支持Shift+Enter换行，Enter发送)"
            autoSize={{ minRows: 1, maxRows: 4 }}
            onPressEnter={(e) => {
              if (!e.shiftKey) {
                e.preventDefault()
                handleSendMessage()
              }
            }}
            disabled={isSending}
            style={{ flex: 1 }}
          />
          <Button 
            type="primary" 
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            loading={isSending}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </div>
        
        <div style={{ marginTop: 8, fontSize: 12, color: '#999' }}>
          <Space split={<span>•</span>}>
            <span>消息将经过安全检查</span>
            <span>Shift+Enter 换行</span>
            <span>Enter 发送</span>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default ChatPage 