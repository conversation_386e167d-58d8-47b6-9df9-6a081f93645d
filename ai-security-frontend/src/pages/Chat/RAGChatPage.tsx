import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  List,
  Avatar,
  Space,
  Typography,
  Spin,
  message,
  Tag,
  Tooltip,
  Row,
  Col,
  Drawer,
  Descriptions,
  Empty,
  Alert,
  Divider,
  Badge,
  Select,
  Modal,
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  FileTextOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  DatabaseOutlined,
  BookOutlined,
  PlusOutlined,
  HistoryOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons';
import api from '../../services/api';
import { ragSessionsAPI } from '../../services/ragSessionsAPI';
import { useAuthStore } from '../../store/authStore';
import type { Session, Message as APIMessage } from '../../types/api';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Text, Title, Paragraph } = Typography;

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: SearchResult[];
  error?: boolean;
}

interface SearchResult {
  document_id: number;
  document_name: string;
  chunk_id: number;
  content: string;
  similarity: number;
  metadata?: any;
}

interface RAGStatistics {
  total_queries: number;
  successful_queries: number;
  failed_queries: number;
  average_documents_retrieved: number;
  most_used_categories: Array<{
    category_name: string;
    usage_count: number;
  }>;
  query_trends: Array<any>;
  most_accessed_documents?: Array<{
    document_name: string;
    access_count: number;
  }>;
}

const RAGChatPage: React.FC = () => {
  const { user } = useAuthStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<RAGStatistics | null>(null);
  const [sourcesDrawerVisible, setSourcesDrawerVisible] = useState(false);
  const [selectedSources, setSelectedSources] = useState<SearchResult[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 会话管理状态
  const [sessions, setSessions] = useState<Session[]>([]);
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [sessionsLoading, setSessionsLoading] = useState(false);
  const [newSessionTitle, setNewSessionTitle] = useState('');

  useEffect(() => {
    fetchStatistics();
    loadSessions();
  }, []);

  // 当选择会话时，加载会话消息
  useEffect(() => {
    if (currentSession) {
      loadSessionMessages(currentSession.id);
    } else {
      // 没有选择会话时显示欢迎消息
      setMessages([{
        id: '1',
        type: 'assistant',
        content: '您好！我是AI知识库助手。我可以基于系统中的知识库文档回答您的问题。请选择或创建一个会话开始对话。',
        timestamp: new Date(),
      }]);
    }
  }, [currentSession]);

  const fetchStatistics = async () => {
    try {
      const response = await api.get('/rag/statistics');
      setStatistics(response.data);
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  // 加载会话列表
  const loadSessions = async () => {
    try {
      setSessionsLoading(true);
      const sessionList = await ragSessionsAPI.list();
      setSessions(sessionList);

      // 如果有会话，默认选择第一个
      if (sessionList.length > 0 && !currentSession) {
        setCurrentSession(sessionList[0]);
      }
    } catch (error) {
      console.error('加载会话列表失败:', error);
      message.error('加载会话列表失败');
    } finally {
      setSessionsLoading(false);
    }
  };

  // 加载会话消息
  const loadSessionMessages = async (sessionId: number) => {
    try {
      const sessionMessages = await ragSessionsAPI.getMessages(sessionId);

      // 转换API消息格式为组件消息格式
      const convertedMessages: Message[] = sessionMessages.map((msg: any) => ({
        id: msg.id.toString(),
        type: msg.sender_type === 'user' ? 'user' : 'assistant',
        content: msg.content,
        timestamp: new Date(msg.created_at),
        error: msg.is_blocked
      }));

      setMessages(convertedMessages);
    } catch (error) {
      console.error('加载会话消息失败:', error);
      message.error('加载会话消息失败');
    }
  };

  // 创建新会话
  const createNewSession = async (title?: string) => {
    try {
      const sessionTitle = title || `知识库问答 ${dayjs().format('MM-DD HH:mm')}`;
      const newSession = await ragSessionsAPI.create({
        title: sessionTitle,
        llm_model_id: 13 // 默认使用阿里云百炼模型
      });

      setSessions(prev => [newSession, ...prev]);
      setCurrentSession(newSession);
      setMessages([]);
      message.success('新会话创建成功');

      return newSession;
    } catch (error) {
      console.error('创建会话失败:', error);
      message.error('创建会话失败');
      return null;
    }
  };

  // 删除会话
  const deleteSession = async (sessionId: number) => {
    try {
      await ragSessionsAPI.delete(sessionId);
      setSessions(prev => prev.filter(s => s.id !== sessionId));

      if (currentSession?.id === sessionId) {
        const remainingSessions = sessions.filter(s => s.id !== sessionId);
        setCurrentSession(remainingSessions.length > 0 ? remainingSessions[0] : null);
      }

      message.success('会话已删除');
    } catch (error) {
      console.error('删除会话失败:', error);
      message.error('删除会话失败');
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!inputValue.trim()) {
      message.warning('请输入您的问题');
      return;
    }

    // 如果没有当前会话，先创建一个
    let sessionToUse = currentSession;
    if (!sessionToUse) {
      sessionToUse = await createNewSession();
      if (!sessionToUse) return; // 创建失败
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const queryText = inputValue.trim();
    setInputValue('');
    setLoading(true);

    try {
      console.log('📤 发送RAG查询:', queryText);
      const startTime = Date.now();

      // 使用生产级RAG会话API发送查询
      const response = await ragSessionsAPI.sendQuery(sessionToUse.id, queryText, {
        model_id: 13,  // 使用阿里云百炼模型
        top_k: 10,     // 增加检索文档数
        temperature: 0.7,
        max_tokens: 800,
        include_sources: true,
      });
      
      const endTime = Date.now();
      console.log(`⏱️ RAG查询耗时: ${(endTime - startTime) / 1000}秒`);

      console.log('🔍 RAG API响应:', response);

      // 检查响应结构
      if (response.success && response.answer) {
        // 由于我们使用了会话API，消息已经保存到数据库
        // 重新加载会话消息以获取最新的对话记录
        await loadSessionMessages(sessionToUse.id);

        if (response.sources && response.sources.length > 0) {
          message.info(`找到 ${response.sources.length} 个相关文档`);
        }

        // 刷新统计信息
        fetchStatistics();
      } else {
        // 处理错误情况
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: response.error || '抱歉，我无法回答您的问题。请尝试重新表述或联系管理员。',
          timestamp: new Date(),
          error: true,
        };

        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error: any) {
      console.error('❌ RAG API调用出错:', error);
      console.error('❌ 错误响应:', error.response);
      console.error('❌ 错误数据:', error.response?.data);
      console.error('❌ 错误状态:', error.response?.status);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: error.response?.data?.detail || '抱歉，处理您的请求时出现了错误。请稍后再试。',
        timestamp: new Date(),
        error: true,
      };
      setMessages(prev => [...prev, errorMessage]);
      message.error(`查询失败: ${error.response?.status || 'unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const showSources = (sources: SearchResult[]) => {
    setSelectedSources(sources);
    setSourcesDrawerVisible(true);
  };

  const renderMessage = (msg: Message) => {
    const isUser = msg.type === 'user';
    
    return (
      <List.Item
        style={{
          padding: '12px 0',
          border: 'none',
        }}
      >
        <Space
          align="start"
          style={{
            width: '100%',
            flexDirection: isUser ? 'row-reverse' : 'row',
          }}
        >
          <Avatar
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
            }}
          />
          <Card
            style={{
              maxWidth: '70%',
              backgroundColor: isUser ? '#e6f7ff' : msg.error ? '#fff1f0' : '#f6ffed',
              border: 'none',
            }}
            styles={{ body: { padding: '12px 16px' } }}
          >
            <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {msg.content}
            </Paragraph>
            {msg.sources && msg.sources.length > 0 && (
              <div style={{ marginTop: 8 }}>
                <Button
                  type="link"
                  size="small"
                  icon={<FileTextOutlined />}
                  onClick={() => showSources(msg.sources!)}
                >
                  查看 {msg.sources.length} 个参考文档
                </Button>
              </div>
            )}
            <Text type="secondary" style={{ fontSize: 12 }}>
              {dayjs(msg.timestamp).format('HH:mm:ss')}
            </Text>
          </Card>
        </Space>
      </List.Item>
    );
  };

  return (
    <Row gutter={16} style={{ height: '100%', padding: 16 }}>
      <Col span={6}>
        <Card
          title={
            <Space>
              <HistoryOutlined />
              <span>会话历史</span>
            </Space>
          }
          extra={
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => createNewSession()}
              loading={sessionsLoading}
            >
              新会话
            </Button>
          }
          style={{ height: '100%', marginBottom: 0 }}
          styles={{ body: { padding: '8px 0' } }}
        >
          <List
            loading={sessionsLoading}
            dataSource={sessions}
            renderItem={(session) => (
              <List.Item
                style={{
                  padding: '8px 16px',
                  cursor: 'pointer',
                  backgroundColor: currentSession?.id === session.id ? '#f0f0f0' : 'transparent',
                }}
                onClick={() => setCurrentSession(session)}
                actions={[
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSession(session.id);
                    }}
                    danger
                  />
                ]}
              >
                <List.Item.Meta
                  title={
                    <div style={{ fontSize: '14px', fontWeight: 'normal' }}>
                      {session.title}
                    </div>
                  }
                  description={
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      {dayjs(session.updated_at).format('MM-DD HH:mm')}
                    </div>
                  }
                />
              </List.Item>
            )}
            locale={{ emptyText: '暂无会话' }}
          />
        </Card>
      </Col>

      <Col span={18}>
        <Card
          title={
            <Space>
              <DatabaseOutlined />
              <span>知识库问答</span>
              {currentSession && (
                <Tag color="blue">{currentSession.title}</Tag>
              )}
            </Space>
          }
          extra={
            <Space>
              <Badge count={messages.length} showZero>
                <Tag icon={<BookOutlined />}>消息</Tag>
              </Badge>
            </Space>
          }
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
          styles={{ body: { flex: 1, display: 'flex', flexDirection: 'column', padding: 0 } }}
        >
          <div
            style={{
              flex: 1,
              overflowY: 'auto',
              padding: '16px',
              borderBottom: '1px solid #f0f0f0',
            }}
          >
            <List
              dataSource={messages}
              renderItem={renderMessage}
              locale={{ emptyText: '暂无对话' }}
            />
            <div ref={messagesEndRef} />
          </div>

          <div style={{ padding: 16 }}>
            <Space.Compact style={{ width: '100%' }}>
              <TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onPressEnter={handleKeyPress}
                placeholder="请输入您的问题..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                disabled={loading}
                style={{ borderRadius: '4px 0 0 4px' }}
              />
              <Button
                type="primary"
                icon={loading ? <Spin size="small" /> : <SendOutlined />}
                onClick={handleSend}
                disabled={loading || !inputValue.trim()}
                style={{ height: 'auto' }}
              >
                发送
              </Button>
            </Space.Compact>
            <Text type="secondary" style={{ fontSize: 12, marginTop: 4 }}>
              按 Enter 发送，Shift + Enter 换行
            </Text>
          </div>
        </Card>
      </Col>

      <Col span={6} style={{ display: 'none' }}>
        {/* 统计面板暂时隐藏，为会话列表让出空间 */}
        <Card
          title={
            <Space>
              <InfoCircleOutlined />
              <span>使用统计</span>
            </Space>
          }
          style={{ marginBottom: 16 }}
        >
          {statistics ? (
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">总查询次数</Text>
                <Title level={4} style={{ margin: 0 }}>
                  {statistics.total_queries}
                </Title>
              </div>
              <Divider style={{ margin: '12px 0' }} />
              <div>
                <Text type="secondary">成功率</Text>
                <Title level={4} style={{ margin: 0 }}>
                  {statistics.total_queries > 0
                    ? ((statistics.successful_queries / statistics.total_queries) * 100).toFixed(1)
                    : 0}%
                </Title>
              </div>
              <Divider style={{ margin: '12px 0' }} />
              <div>
                <Text type="secondary">平均文档数/查询</Text>
                <Title level={4} style={{ margin: 0 }}>
                  {statistics.average_documents_retrieved.toFixed(1)}
                </Title>
              </div>
            </Space>
          ) : (
            <Empty description="暂无统计数据" />
          )}
        </Card>

        <Card
          title={
            <Space>
              <SearchOutlined />
              <span>热门文档</span>
            </Space>
          }
        >
          {statistics && statistics.most_accessed_documents && statistics.most_accessed_documents.length > 0 ? (
            <List
              size="small"
              dataSource={statistics.most_accessed_documents.slice(0, 5)}
              renderItem={(item) => (
                <List.Item>
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <Text ellipsis style={{ flex: 1 }}>
                      {item.document_name}
                    </Text>
                    <Badge count={item.access_count} showZero />
                  </Space>
                </List.Item>
              )}
            />
          ) : (
            <Empty description="暂无数据" />
          )}
        </Card>

        <Alert
          message="使用提示"
          description={
            <ul style={{ paddingLeft: 20, margin: '8px 0' }}>
              <li>系统会基于知识库中的文档回答您的问题</li>
              <li>回答会显示参考的文档来源</li>
              <li>请确保您有相应文档的访问权限</li>
              <li>系统会对敏感信息进行脱敏处理</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Col>

      <Drawer
        title="参考文档"
        placement="right"
        onClose={() => setSourcesDrawerVisible(false)}
        open={sourcesDrawerVisible}
        width={600}
      >
        {selectedSources.map((source) => (
          <Card
            key={source.chunk_id}
            title={
              <Space>
                <FileTextOutlined />
                <Text>{source.document_name}</Text>
                <Tag color="blue">
                  相似度: {(source.similarity * 100).toFixed(1)}%
                </Tag>
              </Space>
            }
            style={{ marginBottom: 16 }}
          >
            <Paragraph
              ellipsis={{ rows: 5, expandable: true }}
              style={{ marginBottom: 0 }}
            >
              {source.content}
            </Paragraph>
            {source.metadata && (
              <Descriptions
                size="small"
                style={{ marginTop: 12 }}
                column={1}
              >
                <Descriptions.Item label="文档ID">
                  {source.document_id}
                </Descriptions.Item>
                <Descriptions.Item label="片段ID">
                  {source.chunk_id}
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>
        ))}
      </Drawer>
    </Row>
  );
};

export default RAGChatPage; 