import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Space,
  Tag,
  message,
  Row,
  Col,
  Statistic,
  Alert,
  Badge,
  Checkbox,
  Typography,
  Spin,
  Tooltip
} from 'antd';
import {
  SecurityScanOutlined,
  EditOutlined,
  ReloadOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import api from '../../services/api';

const { Title, Text } = Typography;

interface LevelSummary {
  level_id: number;
  level_name: string;
  rank_value: number;
  permissions: {
    [key: string]: boolean;
  };
  accessible_count: number;
}

interface LevelDataAccessSummary {
  total_levels: number;
  sensitivity_levels: {
    [key: string]: string;
  };
  summary: LevelSummary[];
}

interface LevelDataAccess {
  id: number;
  level_id: number;
  level_name: string;
  sensitivity_level: number;
  sensitivity_name: string;
  can_access: boolean;
}

interface AccessRule {
  sensitivity_level: number;
  can_access: boolean;
}

const LevelDataAccessManagement: React.FC = () => {
  const [summaryData, setSummaryData] = useState<LevelDataAccessSummary | null>(null);
  const [levelAccess, setLevelAccess] = useState<LevelDataAccess[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedLevelId, setSelectedLevelId] = useState<number | null>(null);
  const [selectedLevelName, setSelectedLevelName] = useState<string>('');
  const [editForm] = Form.useForm();

  const sensitivityConfig = {
    1: { color: 'red', text: '高级敏感', description: '最高级别敏感数据' },
    2: { color: 'orange', text: '中级敏感', description: '中等级别敏感数据' },
    3: { color: 'blue', text: '初级敏感', description: '低级别敏感数据' },
    4: { color: 'green', text: '完全开放', description: '公开数据' }
  };

  const fetchSummaryData = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/level-data-access/levels/data-access/summary');
      console.log('获取到的汇总数据:', response.data);
      
      // 特别打印部门总管理的数据
      const deptMgr = response.data.summary?.find((item: any) => item.level_name === '部门总管理');
      if (deptMgr) {
        console.log('部门总管理权限详情:', deptMgr);
      }
      
      setSummaryData(response.data);
    } catch (error) {
      console.error('获取权限汇总数据失败:', error);
      message.error('获取权限汇总数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchLevelAccess = async (levelId: number) => {
    try {
      const response = await api.get(`/admin/level-data-access/levels/${levelId}/data-access`);
      setLevelAccess(response.data);
      
      const initialValues: { [key: string]: boolean } = {};
      response.data.forEach((access: LevelDataAccess) => {
        initialValues[`level_${access.sensitivity_level}`] = access.can_access;
      });
      
      // 添加调试信息
      console.log('API返回的数据:', response.data);
      console.log('设置的表单初始值:', initialValues);
      
      editForm.setFieldsValue(initialValues);
      
      // 验证设置后的表单值
      setTimeout(() => {
        const currentValues = editForm.getFieldsValue();
        console.log('表单当前值:', currentValues);
      }, 100);
    } catch (error) {
      console.error('获取级别权限详情失败:', error);
      message.error('获取级别权限详情失败');
    }
  };

  useEffect(() => {
    fetchSummaryData();
  }, []);

  const getSensitivityTag = (level: number) => {
    const config = sensitivityConfig[level as keyof typeof sensitivityConfig];
    if (!config) return <Tag>未知</Tag>;
    return (
      <Tooltip title={config.description}>
        <Tag color={config.color}>{config.text}</Tag>
      </Tooltip>
    );
  };

  const handleEdit = async (levelId: number, levelName: string) => {
    setSelectedLevelId(levelId);
    setSelectedLevelName(levelName);
    await fetchLevelAccess(levelId);
    setModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await editForm.validateFields();
      
      const accessRules: AccessRule[] = [];
      Object.keys(sensitivityConfig).forEach(level => {
        const sensitivityLevel = parseInt(level);
        const canAccess = values[`level_${sensitivityLevel}`] || false;
        // 保存所有权限状态，包括 true 和 false
        accessRules.push({
          sensitivity_level: sensitivityLevel,
          can_access: canAccess
        });
      });

      console.log('准备保存的权限配置:', accessRules);
      
      await api.put(`/admin/level-data-access/levels/${selectedLevelId}/data-access`, accessRules);
      message.success('权限配置更新成功');
      setModalVisible(false);
      fetchSummaryData();
    } catch (error) {
      console.error('保存权限配置失败:', error);
      message.error('保存权限配置失败');
    }
  };

  const handleResetDefault = async (levelId: number, levelName: string) => {
    try {
      await api.post(`/admin/level-data-access/levels/${levelId}/data-access/reset-default`);
      message.success(`级别 "${levelName}" 的权限已重置为默认规则`);
      fetchSummaryData();
    } catch (error) {
      console.error('重置权限失败:', error);
      message.error('重置权限失败');
    }
  };

  const summaryColumns: ColumnsType<LevelSummary> = [
    {
      title: '级别名称',
      dataIndex: 'level_name',
      key: 'level_name',
      width: 150,
      render: (text: string, record: LevelSummary) => (
        <Space>
          <SecurityScanOutlined />
          <Text strong>{text}</Text>
          <Badge count={record.rank_value} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      ),
    },
    {
      title: '权限矩阵',
      key: 'permissions',
      render: (_, record: LevelSummary) => (
        <Space size="small">
          {Object.entries(record.permissions).map(([name, allowed]) => (
            <Tooltip key={name} title={`${name}: ${allowed ? '允许访问' : '禁止访问'}`}>
              <Tag 
                color={allowed ? 'green' : 'red'} 
                icon={allowed ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
              >
                {name.replace('敏感', '')}
              </Tag>
            </Tooltip>
          ))}
        </Space>
      ),
    },
    {
      title: '可访问数据类型',
      dataIndex: 'accessible_count',
      key: 'accessible_count',
      width: 150,
      render: (count: number) => (
        <Statistic 
          value={count} 
          suffix="/ 4" 
          valueStyle={{ 
            color: count >= 3 ? '#3f8600' : count >= 2 ? '#fa8c16' : '#cf1322',
            fontSize: '16px'
          }}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record: LevelSummary) => (
        <Space>
          <Button 
            type="primary" 
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.level_id, record.level_name)}
          >
            编辑权限
          </Button>
          <Button 
            size="small"
            icon={<HistoryOutlined />}
            onClick={() => handleResetDefault(record.level_id, record.level_name)}
          >
            重置默认
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Title level={2}>
            <SecurityScanOutlined /> 级别数据访问权限管理
          </Title>
          <Text type="secondary">
            管理不同用户级别对各敏感度数据的访问权限，支持细粒度权限控制
          </Text>
        </div>

        <Alert
          message="权限说明"
          description="高级敏感 > 中级敏感 > 初级敏感 > 完全开放，用户可访问其权限范围内的所有数据类型"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="管理级别总数"
                value={summaryData?.total_levels || 0}
                prefix={<SettingOutlined />}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="敏感度级别"
                value={Object.keys(summaryData?.sensitivity_levels || {}).length}
                prefix={<SecurityScanOutlined />}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={fetchSummaryData}
                  loading={loading}
                >
                  刷新数据
                </Button>
              </div>
            </Card>
          </Col>
        </Row>

        <Card title="级别权限配置汇总" extra={
          <Tooltip title="点击编辑按钮可修改特定级别的数据访问权限">
            <InfoCircleOutlined />
          </Tooltip>
        }>
          <Spin spinning={loading}>
            <Table
              columns={summaryColumns}
              dataSource={summaryData?.summary || []}
              rowKey="level_id"
              pagination={false}
              size="middle"
            />
          </Spin>
        </Card>

        <Modal
          title={`编辑级别权限 - ${selectedLevelName}`}
          open={modalVisible}
          onOk={handleSave}
          onCancel={() => setModalVisible(false)}
          width={600}
          okText="保存"
          cancelText="取消"
        >
          <Alert
            message="权限设置说明"
            description="选中的敏感度级别表示该用户级别可以访问对应的数据类型。请根据业务需求谨慎配置。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Form form={editForm} layout="vertical">
            <div style={{ marginBottom: 16 }}>
              <Text strong>选择可访问的数据敏感度级别：</Text>
            </div>
            
            {Object.entries(sensitivityConfig).map(([level, config]) => (
              <Form.Item
                key={level}
                name={`level_${level}`}
                valuePropName="checked"
              >
                <Card size="small" style={{ marginBottom: 8 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    <Checkbox />
                    {getSensitivityTag(parseInt(level))}
                    <Text>{config.description}</Text>
                  </div>
                </Card>
              </Form.Item>
            ))}
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default LevelDataAccessManagement; 