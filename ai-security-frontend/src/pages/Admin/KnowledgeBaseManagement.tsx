import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Card,
  message,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Progress,
  Tabs,
  Alert,
  Spin,
  Popconfirm,
  Badge,
  Descriptions,
  Typography
} from 'antd';
import {
  UploadOutlined,
  SearchOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileUnknownOutlined,
  InfoCircleOutlined,
  CloudUploadOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile, UploadProps } from 'antd/es/upload';
import api from '../../services/api';
import dayjs from 'dayjs';
import ModelConfigManagement from '../../components/admin/ModelConfigManagement';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

interface KnowledgeDocument {
  id: number;
  title: string;
  description?: string;
  file_name: string;
  file_path: string;
  file_size: number;
  file_hash: string;
  document_type: string;
  error_message?: string;
  category_id: number;
  grade_id: number;
  chunk_count: number;
  version: number;
  created_by: number;
  created_at: string;
  updated_at: string;
  doc_metadata?: any;
  category_name?: string;
  grade_name?: string;
  creator_name?: string;
  
  name?: string;
  file_type?: string;
  status?: string;
  category?: {
    id: number;
    name: string;
  };
  grade?: {
    id: number;
    name: string;
    sensitivity_level: number;
  };
  creator?: {
    id: number;
    username: string;
  };
}

interface SearchResult {
  document_id: number;
  document_name: string;
  chunk_id: number;
  content: string;
  similarity: number;
  metadata: any;
}

interface Statistics {
  total_documents: number;
  total_chunks: number;
  total_size_bytes: number;
  by_status: Record<string, number>;
  by_type: Record<string, number>;
  by_category: Record<string, number>;
  by_grade: Record<string, number>;
}

interface Grade {
  id: number;
  name: string;
  description?: string;
  category_id: number;
  category_name?: string;
  sensitivity_level: number;
}

const KnowledgeBaseManagement: React.FC = () => {
  const [documents, setDocuments] = useState<KnowledgeDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<KnowledgeDocument | null>(null);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [grades, setGrades] = useState<Grade[]>([]);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [activeTab, setActiveTab] = useState('documents');
  
  const [uploadForm] = Form.useForm();
  const [searchForm] = Form.useForm();

  useEffect(() => {
    fetchDocuments();
    fetchStatistics();
    fetchCategories();
    fetchGrades();
  }, []);

  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const response = await api.get('/knowledge-base/documents', {
        params: { limit: 1000 }  // 获取所有文档，使用后端允许的最大值
      });
      if (response.data && response.data.items) {
        setDocuments(response.data.items);
      } else if (Array.isArray(response.data)) {
        setDocuments(response.data);
      } else {
        console.warn('意外的文档列表响应格式:', response.data);
        setDocuments([]);
      }
    } catch (error: any) {
      message.error('获取文档列表失败: ' + (error.response?.data?.detail || error.message));
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await api.get('/knowledge-base/statistics');
      setStatistics(response.data);
    } catch (error: any) {
      console.error('获取统计信息失败:', error);
      setStatistics({
        total_documents: 0,
        total_chunks: 0,
        total_size_bytes: 0,
        by_status: {},
        by_type: {},
        by_category: {},
        by_grade: {}
      });
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await api.get('/knowledge-base/data-categories');
      const categoriesData = Array.isArray(response.data) ? response.data : [];
      console.log('Categories loaded:', categoriesData); // 调试日志
      setCategories(categoriesData);
    } catch (error: any) {
      console.error('获取分类列表失败:', error);
      setCategories([]);
    }
  };

  const fetchGrades = async () => {
    try {
      const response = await api.get('/knowledge-base/data-grades');
      const gradesData = Array.isArray(response.data) ? response.data : [];
      console.log('Grades loaded:', gradesData); // 调试日志
      setGrades(gradesData);
    } catch (error: any) {
      console.error('获取分级列表失败:', error);
      setGrades([]);
    }
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      case 'doc':
      case 'docx':
        return <FileWordOutlined style={{ color: '#1890ff' }} />;
      case 'xls':
      case 'xlsx':
        return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'ppt':
      case 'pptx':
        return <FilePptOutlined style={{ color: '#fa8c16' }} />;
      case 'txt':
        return <FileTextOutlined style={{ color: '#8c8c8c' }} />;
      default:
        return <FileUnknownOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      default:
        return status;
    }
  };

  const handleUpload = async (values: any) => {
    if (fileList.length === 0) {
      message.error('请选择要上传的文件');
      return;
    }

    console.log('Upload values:', values); // 调试日志
    console.log('Categories available:', categories); // 调试日志
    console.log('Grades available:', grades); // 调试日志

    // 验证必需字段
    if (!values.category_id) {
      message.error('请选择文档分类');
      return;
    }
    if (!values.grade_id) {
      message.error('请选择文档分级');
      return;
    }

    const formData = new FormData();
    fileList.forEach(file => {
      // 由于beforeUpload返回false，文件对象就是原始文件
      const fileObj = file.originFileObj || (file as any);
      if (fileObj instanceof File) {
        formData.append('file', fileObj);
      }
    });
    
    // 确保参数正确添加到FormData
    formData.append('category_id', values.category_id.toString());
    formData.append('grade_id', values.grade_id.toString());
    
    if (values.description) {
      formData.append('description', values.description);
    }
    
    // 添加标题（可选）
    if (values.title) {
      formData.append('title', values.title);
    }

    // 调试：打印FormData内容
    console.log('FormData entries:');
    for (let pair of formData.entries()) {
      console.log(pair[0] + ': ' + pair[1]);
    }

    try {
      setLoading(true);
      const response = await api.post('/knowledge-base/documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      message.success('文档上传成功');
      setUploadModalVisible(false);
      uploadForm.resetFields();
      setFileList([]);
      fetchDocuments();
      fetchStatistics();
    } catch (error: any) {
      console.error('Upload error:', error); // 详细错误日志
      console.error('Error response:', error.response); // 错误响应详情
      
      let errorMessage = '未知错误';
      
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        
        // 如果是422验证错误，显示详细信息
        if (error.response.status === 422 && error.response.data?.detail) {
          const details = Array.isArray(error.response.data.detail) 
            ? error.response.data.detail 
            : [error.response.data.detail];
          
          console.error('Validation errors:', details);
          const errorMessages = details.map((detail: any) => {
            if (typeof detail === 'object' && detail.msg) {
              return `${detail.loc?.join?.('.') || 'unknown'}: ${detail.msg}`;
            }
            return detail.toString();
          });
          
          errorMessage = `验证错误：${errorMessages.join(', ')}`;
        } else {
          errorMessage = error.response?.data?.detail || error.message || '请求失败';
        }
      } else {
        errorMessage = error.message || '网络错误';
      }
      
      message.error('文档上传失败: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (values: any) => {
    try {
      setLoading(true);
      const response = await api.post('/knowledge-base/search', {
        query: values.query,
        top_k: values.top_k || 10,
        category_id: values.category_id,
        grade_id: values.grade_id,
        search_mode: values.search_mode || 'semantic',
      });
      setSearchResults(response.data.results || []);
      message.success(`找到 ${response.data.results?.length || 0} 个相关结果`);
    } catch (error: any) {
      message.error('搜索失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setLoading(true);
      await api.delete(`/knowledge-base/documents/${id}`);
      message.success('文档删除成功');
      fetchDocuments();
      fetchStatistics();
    } catch (error: any) {
      message.error('删除失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = (document: KnowledgeDocument) => {
    setSelectedDocument(document);
    setDetailModalVisible(true);
  };

  const uploadProps: UploadProps = {
    fileList,
    multiple: false,
    beforeUpload: (file) => {
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
      ];
      
      if (!allowedTypes.includes(file.type)) {
        message.error('只支持 PDF、Word、Excel、PPT 和文本文件');
        return false;
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB');
        return false;
      }
      
      setFileList([file]);
      return false;
    },
    onRemove: () => {
      setFileList([]);
    },
  };

  const columns: ColumnsType<KnowledgeDocument> = [
    {
      title: '文档名称',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (text, record) => (
        <Space>
          {getFileIcon(record.document_type || record.file_type || '')}
          <a onClick={() => handleViewDetail(record)}>{text || record.name}</a>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120,
      render: (categoryName, record) => {
        const name = categoryName || record.category?.name;
        return name ? <Tag color="blue">{name}</Tag> : '-';
      },
    },
    {
      title: '分级',
      dataIndex: 'grade_name',
      key: 'grade_name',
      width: 100,
      render: (gradeName, record) => {
        const name = gradeName || record.grade?.name;
        const level = record.grade?.sensitivity_level || 1;
        return name ? (
          <Tag color={level <= 2 ? 'red' : level <= 3 ? 'orange' : 'green'}>
            {name}
          </Tag>
        ) : '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => {
        const statusValue = status || record.status || '';
        return (
          <Tag color={getStatusColor(statusValue)}>{getStatusText(statusValue)}</Tag>
        );
      },
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size) => formatFileSize(size),
    },
    {
      title: '分块数',
      dataIndex: 'chunk_count',
      key: 'chunk_count',
      width: 80,
      render: (count) => <Badge count={count} showZero />,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version) => `v${version}`,
    },
    {
      title: '创建者',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
      render: (creatorName, record) => {
        return creatorName || record.creator?.username || '-';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="下载">
            <Button
              type="link"
              size="small"
              icon={<DownloadOutlined />}
              disabled
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个文档吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const searchColumns: ColumnsType<SearchResult> = [
    {
      title: '文档名称',
      dataIndex: 'document_name',
      key: 'document_name',
      width: 200,
      render: (text) => (
        <Space>
          <FileTextOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '内容片段',
      dataIndex: 'content',
      key: 'content',
      render: (text) => (
        <Paragraph ellipsis={{ rows: 2, expandable: true }}>
          {text}
        </Paragraph>
      ),
    },
    {
      title: '相似度',
      dataIndex: 'similarity',
      key: 'similarity',
      width: 120,
      render: (similarity) => (
        <Progress
          percent={Math.round(similarity * 100)}
          size="small"
          status={similarity > 0.8 ? 'success' : similarity > 0.6 ? 'normal' : 'exception'}
        />
      ),
    },
  ];

  return (
    <Card>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="文档管理" key="documents">
          <Space style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
            >
              上传文档
            </Button>
            <Button
              icon={<SearchOutlined />}
              onClick={() => setSearchModalVisible(true)}
            >
              搜索文档
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchDocuments}
              loading={loading}
            >
              刷新
            </Button>
          </Space>

          <Table
            columns={columns}
            dataSource={documents}
            rowKey="id"
            loading={loading}
            scroll={{ x: 1500 }}
            pagination={{
              pageSize: 20,  // 设置默认每页显示20条记录
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
          />
        </TabPane>

        <TabPane tab="统计信息" key="statistics">
          {statistics && (
            <>
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="文档总数"
                      value={statistics.total_documents}
                      prefix={<FolderOpenOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="分块总数"
                      value={statistics.total_chunks}
                      prefix={<FileTextOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="总存储大小"
                      value={formatFileSize(statistics.total_size_bytes)}
                      prefix={<CloudUploadOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="平均分块数"
                      value={statistics && statistics.total_documents > 0 
                        ? (statistics.total_chunks / statistics.total_documents).toFixed(1)
                        : '0'}
                      suffix="块/文档"
                    />
                  </Card>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Card title="按状态统计">
                    {statistics.by_status && Object.entries(statistics.by_status).map(([status, count]) => (
                      <div key={status} style={{ marginBottom: 8 }}>
                        <Space>
                          <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
                          <Text>{count} 个文档</Text>
                        </Space>
                      </div>
                    ))}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="按分类统计">
                    {statistics.by_category && Object.entries(statistics.by_category).map(([category, count]) => (
                      <div key={category} style={{ marginBottom: 8 }}>
                        <Space>
                          <Tag color="blue">{category}</Tag>
                          <Text>{count} 个文档</Text>
                        </Space>
                      </div>
                    ))}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="按分级统计">
                    {statistics.by_grade && Object.entries(statistics.by_grade).map(([grade, count]) => (
                      <div key={grade} style={{ marginBottom: 8 }}>
                        <Space>
                          <Tag color="orange">{grade}</Tag>
                          <Text>{count} 个文档</Text>
                        </Space>
                      </div>
                    ))}
                  </Card>
                </Col>
              </Row>
            </>
          )}
        </TabPane>

        <TabPane tab="模型配置" key="model-config">
          <ModelConfigManagement />
        </TabPane>
      </Tabs>

      {/* 上传文档模态框 */}
      <Modal
        title="上传文档"
        open={uploadModalVisible}
        onOk={() => uploadForm.submit()}
        onCancel={() => {
          setUploadModalVisible(false);
          uploadForm.resetFields();
          setFileList([]);
        }}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={handleUpload}
        >
          <Form.Item
            label="选择文件"
            required
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <Text type="secondary" style={{ fontSize: 12 }}>
              支持 PDF、Word、Excel、PPT 和文本文件，最大 10MB
            </Text>
          </Form.Item>

          <Form.Item
            name="category_id"
            label="文档分类"
            rules={[{ required: true, message: '请选择文档分类' }]}
          >
            <Select placeholder="请选择文档分类">
              {categories.map(cat => (
                <Option key={cat.id} value={cat.id}>{cat.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="grade_id"
            label="文档分级"
            rules={[{ required: true, message: '请选择文档分级' }]}
          >
            <Select placeholder="请选择文档分级">
              {grades.map(grade => (
                <Option key={grade.id} value={grade.id}>
                  <Tag color={grade.sensitivity_level <= 2 ? 'red' : grade.sensitivity_level <= 3 ? 'orange' : 'green'}>
                    {grade.name}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="文档描述"
          >
            <TextArea rows={3} placeholder="请输入文档描述（可选）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 搜索文档模态框 */}
      <Modal
        title="搜索文档"
        open={searchModalVisible}
        onOk={() => searchForm.submit()}
        onCancel={() => {
          setSearchModalVisible(false);
          searchForm.resetFields();
          setSearchResults([]);
        }}
        confirmLoading={loading}
        width={800}
      >
        <Form
          form={searchForm}
          layout="vertical"
          onFinish={handleSearch}
        >
          <Form.Item
            name="query"
            label="搜索内容"
            rules={[{ required: true, message: '请输入搜索内容' }]}
          >
            <TextArea rows={2} placeholder="请输入要搜索的内容" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="search_mode"
                label="搜索模式"
                initialValue="semantic"
              >
                <Select>
                  <Option value="semantic">语义搜索</Option>
                  <Option value="keyword">关键词搜索</Option>
                  <Option value="hybrid">混合搜索</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="top_k"
                label="返回结果数"
                initialValue={10}
              >
                <Select>
                  <Option value={5}>5</Option>
                  <Option value={10}>10</Option>
                  <Option value={20}>20</Option>
                  <Option value={50}>50</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category_id"
                label="限定分类"
              >
                <Select placeholder="不限" allowClear>
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.id}>{cat.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {searchResults.length > 0 && (
          <div style={{ marginTop: 24 }}>
            <Alert
              message={`找到 ${searchResults.length} 个相关结果`}
              type="success"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Table
              columns={searchColumns}
              dataSource={searchResults}
              rowKey="chunk_id"
              pagination={{
                pageSize: 5,
                showSizeChanger: false,
              }}
              size="small"
            />
          </div>
        )}
      </Modal>

      {/* 文档详情模态框 */}
      <Modal
        title="文档详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedDocument(null);
        }}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedDocument && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="文档名称" span={2}>
              <Space>
                {getFileIcon(selectedDocument.document_type || selectedDocument.file_type || '')}
                {selectedDocument.title || selectedDocument.name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="文件名">
              {selectedDocument.file_name || selectedDocument.name}
            </Descriptions.Item>
            <Descriptions.Item label="文件类型">
              {selectedDocument.document_type?.toUpperCase() || selectedDocument.file_type?.toUpperCase()}
            </Descriptions.Item>
            <Descriptions.Item label="文件大小">
              {formatFileSize(selectedDocument.file_size)}
            </Descriptions.Item>
            <Descriptions.Item label="文档分类">
              {selectedDocument.category_name || selectedDocument.category?.name ? (
                <Tag color="blue">{selectedDocument.category_name || selectedDocument.category?.name}</Tag>
              ) : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="文档分级">
              {selectedDocument.grade_name || selectedDocument.grade?.name ? (
                <Tag color={(selectedDocument.grade?.sensitivity_level || 1) <= 2 ? 'red' : 
                  (selectedDocument.grade?.sensitivity_level || 1) <= 3 ? 'orange' : 'green'}>
                  {selectedDocument.grade_name || selectedDocument.grade?.name}
                </Tag>
              ) : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="处理状态">
              <Tag color={getStatusColor(selectedDocument.status || '')}>
                {getStatusText(selectedDocument.status || '')}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="分块数量">
              <Badge count={selectedDocument.chunk_count} showZero />
            </Descriptions.Item>
            <Descriptions.Item label="版本号">
              v{selectedDocument.version}
            </Descriptions.Item>
            <Descriptions.Item label="文件哈希" span={2}>
              <Text code copyable>{selectedDocument.file_hash}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="创建者">
              {selectedDocument.creator_name || selectedDocument.creator?.username || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(selectedDocument.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间" span={2}>
              {dayjs(selectedDocument.updated_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            {selectedDocument.description && (
              <Descriptions.Item label="文档描述" span={2}>
                {selectedDocument.description}
              </Descriptions.Item>
            )}
            {selectedDocument.error_message && (
              <Descriptions.Item label="错误信息" span={2}>
                <Text type="danger">{selectedDocument.error_message}</Text>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </Card>
  );
};

export default KnowledgeBaseManagement; 