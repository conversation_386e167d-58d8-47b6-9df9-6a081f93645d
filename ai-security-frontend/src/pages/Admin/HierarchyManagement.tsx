import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Tabs,
  Button,
  message,
  Spin,
  Row,
  Col,
  Statistic,
  Alert,
  Space,
  Typography,
  Divider,
  Tag
} from 'antd';
import {
  TeamOutlined,
  NodeIndexOutlined,
  SettingOutlined,
  AuditOutlined,
  EyeOutlined,
  UserOutlined,
  ApiOutlined,
  SecurityScanOutlined,
  SafetyOutlined,
  DatabaseOutlined
} from '@ant-design/icons';

// Import hierarchy management components
import OrganizationTree from '../../components/hierarchy/OrganizationTree';
import ModelAssignmentManagement from '../../components/hierarchy/ModelAssignmentManagement';
import PermissionAuditLog from '../../components/hierarchy/PermissionAuditLog';
import PermissionVisualization from '../../components/hierarchy/PermissionVisualization';
import UserHierarchyConfig from '../../components/hierarchy/UserHierarchyConfig';

// Import new permission management components
import RolePermissionConfig from '../../components/admin/RolePermissionConfig';
import DataClassificationManagement from '../../components/admin/DataClassificationManagement';
import { apiFetch } from '../../config/api';
const { Content } = Layout;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

interface HierarchyStats {
  totalUsers: number;
  usersWithManagers: number;
  rootUsers: number;
  totalModelAssignments: number;
  avgSubordinatesPerManager: number;
  hierarchyDepth: number;
}

const HierarchyManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<HierarchyStats>({
    totalUsers: 0,
    usersWithManagers: 0,
    rootUsers: 0,
    totalModelAssignments: 0,
    avgSubordinatesPerManager: 0,
    hierarchyDepth: 0
  });
  const [activeTab, setActiveTab] = useState('organization');
  const [hierarchyValid, setHierarchyValid] = useState<boolean>(true);
  const [hierarchyIssues, setHierarchyIssues] = useState<string[]>([]);

  const loadHierarchyStats = async () => {
    setLoading(true);
    try {
      // 获取认证token
      const getAuthToken = () => {
        const authData = localStorage.getItem('auth-storage');
        if (authData) {
          try {
            const parsed = JSON.parse(authData);
            return parsed.state?.token || parsed.token;
          } catch (error) {
            console.error('解析认证token失败:', error);
          }
        }
        return null;
      };

      const token = getAuthToken();
      
      // 并行获取用户数据和模型分配数据
      const [usersResponse, assignmentsResponse] = await Promise.all([
        fetch('/api/v1/admin/users/', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/v1/admin/model-assignments/assignments', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (usersResponse.ok && assignmentsResponse.ok) {
        const users = await usersResponse.json();
        const assignments = await assignmentsResponse.json();
        
        // 计算真实统计数据
        const totalUsers = users.length;
        const usersWithManagers = users.filter((user: any) => user.manager_id).length;
        const rootUsers = users.filter((user: any) => !user.manager_id).length;
        const totalModelAssignments = assignments.length;
        
        // 计算平均下属数
        const managersWithSubordinates = users.filter((user: any) => 
          users.some((subordinate: any) => subordinate.manager_id === user.id)
        );
        const avgSubordinatesPerManager = managersWithSubordinates.length > 0 
          ? Math.round((usersWithManagers / managersWithSubordinates.length) * 100) / 100 
          : 0;
        
        // 计算层级深度（简化计算）
        let maxDepth = 1;
        users.forEach((user: any) => {
          let depth = 1;
          let currentUser = user;
          const visited = new Set();
          
          while (currentUser.manager_id && !visited.has(currentUser.id)) {
            visited.add(currentUser.id);
            depth++;
            currentUser = users.find((u: any) => u.id === currentUser.manager_id);
            if (!currentUser) break;
          }
          maxDepth = Math.max(maxDepth, depth);
        });

        setStats({
          totalUsers,
          usersWithManagers,
          rootUsers,
          totalModelAssignments,
          avgSubordinatesPerManager,
          hierarchyDepth: maxDepth
        });
        
        message.success('数据已刷新');
      } else {
        throw new Error('API请求失败');
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      message.error('加载统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  const validateHierarchy = async () => {
    setLoading(true);
    try {
      // 获取认证token
      const getAuthToken = () => {
        const authData = localStorage.getItem('auth-storage');
        if (authData) {
          try {
            const parsed = JSON.parse(authData);
            return parsed.state?.token || parsed.token;
          } catch (error) {
            console.error('解析认证token失败:', error);
          }
        }
        return null;
      };

      const token = getAuthToken();
      
      // 调用层级验证API
      const response = await fetch('/api/v1/admin/hierarchy/validate-hierarchy', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const validation = await response.json();
        setHierarchyValid(validation.valid);
        setHierarchyIssues(validation.issues || []);
        
        if (!validation.valid) {
          message.warning(`发现层级结构问题: ${validation.issues?.length || 0} 个`);
        } else {
          message.success('层级结构验证通过');
        }
      } else {
        throw new Error('验证API请求失败');
      }
    } catch (error) {
      console.error('验证层级结构失败:', error);
      message.error('验证层级结构失败');
      // 设置默认值
      setHierarchyValid(true);
      setHierarchyIssues([]);
    } finally {
      setLoading(false);
    }
  };



  const handleRefresh = () => {
    loadHierarchyStats();
    validateHierarchy();
  };

  // 组件初始化时加载数据
  useEffect(() => {
    loadHierarchyStats();
    validateHierarchy();
  }, []);

  const tabItems = [
    {
      key: 'organization',
      label: (
        <span>
          <TeamOutlined />
          组织架构
        </span>
      ),
      children: <OrganizationTree onRefresh={handleRefresh} />
    },
    {
      key: 'modelAssignment',
      label: (
        <span>
          <ApiOutlined />
          模型分配
        </span>
      ),
      children: <ModelAssignmentManagement onRefresh={handleRefresh} />
    },
    {
      key: 'userConfig',
      label: (
        <span>
          <SettingOutlined />
          用户配置
        </span>
      ),
      children: <UserHierarchyConfig onRefresh={handleRefresh} />
    },
    {
      key: 'visualization',
      label: (
        <span>
          <EyeOutlined />
          权限可视化
        </span>
      ),
      children: <PermissionVisualization />
    },
    {
      key: 'auditLog',
      label: (
        <span>
          <AuditOutlined />
          操作日志
        </span>
      ),
      children: <PermissionAuditLog />
    },
    {
      key: 'rolePermissions',
      label: (
        <span>
          <SafetyOutlined />
          角色权限配置
        </span>
      ),
      children: <RolePermissionConfig />
    },
    {
      key: 'dataClassification',
      label: (
        <span>
          <DatabaseOutlined />
          数据分类管理
        </span>
      ),
      children: <DataClassificationManagement />
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div>
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <SecurityScanOutlined style={{ marginRight: '12px' }} />
              分层权限管理
            </Title>
            <Text type="secondary">
              管理用户层级关系、模型分配权限和操作审计
            </Text>
          </div>
          <Space>
            <Button onClick={handleRefresh} loading={loading}>
              刷新数据
            </Button>
            <Button onClick={validateHierarchy}>
              验证结构
            </Button>
          </Space>
        </div>

        {/* 层级验证状态 */}
        {hierarchyValid !== null && (
          <Alert
            style={{ marginBottom: '24px' }}
            type={hierarchyValid ? 'success' : 'warning'}
            message={
              hierarchyValid 
                ? '层级结构验证通过' 
                : `层级结构存在问题 (${hierarchyIssues.length} 个)`
            }
            description={
              hierarchyValid 
                ? '组织架构完整，无循环引用或异常关系'
                : hierarchyIssues.length > 0 
                  ? hierarchyIssues.slice(0, 3).join(', ') + (hierarchyIssues.length > 3 ? '...' : '')
                  : '请检查组织架构配置'
            }
            showIcon
            action={
              <Button size="small" onClick={validateHierarchy}>
                重新验证
              </Button>
            }
          />
        )}

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={4}>
            <Card>
              <Statistic
                title="总用户数"
                value={stats?.totalUsers || 0}
                prefix={<UserOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="有上级用户"
                value={stats?.usersWithManagers || 0}
                prefix={<NodeIndexOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="根级用户"
                value={stats?.rootUsers || 0}
                prefix={<TeamOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="模型分配"
                value={stats?.totalModelAssignments || 0}
                prefix={<ApiOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="平均下属数"
                value={stats?.avgSubordinatesPerManager || 0}
                precision={1}
                prefix={<TeamOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="层级深度"
                value={stats?.hierarchyDepth || 0}
                prefix={<NodeIndexOutlined />}
                loading={loading}
              />
            </Card>
          </Col>
        </Row>



        {/* 主要内容区域 */}
        <Card style={{ minHeight: '600px' }}>
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            items={tabItems}
            size="large"
          />
        </Card>
      </Content>
    </Layout>
  );
};

export default HierarchyManagement; 