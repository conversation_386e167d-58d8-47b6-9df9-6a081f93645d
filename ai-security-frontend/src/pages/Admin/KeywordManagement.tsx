import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Space,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Select,
  Breadcrumb,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ArrowLeftOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuthStore } from '../../store/authStore';

const { Option } = Select;
const { TextArea } = Input;

interface Keyword {
  id: number;
  word: string;
  description?: string;
  is_active: boolean;
  priority: number;
  keyword_group_id: number;
  created_at: string;
  updated_at: string;
}

interface KeywordGroup {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  is_system: boolean;
}

interface SecurityPolicy {
  id: number;
  name: string;
  role_id: number;
  keyword_priority_threshold: number;
  target_name: string;
  enabled: boolean;
}

interface CreateKeywordRequest {
  word: string;
  description?: string;
  keyword_group_id: number;
  is_active: boolean;
  priority: number;
}

interface UpdateKeywordRequest {
  word?: string;
  description?: string;
  keyword_group_id?: number;
  is_active?: boolean;
  priority?: number;
}

interface KeywordListResponse {
  items: Keyword[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

interface KeywordManagementProps {
  groupId?: number;
  groupName?: string;
  onBack?: () => void;
}

const KeywordManagement: React.FC<KeywordManagementProps> = ({
  groupId,
  groupName,
  onBack
}) => {
  // Auth store
  const { token } = useAuthStore();
  
  const [loading, setLoading] = useState(false);
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [keywordGroups, setKeywordGroups] = useState<KeywordGroup[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [securityPolicies, setSecurityPolicies] = useState<SecurityPolicy[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedGroupId, setSelectedGroupId] = useState<number | undefined>(groupId);
  
  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingKeyword, setEditingKeyword] = useState<Keyword | null>(null);
  
  // Forms
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 获取关键词分组列表
  const fetchKeywordGroups = async () => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(
        '/api/v1/admin/keyword-groups/?skip=0&limit=1000',
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setKeywordGroups(data.items);
    } catch (error) {
      console.error('获取关键词分组失败:', error);
      message.error('获取关键词分组失败');
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('/api/v1/admin/roles/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setRoles(data);
    } catch (error) {
      console.error('获取角色失败:', error);
      message.error('获取角色失败');
    }
  };

  // 获取安全策略列表
  const fetchSecurityPolicies = async () => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('/api/v1/admin/security-policies/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setSecurityPolicies(data);
    } catch (error) {
      console.error('获取安全策略失败:', error);
      message.error('获取安全策略失败');
    }
  };

  // 获取关键词列表
  const fetchKeywords = async (page = 1, size = 10, group_id?: number) => {
    setLoading(true);
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      let url = `/api/v1/admin/keywords/?skip=${(page - 1) * size}&limit=${size}`;
      
      if (group_id) {
        url += `&group_id=${group_id}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: KeywordListResponse = await response.json();
      setKeywords(data.items);
      setTotal(data.total);
      setCurrentPage(page);
    } catch (error) {
      console.error('获取关键词失败:', error);
      message.error('获取关键词失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建关键词
  const createKeyword = async (values: CreateKeywordRequest) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('/api/v1/admin/keywords/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '创建失败');
      }

      message.success('关键词创建成功');
      setIsCreateModalVisible(false);
      createForm.resetFields();
      fetchKeywords(currentPage, pageSize, selectedGroupId);
    } catch (error: any) {
      console.error('创建关键词失败:', error);
      message.error(error.message || '创建关键词失败');
    }
  };

  // 更新关键词
  const updateKeyword = async (id: number, values: UpdateKeywordRequest) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(`/api/v1/admin/keywords/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '更新失败');
      }

      message.success('关键词更新成功');
      setIsEditModalVisible(false);
      setEditingKeyword(null);
      editForm.resetFields();
      fetchKeywords(currentPage, pageSize, selectedGroupId);
    } catch (error: any) {
      console.error('更新关键词失败:', error);
      message.error(error.message || '更新关键词失败');
    }
  };

  // 删除关键词
  const deleteKeyword = async (id: number) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(`/api/v1/admin/keywords/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '删除失败');
      }

      message.success('关键词删除成功');
      fetchKeywords(currentPage, pageSize, selectedGroupId);
    } catch (error: any) {
      console.error('删除关键词失败:', error);
      message.error(error.message || '删除关键词失败');
    }
  };

  // 处理编辑
  const handleEdit = (record: Keyword) => {
    setEditingKeyword(record);
    editForm.setFieldsValue({
      word: record.word,
      description: record.description || '',
      keyword_group_id: record.keyword_group_id,
      priority: record.priority,
      is_active: record.is_active,
    });
    setIsEditModalVisible(true);
  };

  // 处理分组筛选变化
  const handleGroupChange = (value: number | undefined) => {
    setSelectedGroupId(value);
    setCurrentPage(1);
    fetchKeywords(1, pageSize, value);
  };

  // 获取分组名称
  const getGroupName = (groupId: number) => {
    const group = keywordGroups.find(g => g.id === groupId);
    return group ? group.name : '未知分组';
  };

  // 计算关键词对哪些角色生效
  const getEffectiveRoles = (keywordPriority: number) => {
    const effectiveRoles: Array<{name: string, threshold: number}> = [];
    
    securityPolicies.forEach(policy => {
      if (policy.enabled && policy.role_id && keywordPriority >= policy.keyword_priority_threshold) {
        const role = roles.find(r => r.id === policy.role_id);
        if (role && role.is_active) {
          effectiveRoles.push({
            name: role.name,
            threshold: policy.keyword_priority_threshold
          });
        }
      }
    });
    
    return effectiveRoles;
  };

  // 表格列定义
  const columns: ColumnsType<Keyword> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '关键词',
      dataIndex: 'word',
      key: 'word',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: '所属分组',
      dataIndex: 'keyword_group_id',
      key: 'keyword_group_id',
      width: 150,
      render: (groupId: number) => (
        <Tag color="blue">{getGroupName(groupId)}</Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      sorter: (a: Keyword, b: Keyword) => b.priority - a.priority,
      render: (priority: number) => (
        <Tag color={priority >= 50 ? 'red' : priority >= 20 ? 'orange' : 'green'}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '生效角色',
      dataIndex: 'priority',
      key: 'effective_roles',
      width: 200,
      render: (priority: number) => {
        const effectiveRoles = getEffectiveRoles(priority);
        if (effectiveRoles.length === 0) {
          return <Tag color="gray">无角色检查</Tag>;
        }
        return (
          <Space size={[0, 4]} wrap>
            {effectiveRoles.map((role, index) => (
              <Tooltip 
                key={index} 
                title={`阈值: ${role.threshold} (关键词优先级 ${priority} >= 阈值 ${role.threshold})`}
              >
                <Tag color="blue" style={{ margin: '2px' }}>
                  {role.name}
                </Tag>
              </Tooltip>
            ))}
          </Space>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个关键词吗？"
              description="删除后无法恢复"
              onConfirm={() => deleteKeyword(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="link" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 初始化数据
  useEffect(() => {
    fetchKeywordGroups();
    fetchRoles();
    fetchSecurityPolicies();
  }, []);

  useEffect(() => {
    fetchKeywords(1, pageSize, selectedGroupId);
  }, [selectedGroupId]);

  return (
    <div style={{ padding: 24 }}>
      {/* 面包屑导航 */}
      {groupName && onBack && (
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>管理后台</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Button type="link" onClick={onBack} style={{ padding: 0 }}>
              关键词分组管理
            </Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{groupName} - 关键词管理</Breadcrumb.Item>
        </Breadcrumb>
      )}

      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              {onBack && (
                <Button 
                  icon={<ArrowLeftOutlined />} 
                  onClick={onBack}
                >
                  返回
                </Button>
              )}
              <h2 style={{ margin: 0 }}>
                关键词管理 {groupName && `- ${groupName}`}
              </h2>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => fetchKeywords(currentPage, pageSize, selectedGroupId)}
              >
                刷新
              </Button>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => {
                  createForm.setFieldValue('keyword_group_id', selectedGroupId);
                  setIsCreateModalVisible(true);
                }}
              >
                新建关键词
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 筛选区域 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <span>分组筛选：</span>
            <Select
              style={{ width: 200 }}
              placeholder="选择关键词分组"
              value={selectedGroupId}
              onChange={handleGroupChange}
              allowClear
            >
              {keywordGroups.map(group => (
                <Option key={group.id} value={group.id}>
                  {group.name}
                </Option>
              ))}
            </Select>
          </Space>
        </div>

        {/* 阈值机制说明 */}
        {securityPolicies.length > 0 && (
          <Alert
            message="关键词检查机制说明"
            description={
              <div>
                <p style={{ margin: '4px 0' }}>关键词检查规则：当 <strong>关键词优先级 ≥ 角色阈值</strong> 时，该角色会检查此关键词。</p>
                <Space wrap>
                  <span>当前角色阈值配置：</span>
                  {securityPolicies
                    .filter(p => p.enabled && p.role_id)
                    .map((policy, index) => (
                      <Tag key={index} color="purple">
                        {policy.target_name}: {policy.keyword_priority_threshold}
                      </Tag>
                    ))}
                </Space>
              </div>
            }
            type="info"
            style={{ marginBottom: 16 }}
            showIcon
            closable
          />
        )}

        {selectedGroupId && (
          <Alert
            message={`当前显示分组：${getGroupName(selectedGroupId)} 的关键词`}
            type="info"
            style={{ marginBottom: 16 }}
            showIcon
          />
        )}

        <Table
          columns={columns}
          dataSource={keywords}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setPageSize(size || 10);
              fetchKeywords(page, size || 10, selectedGroupId);
            },
          }}
        />
      </Card>

      {/* 创建关键词Modal */}
      <Modal
        title="创建关键词"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={createKeyword}
        >
          <Form.Item
            label="关键词"
            name="word"
            rules={[
              { required: true, message: '请输入关键词' },
              { min: 1, max: 200, message: '关键词长度必须在1-200字符之间' }
            ]}
          >
            <Input placeholder="请输入关键词" />
          </Form.Item>
          <Form.Item
            label="所属分组"
            name="keyword_group_id"
            rules={[
              { required: true, message: '请选择关键词分组' }
            ]}
          >
            <Select placeholder="请选择关键词分组">
              {keywordGroups.map(group => (
                <Option key={group.id} value={group.id}>
                  {group.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
            rules={[
              { max: 500, message: '描述长度不能超过500字符' }
            ]}
          >
            <TextArea 
              placeholder="请输入关键词描述（可选）" 
              rows={3}
            />
          </Form.Item>
          <Form.Item
            label="优先级"
            name="priority"
            rules={[
              { required: true, message: '请设置优先级' }
            ]}
            initialValue={0}
            tooltip="优先级决定关键词检查的敏感度，数值越高越重要。只有当关键词优先级 ≥ 用户角色阈值时才会触发检查。"
          >
            <Input 
              type="number" 
              min={0} 
              max={999} 
              placeholder="请输入优先级 (0-999)" 
            />
          </Form.Item>
          <Form.Item
            label="状态"
            name="is_active"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑关键词Modal */}
      <Modal
        title="编辑关键词"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingKeyword(null);
          editForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingKeyword) {
              updateKeyword(editingKeyword.id, values);
            }
          }}
        >
          <Form.Item
            label="关键词"
            name="word"
            rules={[
              { required: true, message: '请输入关键词' },
              { min: 1, max: 200, message: '关键词长度必须在1-200字符之间' }
            ]}
          >
            <Input placeholder="请输入关键词" />
          </Form.Item>
          <Form.Item
            label="所属分组"
            name="keyword_group_id"
            rules={[
              { required: true, message: '请选择关键词分组' }
            ]}
          >
            <Select placeholder="请选择关键词分组">
              {keywordGroups.map(group => (
                <Option key={group.id} value={group.id}>
                  {group.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
            rules={[
              { max: 500, message: '描述长度不能超过500字符' }
            ]}
          >
            <TextArea 
              placeholder="请输入关键词描述（可选）" 
              rows={3}
            />
          </Form.Item>
          <Form.Item
            label="优先级"
            name="priority"
            rules={[
              { required: true, message: '请设置优先级' }
            ]}
            tooltip="优先级决定关键词检查的敏感度，数值越高越重要。只有当关键词优先级 ≥ 用户角色阈值时才会触发检查。"
          >
            <Input 
              type="number" 
              min={0} 
              max={999} 
              placeholder="请输入优先级 (0-999)" 
            />
          </Form.Item>
          <Form.Item
            label="状态"
            name="is_active"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsEditModalVisible(false);
                setEditingKeyword(null);
                editForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KeywordManagement; 