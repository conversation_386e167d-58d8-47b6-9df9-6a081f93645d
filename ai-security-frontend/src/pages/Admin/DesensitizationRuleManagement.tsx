import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, Select, message, Space, Tooltip, Tag, Switch, Popconfirm, Badge, Tabs, Descriptions } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, PlayCircleOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { desensitizationApi } from '../../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface DesensitizationRule {
  id: number;
  name: string;
  description: string;
  data_type: string;
  pattern: string;
  masking_level: string;
  masking_strategy: string;
  replacement_text?: string;
  show_first?: number;
  show_last?: number;
  min_mask_length?: number;
  confidence_threshold: number;
  priority: number;
  is_active: boolean;
  role_id?: number;
  role?: {
    id: number;
    name: string;
  };
  match_count: number;
  mask_count: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
}

interface DesensitizationOptions {
  data_types: Array<{
    value: string;
    label: string;
    description: string;
  }>;
  masking_levels: Array<{
    value: string;
    label: string;
    description: string;
  }>;
  masking_strategies: Array<{
    value: string;
    label: string;
    description: string;
  }>;
}

interface TestResult {
  original_text: string;
  processed_text: string;
  matches: Array<{
    start: number;
    end: number;
    matched_text: string;
    masked_text: string;
    data_type: string;
    rule_name: string;
    confidence: number;
  }>;
  total_matches: number;
  rules_applied: number;
}

const DesensitizationRuleManagement: React.FC = () => {
  const [rules, setRules] = useState<DesensitizationRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<DesensitizationRule | null>(null);
  const [form] = Form.useForm();
  const [testForm] = Form.useForm();
  const [options, setOptions] = useState<DesensitizationOptions | null>(null);
  const [roles, setRoles] = useState<Array<{ id: number; name: string }>>([]);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [testLoading, setTestLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState<{
    data_type?: string;
    masking_level?: string;
    is_active?: boolean;
    role_id?: number;
    search?: string;
  }>({
    data_type: undefined,
    masking_level: undefined,
    is_active: undefined,
    role_id: undefined,
    search: undefined,
  });

  useEffect(() => {
    loadRules();
    loadOptions();
    loadRoles();
  }, [pagination.current, pagination.pageSize, filters]);

  const loadRules = async () => {
    setLoading(true);
    try {
      const response = await desensitizationApi.getRules({
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...filters,
      });
      setRules(response.items);
      setPagination(prev => ({
        ...prev,
        total: response.total,
      }));
    } catch (error) {
      message.error('加载脱敏规则失败');
    } finally {
      setLoading(false);
    }
  };

  const loadOptions = async () => {
    try {
      const response = await desensitizationApi.getOptions();
      setOptions(response);
    } catch (error) {
      message.error('加载配置选项失败');
    }
  };

  const loadRoles = async () => {
    try {
      // 暂时使用模拟角色数据，后续可以添加真实的角色API
      const mockRoles = [
        { id: 1, name: '管理员' },
        { id: 2, name: '普通用户' },
        { id: 3, name: '审核员' },
      ];
      setRoles(mockRoles);
    } catch (error) {
      console.error('加载角色失败:', error);
    }
  };

  const handleCreateRule = () => {
    setEditingRule(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditRule = (rule: DesensitizationRule) => {
    setEditingRule(rule);
    form.setFieldsValue({
      ...rule,
      role_id: rule.role_id || 0, // 0表示全局规则
    });
    setModalVisible(true);
  };

  const handleDeleteRule = async (id: number) => {
    try {
      await desensitizationApi.deleteRule(id);
      message.success('删除成功');
      loadRules();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleToggleRule = async (id: number) => {
    try {
      await desensitizationApi.toggleRule(id);
      message.success('状态更新成功');
      loadRules();
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        role_id: values.role_id === 0 ? null : values.role_id, // 0转换为null表示全局规则
      };

      if (editingRule) {
        await desensitizationApi.updateRule(editingRule.id, submitData);
        message.success('更新成功');
      } else {
        await desensitizationApi.createRule(submitData);
        message.success('创建成功');
      }
      
      setModalVisible(false);
      loadRules();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '操作失败');
    }
  };

  const handleTestRule = async (values: any) => {
    setTestLoading(true);
    try {
      const response = await desensitizationApi.testRule({
        text: values.text,
        rule_id: values.rule_id || null,
      });
      setTestResult(response);
    } catch (error: any) {
      message.error(error.response?.data?.detail || '测试失败');
    } finally {
      setTestLoading(false);
    }
  };

  const handleCreateDefaults = async () => {
    try {
      const response = await desensitizationApi.createDefaults();
      message.success(response.message);
      loadRules();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '创建默认规则失败');
    }
  };

  const columns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DesensitizationRule) => (
        <Space direction="vertical" size={0}>
          <span style={{ fontWeight: 500 }}>{text}</span>
          {record.description && (
            <span style={{ fontSize: '12px', color: '#666' }}>{record.description}</span>
          )}
        </Space>
      ),
    },
    {
      title: '数据类型',
      dataIndex: 'data_type',
      key: 'data_type',
      render: (text: string) => {
        const typeMap: Record<string, { color: string; label: string }> = {
          identity_card: { color: 'red', label: '身份证号' },
          credit_card: { color: 'orange', label: '信用卡号' },
          phone: { color: 'blue', label: '手机号' },
          email: { color: 'green', label: '邮箱地址' },
          ip_address: { color: 'purple', label: 'IP地址' },
          url: { color: 'cyan', label: '网址' },
          address: { color: 'geekblue', label: '地址' },
          bank_account: { color: 'volcano', label: '银行账号' },
          password: { color: 'magenta', label: '密码' },
          username: { color: 'gold', label: '用户名' },
          social_security: { color: 'lime', label: '社会保障号' },
          custom: { color: 'default', label: '自定义' },
        };
        const type = typeMap[text] || { color: 'default', label: text };
        return <Tag color={type.color}>{type.label}</Tag>;
      },
    },
    {
      title: '脱敏级别',
      dataIndex: 'masking_level',
      key: 'masking_level',
      render: (text: string) => {
        const levelMap: Record<string, { color: string; label: string }> = {
          low: { color: 'green', label: '低级别' },
          medium: { color: 'orange', label: '中级别' },
          high: { color: 'red', label: '高级别' },
          complete: { color: 'purple', label: '完全脱敏' },
        };
        const level = levelMap[text] || { color: 'default', label: text };
        return <Tag color={level.color}>{level.label}</Tag>;
      },
    },
    {
      title: '应用范围',
      dataIndex: 'role',
      key: 'role',
      render: (role: any, record: DesensitizationRule) => (
        role ? (
          <Tag color="blue">{role.name}</Tag>
        ) : (
          <Tag color="green">全局规则</Tag>
        )
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      sorter: true,
    },
    {
      title: '统计信息',
      key: 'stats',
      render: (record: DesensitizationRule) => (
        <Space direction="vertical" size={0}>
          <span style={{ fontSize: '12px' }}>
            匹配: <Badge count={record.match_count} style={{ backgroundColor: '#52c41a' }} />
          </span>
          <span style={{ fontSize: '12px' }}>
            脱敏: <Badge count={record.mask_count} style={{ backgroundColor: '#1890ff' }} />
          </span>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: DesensitizationRule) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleRule(record.id)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: DesensitizationRule) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                Modal.info({
                  title: '脱敏规则详情',
                  width: 800,
                  content: (
                    <Descriptions column={1} bordered size="small">
                      <Descriptions.Item label="规则名称">{record.name}</Descriptions.Item>
                      <Descriptions.Item label="描述">{record.description}</Descriptions.Item>
                      <Descriptions.Item label="正则表达式">
                        <code style={{ backgroundColor: '#f5f5f5', padding: '2px 4px' }}>
                          {record.pattern}
                        </code>
                      </Descriptions.Item>
                      <Descriptions.Item label="脱敏策略">{record.masking_strategy}</Descriptions.Item>
                      {record.replacement_text && (
                        <Descriptions.Item label="替换文本">{record.replacement_text}</Descriptions.Item>
                      )}
                      <Descriptions.Item label="置信度阈值">{record.confidence_threshold}</Descriptions.Item>
                      <Descriptions.Item label="创建时间">{record.created_at}</Descriptions.Item>
                      <Descriptions.Item label="创建者">{record.created_by}</Descriptions.Item>
                      <Descriptions.Item label="更新时间">{record.updated_at}</Descriptions.Item>
                      <Descriptions.Item label="更新者">{record.updated_by}</Descriptions.Item>
                    </Descriptions>
                  ),
                });
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEditRule(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个脱敏规则吗？"
              onConfirm={() => handleDeleteRule(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                icon={<DeleteOutlined />} 
                size="small" 
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card 
        title="脱敏规则管理" 
        extra={
          <Space>
            <Button 
              icon={<PlayCircleOutlined />}
              onClick={() => setTestModalVisible(true)}
            >
              测试脱敏
            </Button>
            <Button 
              icon={<SettingOutlined />}
              onClick={handleCreateDefaults}
            >
              创建默认规则
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleCreateRule}
            >
              新建规则
            </Button>
          </Space>
        }
      >
        {/* 过滤器 */}
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Select
              placeholder="数据类型"
              allowClear
              style={{ width: 120 }}
              value={filters.data_type}
              onChange={(value) => setFilters({ ...filters, data_type: value })}
            >
              {options?.data_types.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
            <Select
              placeholder="脱敏级别"
              allowClear
              style={{ width: 120 }}
              value={filters.masking_level}
              onChange={(value) => setFilters({ ...filters, masking_level: value })}
            >
              {options?.masking_levels.map(level => (
                <Option key={level.value} value={level.value}>{level.label}</Option>
              ))}
            </Select>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: 100 }}
              value={filters.is_active}
              onChange={(value) => setFilters({ ...filters, is_active: value })}
            >
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
            <Select
              placeholder="应用范围"
              allowClear
              style={{ width: 120 }}
              value={filters.role_id}
              onChange={(value) => setFilters({ ...filters, role_id: value })}
            >
              <Option value={0}>全局规则</Option>
              {roles.map(role => (
                <Option key={role.id} value={role.id}>{role.name}</Option>
              ))}
            </Select>
            <Input.Search
              placeholder="搜索规则名称或描述"
              style={{ width: 200 }}
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              onSearch={() => {
                setPagination({ ...pagination, current: 1 });
              }}
              allowClear
            />
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={rules}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => setPagination({ ...pagination, current: page, pageSize }),
            onShowSizeChange: (current, size) => setPagination({ ...pagination, current: 1, pageSize: size }),
          }}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingRule ? '编辑脱敏规则' : '新建脱敏规则'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="输入规则名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={2} placeholder="输入规则描述" />
          </Form.Item>

          <Form.Item
            name="data_type"
            label="数据类型"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择数据类型">
              {options?.data_types.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                  <span style={{ color: '#666', marginLeft: 8 }}>({type.description})</span>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="pattern"
            label={
              <span>
                正则表达式
                <Tooltip title="用于匹配敏感数据的正则表达式模式">
                  <InfoCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: '请输入正则表达式' }]}
          >
            <Input placeholder="输入正则表达式模式" />
          </Form.Item>

          <Form.Item
            name="masking_level"
            label="脱敏级别"
            rules={[{ required: true, message: '请选择脱敏级别' }]}
          >
            <Select placeholder="选择脱敏级别">
              {options?.masking_levels.map(level => (
                <Option key={level.value} value={level.value}>
                  {level.label}
                  <span style={{ color: '#666', marginLeft: 8 }}>({level.description})</span>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="masking_strategy"
            label="脱敏策略"
            rules={[{ required: true, message: '请选择脱敏策略' }]}
          >
            <Select placeholder="选择脱敏策略">
              {options?.masking_strategies.map(strategy => (
                <Option key={strategy.value} value={strategy.value}>
                  {strategy.label}
                  <span style={{ color: '#666', marginLeft: 8 }}>({strategy.description})</span>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="replacement_text"
            label="替换文本"
            tooltip="当脱敏策略为'指定文本替换'时使用"
          >
            <Input placeholder="输入替换文本" />
          </Form.Item>

          <Space>
            <Form.Item
              name="show_first"
              label="显示前几位"
              tooltip="部分显示策略：保留前几位字符"
            >
              <Input type="number" placeholder="前几位" min={0} />
            </Form.Item>

            <Form.Item
              name="show_last"
              label="显示后几位"
              tooltip="部分显示策略：保留后几位字符"
            >
              <Input type="number" placeholder="后几位" min={0} />
            </Form.Item>

            <Form.Item
              name="min_mask_length"
              label="最小脱敏长度"
              tooltip="脱敏部分的最小字符数"
            >
              <Input type="number" placeholder="最小长度" min={1} />
            </Form.Item>
          </Space>

          <Space>
            <Form.Item
              name="confidence_threshold"
              label="置信度阈值"
              rules={[{ required: true, message: '请输入置信度阈值' }]}
              tooltip="匹配置信度达到此阈值才应用脱敏"
            >
              <Input type="number" placeholder="0.0-1.0" min={0} max={1} step={0.1} />
            </Form.Item>

            <Form.Item
              name="priority"
              label="优先级"
              rules={[{ required: true, message: '请输入优先级' }]}
              tooltip="数值越大优先级越高"
            >
              <Input type="number" placeholder="优先级" min={1} />
            </Form.Item>
          </Space>

          <Form.Item
            name="role_id"
            label="应用范围"
            initialValue={0}
          >
            <Select placeholder="选择应用范围">
              <Option value={0}>全局规则（所有角色）</Option>
              {roles.map(role => (
                <Option key={role.id} value={role.id}>{role.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingRule ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试模态框 */}
      <Modal
        title="测试脱敏规则"
        open={testModalVisible}
        onCancel={() => {
          setTestModalVisible(false);
          setTestResult(null);
          testForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={testForm}
          layout="vertical"
          onFinish={handleTestRule}
        >
          <Form.Item
            name="rule_id"
            label="测试范围"
          >
            <Select placeholder="选择要测试的规则（留空测试所有激活规则）" allowClear>
              {rules.filter(rule => rule.is_active).map(rule => (
                <Option key={rule.id} value={rule.id}>
                  {rule.name} ({rule.data_type})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="text"
            label="测试文本"
            rules={[{ required: true, message: '请输入测试文本' }]}
          >
            <TextArea 
              rows={4} 
              placeholder="输入包含敏感数据的测试文本..." 
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={testLoading}>
              开始测试
            </Button>
          </Form.Item>
        </Form>

        {testResult && (
          <div style={{ marginTop: 24 }}>
            <Tabs defaultActiveKey="result">
              <TabPane tab="脱敏结果" key="result">
                <div>
                  <h4>原始文本：</h4>
                  <div style={{ 
                    padding: 12, 
                    backgroundColor: '#f5f5f5', 
                    borderRadius: 4,
                    marginBottom: 16
                  }}>
                    {testResult.original_text}
                  </div>

                  <h4>脱敏后文本：</h4>
                  <div style={{ 
                    padding: 12, 
                    backgroundColor: '#e6f7ff', 
                    borderRadius: 4,
                    marginBottom: 16
                  }}>
                    {testResult.processed_text}
                  </div>

                  <div>
                    <Badge count={testResult.total_matches} style={{ backgroundColor: '#52c41a' }} />
                    <span style={{ marginLeft: 8 }}>个匹配项</span>
                    <Badge 
                      count={testResult.rules_applied} 
                      style={{ backgroundColor: '#1890ff', marginLeft: 16 }} 
                    />
                    <span style={{ marginLeft: 8 }}>个规则生效</span>
                  </div>
                </div>
              </TabPane>

              <TabPane tab="详细匹配" key="matches">
                {testResult.matches.length > 0 ? (
                  <Table
                    size="small"
                    dataSource={testResult.matches}
                    rowKey={(record, index) => index || 0}
                    pagination={false}
                    columns={[
                      {
                        title: '匹配文本',
                        dataIndex: 'matched_text',
                        key: 'matched_text',
                      },
                      {
                        title: '脱敏文本',
                        dataIndex: 'masked_text',
                        key: 'masked_text',
                      },
                      {
                        title: '数据类型',
                        dataIndex: 'data_type',
                        key: 'data_type',
                      },
                      {
                        title: '规则名称',
                        dataIndex: 'rule_name',
                        key: 'rule_name',
                      },
                      {
                        title: '置信度',
                        dataIndex: 'confidence',
                        key: 'confidence',
                        render: (confidence: number) => `${(confidence * 100).toFixed(1)}%`,
                      },
                      {
                        title: '位置',
                        key: 'position',
                        render: (record: any) => `${record.start}-${record.end}`,
                      },
                    ]}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: 24 }}>
                    暂无匹配项
                  </div>
                )}
              </TabPane>
            </Tabs>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DesensitizationRuleManagement; 