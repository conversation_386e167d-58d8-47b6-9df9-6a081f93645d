import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Select,
  message,
  Popconfirm,
  Tag,
  Input,
  InputNumber,
  Row,
  Col,
  Divider,
  Typography,
  Alert
} from 'antd';
import {
  SecurityScanOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useAuthStore } from '../../store/authStore';
import { userApi, userDocumentPermissionApi, knowledgeBaseApi } from '../../services/api';

const { Title } = Typography;
const { Option } = Select;

interface User {
  id: number;
  username: string;
  email: string;
  role: {
    id: number;
    name: string;
  };
}

interface Document {
  id: number;
  title: string;
  file_name: string;
  filename?: string; // 兼容字段
  upload_time?: string;
  created_at?: string;
  status?: string;
}

interface UserDocumentPermission {
  id: number;
  user_id: number;
  document_id: number;
  permission_type: string;
  granted: boolean;
  granted_by: number;
  granted_at: string;
  expires_at?: string;
  reason?: string;
  is_expired: boolean;
  days_until_expiry?: number;
  // 关联数据
  user?: User;
  document?: Document;
  granted_by_user?: User;
}

const UserDocumentPermissions: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [permissions, setPermissions] = useState<UserDocumentPermission[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isBatchModalVisible, setIsBatchModalVisible] = useState(false);
  const [editingPermission, setEditingPermission] = useState<UserDocumentPermission | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();
  const [batchForm] = Form.useForm();

  // 获取权限列表
  const fetchPermissions = async () => {
    setLoading(true);
    try {
      const permissionsData = await userDocumentPermissionApi.getPermissions();
      console.log('获取到的权限数据:', permissionsData);

      // 确保permissionsData是数组
      const permissionsArray = Array.isArray(permissionsData) ? permissionsData : [];

      // 权限数据现在应该包含用户名和文档名信息
      const enrichedPermissions = permissionsArray.map((perm: any) => {
        return {
          ...perm,
          user: perm.user_name ? { 
            id: perm.user_id, 
            username: perm.user_name, 
            email: '', 
            role: null 
          } : { id: perm.user_id, username: `用户${perm.user_id}`, email: '', role: null },
          document: perm.document_name ? { 
            id: perm.document_id, 
            title: perm.document_name, 
            file_name: perm.document_name 
          } : { id: perm.document_id, title: `文档${perm.document_id}`, file_name: '' },
          granted_by_user: perm.granted_by_name ? { 
            id: perm.granted_by, 
            username: perm.granted_by_name, 
            email: '', 
            role: null 
          } : { id: perm.granted_by, username: `用户${perm.granted_by}`, email: '', role: null },
          can_access: perm.granted && perm.permission_type === 'read' // 简化权限判断
        };
      });

      setPermissions(enrichedPermissions);
      console.log('处理后的权限数据:', enrichedPermissions);
    } catch (error) {
      console.error('获取权限列表失败:', error);
      message.error('获取权限列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const data = await userApi.getUsers();
      // 确保data是数组
      const usersArray = Array.isArray(data) ? data : [];
      setUsers(usersArray);
      console.log('获取到的用户数据:', usersArray);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      setUsers([]);
    }
  };

  // 获取文档列表
  const fetchDocuments = async () => {
    try {
      const data = await knowledgeBaseApi.getDocuments();
      // 知识库API返回的是 {total: number, items: Document[]} 格式
      const documentsArray = Array.isArray(data) ? data : (data.items || data.documents || []);
      setDocuments(documentsArray);
      console.log('获取到的文档数据:', documentsArray);
    } catch (error) {
      console.error('获取文档列表失败:', error);
      setDocuments([]);
    }
  };

  // 授予或撤销权限
  const handlePermissionChange = async (values: any) => {
    setLoading(true);
    try {
      // 转换数据格式以匹配后端API
      const requestData = {
        user_id: values.user_id,
        document_id: values.document_id,
        permission_type: values.can_access ? 'read' : 'none', // 简化权限类型
        expires_days: null, // 暂时不设置过期时间
        reason: editingPermission ? '权限更新' : '权限授予'
      };

      await userDocumentPermissionApi.grantPermission(requestData);
      message.success(editingPermission ? '权限更新成功' : '权限授予成功');
      setIsModalVisible(false);
      setEditingPermission(null);
      form.resetFields();
      fetchPermissions();
    } catch (error: any) {
      console.error('权限操作失败:', error);
      message.error(error.detail || '权限操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除权限 - 使用revoke端点
  const handleDeletePermission = async (id: number) => {
    setLoading(true);
    try {
      // 找到对应的权限记录
      const permission = permissions.find(p => p.id === id);
      if (!permission) {
        message.error('权限记录不存在');
        return;
      }

      await userDocumentPermissionApi.revokePermission({
        user_id: permission.user_id,
        document_id: permission.document_id,
        permission_type: permission.permission_type
      });

      message.success('权限删除成功');
      fetchPermissions();
    } catch (error: any) {
      console.error('权限删除失败:', error);
      message.error(error.detail || '权限删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量授权
  const handleBatchPermissionGrant = async (values: any) => {
    setLoading(true);
    try {
      const result = await userDocumentPermissionApi.batchGrantPermissions({
        user_ids: values.user_ids,
        document_ids: values.document_ids,
        permission_types: values.permission_types,
        expires_days: values.expires_days,
        reason: values.reason || '批量权限授予'
      });

      message.success(`批量授权成功！共处理 ${result.total_operations} 个操作，成功 ${result.granted_count} 个`);
      if (result.errors && result.errors.length > 0) {
        message.warning(`部分操作失败：${result.errors.slice(0, 3).join('; ')}${result.errors.length > 3 ? '...' : ''}`);
      }
      setIsBatchModalVisible(false);
      batchForm.resetFields();
      fetchPermissions();
    } catch (error: any) {
      console.error('批量授权失败:', error);
      message.error(error.detail || '批量授权失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 先获取用户和文档数据，然后获取权限数据
    const loadData = async () => {
      try {
        console.log('开始加载数据...');
        await Promise.all([fetchUsers(), fetchDocuments()]);
        console.log('用户和文档数据加载完成，开始加载权限数据...');
        await fetchPermissions();
        console.log('所有数据加载完成');
      } catch (error) {
        console.error('数据加载失败:', error);
        message.error('数据加载失败');
      }
    };
    loadData();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '用户',
      dataIndex: 'user_id',
      key: 'username',
      render: (userId: number, record: UserDocumentPermission) => {
        const user = record.user;
        if (!user) return `用户ID: ${userId}`;
        return (
          <Space>
            <UserOutlined />
            <div>
              <div>{user.username}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>{user.email}</div>
            </div>
          </Space>
        );
      },
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value: any, record: UserDocumentPermission) => {
        const user = record.user;
        if (!user) return false;
        return user.username.toLowerCase().includes(value.toLowerCase()) ||
               user.email.toLowerCase().includes(value.toLowerCase());
      },
    },
    {
      title: '角色',
      dataIndex: 'user_id',
      key: 'role',
      render: (userId: number, record: UserDocumentPermission) => {
        const user = record.user;
        if (!user || !user.role) return '-';
        return <Tag color="blue">{user.role.name}</Tag>;
      },
    },
    {
      title: '文档',
      dataIndex: 'document_id',
      key: 'document',
      render: (documentId: number, record: UserDocumentPermission) => {
        const document = record.document;
        if (!document) return `文档ID: ${documentId}`;
        return (
          <Space>
            <FileTextOutlined />
            <div>
              <div>{document.title}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>{document.file_name || document.filename}</div>
            </div>
          </Space>
        );
      },
    },
    {
      title: '权限类型',
      dataIndex: 'permission_type',
      key: 'permission_type',
      render: (permissionType: string) => (
        <Tag color="blue">{permissionType}</Tag>
      ),
    },
    {
      title: '访问权限',
      dataIndex: 'granted',
      key: 'granted',
      render: (granted: boolean, record: UserDocumentPermission) => (
        <Tag color={granted && !record.is_expired ? 'green' : 'red'}>
          {granted && !record.is_expired ? '允许访问' : '禁止访问'}
        </Tag>
      ),
    },
    {
      title: '授权人',
      dataIndex: 'granted_by',
      key: 'granted_by',
      render: (grantedBy: number, record: UserDocumentPermission) => {
        const grantedByUser = record.granted_by_user;
        return grantedByUser ? grantedByUser.username : `用户ID: ${grantedBy}`;
      },
    },
    {
      title: '授权时间',
      dataIndex: 'granted_at',
      key: 'granted_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (text: any, record: UserDocumentPermission) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingPermission(record);
              form.setFieldsValue({
                user_id: record.user_id,
                document_id: record.document_id,
                can_access: record.granted,
              });
              setIsModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个权限吗？"
            onConfirm={() => handleDeletePermission(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 检查用户权限
  if (!user) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Alert
          message="未登录"
          description="请先登录后再访问此页面"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Card
        title={
          <Space>
            <SecurityScanOutlined />
            <Title level={4} style={{ margin: 0 }}>用户文档权限管理</Title>
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingPermission(null);
                form.resetFields();
                setIsModalVisible(true);
              }}
            >
              授予权限
            </Button>
            <Button
              type="primary"
              ghost
              icon={<PlusOutlined />}
              onClick={() => {
                setIsBatchModalVisible(true);
                batchForm.resetFields();
              }}
            >
              批量授权
            </Button>
          </Space>
        }
      >
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索用户名或邮箱"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={permissions}
          rowKey="id"
          loading={loading}
          pagination={{
            total: permissions.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title={editingPermission ? '编辑权限' : '授予权限'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingPermission(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handlePermissionChange}
        >
          <Form.Item
            name="user_id"
            label="用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              placeholder="选择用户"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.username} ({user.email}) - {user.role?.name || '未分配角色'}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="document_id"
            label="文档"
            rules={[{ required: true, message: '请选择文档' }]}
          >
            <Select
              placeholder="选择文档"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {documents.map(doc => (
                <Option key={doc.id} value={doc.id}>
                  {doc.title} ({doc.file_name || doc.filename})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="can_access"
            label="访问权限"
            rules={[{ required: true, message: '请选择访问权限' }]}
          >
            <Select placeholder="选择访问权限">
              <Option value={true}>允许访问</Option>
              <Option value={false}>禁止访问</Option>
            </Select>
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsModalVisible(false);
                setEditingPermission(null);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingPermission ? '更新' : '授予'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量授权Modal */}
      <Modal
        title="批量授权"
        open={isBatchModalVisible}
        onCancel={() => {
          setIsBatchModalVisible(false);
          batchForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={handleBatchPermissionGrant}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="user_ids"
                label="选择用户"
                rules={[{ required: true, message: '请选择用户' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="选择多个用户"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {users.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.username} ({user.email}) - {user.role?.name || '未分配角色'}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="document_ids"
                label="选择文档"
                rules={[{ required: true, message: '请选择文档' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="选择多个文档"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {documents.map(doc => (
                    <Option key={doc.id} value={doc.id}>
                      {doc.title} ({doc.file_name || doc.filename})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="permission_types"
                label="权限类型"
                rules={[{ required: true, message: '请选择权限类型' }]}
                initialValue={['read']}
              >
                <Select
                  mode="multiple"
                  placeholder="选择权限类型"
                >
                  <Option value="read">阅读</Option>
                  <Option value="download">下载</Option>
                  <Option value="search">搜索</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="expires_days"
                label="有效期（天）"
                tooltip="留空表示永久有效"
              >
                <InputNumber
                  placeholder="例如：30"
                  min={1}
                  max={3650}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="reason"
            label="授权原因"
          >
            <Input.TextArea
              placeholder="请输入授权原因（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsBatchModalVisible(false);
                batchForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                批量授权
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserDocumentPermissions;
