import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Space,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Select,
  Spin,
  Badge
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UserOutlined,
  KeyOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuthStore } from '../../store/authStore';

import { apiFetch } from '../../config/api';
interface User {
  id: number;
  username: string;
  email?: string;
  is_active: boolean;
  role_id?: number;
  level_id?: number;
  created_at: string;
  updated_at: string;
}

interface Role {
  id: number;
  name: string;
  description?: string;
}

interface Level {
  id: number;
  name: string;
  description?: string;
}

interface CreateUserRequest {
  username: string;
  email?: string;
  password: string;
  role_id: number;
  level_id?: number;
  is_active: boolean;
}

interface UpdateUserRequest {
  username?: string;
  email?: string;
  password?: string;
  role_id?: number;
  level_id?: number;
  is_active?: boolean;
}

interface UserListResponse {
  items: User[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

const UserManagement: React.FC = () => {
  // Auth store
  const { token } = useAuthStore();
  
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [levels, setLevels] = useState<Level[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // Filters
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | undefined>(undefined);
  const [searchText, setSearchText] = useState('');
  
  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  // Forms
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 获取用户列表
  const fetchUsers = async (page = 1, size = 10, isActive?: boolean, search?: string) => {
    setLoading(true);
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      
      const params = new URLSearchParams();
      params.append('skip', ((page - 1) * size).toString());
      params.append('limit', size.toString());
      if (isActive !== undefined) {
        params.append('is_active', isActive.toString());
      }
      if (search) {
        params.append('search', search);
      }

      const response = await fetch(
        `/api/v1/admin/users/?${params}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setUsers(data);
      setTotal(data.length);
      setCurrentPage(page);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('/api/v1/admin/roles/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRoles(data);
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    }
  };

  // 获取权限级别列表
  const fetchLevels = async () => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('/api/v1/admin/levels/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setLevels(data);
      }
    } catch (error) {
      console.error('获取权限级别失败:', error);
      message.error('获取权限级别失败');
    }
  };

  // 创建用户
  const createUser = async (values: CreateUserRequest) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('/api/v1/admin/users/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '创建失败');
      }

      message.success('用户创建成功');
      setIsCreateModalVisible(false);
      createForm.resetFields();
      fetchUsers(currentPage, pageSize, isActiveFilter, searchText);
    } catch (error: any) {
      console.error('创建用户失败:', error);
      message.error(error.message || '创建用户失败');
    }
  };

  // 更新用户
  const updateUser = async (id: number, values: UpdateUserRequest) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(`/api/v1/admin/users/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '更新失败');
      }

      message.success('用户更新成功');
      setIsEditModalVisible(false);
      setEditingUser(null);
      editForm.resetFields();
      fetchUsers(currentPage, pageSize, isActiveFilter, searchText);
    } catch (error: any) {
      console.error('更新用户失败:', error);
      message.error(error.message || '更新用户失败');
    }
  };

  // 删除用户
  const deleteUser = async (id: number) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(`/api/v1/admin/users/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '删除失败');
      }

      message.success('用户删除成功');
      fetchUsers(currentPage, pageSize, isActiveFilter, searchText);
    } catch (error: any) {
      console.error('删除用户失败:', error);
      message.error(error.message || '删除用户失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchUsers();
    fetchRoles();
    fetchLevels();
  }, []);

  // 处理编辑
  const handleEdit = (record: User) => {
    setEditingUser(record);
    editForm.setFieldsValue({
      username: record.username,
      email: record.email,
      role_id: record.role_id,
      level_id: record.level_id,
      is_active: record.is_active,
    });
    setIsEditModalVisible(true);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchUsers(1, pageSize, isActiveFilter, searchText);
  };

  // 处理过滤重置
  const handleReset = () => {
    setIsActiveFilter(undefined);
    setSearchText('');
    fetchUsers(1, pageSize);
  };

  // 获取角色名称
  const getRoleName = (roleId?: number) => {
    const role = roles.find(r => r.id === roleId);
    return role?.name || '未知';
  };

  // 获取权限级别名称
  const getLevelName = (levelId?: number) => {
    const level = levels.find(l => l.id === levelId);
    return level?.name || '未知';
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (text) => text || '-',
    },
    {
      title: '角色',
      dataIndex: 'role_id',
      key: 'role_id',
      render: (roleId) => (
        <Tag color="blue">{getRoleName(roleId)}</Tag>
      ),
    },
    {
      title: '权限级别',
      dataIndex: 'level_id',
      key: 'level_id',
      render: (levelId) => (
        <Tag color="green">{getLevelName(levelId)}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? '激活' : '禁用'} 
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个用户吗？"
            description="删除后无法恢复"
            onConfirm={() => deleteUser(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Space size="middle">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsCreateModalVisible(true)}
              >
                新建用户
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchUsers(currentPage, pageSize, isActiveFilter, searchText)}
              >
                刷新
              </Button>
            </Space>
          </Col>
          
          <Col span={24}>
            <Space size="middle">
              <Input.Search
                placeholder="搜索用户名或邮箱"
                style={{ width: 300 }}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={handleSearch}
                allowClear
              />
              <Select
                placeholder="状态过滤"
                style={{ width: 120 }}
                value={isActiveFilter}
                onChange={setIsActiveFilter}
                allowClear
              >
                <Select.Option value={true}>激活</Select.Option>
                <Select.Option value={false}>禁用</Select.Option>
              </Select>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setPageSize(size || 10);
              fetchUsers(page, size || 10, isActiveFilter, searchText);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建用户Modal */}
      <Modal
        title="新建用户"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={createUser}
          initialValues={{ is_active: true }}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 50, message: '用户名最多50个字符' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item
            label="角色"
            name="role_id"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              {roles.map(role => (
                <Select.Option key={role.id} value={role.id}>
                  {role.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="权限级别"
            name="level_id"
          >
            <Select placeholder="请选择权限级别" allowClear>
              {levels.map(level => (
                <Select.Option key={level.id} value={level.id}>
                  {level.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="状态"
            name="is_active"
            valuePropName="checked"
          >
            <Switch checkedChildren="激活" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑用户Modal */}
      <Modal
        title="编辑用户"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingUser(null);
          editForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingUser) {
              updateUser(editingUser.id, values);
            }
          }}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 50, message: '用户名最多50个字符' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password placeholder="留空则不修改密码" />
          </Form.Item>

          <Form.Item
            label="角色"
            name="role_id"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              {roles.map(role => (
                <Select.Option key={role.id} value={role.id}>
                  {role.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="权限级别"
            name="level_id"
          >
            <Select placeholder="请选择权限级别" allowClear>
              {levels.map(level => (
                <Select.Option key={level.id} value={level.id}>
                  {level.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="状态"
            name="is_active"
            valuePropName="checked"
          >
            <Switch checkedChildren="激活" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                更新
              </Button>
              <Button onClick={() => {
                setIsEditModalVisible(false);
                setEditingUser(null);
                editForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement; 