import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Modal,
  Form,
  message,
  Tag,
  Switch,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  Drawer,
  Badge,
  Typography,
  Tabs,
  Alert,
  Spin,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExperimentOutlined,
  EyeOutlined,
  Bar<PERSON><PERSON>Outlined,
  BugOutlined,
} from '@ant-design/icons';
import { regexRuleApi, userApi } from '../../services/api';

const { Search } = Input;
const { Option } = Select;
const { Text, Title } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

// 定义枚举类型
const IntentCategory = {
  PRIVILEGE_ESCALATION: 'privilege_escalation',
  PRIVACY_EXTRACTION: 'privacy_extraction',
  DATA_POISONING: 'data_poisoning',
  SYSTEM_BYPASS: 'system_bypass',
  SOCIAL_ENGINEERING: 'social_engineering',
  MALICIOUS_CODE: 'malicious_code',
  INFORMATION_DISCLOSURE: 'information_disclosure',
  // 越狱攻击专门分类
  JAILBREAK_GOAL_HIJACKING: 'jailbreak_goal_hijacking',
  JAILBREAK_ROLE_PLAYING: 'jailbreak_role_playing',
  JAILBREAK_ENCODING_TRANSLATION: 'jailbreak_encoding_translation',
  JAILBREAK_INSTRUCTION_HIJACKING: 'jailbreak_instruction_hijacking',
  JAILBREAK_CONTEXT_LEARNING: 'jailbreak_context_learning',
  JAILBREAK_PROMPT_INJECTION: 'jailbreak_prompt_injection',
  JAILBREAK_CHAIN_PROMPT: 'jailbreak_chain_prompt',
  OTHER: 'other',
};

const RuleSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

const categoryLabels = {
  [IntentCategory.PRIVILEGE_ESCALATION]: '权限提升类',
  [IntentCategory.PRIVACY_EXTRACTION]: '隐私套取类',
  [IntentCategory.DATA_POISONING]: '数据投毒类',
  [IntentCategory.SYSTEM_BYPASS]: '系统绕过类',
  [IntentCategory.SOCIAL_ENGINEERING]: '社会工程学类',
  [IntentCategory.MALICIOUS_CODE]: '恶意代码类',
  [IntentCategory.INFORMATION_DISCLOSURE]: '信息泄露类',
  // 越狱攻击专门分类标签
  [IntentCategory.JAILBREAK_GOAL_HIJACKING]: '越狱-目标竞争攻击',
  [IntentCategory.JAILBREAK_ROLE_PLAYING]: '越狱-角色扮演攻击',
  [IntentCategory.JAILBREAK_ENCODING_TRANSLATION]: '越狱-编码翻译攻击',
  [IntentCategory.JAILBREAK_INSTRUCTION_HIJACKING]: '越狱-指令劫持攻击',
  [IntentCategory.JAILBREAK_CONTEXT_LEARNING]: '越狱-上下文学习攻击',
  [IntentCategory.JAILBREAK_PROMPT_INJECTION]: '越狱-提示注入攻击',
  [IntentCategory.JAILBREAK_CHAIN_PROMPT]: '越狱-链式提示攻击',
  [IntentCategory.OTHER]: '其他类型',
};

const severityLabels = {
  [RuleSeverity.LOW]: '低风险',
  [RuleSeverity.MEDIUM]: '中风险',
  [RuleSeverity.HIGH]: '高风险',
  [RuleSeverity.CRITICAL]: '严重',
};

const severityColors = {
  [RuleSeverity.LOW]: 'green',
  [RuleSeverity.MEDIUM]: 'orange',
  [RuleSeverity.HIGH]: 'volcano',
  [RuleSeverity.CRITICAL]: 'red',
};

interface RegexRule {
  id: number;
  name: string;
  pattern: string;
  description?: string;
  category: string;
  severity: string;
  is_active: boolean;
  priority: number;
  flags: number;
  timeout_ms: number;
  version: number;
  parent_rule_id?: number;
  role_id?: number;
  match_count: number;
  false_positive_count: number;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  role_name?: string;
  accuracy_rate?: number;
  has_child_versions: boolean;
}

interface Role {
  id: number;
  name: string;
}

const RegexRuleManagement: React.FC = () => {
  const [rules, setRules] = useState<RegexRule[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('');
  const [selectedRoleId, setSelectedRoleId] = useState<number>();
  const [activeOnly, setActiveOnly] = useState(false);

  // Modal states
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<RegexRule | null>(null);
  const [isTestModalVisible, setIsTestModalVisible] = useState(false);
  const [isStatsDrawerVisible, setIsStatsDrawerVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [selectedRule, setSelectedRule] = useState<RegexRule | null>(null);

  // Test related states
  const [testTexts, setTestTexts] = useState<string[]>(['']);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [testLoading, setTestLoading] = useState(false);

  // Stats
  const [stats, setStats] = useState<any>(null);

  const [form] = Form.useForm();
  const [testForm] = Form.useForm();

  // 加载数据
  const loadRules = async () => {
    setLoading(true);
    try {
      const params = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
        active_only: activeOnly,
        ...(selectedCategory && { category: selectedCategory }),
        ...(selectedSeverity && { severity: selectedSeverity }),
        ...(selectedRoleId && { role_id: selectedRoleId }),
        ...(searchText && { search: searchText }),
      };

      const response = await regexRuleApi.getRules(params);
      setRules(response.items || []);
      setTotal(response.total || 0);
    } catch (error: any) {
      message.error(`加载规则失败: ${error.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 加载角色数据
  const loadRoles = async () => {
    try {
      const response = await userApi.getRoles();
      setRoles(response || []);
    } catch (error: any) {
      console.error('加载角色失败:', error);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await regexRuleApi.getStats();
      setStats(response);
    } catch (error: any) {
      message.error(`加载统计数据失败: ${error.detail || error.message}`);
    }
  };

  useEffect(() => {
    loadRules();
  }, [currentPage, pageSize, searchText, selectedCategory, selectedSeverity, selectedRoleId, activeOnly]);

  useEffect(() => {
    loadRoles();
  }, []);

  // 创建规则
  const handleCreate = () => {
    setEditingRule(null);
    form.resetFields();
    form.setFieldsValue({
      is_active: true,
      priority: 0,
      flags: 2, // re.IGNORECASE
      timeout_ms: 1000,
      severity: RuleSeverity.MEDIUM,
    });
    setIsModalVisible(true);
  };

  // 编辑规则
  const handleEdit = (rule: RegexRule) => {
    setEditingRule(rule);
    form.setFieldsValue({
      ...rule,
    });
    setIsModalVisible(true);
  };

  // 查看详情
  const handleDetail = async (rule: RegexRule) => {
    try {
      const response = await regexRuleApi.getRule(rule.id);
      setSelectedRule(response);
      setIsDetailModalVisible(true);
    } catch (error: any) {
      message.error(`获取规则详情失败: ${error.detail || error.message}`);
    }
  };

  // 保存规则
  const handleSave = async (values: any) => {
    try {
      if (editingRule) {
        await regexRuleApi.updateRule(editingRule.id, values);
        message.success('规则更新成功');
      } else {
        await regexRuleApi.createRule(values);
        message.success('规则创建成功');
      }
      setIsModalVisible(false);
      loadRules();
    } catch (error: any) {
      message.error(`保存失败: ${error.detail || error.message}`);
    }
  };

  // 删除规则
  const handleDelete = async (id: number) => {
    try {
      await regexRuleApi.deleteRule(id);
      message.success('规则删除成功');
      loadRules();
    } catch (error: any) {
      message.error(`删除失败: ${error.detail || error.message}`);
    }
  };

  // 切换规则状态
  const handleToggleActive = async (rule: RegexRule) => {
    try {
      if (rule.is_active) {
        await regexRuleApi.deactivateRule(rule.id);
        message.success('规则已停用');
      } else {
        await regexRuleApi.activateRule(rule.id);
        message.success('规则已激活');
      }
      loadRules();
    } catch (error: any) {
      message.error(`状态切换失败: ${error.detail || error.message}`);
    }
  };

  // 标记误报
  const handleMarkFalsePositive = async (id: number) => {
    try {
      await regexRuleApi.markFalsePositive(id);
      message.success('已标记为误报');
      loadRules();
    } catch (error: any) {
      message.error(`标记失败: ${error.detail || error.message}`);
    }
  };

  // 测试规则
  const handleTest = async (values: any) => {
    setTestLoading(true);
    try {
      const response = await regexRuleApi.testRule({
        pattern: values.pattern,
        flags: values.flags || 2,
        test_texts: testTexts.filter(text => text.trim()),
        timeout_ms: values.timeout_ms || 1000,
      });
      setTestResults(response.test_results || []);
      message.success('测试完成');
    } catch (error: any) {
      message.error(`测试失败: ${error.detail || error.message}`);
    } finally {
      setTestLoading(false);
    }
  };

  // 添加测试文本
  const addTestText = () => {
    setTestTexts([...testTexts, '']);
  };

  // 删除测试文本
  const removeTestText = (index: number) => {
    const newTexts = testTexts.filter((_, i) => i !== index);
    setTestTexts(newTexts);
  };

  // 更新测试文本
  const updateTestText = (index: number, value: string) => {
    const newTexts = [...testTexts];
    newTexts[index] = value;
    setTestTexts(newTexts);
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text: string, record: RegexRule) => (
        <Space>
          <Text strong>{text}</Text>
          {record.has_child_versions && <Badge count="V" style={{ backgroundColor: '#1890ff' }} />}
        </Space>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: string) => (
        <Tag color="blue">{categoryLabels[category as keyof typeof categoryLabels]}</Tag>
      ),
    },
    {
      title: '严重性',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={severityColors[severity as keyof typeof severityColors]}>
          {severityLabels[severity as keyof typeof severityLabels]}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (is_active: boolean, record: RegexRule) => (
        <Switch
          checked={is_active}
          onChange={() => handleToggleActive(record)}
          checkedChildren="启用"
          unCheckedChildren="停用"
        />
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
    },
    {
      title: '匹配次数',
      dataIndex: 'match_count',
      key: 'match_count',
      width: 100,
      render: (count: number) => <Badge count={count} showZero color="#1890ff" />,
    },
    {
      title: '准确率',
      dataIndex: 'accuracy_rate',
      key: 'accuracy_rate',
      width: 100,
      render: (rate: number) => (
        <Text type={rate >= 0.8 ? 'success' : rate >= 0.6 ? 'warning' : 'danger'}>
          {rate ? `${(rate * 100).toFixed(1)}%` : 'N/A'}
        </Text>
      ),
    },
    {
      title: '关联角色',
      dataIndex: 'role_name',
      key: 'role_name',
      width: 120,
      render: (role_name: string) => role_name ? <Tag>{role_name}</Tag> : <Text type="secondary">全局</Text>,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: RegexRule) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="标记误报">
            <Button
              type="link"
              icon={<BugOutlined />}
              onClick={() => handleMarkFalsePositive(record.id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除此规则吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
              <Title level={2}>恶意意图识别管理</Title>
      
      {/* 操作区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索规则名称或描述"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={loadRules}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择类别"
              value={selectedCategory}
              onChange={setSelectedCategory}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(categoryLabels).map(([value, label]) => (
                <Option key={value} value={value}>{label}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择严重性"
              value={selectedSeverity}
              onChange={setSelectedSeverity}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(severityLabels).map(([value, label]) => (
                <Option key={value} value={value}>{label}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择角色"
              value={selectedRoleId}
              onChange={setSelectedRoleId}
              allowClear
              style={{ width: '100%' }}
            >
              {roles.map((role) => (
                <Option key={role.id} value={role.id}>{role.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Space>
              <Switch
                checked={activeOnly}
                onChange={setActiveOnly}
                checkedChildren="仅启用"
                unCheckedChildren="全部"
              />
            </Space>
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
              创建恶意意图识别规则
            </Button>
          </Col>
          <Col>
            <Button icon={<ExperimentOutlined />} onClick={() => setIsTestModalVisible(true)}>
              测试规则
            </Button>
          </Col>
          <Col>
            <Button
              icon={<BarChartOutlined />}
              onClick={() => {
                setIsStatsDrawerVisible(true);
                loadStats();
              }}
            >
              查看统计
            </Button>
          </Col>
          <Col>
            <Button icon={<ReloadOutlined />} onClick={loadRules}>
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 规则列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={rules}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrrentPage(page);
              setPageSize(size || 10);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建/编辑规则弹窗 */}
      <Modal
                  title={editingRule ? '编辑恶意意图识别规则' : '创建恶意意图识别规则'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="规则名称"
                rules={[{ required: true, message: '请输入规则名称' }]}
              >
                <Input placeholder="请输入规则名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="恶意意图分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {Object.entries(categoryLabels).map(([value, label]) => (
                    <Option key={value} value={value}>{label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="pattern"
            label="正则表达式"
            rules={[{ required: true, message: '请输入正则表达式' }]}
          >
            <TextArea
              placeholder="请输入正则表达式"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="规则描述"
          >
            <TextArea
              placeholder="请输入规则描述"
              rows={2}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="severity"
                label="严重性等级"
                rules={[{ required: true, message: '请选择严重性等级' }]}
              >
                <Select placeholder="请选择严重性等级">
                  {Object.entries(severityLabels).map(([value, label]) => (
                    <Option key={value} value={value}>{label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请输入优先级' }]}
              >
                <Input
                  type="number"
                  min={0}
                  max={999}
                  placeholder="优先级 (0-999)"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="timeout_ms"
                label="超时时间(ms)"
                rules={[{ required: true, message: '请输入超时时间' }]}
              >
                <Input
                  type="number"
                  min={100}
                  max={10000}
                  placeholder="超时时间"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role_id"
                label="关联角色"
              >
                <Select placeholder="选择角色 (为空表示全局规则)" allowClear>
                  {roles.map((role) => (
                    <Option key={role.id} value={role.id}>{role.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="状态"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="停用" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 规则测试弹窗 */}
      <Modal
                  title="测试恶意意图识别规则"
        open={isTestModalVisible}
        onCancel={() => setIsTestModalVisible(false)}
        onOk={() => testForm.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={testForm}
          layout="vertical"
          onFinish={handleTest}
        >
          <Form.Item
            name="pattern"
            label="正则表达式"
            rules={[{ required: true, message: '请输入正则表达式' }]}
          >
            <TextArea
              placeholder="请输入要测试的正则表达式"
              rows={3}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="flags"
                label="正则标志"
                initialValue={2}
              >
                <Input type="number" placeholder="正则标志" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timeout_ms"
                label="超时时间(ms)"
                initialValue={1000}
              >
                <Input type="number" min={100} max={10000} placeholder="超时时间" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="测试文本">
            {testTexts.map((text, index) => (
              <div key={index} style={{ marginBottom: 8 }}>
                <Row gutter={8}>
                  <Col span={22}>
                    <Input
                      value={text}
                      onChange={(e) => updateTestText(index, e.target.value)}
                      placeholder={`测试文本 ${index + 1}`}
                    />
                  </Col>
                  <Col span={2}>
                    {testTexts.length > 1 && (
                      <Button
                        type="link"
                        danger
                        onClick={() => removeTestText(index)}
                      >
                        删除
                      </Button>
                    )}
                  </Col>
                </Row>
              </div>
            ))}
            <Button type="dashed" onClick={addTestText} style={{ width: '100%' }}>
              添加测试文本
            </Button>
          </Form.Item>
        </Form>

        {testLoading && (
          <div style={{ textAlign: 'center', margin: '20px 0' }}>
            <Spin />
            <Text style={{ marginLeft: 8 }}>测试中...</Text>
          </div>
        )}

        {testResults.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Title level={5}>测试结果</Title>
            {testResults.map((result, index) => (
              <Alert
                key={index}
                type={result.matched ? 'success' : 'info'}
                message={`文本 ${index + 1}: ${result.matched ? '匹配' : '不匹配'}`}
                description={result.details}
                style={{ marginBottom: 8 }}
              />
            ))}
          </div>
        )}
      </Modal>

      {/* 规则详情弹窗 */}
      <Modal
        title="规则详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedRule && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>规则名称：</Text>
                  <Text>{selectedRule.name}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>类别：</Text>
                  <Tag color="blue">
                    {categoryLabels[selectedRule.category as keyof typeof categoryLabels]}
                  </Tag>
                </Col>
              </Row>
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={12}>
                  <Text strong>严重性：</Text>
                  <Tag color={severityColors[selectedRule.severity as keyof typeof severityColors]}>
                    {severityLabels[selectedRule.severity as keyof typeof severityLabels]}
                  </Tag>
                </Col>
                <Col span={12}>
                  <Text strong>状态：</Text>
                  <Tag color={selectedRule.is_active ? 'green' : 'red'}>
                    {selectedRule.is_active ? '启用' : '停用'}
                  </Tag>
                </Col>
              </Row>
              <Row style={{ marginTop: 16 }}>
                <Col span={24}>
                  <Text strong>描述：</Text>
                  <br />
                  <Text>{selectedRule.description || '无描述'}</Text>
                </Col>
              </Row>
              <Row style={{ marginTop: 16 }}>
                <Col span={24}>
                  <Text strong>正则表达式：</Text>
                  <br />
                  <Text code>{selectedRule.pattern}</Text>
                </Col>
              </Row>
            </TabPane>
            <TabPane tab="统计信息" key="stats">
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic title="匹配次数" value={selectedRule.match_count} />
                </Col>
                <Col span={8}>
                  <Statistic title="误报次数" value={selectedRule.false_positive_count} />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="准确率"
                    value={selectedRule.accuracy_rate ? selectedRule.accuracy_rate * 100 : 0}
                    suffix="%"
                    precision={1}
                  />
                </Col>
              </Row>
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={8}>
                  <Statistic title="优先级" value={selectedRule.priority} />
                </Col>
                <Col span={8}>
                  <Statistic title="版本" value={selectedRule.version} />
                </Col>
                <Col span={8}>
                  <Statistic title="超时时间" value={selectedRule.timeout_ms} suffix="ms" />
                </Col>
              </Row>
            </TabPane>
            <TabPane tab="元数据" key="metadata">
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>创建者：</Text>
                  <Text>{selectedRule.created_by || 'N/A'}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>更新者：</Text>
                  <Text>{selectedRule.updated_by || 'N/A'}</Text>
                </Col>
              </Row>
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={12}>
                  <Text strong>创建时间：</Text>
                  <Text>{new Date(selectedRule.created_at).toLocaleString()}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>更新时间：</Text>
                  <Text>{new Date(selectedRule.updated_at).toLocaleString()}</Text>
                </Col>
              </Row>
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={12}>
                  <Text strong>关联角色：</Text>
                  <Text>{selectedRule.role_name || '全局规则'}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>父规则ID：</Text>
                  <Text>{selectedRule.parent_rule_id || 'N/A'}</Text>
                </Col>
              </Row>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 统计抽屉 */}
      <Drawer
        title="规则统计"
        placement="right"
        onClose={() => setIsStatsDrawerVisible(false)}
        open={isStatsDrawerVisible}
        width={600}
      >
        {stats && (
          <div>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Statistic title="总规则数" value={stats.total_rules} />
              </Col>
              <Col span={12}>
                <Statistic title="激活规则数" value={stats.active_rules} />
              </Col>
            </Row>
            
            <Title level={5}>按分类统计</Title>
            <div style={{ marginBottom: 24 }}>
              {Object.entries(stats.rules_by_category || {}).map(([category, count]) => (
                <Row key={category} style={{ marginBottom: 8 }}>
                  <Col span={16}>
                    <Tag color="blue">
                      {categoryLabels[category as keyof typeof categoryLabels]}
                    </Tag>
                  </Col>
                  <Col span={8} style={{ textAlign: 'right' }}>
                    <Badge count={count as number} showZero color="#1890ff" />
                  </Col>
                </Row>
              ))}
            </div>

            <Title level={5}>按严重性统计</Title>
            <div style={{ marginBottom: 24 }}>
              {Object.entries(stats.rules_by_severity || {}).map(([severity, count]) => (
                <Row key={severity} style={{ marginBottom: 8 }}>
                  <Col span={16}>
                    <Tag color={severityColors[severity as keyof typeof severityColors]}>
                      {severityLabels[severity as keyof typeof severityLabels]}
                    </Tag>
                  </Col>
                  <Col span={8} style={{ textAlign: 'right' }}>
                    <Badge count={count as number} showZero color="#1890ff" />
                  </Col>
                </Row>
              ))}
            </div>

            <Statistic
              title="平均准确率"
              value={stats.avg_accuracy_rate * 100}
              suffix="%"
              precision={1}
              style={{ marginBottom: 24 }}
            />

            <Title level={5}>热门规则</Title>
            {stats.top_matched_rules?.map((rule: any, index: number) => (
              <Card key={rule.id} size="small" style={{ marginBottom: 8 }}>
                <Row>
                  <Col span={16}>
                    <Text strong>{rule.name}</Text>
                  </Col>
                  <Col span={8} style={{ textAlign: 'right' }}>
                    <Badge count={rule.match_count} showZero color="#f50" />
                  </Col>
                </Row>
              </Card>
            ))}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default RegexRuleManagement; 