import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Switch,
  Tag,
  Card,
  Select,
  Row,
  Col,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  UserOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { userApi } from '../../services/api';

const { Search } = Input;
const { Option } = Select;

interface Role {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  user_count: number;
  can_be_deleted: boolean;
}

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [form] = Form.useForm();

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await userApi.getRoles();
      setRoles(response);
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  // 筛选角色数据
  const filteredRoles = roles.filter(role => {
    const matchSearch = !searchText || 
      role.name.toLowerCase().includes(searchText.toLowerCase()) ||
      role.description.toLowerCase().includes(searchText.toLowerCase());
    const matchStatus = statusFilter === undefined || role.is_active === statusFilter;
    return matchSearch && matchStatus;
  });

  // 打开创建/编辑模态框
  const handleEdit = (role?: Role) => {
    setEditingRole(role || null);
    setModalVisible(true);
    if (role) {
      form.setFieldsValue({
        name: role.name,
        description: role.description,
        is_active: role.is_active,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({ is_active: true });
    }
  };

  // 保存角色
  const handleSave = async (values: any) => {
    try {
      if (editingRole) {
        // 编辑角色
        await userApi.updateRole(editingRole.id, values);
        message.success('角色更新成功');
      } else {
        // 创建角色
        await userApi.createRole(values);
        message.success('角色创建成功');
      }
      setModalVisible(false);
      fetchRoles();
    } catch (error) {
      console.error('保存角色失败:', error);
      message.error('保存角色失败');
    }
  };

  // 删除角色
  const handleDelete = async (id: number) => {
    try {
      await userApi.deleteRole(id);
      message.success('角色删除成功');
      fetchRoles();
    } catch (error) {
      console.error('删除角色失败:', error);
      message.error('删除角色失败');
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Role) => (
        <Space>
          <SafetyOutlined />
          <span style={{ fontWeight: record.is_system ? 'bold' : 'normal' }}>
            {text}
          </span>
          {record.is_system && <Tag color="orange">系统角色</Tag>}
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 120,
      render: (is_active: boolean) => (
        <Tag color={is_active ? 'green' : 'red'}>
          {is_active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '用户数量',
      dataIndex: 'user_count',
      key: 'user_count',
      width: 120,
      render: (count: number) => (
        <Space>
          <UserOutlined />
          <span>{count}</span>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_: any, record: Role) => (
        <Space size="small">
          <Tooltip title="编辑角色">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          </Tooltip>
          <Popconfirm
            title="确定要删除这个角色吗？"
            description={record.user_count > 0 ? `该角色下还有 ${record.user_count} 个用户` : ''}
            onConfirm={() => handleDelete(record.id)}
            disabled={!record.can_be_deleted}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title={!record.can_be_deleted ? "系统角色不能删除" : "删除角色"}>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                disabled={!record.can_be_deleted}
              >
                删除
              </Button>
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <SafetyOutlined />
            <span>角色权限管理</span>
          </Space>
        }
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => handleEdit()}
          >
            新建角色
          </Button>
        }
      >
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Search
              placeholder="搜索角色名称或描述"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={setSearchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="状态筛选"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredRoles}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredRoles.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title={editingRole ? '编辑角色' : '新建角色'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="name"
            label="角色名称"
            rules={[
              { required: true, message: '请输入角色名称' },
              { max: 50, message: '角色名称不能超过50个字符' }
            ]}
          >
            <Input 
              placeholder="请输入角色名称" 
              disabled={editingRole?.is_system}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="角色描述"
            rules={[
              { max: 200, message: '角色描述不能超过200个字符' }
            ]}
          >
            <Input.TextArea 
              placeholder="请输入角色描述" 
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="启用" 
              unCheckedChildren="禁用" 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RoleManagement; 