import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Card,
  Select,
  Row,
  Col,
  Tooltip,
  InputNumber,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  NumberOutlined,
  SafetyOutlined,
  SortAscendingOutlined,
} from '@ant-design/icons';
import { userApi } from '../../services/api';

const { Search } = Input;
const { Option } = Select;

interface Level {
  id: number;
  role_id: number;
  name: string;
  rank_value: number;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  user_count: number;
  can_be_deleted: boolean;
  role_name: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

const LevelManagement: React.FC = () => {
  const [levels, setLevels] = useState<Level[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [roleFilter, setRoleFilter] = useState<number | undefined>(undefined);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingLevel, setEditingLevel] = useState<Level | null>(null);
  const [form] = Form.useForm();

  // 获取权限级别列表
  const fetchLevels = async () => {
    try {
      setLoading(true);
      const response = await userApi.getLevels({
        skip: 0,
        limit: 100,
        is_active: statusFilter,
        search: searchText || undefined,
      });
      setLevels(response);
    } catch (error) {
      console.error('获取权限级别列表失败:', error);
      message.error('获取权限级别列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await userApi.getRoles();
      setRoles(response.filter((role: Role) => role.is_active));
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    }
  };

  useEffect(() => {
    fetchLevels();
    fetchRoles();
  }, [statusFilter, searchText]);

  // 筛选权限级别数据
  const filteredLevels = levels.filter(level => {
    const matchRole = roleFilter === undefined || level.role_id === roleFilter;
    return matchRole;
  });

  // 打开创建/编辑模态框
  const handleEdit = (level?: Level) => {
    setEditingLevel(level || null);
    setModalVisible(true);
    if (level) {
      form.setFieldsValue({
        role_id: level.role_id,
        name: level.name,
        rank_value: level.rank_value,
        description: level.description,
        is_active: level.is_active,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({ 
        is_active: true,
        rank_value: 1
      });
    }
  };

  // 保存权限级别
  const handleSave = async (values: any) => {
    try {
      if (editingLevel) {
        // 编辑权限级别
        await userApi.updateLevel(editingLevel.id, values);
        message.success('权限级别更新成功');
      } else {
        // 创建权限级别
        await userApi.createLevel(values);
        message.success('权限级别创建成功');
      }
      setModalVisible(false);
      fetchLevels();
    } catch (error) {
      console.error('保存权限级别失败:', error);
      message.error('保存权限级别失败');
    }
  };

  // 删除权限级别
  const handleDelete = async (id: number) => {
    try {
      await userApi.deleteLevel(id);
      message.success('权限级别删除成功');
      fetchLevels();
    } catch (error) {
      console.error('删除权限级别失败:', error);
      message.error('删除权限级别失败');
    }
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '所属角色',
      dataIndex: 'role_name',
      key: 'role_name',
      width: 150,
      render: (text: string) => (
        <Space>
          <SafetyOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '级别名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Level) => (
        <Space>
          <NumberOutlined />
          <span style={{ fontWeight: 'bold' }}>
            {text}
          </span>
        </Space>
      ),
    },
    {
      title: '排序值',
      dataIndex: 'rank_value',
      key: 'rank_value',
      width: 100,
      render: (value: number) => (
        <Space>
          <SortAscendingOutlined />
          <Tag color="blue">{value}</Tag>
        </Space>
      ),
      sorter: (a: Level, b: Level) => a.rank_value - b.rank_value,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 120,
      render: (is_active: boolean) => (
        <Tag color={is_active ? 'green' : 'red'}>
          {is_active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '用户数量',
      dataIndex: 'user_count',
      key: 'user_count',
      width: 120,
      render: (count: number) => (
        <span>{count || 0}</span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_: any, record: Level) => (
        <Space size="small">
          <Tooltip title="编辑权限级别">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          </Tooltip>
          <Tooltip title="删除权限级别">
            <Popconfirm
              title="确定要删除这个权限级别吗？"
              description={
                record.user_count > 0 
                  ? `该级别下有 ${record.user_count} 个用户，删除可能影响系统功能。` 
                  : "删除后不可恢复。"
              }
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
              disabled={!record.can_be_deleted}
            >
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                disabled={!record.can_be_deleted}
              >
                删除
              </Button>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: '16px' }}>
          <Col>
            <h2>权限级别管理</h2>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleEdit()}
            >
              新建权限级别
            </Button>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col span={8}>
            <Search
              placeholder="搜索权限级别名称"
              allowClear
              onSearch={handleSearch}
              onChange={(e) => {
                if (!e.target.value) {
                  setSearchText('');
                }
              }}
            />
          </Col>
          <Col span={8}>
            <Select
              placeholder="筛选状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setStatusFilter(value)}
            >
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Select
              placeholder="筛选角色"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setRoleFilter(value)}
            >
              {roles.map((role) => (
                <Option key={role.id} value={role.id}>
                  {role.name}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredLevels}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingLevel ? '编辑权限级别' : '新建权限级别'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          preserve={false}
        >
          <Form.Item
            name="role_id"
            label="所属角色"
            rules={[{ required: true, message: '请选择所属角色' }]}
          >
            <Select placeholder="请选择角色">
              {roles.map((role) => (
                <Option key={role.id} value={role.id}>
                  {role.name} - {role.description}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="name"
            label="级别名称"
            rules={[
              { required: true, message: '请输入级别名称' },
              { max: 100, message: '级别名称不能超过100个字符' },
            ]}
          >
            <Input placeholder="请输入级别名称" />
          </Form.Item>

          <Form.Item
            name="rank_value"
            label="排序值"
            rules={[
              { required: true, message: '请输入排序值' },
              { type: 'number', min: 1, message: '排序值必须大于0' },
            ]}
            extra="数字越小，级别越高"
          >
            <InputNumber
              placeholder="请输入排序值"
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="级别描述"
            rules={[{ max: 255, message: '描述不能超过255个字符' }]}
          >
            <Input.TextArea
              placeholder="请输入级别描述"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Select defaultValue={true}>
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingLevel ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default LevelManagement;