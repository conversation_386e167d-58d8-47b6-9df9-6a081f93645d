import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Space,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import KeywordManagement from './KeywordManagement';
import { useAuthStore } from '../../store/authStore';

interface KeywordGroup {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  keyword_count: number;
  can_be_deleted: boolean;
}

interface CreateKeywordGroupRequest {
  name: string;
  description?: string;
  is_active: boolean;
}

interface UpdateKeywordGroupRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
}

interface KeywordGroupListResponse {
  items: KeywordGroup[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

const KeywordGroupManagement: React.FC = () => {
  // Auth store
  const { token } = useAuthStore();
  
  const [loading, setLoading] = useState(false);
  const [keywordGroups, setKeywordGroups] = useState<KeywordGroup[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingGroup, setEditingGroup] = useState<KeywordGroup | null>(null);
  
  // Keyword management states
  const [showKeywordManagement, setShowKeywordManagement] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<KeywordGroup | null>(null);
  
  // Forms
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 获取关键词分组列表
  const fetchKeywordGroups = async (page = 1, size = 10) => {
    setLoading(true);
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(
        `http://localhost:8000/api/v1/admin/keyword-groups/?skip=${(page - 1) * size}&limit=${size}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: KeywordGroupListResponse = await response.json();
      setKeywordGroups(data.items);
      setTotal(data.total);
      setCurrentPage(page);
    } catch (error) {
      console.error('获取关键词分组失败:', error);
      message.error('获取关键词分组失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建关键词分组
  const createKeywordGroup = async (values: CreateKeywordGroupRequest) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch('http://localhost:8000/api/v1/admin/keyword-groups/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '创建失败');
      }

      message.success('关键词分组创建成功');
      setIsCreateModalVisible(false);
      createForm.resetFields();
      fetchKeywordGroups(currentPage, pageSize);
    } catch (error: any) {
      console.error('创建关键词分组失败:', error);
      message.error(error.message || '创建关键词分组失败');
    }
  };

  // 更新关键词分组
  const updateKeywordGroup = async (id: number, values: UpdateKeywordGroupRequest) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(`http://localhost:8000/api/v1/admin/keyword-groups/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '更新失败');
      }

      message.success('关键词分组更新成功');
      setIsEditModalVisible(false);
      setEditingGroup(null);
      editForm.resetFields();
      fetchKeywordGroups(currentPage, pageSize);
    } catch (error: any) {
      console.error('更新关键词分组失败:', error);
      message.error(error.message || '更新关键词分组失败');
    }
  };

  // 删除关键词分组
  const deleteKeywordGroup = async (id: number) => {
    try {
      if (!token) {
        throw new Error('未找到认证token');
      }
      const response = await fetch(`http://localhost:8000/api/v1/admin/keyword-groups/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '删除失败');
      }

      message.success('关键词分组删除成功');
      fetchKeywordGroups(currentPage, pageSize);
    } catch (error: any) {
      console.error('删除关键词分组失败:', error);
      message.error(error.message || '删除关键词分组失败');
    }
  };

  // 处理编辑
  const handleEdit = (record: KeywordGroup) => {
    setEditingGroup(record);
    editForm.setFieldsValue({
      name: record.name,
      description: record.description || '',
      is_active: record.is_active,
    });
    setIsEditModalVisible(true);
  };

  // 处理查看关键词详情
  const handleViewKeywords = (record: KeywordGroup) => {
    setSelectedGroup(record);
    setShowKeywordManagement(true);
  };

  // 返回分组列表
  const handleBackToGroups = () => {
    setShowKeywordManagement(false);
    setSelectedGroup(null);
    // 刷新分组列表以更新关键词数量
    fetchKeywordGroups(currentPage, pageSize);
  };

  // 表格列定义
  const columns: ColumnsType<KeywordGroup> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '分组名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '关键词数量',
      dataIndex: 'keyword_count',
      key: 'keyword_count',
      width: 120,
      render: (count: number) => (
        <Tag color="blue">{count} 个</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="管理关键词">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewKeywords(record)}
            >
              管理关键词
            </Button>
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个关键词分组吗？"
              description={record.can_be_deleted ? "删除后无法恢复" : "该分组下还有关键词，无法删除"}
              onConfirm={() => deleteKeywordGroup(record.id)}
              okText="确定"
              cancelText="取消"
              disabled={!record.can_be_deleted}
            >
              <Button 
                type="link" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
                disabled={!record.can_be_deleted}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 初始化数据
  useEffect(() => {
    fetchKeywordGroups();
  }, []);

  // 如果显示关键词管理，则渲染关键词管理组件
  if (showKeywordManagement && selectedGroup) {
    return (
      <KeywordManagement
        groupId={selectedGroup.id}
        groupName={selectedGroup.name}
        onBack={handleBackToGroups}
      />
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <h2 style={{ margin: 0 }}>关键词分组管理</h2>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => fetchKeywordGroups(currentPage, pageSize)}
              >
                刷新
              </Button>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => setIsCreateModalVisible(true)}
              >
                新建分组
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={keywordGroups}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setPageSize(size || 10);
              fetchKeywordGroups(page, size || 10);
            },
          }}
        />
      </Card>

      {/* 创建分组Modal */}
      <Modal
        title="创建关键词分组"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={createKeywordGroup}
        >
          <Form.Item
            label="分组名称"
            name="name"
            rules={[
              { required: true, message: '请输入分组名称' },
              { min: 1, max: 100, message: '分组名称长度必须在1-100字符之间' }
            ]}
          >
            <Input placeholder="请输入分组名称" />
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
            rules={[
              { max: 500, message: '描述长度不能超过500字符' }
            ]}
          >
            <Input.TextArea 
              placeholder="请输入分组描述（可选）" 
              rows={3}
            />
          </Form.Item>
          <Form.Item
            label="状态"
            name="is_active"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑分组Modal */}
      <Modal
        title="编辑关键词分组"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingGroup(null);
          editForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingGroup) {
              updateKeywordGroup(editingGroup.id, values);
            }
          }}
        >
          <Form.Item
            label="分组名称"
            name="name"
            rules={[
              { required: true, message: '请输入分组名称' },
              { min: 1, max: 100, message: '分组名称长度必须在1-100字符之间' }
            ]}
          >
            <Input placeholder="请输入分组名称" />
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
            rules={[
              { max: 500, message: '描述长度不能超过500字符' }
            ]}
          >
            <Input.TextArea 
              placeholder="请输入分组描述（可选）" 
              rows={3}
            />
          </Form.Item>
          <Form.Item
            label="状态"
            name="is_active"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsEditModalVisible(false);
                setEditingGroup(null);
                editForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KeywordGroupManagement; 