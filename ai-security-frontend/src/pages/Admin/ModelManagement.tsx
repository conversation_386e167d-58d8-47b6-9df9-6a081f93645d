import React, { useState, useEffect } from 'react'
import { Card, Table, Button, Modal, Form, Input, Switch, message, Space, Popconfirm, InputNumber, Select } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

import { apiFetch } from '../../config/api';
// 模型类型定义（与后端对应）
interface LLMModel {
  id: number
  name: string
  api_url: string
  config_params?: {
    temperature?: number
    max_tokens?: number
    top_p?: number
    model?: string  // 模型标识可以放在 config_params 中
    api_format?: string  // API格式类型：openai, ollama等
  }
  is_active: boolean
  created_at: string
  updated_at: string
}

interface ModelFormValues {
  name: string
  api_url: string
  api_key?: string
  api_format?: string  // API格式类型
  temperature?: number
  max_tokens?: number
  model?: string  // 模型标识
  is_active?: boolean
}

export const ModelManagement: React.FC = () => {
  const [models, setModels] = useState<LLMModel[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingModel, setEditingModel] = useState<LLMModel | null>(null)
  const [form] = Form.useForm<ModelFormValues>()

  // 获取模型列表
  const fetchModels = async () => {
    setLoading(true)
    try {
      const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}')
      const token = authData?.state?.token
      
      if (!token) {
        message.error('请先登录')
        return
      }
      
      const response = await fetch('/api/v1/admin/llm-models/', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setModels(data)
      } else {
        message.error('获取模型列表失败')
      }
    } catch (error) {
      message.error('网络错误')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchModels()
  }, [])

  // 创建或更新模型
  const handleSubmit = async (values: ModelFormValues) => {
    try {
      const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}')
      const token = authData?.state?.token
      
      if (!token) {
        message.error('请先登录')
        return
      }
      
      const url = editingModel 
        ? `http://localhost:8000/api/v1/admin/llm-models/${editingModel.id}`
        : '/api/v1/admin/llm-models/'
      
      // 构建请求体，过滤掉空的API密钥
      const body: any = {
        name: values.name,
        api_url: values.api_url,
        is_active: values.is_active,
        config_params: {
          temperature: values.temperature,
          max_tokens: values.max_tokens,
          model: values.model,  // 如果有模型标识，放到 config_params 中
          api_format: values.api_format || 'openai'  // API格式，默认为openai
        }
      }
      
      // 只有当 API 密钥有值时才添加到请求体中
      if (values.api_key && values.api_key.trim()) {
        body.api_key = values.api_key
      }

      const response = await fetch(url, {
        method: editingModel ? 'PUT' : 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })

      if (response.ok) {
        message.success(editingModel ? '更新成功' : '创建成功')
        setModalVisible(false)
        form.resetFields()
        setEditingModel(null)
        fetchModels()
      } else {
        const error = await response.json()
        message.error(error.detail || '操作失败')
      }
    } catch (error) {
      message.error('网络错误')
    }
  }

  // 删除模型
  const handleDelete = async (id: number) => {
    try {
      const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}')
      const token = authData?.state?.token
      
      if (!token) {
        message.error('请先登录')
        return
      }
      
      const response = await fetch(`http://localhost:8000/api/v1/admin/llm-models/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        message.success('删除成功')
        fetchModels()
      } else {
        const error = await response.json()
        message.error(error.detail || '删除失败')
      }
    } catch (error) {
      message.error('网络错误')
    }
  }

  const columns: ColumnsType<LLMModel> = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'API地址',
      dataIndex: 'api_url',
      key: 'api_url',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Switch checked={active} disabled />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingModel(record)
              form.setFieldsValue({
                name: record.name,
                api_url: record.api_url,
                api_format: record.config_params?.api_format || 'openai',
                temperature: record.config_params?.temperature,
                max_tokens: record.config_params?.max_tokens,
                model: record.config_params?.model,
                is_active: record.is_active,
              })
              setModalVisible(true)
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个模型吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <Card
      title="模型管理"
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingModel(null)
            form.resetFields()
            setModalVisible(true)
          }}
        >
          新建模型
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={models}
        rowKey="id"
        loading={loading}
      />

      <Modal
        title={editingModel ? '编辑模型' : '新建模型'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingModel(null)
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true, api_format: 'openai' }}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="例如：GPT-3.5、本地Llama2、ChatGLM-6B" />
          </Form.Item>

          <Form.Item
            name="api_format"
            label="API格式"
            tooltip="选择模型API的格式类型"
            initialValue="openai"
          >
            <Select>
              <Select.Option value="openai">OpenAI (默认)</Select.Option>
              <Select.Option value="ollama">Ollama</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="model"
            label="模型标识"
            tooltip="例如：gpt-3.5-turbo、qwen:1.8b、llama2-7b"
          >
            <Input placeholder="例如：gpt-3.5-turbo、qwen:1.8b（Ollama必填）" />
          </Form.Item>

          <Form.Item
            name="api_url"
            label="API地址"
            rules={[
              { required: true, message: '请输入API地址' },
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input placeholder="例如：http://*************:8080/v1/chat/completions" />
          </Form.Item>

          <Form.Item
            name="api_key"
            label="API密钥"
            extra={editingModel ? "留空表示不更改现有API密钥" : "可选：如果您的模型不需要API密钥，可以留空"}
          >
            <Input.Password placeholder={editingModel ? "留空不更改，输入新密钥则更新" : "输入API密钥（可选）"} />
          </Form.Item>

          <Form.Item
            name="temperature"
            label="Temperature"
            tooltip="控制生成文本的随机性，0-2之间"
          >
            <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="max_tokens"
            label="最大Token数"
            tooltip="生成文本的最大长度"
          >
            <InputNumber min={1} max={8192} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="是否启用"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingModel ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}