import React, { useEffect, useState } from 'react'
import {
  Card,
  Button,
  List,
  Typography,
  Space,
  Modal,
  Form,
  Input,
  message,
  Empty,
  Spin,
  Tag,
  Select,
} from 'antd'
import {
  PlusOutlined,
  MessageOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useSessionStore } from '../../store/sessionStore'
import { sessionsAPI } from '../../services/api'  // 使用真实API
import type { Session } from '../../types/api'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const { Title, Text } = Typography
const { confirm } = Modal

const SessionsPage: React.FC = () => {
  const navigate = useNavigate()
  const {
    sessions,
    loading,
    setSessions,
    addSession,
    updateSession,
    deleteSession,
    setLoading,
  } = useSessionStore()

  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [editingSession, setEditingSession] = useState<Session | null>(null)
  const [models, setModels] = useState<any[]>([])
  const [modelsLoading, setModelsLoading] = useState(false)
  const [form] = Form.useForm()

  // 加载真实会话数据
  useEffect(() => {
    const loadSessions = async () => {
      setLoading(true)
      try {
        // 调用真实API获取会话列表
        const response = await sessionsAPI.list()
        // 处理响应数据，API返回数组而不是SessionListResponse对象
        const sessionsData = Array.isArray(response) ? response : []
        setSessions(sessionsData)
        console.log('✅ 成功加载会话:', sessionsData)
      } catch (error: any) {
        console.error('❌ 加载会话失败:', error)
        message.error(error.detail || '加载会话失败')
        // 如果API失败，使用空数组而不是模拟数据
        setSessions([])
      } finally {
        setLoading(false)
      }
    }

    loadSessions()
  }, [setSessions, setLoading])

  // 加载可用的模型列表
  const loadModels = async () => {
    setModelsLoading(true)
    try {
      const token = JSON.parse(localStorage.getItem('auth-storage') || '{}')?.state?.token
      
      if (!token) {
        message.error('请先登录')
        return
      }
      
      // 使用用户权限控制的模型列表端点
      const response = await fetch('http://localhost:8000/api/v1/llm-models/my-available', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        // my-available端点返回用户可用的模型
        setModels(data)
        console.log('✅ 成功加载用户可用模型列表:', data)
        
        // 详细检查每个模型的数据结构
        data.forEach((model: any, index: number) => {
          console.log(`模型 ${index + 1}:`, {
            id: model.id,
            name: model.name,
            config_params: model.config_params,
            config_params_type: typeof model.config_params,
            api_format: model.config_params?.api_format
          })
        })
        
        if (data.length === 0) {
          message.warning('您没有可用的AI模型权限，请联系管理员分配模型')
        }
      } else {
        console.error('获取模型列表失败:', response.status)
        message.error('获取模型列表失败')
        setModels([])
      }
    } catch (error) {
      console.error('❌ 获取模型列表失败:', error)
      setModels([])
    } finally {
      setModelsLoading(false)
    }
  }

  // 打开创建弹窗时加载模型列表
  const handleOpenCreateModal = () => {
    setCreateModalVisible(true)
    loadModels()
  }

  const handleCreateSession = async (values: { name: string; description?: string; llm_model_id: number }) => {
    try {
      setLoading(true)
      
      if (!values.llm_model_id) {
        message.error('请选择一个模型')
        return
      }
      
      // 调用真实API创建会话，只传递API支持的字段
      const newSession = await sessionsAPI.create({
        title: values.name,
        llm_model_id: values.llm_model_id,
      })

      addSession(newSession)
      setCreateModalVisible(false)
      form.resetFields()
      message.success('会话创建成功')
      
      // 创建后直接跳转到该会话
      navigate(`/chat/${newSession.id}`)
    } catch (error: any) {
      console.error('❌ 创建会话失败:', error)
      message.error(error.detail || '创建会话失败')
    } finally {
      setLoading(false)
    }
  }

  const handleEditSession = async (values: { name: string; description?: string; llm_model_id?: number }) => {
    if (!editingSession) return

    try {
      setLoading(true)
      // 调用真实API更新会话，只传递API支持的字段
      const updateData: any = {
        title: values.name,
      }
      
      // 如果选择了新模型，添加到更新数据中
      if (values.llm_model_id && values.llm_model_id !== editingSession.llm_model_id) {
        updateData.llm_model_id = values.llm_model_id
      }
      
      const updatedSession = await sessionsAPI.update(editingSession.id, updateData)

      updateSession(editingSession.id, updatedSession)
      setEditModalVisible(false)
      setEditingSession(null)
      form.resetFields()
      message.success('会话更新成功')
    } catch (error: any) {
      console.error('❌ 更新会话失败:', error)
      message.error(error.detail || '更新会话失败')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSession = (session: Session) => {
    confirm({
      title: '确认删除',
      content: `确定要删除会话"${session.name || session.title}"吗？`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          setLoading(true)
          // 调用真实API删除会话
          await sessionsAPI.delete(session.id)
          deleteSession(session.id)
          message.success('会话删除成功')
        } catch (error: any) {
          console.error('❌ 删除会话失败:', error)
          message.error(error.detail || '删除会话失败')
        } finally {
          setLoading(false)
        }
      },
    })
  }

  const handleEditClick = (session: Session) => {
    setEditingSession(session)
    form.setFieldsValue({
      name: session.name || session.title,
      description: session.description,
      llm_model_id: session.llm_model_id,
    })
    setEditModalVisible(true)
    loadModels() // 加载模型列表
  }

  const handleSessionClick = (session: Session) => {
    navigate(`/chat/${session.id}`)
  }

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24
        }}>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              会话管理
            </Title>
            <Text type="secondary">
              管理您的AI对话会话
            </Text>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleOpenCreateModal}
          >
            新建会话
          </Button>
        </div>

        <Spin spinning={loading}>
          {sessions.length === 0 && !loading ? (
            <Empty
              description="暂无会话"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleOpenCreateModal}
              >
                创建第一个会话
              </Button>
            </Empty>
          ) : (
            <List
              dataSource={sessions}
              renderItem={(session) => (
                <List.Item
                  key={session.id}
                  actions={[
                    <Button
                      key="edit"
                      type="text"
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleEditClick(session)
                      }}
                    >
                      编辑
                    </Button>,
                    <Button
                      key="delete"
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteSession(session)
                      }}
                    >
                      删除
                    </Button>,
                  ]}
                  style={{
                    cursor: 'pointer',
                    padding: '16px 24px',
                    borderRadius: 8,
                    marginBottom: 8,
                    border: '1px solid #f0f0f0',
                  }}
                  onClick={() => handleSessionClick(session)}
                >
                  <List.Item.Meta
                    avatar={
                      <div style={{
                        width: 48,
                        height: 48,
                        borderRadius: 8,
                        background: '#1890ff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <MessageOutlined style={{ color: 'white', fontSize: 20 }} />
                      </div>
                    }
                    title={
                      <Space>
                        <span style={{ fontSize: 16, fontWeight: 500 }}>
                          {session.name || session.title}
                        </span>
                        {session.is_active && (
                          <Tag color="green">活跃</Tag>
                        )}
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size={4}>
                        {session.description && (
                          <Text type="secondary">{session.description}</Text>
                        )}
                        <Space>
                          <ClockCircleOutlined style={{ color: '#999' }} />
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            更新时间：{dayjs(session.updated_at).fromNow()}
                          </Text>
                        </Space>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Spin>
      </Card>

      {/* 创建会话弹窗 */}
      <Modal
        title="创建新会话"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSession}
        >
          <Form.Item
            name="name"
            label="会话名称"
            rules={[
              { required: true, message: '请输入会话名称' },
              { max: 50, message: '会话名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入会话名称" />
          </Form.Item>

          <Form.Item
            name="llm_model_id"
            label="选择模型"
            rules={[
              { required: true, message: '请选择一个模型' },
            ]}
          >
            <Select
              placeholder="请选择要使用的AI模型"
              loading={modelsLoading}
              notFoundContent={modelsLoading ? <Spin size="small" /> : '暂无可用模型'}
            >
              {models.map((model) => {
                const displayName = `${model.name}${model.config_params?.api_format === 'ollama' ? ' (Ollama)' : ''}`
                return (
                  <Select.Option key={model.id} value={model.id} label={displayName}>
                    {displayName}
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="会话描述"
            rules={[
              { max: 200, message: '描述不能超过200个字符' },
            ]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入会话描述（可选）"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setCreateModalVisible(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑会话弹窗 */}
      <Modal
        title="编辑会话"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false)
          setEditingSession(null)
          form.resetFields()
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEditSession}
        >
          <Form.Item
            name="name"
            label="会话名称"
            rules={[
              { required: true, message: '请输入会话名称' },
              { max: 50, message: '会话名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入会话名称" />
          </Form.Item>

          <Form.Item
            name="llm_model_id"
            label="使用模型"
            tooltip="更换模型不会影响历史消息"
          >
            <Select
              placeholder="保持当前模型"
              loading={modelsLoading}
              allowClear
              notFoundContent={modelsLoading ? <Spin size="small" /> : '暂无可用模型'}
            >
              {models.map((model) => {
                const displayName = `${model.name}${model.config_params?.api_format === 'ollama' ? ' (Ollama)' : ''}`
                return (
                  <Select.Option key={model.id} value={model.id} label={displayName}>
                    {displayName}
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="会话描述"
            rules={[
              { max: 200, message: '描述不能超过200个字符' },
            ]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入会话描述（可选）"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setEditModalVisible(false)
                setEditingSession(null)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SessionsPage 