import React, { useState } from 'react'
import { Form, Input, Button, Card, Typography, message, Space, Divider } from 'antd'
import { UserOutlined, LockOutlined, SafetyCertificateOutlined } from '@ant-design/icons'
import { useAuthStore } from '../../store/authStore'
import { authAPI } from '../../services/api'
import type { LoginRequest } from '../../types/api'

const { Title, Text } = Typography

const LoginPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const { login } = useAuthStore()

  const onFinish = async (values: LoginRequest) => {
    setLoading(true)
    try {
      console.log('开始登录...')
      const response = await authAPI.login(values)
      console.log('登录API响应:', response)
      
      login(response.user, response.access_token)
      console.log('调用login方法完成')
      
      message.success('登录成功！')
      
      // 登录成功后，让PublicRoute组件处理重定向
      // 不需要手动导航，因为App.tsx中的逻辑会自动处理
    } catch (error: any) {
      console.error('登录错误详情:', error)
      // 优先使用后端返回的错误信息
      const errorMessage = error.detail || error.response?.data?.detail || error.message || '登录失败，请检查用户名和密码'
      message.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleDemoLogin = (username: string, password: string) => {
    form.setFieldsValue({ username, password })
    onFinish({ username, password })
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card 
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <SafetyCertificateOutlined 
            style={{ 
              fontSize: 48, 
              color: '#1890ff',
              marginBottom: 16 
            }} 
          />
          <Title level={2} style={{ margin: 0, color: '#262626' }}>
            AI安全系统
          </Title>
          <Text type="secondary">
            智能对话安全防护平台
          </Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="请输入用户名" 
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 4, message: '密码至少4个字符' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="请输入密码" 
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 16 }}>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              block
              style={{ height: 44 }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider orientation="center">
          <Text type="secondary" style={{ fontSize: 12 }}>
            演示账号
          </Text>
        </Divider>

        <Space direction="vertical" style={{ width: '100%' }}>
          <Button 
            block 
            size="small"
            onClick={() => handleDemoLogin('admin', 'admin123')}
            style={{ height: 36 }}
          >
            管理员登录 (admin/admin123)
          </Button>
          <Button 
            block 
            size="small"
            onClick={() => handleDemoLogin('user1', 'user123')}
            style={{ height: 36 }}
          >
            普通用户登录 (user1/user123)
          </Button>
        </Space>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            © 2024 AI安全系统. 版权所有
          </Text>
        </div>
      </Card>
    </div>
  )
}

export default LoginPage 