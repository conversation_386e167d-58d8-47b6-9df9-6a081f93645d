/**
 * API配置文件
 * 统一管理所有API相关的配置
 */

// 开发环境检测
const isDev = import.meta.env.DEV;

// API基础配置
export const API_CONFIG = {
  // 使用相对路径，通过Vite代理转发到后端
  BASE_URL: '/api/v1',
  
  // 超时配置
  TIMEOUT: 180000, // 3分钟，给RAG足够的处理时间
  
  // 请求头配置
  HEADERS: {
    'Content-Type': 'application/json',
  },
} as const;

// 获取认证token的工具函数
export const getAuthToken = (): string | null => {
  try {
    const authData = JSON.parse(localStorage.getItem('auth-storage') || '{}');
    return authData?.state?.token || authData?.token || null;
  } catch {
    return null;
  }
};

// 创建带认证的请求头
export const getAuthHeaders = (): Record<string, string> => {
  const headers = { ...API_CONFIG.HEADERS };
  const token = getAuthToken();
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  return headers;
};

// 构建完整的API URL
export const buildApiUrl = (endpoint: string): string => {
  // 如果endpoint已经是完整URL，直接返回
  if (endpoint.startsWith('http')) {
    return endpoint;
  }
  
  // 如果endpoint以/api/v1开头，直接返回
  if (endpoint.startsWith('/api/v1')) {
    return endpoint;
  }
  
  // 否则拼接基础URL
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${API_CONFIG.BASE_URL}/${cleanEndpoint}`;
};

// 通用的fetch包装器
export const apiFetch = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = buildApiUrl(endpoint);
  const headers = getAuthHeaders();
  
  const config: RequestInit = {
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  };
  
  console.log(`API请求: ${config.method || 'GET'} ${url}`);
  
  const response = await fetch(url, config);
  
  if (!response.ok) {
    console.error(`API请求失败: ${response.status} ${response.statusText}`);
  }
  
  return response;
};

export default API_CONFIG;
