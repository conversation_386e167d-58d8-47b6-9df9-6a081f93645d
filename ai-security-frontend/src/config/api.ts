// API配置
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  API_PREFIX: '/api/v1'
};

// 获取完整的API URL
export const getApiUrl = (endpoint: string): string => {
  // 如果endpoint已经是完整URL，直接返回
  if (endpoint.startsWith('http')) {
    return endpoint;
  }
  
  // 如果endpoint以/api开头，直接拼接BASE_URL
  if (endpoint.startsWith('/api')) {
    return `${API_CONFIG.BASE_URL}${endpoint}`;
  }
  
  // 否则拼接完整路径
  return `${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
};

// 导出常用的API端点
export const API_ENDPOINTS = {
  // 认证相关
  LOGIN: '/login/access-token',
  USER_INFO: '/auth/me',
  
  // 管理员相关
  ADMIN: {
    USERS: '/admin/users/',
    ROLES: '/admin/roles/',
    LEVELS: '/admin/levels/',
    LLM_MODELS: '/admin/llm-models/',
    KEYWORDS: '/admin/keywords/',
    KEYWORD_GROUPS: '/admin/keyword-groups/',
    PERMISSIONS: '/admin/permissions/',
    HIERARCHY: '/admin/hierarchy/',
    MODEL_ASSIGNMENTS: '/admin/model-assignments/'
  },
  
  // 普通用户相关
  SESSIONS: '/sessions/',
  MESSAGES: '/messages/',
  LLM_MODELS: '/llm-models/'
}; 