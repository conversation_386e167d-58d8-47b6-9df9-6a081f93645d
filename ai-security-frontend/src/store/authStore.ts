import { create } from 'zustand'
import { persist, subscribeWithSelector } from 'zustand/middleware'
import type { User } from '../types/api'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

interface AuthActions {
  login: (user: User, token: string) => void
  logout: () => void
  setLoading: (loading: boolean) => void
  updateUser: (user: Partial<User>) => void
  initializeAuth: () => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // 初始化认证状态
      initializeAuth: () => {
        const { user, token } = get()
        const shouldBeAuthenticated = !!(user && token)
        if (shouldBeAuthenticated !== get().isAuthenticated) {
          set({ isAuthenticated: shouldBeAuthenticated })
        }
      },

      // 登录
      login: (user: User, token: string) => {
        console.log('AuthStore.login 被调用:', { user, token: token.substring(0, 20) + '...' })
        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
        })
        console.log('AuthStore.login 状态更新完成，当前状态:', {
          user: user.username,
          hasToken: !!token,
          isAuthenticated: true
        })
      },

      // 登出
      logout: () => {
        console.log('AuthStore.logout 被调用')
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
        // 清除本地存储
        localStorage.removeItem('auth-storage')
        // 跳转到登录页
        window.location.href = '/login'
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      },

    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 在状态恢复后，确保认证状态一致
        if (state) {
          state.initializeAuth()
        }
      },
    }
  )
) 