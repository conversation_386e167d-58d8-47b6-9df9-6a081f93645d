import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { Session, Message, LLMModel } from '../types/api'

// 模拟会话数据
export const mockSessions: Session[] = [
  {
    id: 1,
    title: 'AI助手对话',
    name: 'AI助手对话',
    description: '与AI助手进行日常对话和问答',
    llm_model_id: 1,
    user_id: 1,
    is_active: true,
    is_deleted: false,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T15:30:00Z',
    message_count: 25,
    last_message_at: '2024-01-15T15:30:00Z',
  },
  {
    id: 2,
    title: '代码审查助手',
    name: '代码审查助手',
    description: '使用AI进行代码审查和优化建议',
    llm_model_id: 1,
    user_id: 1,
    is_active: true,
    is_deleted: false,
    created_at: '2024-01-14T09:00:00Z',
    updated_at: '2024-01-14T18:45:00Z',
    message_count: 12,
    last_message_at: '2024-01-14T18:45:00Z',
  },
  {
    id: 3,
    title: '文档写作助手',
    name: '文档写作助手',
    description: '协助撰写技术文档和用户手册',
    llm_model_id: 2,
    user_id: 1,
    is_active: false,
    is_deleted: false,
    created_at: '2024-01-13T14:20:00Z',
    updated_at: '2024-01-13T16:10:00Z',
    message_count: 8,
    last_message_at: '2024-01-13T16:10:00Z',
  },
]

interface SessionState {
  sessions: Session[]
  currentSession: Session | null
  messages: Record<number, Message[]> // 修改为按会话ID存储消息
  llmModels: LLMModel[]
  isLoading: boolean
  loading: boolean // 兼容属性
  isCreating: boolean
  isSending: boolean
  isInitialized: boolean // 添加初始化标志
}

interface SessionActions {
  // 会话管理
  setSessions: (sessions: Session[]) => void
  addSession: (session: Session) => void
  updateSession: (id: number, updates: Partial<Session>) => void
  removeSession: (id: number) => void
  deleteSession: (id: number) => void
  setCurrentSession: (session: Session | null) => void
  initializeSessions: () => void // 初始化会话数据
  
  // 消息管理
  setMessages: (sessionId: number, messages: Message[]) => void
  addMessage: (sessionId: number, message: Message) => void
  updateMessage: (sessionId: number, messageId: number, updates: Partial<Message>) => void
  getMessages: (sessionId: number) => Message[]
  
  // LLM模型管理
  setLLMModels: (models: LLMModel[]) => void
  
  // 加载状态管理
  setLoading: (loading: boolean) => void
  setCreating: (creating: boolean) => void
  setSending: (sending: boolean) => void
  
  // 清理状态
  clearAll: () => void
}

type SessionStore = SessionState & SessionActions

export const useSessionStore = create<SessionStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      sessions: [],
      currentSession: null,
      messages: {},
      llmModels: [],
      isLoading: false,
      loading: false,
      isCreating: false,
      isSending: false,
      isInitialized: false,

      // 初始化会话数据
      initializeSessions: () => {
        const state = get()
        if (!state.isInitialized) {
          // 如果没有会话数据，使用模拟数据
          if (state.sessions.length === 0) {
            set({ 
              sessions: [...mockSessions],
              isInitialized: true 
            })
          } else {
            set({ isInitialized: true })
          }
        }
      },

      // 会话管理
      setSessions: (sessions) => set({ sessions }),
      
      addSession: (session) => {
        const newSession = {
          ...session,
          id: session.id || Date.now(), // 确保有唯一ID
          created_at: session.created_at || new Date().toISOString(),
          updated_at: new Date().toISOString(),
          message_count: 0,
          last_message_at: undefined,
        }
        
        set((state) => ({
          sessions: [newSession, ...state.sessions],
          messages: {
            ...state.messages,
            [newSession.id]: [] // 为新会话初始化空消息数组
          }
        }))
        
        return newSession
      },
      
      updateSession: (id, updates) => {
        set((state) => ({
          sessions: state.sessions.map(session =>
            session.id === id 
              ? { ...session, ...updates, updated_at: new Date().toISOString() } 
              : session
          ),
          currentSession: state.currentSession?.id === id
            ? { ...state.currentSession, ...updates, updated_at: new Date().toISOString() }
            : state.currentSession
        }))
      },
      
      removeSession: (id) => {
        set((state) => {
          const newMessages = { ...state.messages }
          delete newMessages[id] // 删除对应的消息数据
          
          return {
            sessions: state.sessions.filter(session => session.id !== id),
            currentSession: state.currentSession?.id === id ? null : state.currentSession,
            messages: newMessages
          }
        })
      },

      deleteSession: (id) => {
        const { removeSession } = get()
        removeSession(id)
      },
      
      setCurrentSession: (session) => {
        set({ currentSession: session })
      },

      // 消息管理
      setMessages: (sessionId, messages) => {
        set((state) => ({
          messages: {
            ...state.messages,
            [sessionId]: messages
          }
        }))
      },
      
      addMessage: (sessionId, message) => {
        set((state) => {
          const sessionMessages = state.messages[sessionId] || []
          const newMessage = {
            ...message,
            id: message.id || Date.now(),
            created_at: message.created_at || new Date().toISOString()
          }
          
          const updatedMessages = {
            ...state.messages,
            [sessionId]: [...sessionMessages, newMessage]
          }
          
          // 同时更新会话的消息计数和最后消息时间
          const updatedSessions = state.sessions.map(session => 
            session.id === sessionId 
              ? {
                  ...session,
                  message_count: (session.message_count || 0) + 1,
                  last_message_at: newMessage.created_at,
                  updated_at: new Date().toISOString()
                }
              : session
          )
          
          return {
            messages: updatedMessages,
            sessions: updatedSessions,
            currentSession: state.currentSession?.id === sessionId
              ? {
                  ...state.currentSession,
                  message_count: (state.currentSession.message_count || 0) + 1,
                  last_message_at: newMessage.created_at,
                  updated_at: new Date().toISOString()
                }
              : state.currentSession
          }
        })
      },
      
      updateMessage: (sessionId, messageId, updates) => {
        set((state) => ({
          messages: {
            ...state.messages,
            [sessionId]: (state.messages[sessionId] || []).map(message =>
              message.id === messageId ? { ...message, ...updates } : message
            )
          }
        }))
      },

      getMessages: (sessionId) => {
        const state = get()
        return state.messages[sessionId] || []
      },

      // LLM模型管理
      setLLMModels: (models) => set({ llmModels: models }),

      // 加载状态管理
      setLoading: (loading) => set({ isLoading: loading, loading }),
      setCreating: (creating) => set({ isCreating: creating }),
      setSending: (sending) => set({ isSending: sending }),

      // 清理状态
      clearAll: () => {
        set({
          sessions: [],
          currentSession: null,
          messages: {},
          llmModels: [],
          isLoading: false,
          loading: false,
          isCreating: false,
          isSending: false,
          isInitialized: false,
        })
      },
    }),
    {
      name: 'ai-security-session-storage', // 本地存储的key
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // 只持久化这些状态，排除临时状态
        sessions: state.sessions,
        messages: state.messages,
        currentSession: state.currentSession,
        isInitialized: state.isInitialized,
      }),
    }
  )
)

// 辅助选择器
export const useCurrentSessionMessages = () => {
  return useSessionStore((state) => {
    const sessionId = state.currentSession?.id
    return sessionId ? state.messages[sessionId] || [] : []
  })
}

export const useSessionById = (id: number) => {
  return useSessionStore((state) => 
    state.sessions.find(session => session.id === id)
  )
}

export const useSessionMessages = (sessionId: number) => {
  return useSessionStore((state) => state.messages[sessionId] || [])
}