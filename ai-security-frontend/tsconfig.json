{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/pages/*": ["./pages/*"], "@/store/*": ["./store/*"], "@/services/*": ["./services/*"], "@/types/*": ["./types/*"]}, "types": ["vite/client"]}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist"]}