---
description: 
globs: 
alwaysApply: false
---
尽量将任务分解为最小单元，并以循序渐进的方式解决问题。
尽量保证模块之间的低耦合高内聚，减少一个模块修改影响到其他模块的概率。
修改任何代码前，要充分考虑是否会影响已有的正常功能，避免引发新的bug，尤其是修改前端功能时，务必不要影响其他前端功能。
修改bug时，要先分析bug的根源，而不是靠猜测原因来直接修改代码。
无论是开放新功能还是修改bug，都遵循小步快跑的原则，一次只做最小的一个功能或改动，测试通过后再做进一步的开发。避免一次开发或改动大量代码。
涉及前端的修改，修改后尽量用MCP工具进行测试，当MCP工具无法使用时，先解决MCP工具无法使用的问题。
启动任何程序前，需要检查是否有重复进程已经在运行。避免重复启动。
任何情况下不要绕过问题、不要用模拟的、临时的方式解决问题，要正面问题，一劳永逸的解决问题。不要使用临时性方案开发功能。
代码不能使用硬编码方式显示和存储数据。
执行任何命令，应仔细检查输出窗口的结果，是否有报错。
使用中文回答我的问题。