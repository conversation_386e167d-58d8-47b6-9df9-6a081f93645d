#!/usr/bin/env python3
"""
RAG功能调试脚本
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.services.knowledge_base_service import KnowledgeBaseService
from app.db.session import get_sync_db
from sqlalchemy.orm import Session
from sqlalchemy import text

async def test_rag_search():
    """测试RAG搜索功能"""
    
    # 获取数据库会话
    db_gen = get_sync_db()
    db: Session = next(db_gen)
    
    try:
        # 初始化服务
        kb_service = KnowledgeBaseService()
        
        # 测试查询
        query = "本地模型路径是什么"
        print(f"查询: {query}")
        print("=" * 50)
        
        # 1. 测试知识库搜索
        print("1. 测试知识库搜索...")
        search_results = await kb_service.search_knowledge_base(
            db=db, 
            query=query, 
            user_id=1,  # 使用测试用户ID
            top_k=5,
            threshold=0.1  # 降低阈值
        )
        print(f"搜索结果数量: {len(search_results)}")
        
        for i, result in enumerate(search_results):
            print(f"\n结果 {i+1}:")
            print(f"  文档ID: {result.get('document_id')}")
            print(f"  文档名: {result.get('document_name')}")
            print(f"  块索引: {result.get('chunk_index')}")
            similarity = result.get('similarity', 0)
            # 处理numpy数组
            if hasattr(similarity, 'item'):
                similarity = similarity.item()
            print(f"  相似度: {similarity:.4f}")
            print(f"  内容片段: {result.get('content', '')[:150]}...")
        
        # 2. 检查文档状态
        print("\n2. 检查文档状态...")
        # 直接查询数据库
        result = db.execute(text("SELECT id, title, file_name, status, is_vectorized, chunk_count FROM knowledge_documents"))
        documents = result.fetchall()
        for doc in documents:
            if "模型" in doc[1] or "路径" in doc[1]:
                print(f"文档: {doc[1]}")
                print(f"  状态: {doc[3]}")
                print(f"  已向量化: {doc[4]}")
                print(f"  块数量: {doc[5]}")
        
        # 3. 测试不同的查询词
        test_queries = [
            "模型路径",
            "本地路径", 
            "NSFW",
            "clip-vit",
            "finetune"
        ]
        
        print("\n3. 测试不同查询词...")
        for test_query in test_queries:
            print(f"\n查询: '{test_query}'")
            results = await kb_service.search_knowledge_base(
                db=db,
                query=test_query,
                user_id=1,
                top_k=3,
                threshold=0.1  # 降低阈值
            )
            print(f"结果数量: {len(results)}")
            for i, result in enumerate(results):
                similarity = result.get('similarity', 0)
                # 处理numpy数组
                if hasattr(similarity, 'item'):
                    similarity = similarity.item()
                print(f"  {i+1}. 相似度: {similarity:.4f}, 内容: {result.get('content', '')[:100]}...")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_rag_search())
