#!/usr/bin/env python3
"""
调试LLM请求详情
"""

import sys
import os
import asyncio
import json
sys.path.append('.')

from app.db.session import get_sync_db
from app.db.models.llm_model import LLMModel

async def debug_llm_request():
    """调试LLM请求详情"""
    
    print("🔍 调试LLM请求详情")
    print("=" * 60)
    
    # 获取数据库会话
    db = next(get_sync_db())
    
    try:
        # 1. 获取本地模型（更可靠）
        print("\n1. 获取本地模型...")
        model = db.query(LLMModel).filter(
            LLMModel.is_active == True,
            LLMModel.api_url.like('%localhost%')
        ).first()
        
        if not model:
            print("❌ 没有本地活跃的LLM模型，使用远程模型")
            model = db.query(LLMModel).filter(
                LLMModel.is_active == True
            ).first()
        
        if not model:
            print("❌ 没有活跃的LLM模型")
            return
        
        print(f"✅ 使用模型: {model.name} (ID: {model.id})")
        print(f"   API地址: {model.api_url}")
        print(f"   配置参数: {json.dumps(model.config_params, ensure_ascii=False, indent=2)}")
        
        # 2. 手动构建请求
        print("\n2. 手动构建请求...")
        
        messages = [
            {"role": "system", "content": "你是一个助手，请简短回答问题。"},
            {"role": "user", "content": "根据以下信息回答问题：\n\n上下文：不安全图片集路径：/root/pic_detect/content-moderation-system/models/finetune/finetune_data/Validation_NSFW\n\n问题：本地模型路径是什么？"}
        ]
        
        # 检查API格式
        api_format = model.config_params.get("api_format", "openai") if model.config_params else "openai"
        print(f"   API格式: {api_format}")
        
        if api_format == "ollama":
            # Ollama 格式
            request_body = {
                "prompt": messages[-1]["content"],  # 使用最后一条用户消息作为prompt
                "stream": False
            }
            
            # 必须指定model
            if model.config_params and "model" in model.config_params:
                request_body["model"] = model.config_params["model"]
                print(f"   模型名: {request_body['model']}")
            else:
                print("❌ Ollama格式需要在config_params中指定model字段")
                return
                
            # 添加其他参数
            if model.config_params:
                options = {}
                if "temperature" in model.config_params:
                    options["temperature"] = model.config_params["temperature"]
                if "top_p" in model.config_params:
                    options["top_p"] = model.config_params["top_p"]
                if "max_tokens" in model.config_params:
                    options["num_predict"] = model.config_params["max_tokens"]
                
                if options:
                    request_body["options"] = options
        else:
            # OpenAI 格式
            request_body = {
                "messages": messages,
                "stream": False
            }
            
            # 添加模型配置参数
            if model.config_params:
                request_body.update({
                    "temperature": model.config_params.get("temperature", 0.7),
                    "max_tokens": model.config_params.get("max_tokens", 1000),
                    "top_p": model.config_params.get("top_p", 0.9),
                })
                
                # 如果配置中有model字段，添加到请求中
                if "model" in model.config_params:
                    request_body["model"] = model.config_params["model"]
                    print(f"   模型名: {request_body['model']}")
        
        print(f"   请求体: {json.dumps(request_body, ensure_ascii=False, indent=2)}")
        
        # 3. 发送请求
        print("\n3. 发送请求...")
        import aiohttp
        
        headers = {"Content-Type": "application/json"}
        
        async with aiohttp.ClientSession() as session:
            try:
                timeout_seconds = 30
                print(f"   发送到: {model.api_url}")
                
                async with session.post(
                    model.api_url,
                    json=request_body,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=timeout_seconds)
                ) as response:
                    print(f"   响应状态: {response.status}")
                    
                    if response.status != 200:
                        error_text = await response.text()
                        print(f"❌ API返回错误: {error_text}")
                    else:
                        response_text = await response.text()
                        print(f"✅ API响应成功")
                        
                        try:
                            response_json = json.loads(response_text)
                            if api_format == "ollama":
                                answer = response_json.get("response", "")
                            else:
                                answer = response_json.get("choices", [{}])[0].get("message", {}).get("content", "")
                            
                            print(f"   回答: {answer}")
                        except:
                            print(f"   原始响应: {response_text[:500]}...")
                            
            except Exception as e:
                print(f"❌ 请求失败: {e}")
    
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_llm_request())
