#!/usr/bin/env python3
"""
测试完整的RAG流程
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.rag_service import RAGService
from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.llm_service import LLMService
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.knowledge_base_audit import KnowledgeBaseAuditService
from app.services.pipeline.factory import SecurityPipelineFactory
from app.db.session import SyncSessionLocal

async def test_rag_complete_flow():
    """测试完整的RAG流程"""
    print("🔍 测试完整的RAG流程...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 初始化服务
        knowledge_service = KnowledgeBaseService()
        llm_service = LLMService()
        permission_service = KnowledgeBasePermissionService(db=db)
        
        from app.core.audit import AuditLogger
        audit_logger = AuditLogger()
        audit_service = KnowledgeBaseAuditService()
        
        security_pipeline_factory = SecurityPipelineFactory
        
        # 创建RAG服务实例
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_service,
            llm_service=llm_service,
            permission_service=permission_service,
            audit_service=audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        # 测试查询
        query = "产品清单有什么产品"
        user_id = 1
        
        print(f"📝 测试查询: {query}")
        
        # 使用完整的RAG流程
        print("\n🚀 执行完整RAG流程...")
        
        result = await rag_service.generate_answer(
            query=query,
            user_id=user_id,
            model_id=None,  # 使用默认模型
            top_k=3,
            temperature=0.7,
            max_tokens=300,
            include_sources=True
        )
        
        print("✅ RAG流程完成")
        print(f"成功: {result.get('success', False)}")
        print(f"答案: {result.get('answer', 'N/A')}")
        print(f"检索文档数: {result.get('documents_retrieved', 0)}")
        print(f"使用模型ID: {result.get('model_id', 'N/A')}")
        print(f"错误: {result.get('error', 'N/A')}")
        
        if result.get('sources'):
            print(f"\n📚 来源文档 ({len(result['sources'])}个):")
            for i, source in enumerate(result['sources'][:3]):
                print(f"  {i+1}. 文档: {source.get('document_title', 'N/A')}")
                print(f"     内容: {source.get('content', 'N/A')[:100]}...")
        
        # 测试不同的查询
        test_queries = [
            "有哪些AI产品",
            "数据安全产品有什么",
            "培训课程包括什么"
        ]
        
        for test_query in test_queries:
            print(f"\n🔍 测试查询: {test_query}")
            
            result = await rag_service.generate_answer(
                query=test_query,
                user_id=user_id,
                model_id=None,
                top_k=3,
                temperature=0.7,
                max_tokens=200,
                include_sources=False
            )
            
            print(f"成功: {result.get('success', False)}")
            print(f"答案: {result.get('answer', 'N/A')}")
            print(f"检索文档数: {result.get('documents_retrieved', 0)}")
            
            if not result.get('success'):
                print(f"错误: {result.get('error', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_rag_complete_flow())
