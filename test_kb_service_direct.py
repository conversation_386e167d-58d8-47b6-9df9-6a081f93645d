#!/usr/bin/env python3
"""
直接测试知识库服务搜索功能
"""

import sys
import os
import asyncio
sys.path.append('.')

async def test_kb_service_search():
    """直接测试知识库服务搜索"""
    
    print("=== 直接测试知识库服务搜索 ===\n")
    
    try:
        # 1. 初始化知识库服务
        from app.services.knowledge_base_service import KnowledgeBaseService
        from app.db.session import SyncSessionLocal
        
        kb_service = KnowledgeBaseService()
        db = SyncSessionLocal()
        
        print(f"1. 知识库服务初始化完成")
        print(f"   向量化器类型: {type(kb_service.vectorizer)}")
        print(f"   向量数据库类型: {type(kb_service.vector_db)}")
        
        # 2. 直接测试搜索
        print("\n2. 直接测试知识库搜索...")
        
        query = "本地模型路径"
        print(f"   查询: {query}")
        
        # 调用搜索方法
        results = await kb_service.search_knowledge_base(
            db=db,
            query=query,
            user_id=1,  # 假设用户ID为1
            top_k=10,
            threshold=0.1
        )
        
        print(f"   搜索结果数量: {len(results)}")
        
        # 显示结果
        for i, result in enumerate(results):
            doc_id = result.get('document_id', 'unknown')
            similarity = result.get('similarity', 0)
            content = result.get('content', '')
            chunk_id = result.get('chunk_id', '')
            
            print(f"   结果{i+1}: 文档{doc_id}, 相似度: {similarity:.3f}")
            print(f"     chunk_id: {chunk_id}")
            print(f"     内容: {content[:100]}...")
            print()
        
        # 检查文档2的结果
        doc2_results = [r for r in results if r.get('document_id') == 2]
        print(f"   文档2的结果数量: {len(doc2_results)}")
        
        if doc2_results:
            print("   ✅ 找到文档2的结果！")
            for i, result in enumerate(doc2_results):
                print(f"     文档2结果{i+1}: 相似度 {result['similarity']:.3f}")
                print(f"       内容: {result['content'][:150]}...")
        else:
            print("   ❌ 没有找到文档2的结果")
        
        db.close()
        return len(doc2_results) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_kb_service_search())
    if success:
        print("\n🎉 知识库服务搜索测试成功！")
    else:
        print("\n⚠️ 知识库服务搜索测试失败")
