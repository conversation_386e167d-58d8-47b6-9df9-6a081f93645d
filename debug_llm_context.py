#!/usr/bin/env python3
"""
调试LLM接收到的上下文内容
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
from app.db.session import SyncSessionLocal
from app.services.knowledge_base_service import knowledge_base_service

async def debug_llm_context():
    """调试LLM上下文"""
    print("🔍 调试LLM接收到的上下文内容")
    print("=" * 60)
    
    # 创建数据库会话
    db = SyncSessionLocal()
    
    try:
        # 1. 搜索知识库
        print("1. 搜索知识库...")
        search_results = await knowledge_base_service.search_knowledge_base(
            db=db,
            query="本地模型路径是什么？",
            user_id=1,
            top_k=10,
            threshold=0.1
        )
        
        print(f"   搜索结果数量: {len(search_results)}")
        
        # 2. 构建上下文（模拟RAG服务的逻辑）
        print("\n2. 构建上下文...")
        context_chunks = []
        
        for i, result in enumerate(search_results, 1):
            chunk_content = result.get("content", "")
            document_id = result.get('document_id')
            chunk_id = result.get('chunk_id', '')
            
            print(f"   Chunk {i} (doc_id={document_id}, chunk_id={chunk_id}):")
            print(f"     内容长度: {len(chunk_content)} 字符")
            print(f"     内容预览: {chunk_content[:100]}...")
            
            context_chunks.append(chunk_content)
            print()
        
        # 3. 合并上下文
        print("3. 合并上下文...")
        context_text = "\n\n".join(context_chunks)
        print(f"   原始上下文长度: {len(context_text)} 字符")
        
        # 4. 应用长度限制
        MAX_CONTEXT_LENGTH = 8000
        if len(context_text) > MAX_CONTEXT_LENGTH:
            truncated_context = context_text[:MAX_CONTEXT_LENGTH] + "..."
            print(f"   截断后上下文长度: {len(truncated_context)} 字符")
            print(f"   ⚠️  上下文被截断！")
        else:
            truncated_context = context_text
            print(f"   上下文长度: {len(truncated_context)} 字符 (未截断)")
        
        # 5. 构建完整提示词
        print("\n4. 构建完整提示词...")
        query = "本地模型路径是什么？"
        prompt = f"""你是一个专业的知识库助手。请根据以下文档内容准确回答用户问题。

文档内容：
{truncated_context}

用户问题：{query}

请仔细阅读文档内容，基于文档中的具体信息进行回答。如果文档包含相关信息，请详细列出；如果文档中没有相关信息，请明确说明。"""
        
        print(f"   完整提示词长度: {len(prompt)} 字符")
        
        # 6. 显示完整上下文内容
        print("\n5. 完整上下文内容:")
        print("-" * 60)
        print(truncated_context)
        print("-" * 60)
        
        # 7. 检查是否包含模型路径信息
        print("\n6. 检查关键信息:")
        keywords = ["模型路径", "CLIP", "本地模型", "/root/pic_detect", "model_cache"]
        for keyword in keywords:
            if keyword in truncated_context:
                print(f"   ✅ 包含关键词: '{keyword}'")
            else:
                print(f"   ❌ 缺少关键词: '{keyword}'")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_llm_context())
