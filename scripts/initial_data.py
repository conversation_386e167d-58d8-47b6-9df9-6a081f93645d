#!/usr/bin/env python3

import asyncio
import logging
import os
import sys

# Add project root to sys.path to allow imports from app
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(PROJECT_ROOT)

from app.core.config import settings
from app.db.session import SessionLocal
from app.db.models import User, Role, Level # Models are defined in app.db.models
from app.crud import crud_user, crud_role, crud_level
from app.schemas.user import UserCreate
from app.schemas.role import RoleCreate
from app.schemas.level import LevelCreate
from app.core.security import get_password_hash

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def init_db() -> None:
    db = SessionLocal()
    try:
        # Create Admin Role
        admin_role = await crud_role.get_role_by_name(db, name=settings.ADMIN_USER_ROLE_NAME)
        if not admin_role:
            role_in = RoleCreate(name=settings.ADMIN_USER_ROLE_NAME, description=settings.ADMIN_USER_ROLE_DESCRIPTION)
            admin_role = await crud_role.create_role(db, role=role_in)
            logger.info(f"Admin role '{admin_role.name}' created.")
        else:
            logger.info(f"Admin role '{admin_role.name}' already exists.")

        # Create Default Admin Level
        admin_level = await crud_level.get_level_by_name(db, name=settings.DEFAULT_ADMIN_LEVEL_NAME)
        if not admin_level:
            level_in = LevelCreate(name=settings.DEFAULT_ADMIN_LEVEL_NAME, description=settings.DEFAULT_ADMIN_LEVEL_DESCRIPTION)
            admin_level = await crud_level.create_level(db, level=level_in)
            logger.info(f"Default admin level '{admin_level.name}' created.")
        else:
            logger.info(f"Default admin level '{admin_level.name}' already exists.")

        # Create First Superuser
        superuser = await crud_user.get_user_by_username(db, username=settings.FIRST_SUPERUSER_USERNAME)
        if not superuser:
            user_in = UserCreate(
                username=settings.FIRST_SUPERUSER_USERNAME,
                email=settings.FIRST_SUPERUSER_EMAIL,
                password=settings.FIRST_SUPERUSER_PASSWORD, # Will be hashed by CRUD
                full_name="Default Superuser",
                is_active=True,
                role_id=admin_role.id,
                level_id=admin_level.id
            )
            superuser = await crud_user.create_user(db, user=user_in) # Assumes create_user handles password hashing
            logger.info(f"Superuser '{superuser.username}' created.")
        else:
            logger.info(f"Superuser '{superuser.username}' already exists.")

    finally:
        await db.close()

async def main() -> None:
    logger.info("Starting data initialization...")
    await init_db()
    logger.info("Data initialization finished.")
    print("SUCCESS: Initial data script completed successfully.")

if __name__ == "__main__":

    asyncio.run(main())
