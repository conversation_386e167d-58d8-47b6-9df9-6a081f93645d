#!/usr/bin/env python3
"""
创建用户文档权限表的数据库迁移脚本
"""
import sqlite3
import os

def create_user_document_permissions_table():
    """创建用户文档权限表"""
    
    # 数据库文件路径
    db_path = "./ai_security.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔗 已连接到数据库")
        
        # 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='user_document_permissions'
        """)
        
        if cursor.fetchone():
            print("✅ user_document_permissions表已存在，跳过创建")
            return
        
        print("📝 开始创建user_document_permissions表...")
        
        # 创建用户文档权限表
        cursor.execute("""
            CREATE TABLE user_document_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                document_id INTEGER NOT NULL,
                permission_type VARCHAR(20) NOT NULL,
                granted BOOLEAN NOT NULL DEFAULT 1,
                granted_by INTEGER NOT NULL,
                granted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NULL,
                reason TEXT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (document_id) REFERENCES knowledge_documents(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES users(id)
            )
        """)
        
        print("✅ user_document_permissions表创建成功")
        
        # 创建索引以提高查询性能
        indexes = [
            ("idx_udp_user_id", "user_id"),
            ("idx_udp_document_id", "document_id"),
            ("idx_udp_permission_type", "permission_type"),
            ("idx_udp_granted", "granted"),
            ("idx_udp_expires_at", "expires_at"),
            ("idx_udp_user_document", "user_id, document_id"),
            ("idx_udp_user_permission", "user_id, permission_type"),
            ("idx_udp_document_permission", "document_id, permission_type"),
            ("idx_udp_user_doc_perm", "user_id, document_id, permission_type")
        ]
        
        for index_name, columns in indexes:
            cursor.execute(f"""
                CREATE INDEX {index_name} 
                ON user_document_permissions({columns})
            """)
            print(f"✅ 索引 {index_name} 创建成功")
        
        # 创建触发器以自动更新updated_at字段
        cursor.execute("""
            CREATE TRIGGER update_user_document_permissions_updated_at
            AFTER UPDATE ON user_document_permissions
            FOR EACH ROW
            BEGIN
                UPDATE user_document_permissions 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = NEW.id;
            END
        """)
        
        print("✅ 触发器创建成功")
        
        # 插入一些示例数据（可选）
        print("\n📊 插入示例权限数据...")
        
        # 获取一些用户和文档ID用于示例
        cursor.execute("SELECT id FROM users LIMIT 3")
        user_ids = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id FROM knowledge_documents LIMIT 3")
        document_ids = [row[0] for row in cursor.fetchall()]
        
        if user_ids and document_ids:
            # 为第一个用户授予第一个文档的读取权限
            cursor.execute("""
                INSERT INTO user_document_permissions 
                (user_id, document_id, permission_type, granted_by, reason)
                VALUES (?, ?, 'read', ?, '系统初始化示例权限')
            """, (user_ids[0], document_ids[0], user_ids[0]))
            
            # 为第二个用户授予第二个文档的下载权限（有过期时间）
            if len(user_ids) > 1 and len(document_ids) > 1:
                cursor.execute("""
                    INSERT INTO user_document_permissions 
                    (user_id, document_id, permission_type, granted_by, expires_at, reason)
                    VALUES (?, ?, 'download', ?, datetime('now', '+30 days'), '临时下载权限')
                """, (user_ids[1], document_ids[1], user_ids[0]))
            
            print(f"✅ 插入了示例权限数据")
        else:
            print("⚠️ 没有找到用户或文档数据，跳过示例数据插入")
        
        # 验证表结构
        cursor.execute("PRAGMA table_info(user_document_permissions)")
        columns = cursor.fetchall()
        print(f"\n📋 表结构验证 (共{len(columns)}个字段):")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        # 验证索引
        cursor.execute("PRAGMA index_list(user_document_permissions)")
        indexes = cursor.fetchall()
        print(f"\n🔍 索引验证 (共{len(indexes)}个索引):")
        for idx in indexes:
            print(f"  - {idx[1]} ({'UNIQUE' if idx[2] else 'NON-UNIQUE'})")
        
        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM user_document_permissions")
        count = cursor.fetchone()[0]
        print(f"\n📊 数据验证: 表中共有 {count} 条权限记录")
        
        # 提交更改
        conn.commit()
        print("\n🎉 用户文档权限表创建完成！")
        
        # 显示使用示例
        print("\n📖 使用示例:")
        print("1. 授予用户文档读取权限:")
        print("   POST /api/v1/user-document-permissions/grant")
        print("   {\"user_id\": 1, \"document_id\": 1, \"permission_type\": \"read\"}")
        
        print("\n2. 查看用户可访问的文档:")
        print("   GET /api/v1/user-document-permissions/user/1/accessible-documents")
        
        print("\n3. 批量授予权限:")
        print("   POST /api/v1/user-document-permissions/batch-grant")
        print("   {\"user_ids\": [1,2], \"document_ids\": [1,2], \"permission_types\": [\"read\", \"download\"]}")
        
    except sqlite3.Error as e:
        print(f"❌ 数据库错误: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ 创建失败: {e}")
    finally:
        if conn:
            conn.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    create_user_document_permissions_table()
