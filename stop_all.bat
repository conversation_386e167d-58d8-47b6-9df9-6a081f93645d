@echo off
chcp 65001 > nul
echo ========================================
echo   AI Security System - Stop Script
echo ========================================
echo.

echo Stopping all services...
echo.

:: Stop Python processes (backend)
echo [1/3] Stopping backend service...
taskkill /F /IM python.exe 2>nul
if %errorlevel% == 0 (
    echo      [OK] Backend service stopped
) else (
    echo      [-] Backend service not running
)

:: Stop Node.js processes (frontend)
echo [2/3] Stopping frontend service...
taskkill /F /IM node.exe 2>nul
if %errorlevel% == 0 (
    echo      [OK] Frontend service stopped
) else (
    echo      [-] Frontend service not running
)

:: Stop other processes
echo [3/3] Cleaning up other processes...
taskkill /F /FI "WINDOWTITLE eq AI Security*" 2>nul
taskkill /F /FI "WINDOWTITLE eq Vite*" 2>nul

echo.
echo ========================================
echo   All services stopped!
echo ========================================
echo.
echo Press any key to exit...
pause > nul