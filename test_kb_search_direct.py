#!/usr/bin/env python3
"""
直接测试知识库搜索功能
"""

import sys
import os
import asyncio
import requests
import json
sys.path.append('.')

async def test_kb_search_direct():
    """直接测试知识库搜索"""
    
    print("🔍 直接测试知识库搜索")
    print("=" * 60)
    
    try:
        # 1. 登录获取token
        print("\n1. 登录获取token...")
        login_url = "http://localhost:8000/api/v1/login/access-token"
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = requests.post(login_url, data=login_data)
        if login_response.status_code != 200:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            return
        
        token_data = login_response.json()
        access_token = token_data.get('access_token')
        print(f"   ✅ 登录成功")
        
        # 2. 直接调用知识库搜索API
        print("\n2. 直接调用知识库搜索API...")
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        kb_url = "http://localhost:8000/api/v1/knowledge-base/search"
        
        query = "本地模型路径"
        print(f"   查询: {query}")
        
        kb_data = {
            "query": query,
            "top_k": 10,
            "threshold": 0.1
        }
        
        try:
            kb_response = requests.post(kb_url, json=kb_data, headers=headers)
            
            print(f"   响应状态码: {kb_response.status_code}")
            
            if kb_response.status_code == 200:
                result = kb_response.json()
                
                print(f"   响应数据结构:")
                for key in result.keys():
                    print(f"     - {key}: {type(result[key])}")
                
                # 详细分析results
                results = result.get('results', [])
                print(f"\n   📊 搜索结果详情 ({len(results)}个):")
                
                doc2_count = 0
                for i, item in enumerate(results, 1):
                    print(f"     Result {i}:")
                    print(f"       document_id: {item.get('document_id')}")
                    print(f"       document_name: {item.get('document_name')}")
                    print(f"       similarity: {item.get('similarity')}")
                    print(f"       chunk_id: {item.get('chunk_id')}")
                    content = item.get('content', '')
                    print(f"       content: {content[:150]}...")
                    print()
                    
                    if item.get('document_id') == 2:
                        doc2_count += 1
                
                print(f"   文档2结果数量: {doc2_count}")
                
                if doc2_count > 0:
                    print("   ✅ 知识库搜索能找到文档2")
                else:
                    print("   ❌ 知识库搜索未找到文档2")
                
            else:
                print(f"   ❌ 知识库搜索失败: {kb_response.status_code}")
                print(f"      错误信息: {kb_response.text}")
        
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_kb_search_direct())
