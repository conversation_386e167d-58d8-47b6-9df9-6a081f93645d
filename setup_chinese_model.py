#!/usr/bin/env python3
"""
配置使用中文模型 - 推荐使用Ollama或轻量级中文模型
"""

import subprocess
import sys
import sqlite3
import json

def check_ollama_with_chinese():
    """检查Ollama和中文模型"""
    try:
        # 检查Ollama是否运行
        result = subprocess.run(['curl', '-s', 'http://localhost:11434/api/tags'], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            models = json.loads(result.stdout).get('models', [])
            chinese_models = [m for m in models if 'qwen' in m['name'].lower() or 'chinese' in m['name'].lower()]
            if chinese_models:
                print(f"✅ 发现本地中文模型: {chinese_models[0]['name']}")
                return True, chinese_models[0]['name']
            else:
                print("❌ Ollama运行中但没有中文模型")
                print("💡 建议运行: ollama pull qwen:0.5b")
                return False, None
    except:
        pass
    return False, None

def setup_chinese_llm():
    """配置中文LLM"""
    print("🔍 检查可用的中文模型方案...\n")
    
    # 方案1: 检查Ollama
    has_ollama, model_name = check_ollama_with_chinese()
    
    if has_ollama:
        print("\n✅ 使用本地Ollama中文模型")
        config_ollama_model(model_name)
    else:
        print("\n📋 中文模型方案:")
        print("1. 安装Ollama并下载qwen模型（推荐）")
        print("2. 使用在线API（当前方案）")
        print("3. 下载小型中文模型（需要额外配置）")
        
        choice = input("\n选择方案 (1/2/3，默认2): ").strip() or "2"
        
        if choice == "1":
            print("\n📋 Ollama安装步骤:")
            print("1. brew install ollama")
            print("2. ollama serve (新终端)")
            print("3. ollama pull qwen:0.5b")
            print("4. 重新运行此脚本")
        elif choice == "3":
            print("\n⚠️  小型中文模型需要:")
            print("1. 下载Wenzhong-GPT2等模型")
            print("2. 修改后端代码支持")
            print("3. 建议还是使用Ollama方案")
        else:
            print("\n✅ 继续使用当前的远程API")

def config_ollama_model(model_name):
    """配置数据库使用本地Ollama模型"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 配置本地模型
    config_params = json.dumps({
        "api_format": "ollama",
        "model": model_name,
        "temperature": 0.7,
        "top_p": 0.9
    })
    
    # 更新或创建本地配置
    cursor.execute("""
        UPDATE llm_models 
        SET is_active = 0
        WHERE api_url LIKE '%*************%'
    """)
    
    cursor.execute("""
        INSERT OR REPLACE INTO llm_models (name, api_url, config_params, is_active)
        VALUES (?, ?, ?, 1)
    """, (f"本地 {model_name}", "http://localhost:11434/api/generate", config_params))
    
    conn.commit()
    conn.close()
    
    print(f"✅ 已配置使用本地{model_name}模型")
    print("🚀 RAG响应速度将从1-2分钟降低到5-10秒！")

def main():
    print("🌟 配置中文AI模型")
    print("=" * 50)
    
    setup_chinese_llm()
    
    print("\n💡 总结:")
    print("- 嵌入模型: multilingual-e5-small (已有)")
    print("- 生成模型: 建议使用Ollama + qwen")
    print("- 响应速度: 本地模型快10-20倍")

if __name__ == "__main__":
    main() 
"""
配置使用中文模型 - 推荐使用Ollama或轻量级中文模型
"""

import subprocess
import sys
import sqlite3
import json

def check_ollama_with_chinese():
    """检查Ollama和中文模型"""
    try:
        # 检查Ollama是否运行
        result = subprocess.run(['curl', '-s', 'http://localhost:11434/api/tags'], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            models = json.loads(result.stdout).get('models', [])
            chinese_models = [m for m in models if 'qwen' in m['name'].lower() or 'chinese' in m['name'].lower()]
            if chinese_models:
                print(f"✅ 发现本地中文模型: {chinese_models[0]['name']}")
                return True, chinese_models[0]['name']
            else:
                print("❌ Ollama运行中但没有中文模型")
                print("💡 建议运行: ollama pull qwen:0.5b")
                return False, None
    except:
        pass
    return False, None

def setup_chinese_llm():
    """配置中文LLM"""
    print("🔍 检查可用的中文模型方案...\n")
    
    # 方案1: 检查Ollama
    has_ollama, model_name = check_ollama_with_chinese()
    
    if has_ollama:
        print("\n✅ 使用本地Ollama中文模型")
        config_ollama_model(model_name)
    else:
        print("\n📋 中文模型方案:")
        print("1. 安装Ollama并下载qwen模型（推荐）")
        print("2. 使用在线API（当前方案）")
        print("3. 下载小型中文模型（需要额外配置）")
        
        choice = input("\n选择方案 (1/2/3，默认2): ").strip() or "2"
        
        if choice == "1":
            print("\n📋 Ollama安装步骤:")
            print("1. brew install ollama")
            print("2. ollama serve (新终端)")
            print("3. ollama pull qwen:0.5b")
            print("4. 重新运行此脚本")
        elif choice == "3":
            print("\n⚠️  小型中文模型需要:")
            print("1. 下载Wenzhong-GPT2等模型")
            print("2. 修改后端代码支持")
            print("3. 建议还是使用Ollama方案")
        else:
            print("\n✅ 继续使用当前的远程API")

def config_ollama_model(model_name):
    """配置数据库使用本地Ollama模型"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 配置本地模型
    config_params = json.dumps({
        "api_format": "ollama",
        "model": model_name,
        "temperature": 0.7,
        "top_p": 0.9
    })
    
    # 更新或创建本地配置
    cursor.execute("""
        UPDATE llm_models 
        SET is_active = 0
        WHERE api_url LIKE '%*************%'
    """)
    
    cursor.execute("""
        INSERT OR REPLACE INTO llm_models (name, api_url, config_params, is_active)
        VALUES (?, ?, ?, 1)
    """, (f"本地 {model_name}", "http://localhost:11434/api/generate", config_params))
    
    conn.commit()
    conn.close()
    
    print(f"✅ 已配置使用本地{model_name}模型")
    print("🚀 RAG响应速度将从1-2分钟降低到5-10秒！")

def main():
    print("🌟 配置中文AI模型")
    print("=" * 50)
    
    setup_chinese_llm()
    
    print("\n💡 总结:")
    print("- 嵌入模型: multilingual-e5-small (已有)")
    print("- 生成模型: 建议使用Ollama + qwen")
    print("- 响应速度: 本地模型快10-20倍")

if __name__ == "__main__":
    main() 