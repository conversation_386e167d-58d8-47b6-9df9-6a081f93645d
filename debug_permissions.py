#!/usr/bin/env python3
"""
权限控制诊断脚本
快速检查权限系统是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.db import models
from app.core.permissions import is_admin, can_access_session
from app.core.config import settings

# 创建同步数据库连接
engine = create_engine(settings.DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def diagnose_permissions():
    """诊断权限控制系统"""
    print("🔍 开始权限控制诊断...")
    
    db = SessionLocal()
    try:
        # 1. 检查所有用户和角色
        print("\n📋 用户和角色信息:")
        users = db.query(models.User).all()
        for user in users:
            role_name = user.role.name if user.role else "无角色"
            admin_status = "是管理员" if is_admin(user) else "普通用户"
            print(f"  用户ID: {user.id}, 用户名: {user.username}, 角色: {role_name}, 状态: {admin_status}")
        
        # 2. 检查所有会话
        print("\n💬 会话信息:")
        sessions = db.query(models.Session).all()
        for session in sessions:
            print(f"  会话ID: {session.id}, 标题: {session.title}, 所属用户ID: {session.user_id}")
        
        # 3. 模拟权限检查
        print("\n🛡️ 权限检查测试:")
        if users and sessions:
            test_user = users[0]  # 取第一个用户
            test_session = sessions[0]  # 取第一个会话
            
            print(f"测试用户: {test_user.username} (ID: {test_user.id})")
            print(f"测试会话: {test_session.title} (所属用户ID: {test_session.user_id})")
            print(f"用户是否为管理员: {is_admin(test_user)}")
            print(f"用户能否访问此会话: {can_access_session(test_user, test_session)}")
            
            # 如果用户不是会话的所有者且不是管理员，应该返回False
            if test_user.id != test_session.user_id and not is_admin(test_user):
                expected_result = False
                actual_result = can_access_session(test_user, test_session)
                if actual_result != expected_result:
                    print(f"❌ 权限检查异常! 期望: {expected_result}, 实际: {actual_result}")
                else:
                    print("✅ 权限检查正常")
        
        # 4. 检查角色配置
        print("\n🎭 角色配置:")
        roles = db.query(models.Role).all()
        for role in roles:
            print(f"  角色: {role.name}, 是否激活: {role.is_active}")
            
        # 5. 检查具体的API端点行为
        print("\n🔗 API端点检查:")
        print("主要的sessions API端点:")
        print("  GET /api/v1/sessions/ - 普通用户获取自己的会话")
        print("  GET /api/v1/sessions/admin/all - 管理员获取所有会话")
        print("⚠️  请检查前端调用的是哪个端点!")
            
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    diagnose_permissions() 