#!/usr/bin/env python3
"""
调试KnowledgeBaseService搜索问题
"""

import sys
import os
import asyncio
sys.path.append('.')

from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.document_vectorizer import DocumentVectorizer
from app.db.session import get_sync_db
from sqlalchemy.orm import Session
from sqlalchemy import text

async def debug_search():
    """调试搜索问题"""
    
    # 获取数据库会话
    db_gen = get_sync_db()
    db: Session = next(db_gen)
    
    try:
        # 1. 直接使用DocumentVectorizer搜索
        print("1. 直接使用DocumentVectorizer搜索...")
        vectorizer = DocumentVectorizer()
        
        direct_results = await vectorizer.search_similar_chunks(
            query="本地模型路径",
            top_k=5,
            threshold=0.1
        )
        
        print(f"直接搜索结果数量: {len(direct_results)}")
        for i, result in enumerate(direct_results):
            print(f"  {i+1}. ID: {result['chunk_id']}, 相似度: {result['similarity']:.4f}")
            print(f"      内容: {result['content'][:100]}...")
            print()
        
        # 2. 使用KnowledgeBaseService搜索
        print("2. 使用KnowledgeBaseService搜索...")
        kb_service = KnowledgeBaseService()
        
        kb_results = await kb_service.search_knowledge_base(
            db=db,
            query="本地模型路径",
            user_id=1,
            top_k=5,
            threshold=0.1
        )
        
        print(f"KB服务搜索结果数量: {len(kb_results)}")
        for i, result in enumerate(kb_results):
            similarity = result.get('similarity', 0)
            # 处理numpy数组
            if hasattr(similarity, 'item'):
                similarity = similarity.item()
            print(f"  {i+1}. 文档ID: {result.get('document_id')}, 相似度: {similarity:.4f}")
            print(f"      文档名: {result.get('document_name')}")
            print(f"      内容: {result.get('content', '')[:100]}...")
            print()
        
        # 3. 检查KnowledgeBaseService使用的向量化器
        print("3. 检查KnowledgeBaseService使用的向量化器...")
        print(f"KB服务的向量化器类型: {type(kb_service.vectorizer)}")
        print(f"是否是生产级服务: {kb_service.vectorizer.__class__.__module__}")
        
        # 4. 检查PRODUCTION_SERVICES配置
        from app.services.knowledge_base_service import PRODUCTION_SERVICES
        print(f"PRODUCTION_SERVICES: {PRODUCTION_SERVICES}")
        
        # 5. 尝试调用KB服务的向量化器
        print("\n4. 尝试调用KB服务的向量化器...")
        try:
            if PRODUCTION_SERVICES:
                kb_vectorizer_results = await kb_service.vectorizer.search_similar_chunks(
                    query="本地模型路径",
                    vector_db=kb_service.vector_db,
                    top_k=5,
                    threshold=0.1
                )
            else:
                kb_vectorizer_results = await kb_service.vectorizer.search_similar_chunks(
                    query="本地模型路径",
                    top_k=5,
                    threshold=0.1
                )
            
            print(f"KB向量化器搜索结果数量: {len(kb_vectorizer_results)}")
            for i, result in enumerate(kb_vectorizer_results):
                similarity = result.get('similarity', result.get('relevance_score', 0))
                # 处理numpy数组
                if hasattr(similarity, 'item'):
                    similarity = similarity.item()
                print(f"  {i+1}. ID: {result.get('chunk_id', result.get('id'))}, 相似度: {similarity:.4f}")
                print(f"      内容: {result.get('content', '')[:100]}...")
                print()
        except Exception as e:
            print(f"KB向量化器搜索失败: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_search())
