import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

print("="*60)
print("更新Qwen模型的API端点")
print("="*60)

# 1. 管理员登录
print("\n[1] 管理员登录...")
login_data = {
    "username": "admin",
    "password": "admin123"
}
response = requests.post(f"{BASE_URL}/login/access-token", data=login_data)
if response.status_code == 200:
    tokens = response.json()
    access_token = tokens["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    print("✅ 登录成功")
else:
    print(f"❌ 登录失败: {response.text}")
    exit(1)

# 2. 获取Qwen模型的当前配置
print("\n[2] 获取Qwen模型当前配置...")
response = requests.get(f"{BASE_URL}/admin/llm-models/5", headers=headers)
if response.status_code == 200:
    model = response.json()
    print(f"✅ 找到模型: {model['name']}")
    print(f"   当前API端点: {model.get('api_endpoint', 'N/A')}")
    print(f"   当前配置: {json.dumps(model.get('config_params', {}), ensure_ascii=False)}")
else:
    print(f"❌ 获取模型失败: {response.text}")
    exit(1)

# 3. 更新模型配置
print("\n[3] 更新模型配置...")
update_data = {
    "name": model['name'],
    "provider": model['provider'],
    "api_endpoint": "http://39.99.156.233:11434/api/generate",  # Ollama API端点
    "api_key": "",  # Ollama不需要API密钥
    "config_params": model.get('config_params', {}),
    "is_active": True
}

response = requests.put(
    f"{BASE_URL}/admin/llm-models/5", 
    json=update_data, 
    headers=headers
)

if response.status_code == 200:
    updated_model = response.json()
    print("✅ 模型更新成功！")
    print(f"   新API端点: {updated_model.get('api_endpoint', 'N/A')}")
    print(f"   配置参数: {json.dumps(updated_model.get('config_params', {}), ensure_ascii=False)}")
else:
    print(f"❌ 更新失败: {response.status_code}")
    print(f"   响应: {response.text}")

print("\n="*60)
print("更新完成！")
print("="*60) 