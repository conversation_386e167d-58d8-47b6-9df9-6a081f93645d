@echo off
chcp 65001 > nul
echo ========================================
echo   AI Security System - Status Check
echo ========================================
echo.

:: Check backend service
echo [Backend Service Status]
powershell -Command "if (Test-NetConnection localhost -Port 8000 -InformationLevel Quiet) { Write-Host '  [OK] Backend service running (port 8000)' -ForegroundColor Green } else { Write-Host '  [X] Backend service not running' -ForegroundColor Red }"

echo.
echo [Frontend Service Status]
powershell -Command "if (Test-NetConnection localhost -Port 5173 -InformationLevel Quiet) { Write-Host '  [OK] Frontend service running (port 5173)' -ForegroundColor Green } else { Write-Host '  [X] Frontend service not running' -ForegroundColor Red }"

echo.
echo [Python Process]
tasklist /FI "IMAGENAME eq python.exe" 2>nul | find /I "python.exe" >nul
if %errorlevel% == 0 (
    echo   [OK] Python process is running
) else (
    echo   [X] No Python process found
)

echo.
echo [Node.js Process]
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %errorlevel% == 0 (
    echo   [OK] Node.js process is running
) else (
    echo   [X] No Node.js process found
)

echo.
echo ========================================
echo.
echo Press any key to exit...
pause > nul