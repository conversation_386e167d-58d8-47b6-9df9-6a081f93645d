#!/usr/bin/env python3
"""
测试RAG系统的中文语义搜索功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_vectorizer import DocumentVectorizer
from app.services.semantic_embedding import semantic_embedding_service


async def test_chinese_rag_search():
    """测试中文RAG搜索"""
    
    print("=" * 80)
    print("RAG系统中文语义搜索测试")
    print("=" * 80)
    
    # 创建向量化服务
    vectorizer = DocumentVectorizer()
    
    print(f"\n嵌入服务状态:")
    print(f"- 语义嵌入可用: {hasattr(vectorizer, 'semantic_embedding_service') or 'semantic_embedding_service' in globals()}")
    print(f"- 嵌入维度: {semantic_embedding_service.dimension if 'semantic_embedding_service' in globals() else '未知'}")
    
    # 测试数据 - 模拟已经处理过的文档块
    test_chunks = [
        {
            'chunk_index': 0,
            'content': "人工智能（AI）是计算机科学的一个重要分支，它致力于开发能够模拟人类智能的系统和算法。",
            'start_char': 0,
            'end_char': 100,
            'metadata': {
                'document_id': 1,
                'file_name': 'ai_introduction.txt',
                'file_type': 'text'
            }
        },
        {
            'chunk_index': 1,
            'content': "机器学习是实现人工智能的一种方法，它使计算机能够从数据中自动学习规律和模式。",
            'start_char': 100,
            'end_char': 200,
            'metadata': {
                'document_id': 1,
                'file_name': 'ai_introduction.txt',
                'file_type': 'text'
            }
        },
        {
            'chunk_index': 2,
            'content': "深度学习是机器学习的一个子领域，它使用多层神经网络来处理复杂的数据和任务。",
            'start_char': 200,
            'end_char': 300,
            'metadata': {
                'document_id': 2,
                'file_name': 'deep_learning.txt',
                'file_type': 'text'
            }
        },
        {
            'chunk_index': 3,
            'content': "自然语言处理（NLP）是人工智能的重要应用领域，专注于让计算机理解和生成人类语言。",
            'start_char': 0,
            'end_char': 100,
            'metadata': {
                'document_id': 3,
                'file_name': 'nlp_guide.txt',
                'file_type': 'text'
            }
        },
        {
            'chunk_index': 4,
            'content': "计算机视觉使机器能够理解和分析图像及视频内容，在安防、医疗等领域有广泛应用。",
            'start_char': 0,
            'end_char': 100,
            'metadata': {
                'document_id': 4,
                'file_name': 'computer_vision.txt',
                'file_type': 'text'
            }
        }
    ]
    
    # 生成向量并添加到向量数据库
    print("\n正在向量化文档块...")
    embeddings = []
    for chunk in test_chunks:
        embedding = vectorizer.simple_embedding(chunk['content'])
        embeddings.append(embedding)
        print(f"- 处理块 {chunk['chunk_index']}: {chunk['content'][:30]}...")
    
    # 添加到向量数据库
    vectorizer.vector_db.add_document_chunks(test_chunks, document_id=999, embeddings=embeddings)
    
    # 测试查询
    test_queries = [
        "什么是人工智能？",
        "深度神经网络",
        "如何让计算机理解中文？",
        "图像识别技术",
        "机器学习和AI的关系"
    ]
    
    print("\n" + "=" * 80)
    print("执行语义搜索测试")
    print("=" * 80)
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        print("-" * 40)
        
        # 执行搜索
        results = await vectorizer.search_similar_chunks(
            query=query,
            top_k=3,
            threshold=0.5
        )
        
        # 显示结果
        if results:
            for i, result in enumerate(results, 1):
                print(f"{i}. 相似度: {result['similarity']:.4f}")
                print(f"   内容: {result['content']}")
                print(f"   来源: {result['metadata'].get('file_name', '未知')}")
        else:
            print("   没有找到相关内容")
    
    # 清理测试数据
    vectorizer.vector_db.delete_document_chunks(999)
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(test_chinese_rag_search()) 