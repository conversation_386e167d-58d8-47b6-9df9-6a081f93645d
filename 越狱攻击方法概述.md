# 越狱攻击方法概述

越狱攻击是指通过精心设计的输入，绕过大语言模型（LLM）的安全限制，诱导模型产生违反其设计初衷或安全准则的输出。以下是越狱攻击的常见方法分类：

## 一、基于人工设计的攻击

### （一）目标竞争攻击
1. **前缀注入**
   - 通过命令模型的输出必须含有一个看似无害的前缀来改变上下文信息，从而诱导模型输出有害内容。
2. **拒绝抑制**
   - 要求模型不能输出含有拒绝意义的常见词汇，从而诱导模型输出有害内容。
3. **风格注入**
   - 要求模型以特定的风格输出响应，从而诱导模型输出有害内容。

### （二）基于虚构场景的攻击
1. **伪装攻击**
   - 改变对话背景与上下文，例如要求模型扮演特定角色、承担某种责任或模拟科学实验。
2. **注意力转移攻击**
   - 改变对话上下文与当前任务，例如要求模型进行逻辑推理、文本翻译等。
3. **权限提升攻击**
   - 通过构建虚假的“sudo”模式或模拟越狱进程，打破对模型的所有限制。

### （三）基于上下文学习的攻击
- 通过提供多条恶意问题与对应的不安全回复作为上下文，诱导模型对最后一个问题给出有害回复。

### （四）基于生成策略的攻击
- 攻击者通过操纵模型的生成策略（如解码超参数与采样方法）来破坏模型对齐，诱导模型输出有害信息。

### （五）基于编码与翻译的攻击
1. **语言翻译攻击**
   - 将有害提示翻译为低资源语言（安全训练中未覆盖到的语言）以提高攻击成功率。
2. **编码攻击**
   - 利用编码对模型进行越狱攻击，例如要求模型使用特定密码聊天。

## 二、基于模型生成的攻击

### （一）基于迭代优化的攻击
- 使用助手模型迭代细化越狱提示，以高效生成具有针对性的越狱攻击。

### （二）基于模块化生成的攻击
- 将越狱提示分解为不同模块（如攻击的危害类别、具体的滥用指令、模型扮演的角色等），利用助手模型组合生成越狱提示。

## 三、白盒攻击与黑盒攻击

### （一）白盒攻击
- 攻击者可以访问模型的内部状态与参数，通过计算梯度等方式优化输入。例如，基于梯度的攻击会在有害问题后初始化对抗性后缀，并基于模型的梯度反馈不断优化后缀。

### （二）黑盒攻击
- 攻击者无法访问模型的内部状态和参数，通常依赖于人工设计提示或利用模型自动生成攻击提示。例如：
  - **模板补全**：使用预先定义的模板插入有害问题。
  - **提示重写**：通过加密、翻译等方法改写提示的文本结构。
  - **基于大模型的生成**：训练一个大模型作为攻击模型来生成越狱攻击的提示。

## 四、其他攻击方式

### （一）链式提示攻击
- 通过一系列看似无害的提示，逐步引导模型产生目标输出。

### （二）通用且可转移的攻击
- 生成一组字符组成的对抗性后缀，诱发模型几乎所有类型的有害输出，且这类对抗性后缀可以轻松转移。

---

**注意**：越狱攻击对大语言模型的安全性构成了严重威胁，需要通过技术手段和策略加以防范。