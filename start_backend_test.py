#!/usr/bin/env python3
"""
测试后端启动脚本
"""
import sys
import os

# 添加项目路径到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    try:
        print("🚀 尝试启动后端服务...")
        print("=" * 50)
        
        # 导入必要的模块
        from app.main import app
        import uvicorn
        
        print("✅ 成功导入所有模块！")
        print("🌐 启动服务器...")
        print("   访问地址: http://localhost:8000")
        print("   API文档: http://localhost:8000/docs")
        print("=" * 50)
        
        # 启动服务器
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print(f"   详细信息: {e.__class__.__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc() 