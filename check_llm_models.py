#!/usr/bin/env python3
"""
检查LLM模型配置
"""

import sqlite3
import json

def check_llm_models():
    """检查数据库中的LLM模型配置"""
    conn = sqlite3.connect('ai_security.db')
    cursor = conn.cursor()
    
    # 查询所有模型
    cursor.execute("""
        SELECT id, name, api_url, config_params, is_active, created_at
        FROM llm_models
        ORDER BY id
    """)
    
    models = cursor.fetchall()
    
    print("🔍 当前LLM模型配置:")
    print("=" * 80)
    
    for model in models:
        model_id, name, api_url, config_params, is_active, created_at = model
        
        print(f"ID: {model_id}")
        print(f"名称: {name}")
        print(f"API URL: {api_url}")
        print(f"激活状态: {'✅ 激活' if is_active else '❌ 未激活'}")
        print(f"创建时间: {created_at}")
        
        if config_params:
            try:
                config = json.loads(config_params)
                print(f"配置参数: {json.dumps(config, indent=2, ensure_ascii=False)}")
            except:
                print(f"配置参数: {config_params}")
        else:
            print("配置参数: 无")
        
        print("-" * 40)
    
    # 查询当前激活的模型
    cursor.execute("""
        SELECT id, name, api_url, config_params
        FROM llm_models
        WHERE is_active = 1
        ORDER BY id
    """)
    
    active_models = cursor.fetchall()
    
    print(f"\n🚀 当前激活的模型数量: {len(active_models)}")
    for model in active_models:
        model_id, name, api_url, config_params = model
        print(f"  - ID {model_id}: {name} ({api_url})")
    
    conn.close()

if __name__ == "__main__":
    check_llm_models()
