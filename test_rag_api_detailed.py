#!/usr/bin/env python3
"""
详细测试RAG API返回的数据
"""

import sys
import os
import asyncio
import requests
import json
sys.path.append('.')

async def test_rag_api_detailed():
    """详细测试RAG API"""
    
    print("🔍 详细测试RAG API")
    print("=" * 60)
    
    try:
        # 1. 登录获取token
        print("\n1. 登录获取token...")
        login_url = "http://localhost:8000/api/v1/login/access-token"
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = requests.post(login_url, data=login_data)
        if login_response.status_code != 200:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            return
        
        token_data = login_response.json()
        access_token = token_data.get('access_token')
        print(f"   ✅ 登录成功")
        
        # 2. 测试数据脱敏查询
        print("\n2. 测试数据脱敏查询...")
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        rag_url = "http://localhost:8000/api/v1/rag/query"
        
        query = "元数据稽核是什么？"
        print(f"   查询: {query}")
        
        rag_data = {
            "query": query,
            "top_k": 5,
            "threshold": 0.1
        }
        
        try:
            rag_response = requests.post(rag_url, json=rag_data, headers=headers)
            
            print(f"   响应状态码: {rag_response.status_code}")
            
            if rag_response.status_code == 200:
                result = rag_response.json()
                
                print(f"   响应数据结构:")
                for key in result.keys():
                    print(f"     - {key}: {type(result[key])}")
                
                # 详细分析sources
                sources = result.get('sources', [])
                print(f"\n   📊 Sources详情 ({len(sources)}个):")
                
                for i, source in enumerate(sources, 1):
                    print(f"     Source {i}:")
                    for key, value in source.items():
                        if key == 'content':
                            print(f"       {key}: {str(value)[:100]}...")
                        else:
                            print(f"       {key}: {value}")
                    print()
                
                # 检查是否有文档10
                doc10_sources = [s for s in sources if s.get('document_id') == 10]
                print(f"   文档10 sources数量: {len(doc10_sources)}")
                
                if doc10_sources:
                    print("   ✅ 找到文档10的sources:")
                    for i, source in enumerate(doc10_sources, 1):
                        similarity = source.get('similarity', source.get('relevance_score', 0))
                        print(f"     {i}. 相似度: {similarity}")
                        print(f"        内容: {source.get('content', '')[:100]}...")
                else:
                    print("   ❌ 未找到文档10的sources")
                
                # 分析答案
                answer = result.get('answer', '')
                context_used = result.get('context_used', False)
                
                print(f"\n   💬 答案详情:")
                print(f"     context_used: {context_used}")
                
                # 修复NoneType错误
                if answer is None:
                    print(f"     answer: None (LLM可能未返回结果)")
                    print(f"     answer长度: 0")
                    
                    # 检查错误信息
                    error_msg = result.get('error')
                    if error_msg:
                        print(f"     错误信息: {error_msg}")
                    
                    # 检查模型状态
                    model_id = result.get('model_id')
                    print(f"     model_id: {model_id}")
                    
                elif answer == '':
                    print(f"     answer: 空字符串")
                    print(f"     answer长度: 0")
                else:
                    print(f"     answer长度: {len(answer)}")
                    print(f"     answer内容: {answer[:200]}...")
                
                # 完整的JSON输出（用于调试）
                print(f"\n   🔍 完整响应JSON:")
                print(json.dumps(result, ensure_ascii=False, indent=2)[:1000] + "...")
                
            else:
                print(f"   ❌ RAG请求失败: {rag_response.status_code}")
                print(f"      错误信息: {rag_response.text}")
        
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_rag_api_detailed())
