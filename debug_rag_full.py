#!/usr/bin/env python3
"""
完整调试RAG服务
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.rag_service import RAGService
from app.services.knowledge_base_service import KnowledgeBaseService
from app.services.llm_service import LLMService
from app.services.knowledge_base_permissions import KnowledgeBasePermissionService
from app.services.knowledge_base_audit import KnowledgeBaseAuditService
from app.services.pipeline.factory import SecurityPipelineFactory
from app.db.session import SyncSessionLocal

async def debug_rag_full():
    """完整调试RAG服务"""
    print("🔍 完整调试RAG服务...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 初始化服务
        print("🔧 初始化服务...")
        knowledge_service = KnowledgeBaseService()
        llm_service = LLMService()
        permission_service = KnowledgeBasePermissionService(db=db)
        
        from app.core.audit import AuditLogger
        audit_logger = AuditLogger()
        audit_service = KnowledgeBaseAuditService()
        
        # SecurityPipelineFactory 是静态类，不需要实例化
        security_pipeline_factory = SecurityPipelineFactory
        
        # 创建RAG服务实例
        rag_service = RAGService(
            db=db,
            knowledge_service=knowledge_service,
            llm_service=llm_service,
            permission_service=permission_service,
            audit_service=audit_service,
            security_pipeline_factory=security_pipeline_factory
        )
        
        print("✅ 服务初始化完成")
        
        # 测试参数
        query = "什么是人工智能安全？"
        user_id = 1
        model_id = None  # 使用默认模型
        top_k = 3
        temperature = 0.7
        max_tokens = 200
        
        print(f"\n📝 测试参数:")
        print(f"   查询: {query}")
        print(f"   用户ID: {user_id}")
        print(f"   模型ID: {model_id}")
        print(f"   返回数量: {top_k}")
        print(f"   最大token: {max_tokens}")
        
        # 执行RAG查询
        print(f"\n🚀 开始RAG查询...")
        start_time = time.time()
        
        result = await rag_service.generate_answer(
            query=query,
            user_id=user_id,
            model_id=model_id,
            top_k=top_k,
            temperature=temperature,
            max_tokens=max_tokens,
            include_sources=True
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ RAG查询耗时: {duration:.2f} 秒")
        print(f"📊 查询结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   答案: {result.get('answer', 'N/A')}")
        print(f"   检索文档数: {result.get('documents_retrieved', 0)}")
        print(f"   使用上下文: {result.get('context_used', False)}")
        print(f"   模型ID: {result.get('model_id', 'N/A')}")
        print(f"   错误: {result.get('error', 'N/A')}")
        
        if result.get('sources'):
            print(f"\n📄 来源信息:")
            for i, source in enumerate(result['sources']):
                print(f"   {i+1}. 文档ID: {source.get('document_id', 'N/A')}")
                print(f"      文档标题: {source.get('document_title', 'N/A')}")
                print(f"      块索引: {source.get('chunk_index', 'N/A')}")
                print(f"      相关性: {source.get('relevance_score', 0.0):.3f}")
        
        # 如果失败，进行分步调试
        if not result.get('success'):
            print(f"\n🔍 分步调试...")
            
            # 1. 测试权限检查
            print("1️⃣ 测试权限检查...")
            try:
                has_access = permission_service.check_module_access_by_user_id(user_id)
                print(f"   权限检查结果: {has_access}")
            except Exception as e:
                print(f"   权限检查失败: {str(e)}")
            
            # 2. 测试知识库搜索
            print("2️⃣ 测试知识库搜索...")
            try:
                search_results = await knowledge_service.search_knowledge_base(
                    db=db,
                    query=query,
                    user_id=user_id,
                    top_k=top_k,
                    threshold=0.1
                )
                print(f"   搜索结果数: {len(search_results)}")
                for i, result in enumerate(search_results[:2]):  # 只显示前2个
                    print(f"   {i+1}. 文档ID: {result.get('document_id', 'N/A')}")
                    print(f"      相似度: {result.get('similarity', 0.0):.3f}")
            except Exception as e:
                print(f"   知识库搜索失败: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # 3. 测试LLM调用
            print("3️⃣ 测试LLM调用...")
            try:
                from app.db.models.llm_model import LLMModel
                llm_model = db.query(LLMModel).filter(
                    LLMModel.is_active == True
                ).first()
                
                if llm_model:
                    print(f"   找到模型: {llm_model.name}")
                    
                    # 简单测试
                    test_messages = [
                        {"role": "user", "content": "你好，请简单回答：什么是AI？"}
                    ]
                    
                    import copy
                    test_model = copy.deepcopy(llm_model)
                    if test_model.config_params:
                        test_model.config_params['max_tokens'] = 50
                    else:
                        test_model.config_params = {'max_tokens': 50}
                    
                    response = await llm_service._call_llm_api(
                        llm_model=test_model,
                        messages=test_messages,
                        stream=False
                    )
                    print(f"   LLM响应: {response}")
                else:
                    print("   没有找到激活的LLM模型")
                    
            except Exception as e:
                print(f"   LLM调用失败: {str(e)}")
                import traceback
                traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_rag_full())
