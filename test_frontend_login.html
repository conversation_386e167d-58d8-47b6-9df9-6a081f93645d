<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .test-buttons button {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI安全系统 - 前端登录测试</h1>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" placeholder="请输入密码">
        </div>
        
        <div class="test-buttons">
            <button onclick="testDirectAPI()">测试直接API调用</button>
            <button onclick="testViteProxy()">测试Vite代理</button>
            <button onclick="testFrontendAPI()">测试前端API服务</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function clearResults() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }

        async function testDirectAPI() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showResult('正在测试直接API调用...', 'info');
            
            try {
                const params = new URLSearchParams();
                params.append('username', username);
                params.append('password', password);
                
                const response = await fetch('http://localhost:8000/api/v1/login/access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: params
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`直接API调用成功！\n\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`直接API调用失败！\n\n状态码: ${response.status}\n错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`直接API调用出错！\n\n错误信息: ${error.message}\n\n可能的原因:\n1. 网络连接问题\n2. CORS跨域问题\n3. 后端服务未启动`, 'error');
            }
        }

        async function testViteProxy() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showResult('正在测试Vite代理...', 'info');
            
            try {
                const params = new URLSearchParams();
                params.append('username', username);
                params.append('password', password);
                
                const response = await fetch('/api/v1/login/access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: params
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Vite代理测试成功！\n\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`Vite代理测试失败！\n\n状态码: ${response.status}\n错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`Vite代理测试出错！\n\n错误信息: ${error.message}\n\n可能的原因:\n1. Vite代理配置问题\n2. 后端服务连接问题`, 'error');
            }
        }

        async function testFrontendAPI() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showResult('正在测试前端API服务...', 'info');
            
            try {
                // 模拟前端API调用逻辑
                const params = new URLSearchParams();
                params.append('username', username);
                params.append('password', password);
                
                const response = await fetch('/api/v1/login/access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: params
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 模拟存储到localStorage
                    const authData = {
                        state: {
                            user: data.user,
                            token: data.access_token,
                            isAuthenticated: true
                        }
                    };
                    localStorage.setItem('auth-storage', JSON.stringify(authData));
                    
                    showResult(`前端API服务测试成功！\n\n响应数据:\n${JSON.stringify(data, null, 2)}\n\n已保存到localStorage`, 'success');
                } else {
                    showResult(`前端API服务测试失败！\n\n状态码: ${response.status}\n错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`前端API服务测试出错！\n\n错误信息: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示当前localStorage状态
        window.onload = function() {
            const authStorage = localStorage.getItem('auth-storage');
            if (authStorage) {
                try {
                    const authData = JSON.parse(authStorage);
                    showResult(`当前localStorage中的认证数据:\n${JSON.stringify(authData, null, 2)}`, 'info');
                } catch (error) {
                    showResult(`localStorage中的认证数据格式错误:\n${authStorage}`, 'error');
                }
            } else {
                showResult('localStorage中没有认证数据', 'info');
            }
        };
    </script>
</body>
</html>
