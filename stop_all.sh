#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "========================================"
echo "   AI Security System - Stop Script"
echo "========================================"
echo

echo "Stopping all services..."
echo

# 停止Python进程（后端服务）
echo -e "${BLUE}[1/3] Stopping backend service...${NC}"
PYTHON_PIDS=$(pgrep -f "python.*start_backend.py\|uvicorn.*app.main:app\|python.*uvicorn")
if [ ! -z "$PYTHON_PIDS" ]; then
    kill -TERM $PYTHON_PIDS 2>/dev/null
    sleep 2
    # 如果还没停止，强制杀死
    PYTHON_PIDS=$(pgrep -f "python.*start_backend.py\|uvicorn.*app.main:app\|python.*uvicorn")
    if [ ! -z "$PYTHON_PIDS" ]; then
        kill -KILL $PYTHON_PIDS 2>/dev/null
    fi
    echo -e "${GREEN}      [OK] Backend service stopped${NC}"
else
    echo -e "${YELLOW}      [-] Backend service not running${NC}"
fi

# 停止Node.js进程（前端服务）
echo -e "${BLUE}[2/3] Stopping frontend service...${NC}"
NODE_PIDS=$(pgrep -f "node.*vite\|npm.*run.*dev")
if [ ! -z "$NODE_PIDS" ]; then
    kill -TERM $NODE_PIDS 2>/dev/null
    sleep 2
    # 如果还没停止，强制杀死
    NODE_PIDS=$(pgrep -f "node.*vite\|npm.*run.*dev")
    if [ ! -z "$NODE_PIDS" ]; then
        kill -KILL $NODE_PIDS 2>/dev/null
    fi
    echo -e "${GREEN}      [OK] Frontend service stopped${NC}"
else
    echo -e "${YELLOW}      [-] Frontend service not running${NC}"
fi

# 清理其他可能的进程
echo -e "${BLUE}[3/3] Cleaning up other processes...${NC}"
# 杀死监听8000、5173、5174和5175端口的进程
BACKEND_PORT_8000_PIDS=$(lsof -ti:8000 2>/dev/null)
FRONTEND_PORT_5173_PIDS=$(lsof -ti:5173 2>/dev/null)
FRONTEND_PORT_5174_PIDS=$(lsof -ti:5174 2>/dev/null)
FRONTEND_PORT_5175_PIDS=$(lsof -ti:5175 2>/dev/null)

if [ ! -z "$BACKEND_PORT_8000_PIDS" ]; then
    kill -TERM $BACKEND_PORT_8000_PIDS 2>/dev/null
    echo -e "${GREEN}      [OK] Processes on port 8000 stopped${NC}"
fi

if [ ! -z "$FRONTEND_PORT_5173_PIDS" ]; then
    kill -TERM $FRONTEND_PORT_5173_PIDS 2>/dev/null
    echo -e "${GREEN}      [OK] Processes on port 5173 stopped${NC}"
fi

if [ ! -z "$FRONTEND_PORT_5174_PIDS" ]; then
    kill -TERM $FRONTEND_PORT_5174_PIDS 2>/dev/null
    echo -e "${GREEN}      [OK] Processes on port 5174 stopped${NC}"
fi

if [ ! -z "$FRONTEND_PORT_5175_PIDS" ]; then
    kill -TERM $FRONTEND_PORT_5175_PIDS 2>/dev/null
    echo -e "${GREEN}      [OK] Processes on port 5175 stopped${NC}"
fi

echo
echo "========================================"
echo "   All services stopped!"
echo "========================================"
echo 