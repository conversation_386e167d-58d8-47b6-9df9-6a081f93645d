#!/usr/bin/env python3
"""
重新激活远程模型
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel
from app.db.models.session import Session

def reactivate_remote_models():
    """重新激活远程模型"""
    print("🔧 重新激活远程模型...")
    
    db = SyncSessionLocal()
    
    try:
        # 1. 查询所有会话使用的模型
        sessions = db.query(Session).filter(
            Session.is_deleted == False
        ).all()
        
        used_model_ids = set()
        for session in sessions:
            if session.llm_model_id:
                used_model_ids.add(session.llm_model_id)
        
        print(f"📊 会话使用的模型ID: {list(used_model_ids)}")
        
        # 2. 激活所有被会话使用的模型
        activated_models = []
        for model_id in used_model_ids:
            model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
            if model:
                if not model.is_active:
                    model.is_active = True
                    activated_models.append(model)
                    print(f"✅ 激活模型: {model.name} (ID: {model.id})")
                else:
                    print(f"ℹ️ 模型已激活: {model.name} (ID: {model.id})")
            else:
                print(f"❌ 模型不存在: ID {model_id}")
        
        # 3. 提交更改
        if activated_models:
            db.commit()
            print(f"✅ 成功激活 {len(activated_models)} 个模型")
        else:
            print("ℹ️ 没有需要激活的模型")
        
        # 4. 验证结果
        print("\n🔍 验证激活状态...")
        active_models = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).all()
        
        print(f"当前激活的模型 ({len(active_models)} 个):")
        for model in active_models:
            print(f"  ✅ {model.name} (ID: {model.id})")
            if model.config_params:
                api_format = model.config_params.get('api_format', 'openai')
                model_name = model.config_params.get('model', 'unknown')
                print(f"     格式: {api_format}, 模型: {model_name}")
        
        # 5. 检查会话模型绑定状态
        print(f"\n🔍 检查会话模型绑定状态...")
        problem_sessions = []
        for session in sessions:
            if session.llm_model_id:
                model = db.query(LLMModel).filter(
                    LLMModel.id == session.llm_model_id,
                    LLMModel.is_active == True
                ).first()
                if not model:
                    problem_sessions.append(session.id)
        
        if problem_sessions:
            print(f"❌ 仍有 {len(problem_sessions)} 个会话的模型绑定有问题")
            print(f"   会话ID: {problem_sessions}")
        else:
            print(f"✅ 所有会话的模型绑定都正常")
        
    except Exception as e:
        print(f"❌ 激活失败: {str(e)}")
        db.rollback()
        
    finally:
        db.close()

if __name__ == "__main__":
    reactivate_remote_models()
