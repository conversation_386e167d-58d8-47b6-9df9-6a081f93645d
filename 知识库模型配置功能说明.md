# 知识库模型配置功能说明

## 功能概述

本功能为AI安全防护系统的知识库模块添加了可配置的大模型支持，允许管理员和用户为不同的场景配置不同的AI模型，以实现更精细化的模型管理和更好的问答效果。

## 核心特性

### 1. 多层级模型配置
- **全局配置**: 系统默认使用的模型配置
- **用户配置**: 用户个人自定义的模型配置
- **分类配置**: 基于数据分类的模型配置
- **分级配置**: 基于数据分级的模型配置

### 2. 智能模型选择
系统按照以下优先级自动选择模型：
1. 用户自定义配置（优先级最高）
2. 数据分级配置
3. 数据分类配置
4. 全局默认配置（优先级最低）

### 3. 细粒度权限控制
- 普通用户：可以创建和管理自己的用户配置
- 管理员：可以创建和管理所有类型的配置
- 基于现有的角色权限系统进行权限控制

### 4. 模型参数配置
每个配置可以设置模型特定的参数：
- `temperature`: 生成温度（控制创造性）
- `max_tokens`: 最大生成token数
- `top_p`: 核心采样参数
- 其他模型支持的参数

## 数据库设计

### 核心表：knowledge_base_model_configs

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键ID |
| config_type | VARCHAR(20) | 配置类型：global/user/category/grade |
| model_id | INTEGER | 关联的LLM模型ID |
| user_id | INTEGER | 用户ID（用户配置时使用） |
| category_id | INTEGER | 分类ID（分类配置时使用） |
| grade_id | INTEGER | 分级ID（分级配置时使用） |
| config_name | VARCHAR(100) | 配置名称 |
| description | TEXT | 配置描述 |
| model_params | JSON | 模型参数 |
| is_active | BOOLEAN | 是否启用 |
| priority | INTEGER | 优先级（数字越小优先级越高） |
| created_by | INTEGER | 创建者ID |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

## API接口

### 1. 获取模型配置列表
```
GET /api/v1/knowledge-base/model-configs
```

**查询参数**：
- `config_type`: 配置类型筛选
- `model_id`: 模型ID筛选
- `is_active`: 是否启用筛选
- `skip`: 跳过数量（分页）
- `limit`: 限制数量（分页）

### 2. 创建模型配置
```
POST /api/v1/knowledge-base/model-configs
```

**请求体**：
```json
{
  "config_type": "user",
  "model_id": 1,
  "config_name": "我的专用配置",
  "description": "用于技术文档问答的配置",
  "model_params": {
    "temperature": 0.3,
    "max_tokens": 800
  },
  "is_active": true,
  "priority": 50
}
```

### 3. 更新模型配置
```
PUT /api/v1/knowledge-base/model-configs/{config_id}
```

### 4. 删除模型配置
```
DELETE /api/v1/knowledge-base/model-configs/{config_id}
```

### 5. 获取当前模型选择
```
GET /api/v1/knowledge-base/model-configs/selection/current
```

**查询参数**：
- `category_id`: 文档分类ID
- `grade_id`: 文档分级ID

**响应**：
```json
{
  "config_id": 1,
  "model_id": 5,
  "model_name": "阿里云通义千问-Plus",
  "model_api_url": "https://api.example.com",
  "model_params": {
    "temperature": 0.7,
    "max_tokens": 1000
  },
  "config_type": "global",
  "priority": 100,
  "selection_reason": "全局默认配置"
}
```

## 前端界面

### 知识库管理页面新增"模型配置"标签页

1. **配置管理**：
   - 模型配置列表展示
   - 创建/编辑/删除配置
   - 配置类型和适用范围显示
   - 权限控制（根据用户角色显示可操作的功能）

2. **配置摘要**：
   - 配置统计信息
   - 使用中的模型列表
   - 最近更新记录

### 配置表单

- **基本信息**：配置名称、类型、关联模型、优先级
- **适用范围**：根据配置类型动态显示相关字段
- **模型参数**：JSON格式的模型参数配置
- **高级选项**：分类/分级选择（仅管理员可见）

## 权限系统

### 权限层级

1. **模块访问权限**：
   - 基于现有的 `knowledge_base` 模块权限
   - 用户必须有知识库访问权限才能查看模型配置

2. **操作权限**：
   - `model_config_read`: 查看模型配置
   - `model_config_create`: 创建模型配置
   - `model_config_update`: 更新模型配置
   - `model_config_delete`: 删除模型配置
   - `model_config_global`: 配置全局模型
   - `model_config_category`: 配置分类模型
   - `model_config_grade`: 配置分级模型

3. **数据权限**：
   - 普通用户只能查看和管理自己创建的配置以及全局配置
   - 管理员可以查看和管理所有配置
   - 配置的可见性基于创建者和配置类型进行过滤

## 使用流程

### 管理员配置流程

1. **设置全局默认配置**：
   - 进入知识库管理 → 模型配置页面
   - 创建全局配置，选择默认使用的模型
   - 设置合适的模型参数

2. **按分类/分级配置**：
   - 为特定的数据分类或分级创建专门的模型配置
   - 例如：技术文档使用精确度高的模型，营销文档使用创造性强的模型

### 用户配置流程

1. **创建个人配置**：
   - 用户可以创建自己的模型配置
   - 选择偏好的模型和参数

2. **配置生效**：
   - 用户进行知识库问答时，系统自动选择最适合的配置
   - 优先使用用户自定义配置

## 配置示例

### 全局默认配置
```json
{
  "config_name": "默认知识库模型",
  "config_type": "global",
  "model_id": 1,
  "model_params": {
    "temperature": 0.7,
    "max_tokens": 1000
  },
  "priority": 100
}
```

### 技术文档专用配置
```json
{
  "config_name": "技术文档配置",
  "config_type": "category",
  "model_id": 2,
  "category_id": 1,
  "model_params": {
    "temperature": 0.3,
    "max_tokens": 1500
  },
  "priority": 50
}
```

### 用户个性化配置
```json
{
  "config_name": "我的问答配置",
  "config_type": "user",
  "model_id": 3,
  "user_id": 123,
  "model_params": {
    "temperature": 0.8,
    "max_tokens": 800
  },
  "priority": 10
}
```

## 注意事项

1. **模型可用性**：确保配置的模型处于激活状态
2. **参数验证**：模型参数必须符合JSON格式，系统会验证常见参数
3. **权限限制**：普通用户无法创建全局、分类、分级配置
4. **配置优先级**：数字越小优先级越高，系统会自动选择最高优先级的适用配置
5. **默认配置**：系统会自动创建一个默认的全局配置，确保始终有可用的模型

## 故障排除

1. **配置不生效**：检查配置是否启用，模型是否激活
2. **权限不足**：确认用户角色具有相应的知识库权限
3. **参数错误**：检查模型参数JSON格式是否正确
4. **模型选择**：查看配置优先级和适用范围设置

## 扩展性

本功能设计时考虑了扩展性：

1. **新增配置类型**：可以轻松添加新的配置类型（如按部门、按项目等）
2. **参数扩展**：支持任意的模型参数配置
3. **权限细化**：可以进一步细化权限控制粒度
4. **统计分析**：可以基于配置使用情况进行统计分析