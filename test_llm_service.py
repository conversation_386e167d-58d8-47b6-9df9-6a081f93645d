#!/usr/bin/env python3
"""
测试LLM服务
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.llm_service import LLMService
from app.db.session import SyncSessionLocal
from app.db.models.llm_model import LLMModel

async def test_llm_service():
    """测试LLM服务"""
    print("🔍 测试LLM服务...")
    
    # 获取数据库会话
    db = SyncSessionLocal()
    
    try:
        # 获取激活的模型
        llm_model = db.query(LLMModel).filter(
            LLMModel.is_active == True
        ).first()
        
        if not llm_model:
            print("❌ 没有找到激活的LLM模型")
            return
        
        print(f"✅ 找到激活模型: {llm_model.name}")
        print(f"   API URL: {llm_model.api_url}")
        print(f"   配置参数: {llm_model.config_params}")
        
        # 测试模型连接
        print("\n🔗 测试模型连接...")
        connection_result = await LLMService.test_model_connection(llm_model)
        
        if connection_result["success"]:
            print("✅ 模型连接成功")
            print(f"   响应: {connection_result['response']}")
        else:
            print("❌ 模型连接失败")
            print(f"   错误: {connection_result['error']}")
            return
        
        # 测试RAG风格的调用
        print("\n🤖 测试RAG风格的LLM调用...")
        
        messages = [
            {"role": "system", "content": "你是一个基于知识库的问答助手。请根据提供的上下文准确回答问题。"},
            {"role": "user", "content": """基于以下知识库内容回答用户的问题。如果知识库中没有相关信息，请明确说明。

知识库内容：
人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。

用户问题：什么是人工智能？

请基于上述知识库内容，准确、详细地回答用户的问题。如果知识库中的信息不足以完全回答问题，请说明哪些部分可以回答，哪些部分缺少信息。
"""}
        ]
        
        # 创建模型副本以避免修改原始配置
        import copy
        test_model = copy.deepcopy(llm_model)
        if test_model.config_params:
            test_model.config_params['max_tokens'] = 200
        else:
            test_model.config_params = {'max_tokens': 200}
        
        try:
            response = await LLMService._call_llm_api(
                llm_model=test_model,
                messages=messages,
                stream=False
            )
            
            print("✅ RAG调用成功")
            print(f"   响应: {response}")
            
        except Exception as e:
            print("❌ RAG调用失败")
            print(f"   错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_llm_service())
