#!/usr/bin/env python3

import asyncio
import logging
from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.security import get_password_hash

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_admin_user():
    """创建管理员用户"""
    async with SessionLocal() as db:
        try:
            # 检查是否已有admin用户
            result = await db.execute(text("SELECT * FROM users WHERE username = 'admin'"))
            admin_user = result.fetchone()
            
            if admin_user:
                logger.info("Admin user already exists")
                return
            
            # 创建admin角色（如果不存在）
            result = await db.execute(text("SELECT * FROM roles WHERE name = 'admin'"))
            admin_role = result.fetchone()
            
            if not admin_role:
                await db.execute(text("""
                    INSERT INTO roles (name, description, created_at, updated_at) 
                    VALUES ('admin', 'Administrator role', datetime('now'), datetime('now'))
                """))
                await db.commit()
                logger.info("Admin role created")
                
                # 获取刚创建的角色ID
                result = await db.execute(text("SELECT id FROM roles WHERE name = 'admin'"))
                admin_role = result.fetchone()
            
            role_id = admin_role[0] if admin_role else 1
            
            # 创建admin用户
            hashed_password = get_password_hash("adminpassword")
            await db.execute(text("""
                INSERT INTO users (username, email, hashed_password, full_name, is_active, role_id, created_at, updated_at) 
                VALUES ('admin', '<EMAIL>', :password, 'Administrator', 1, :role_id, datetime('now'), datetime('now'))
            """), {"password": hashed_password, "role_id": role_id})
            
            await db.commit()
            logger.info("Admin user created with password: adminpassword")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating admin user: {e}")
            raise

async def main():
    logger.info("Initializing database with admin user...")
    await create_admin_user()
    logger.info("Database initialization completed!")

if __name__ == "__main__":
    asyncio.run(main()) 