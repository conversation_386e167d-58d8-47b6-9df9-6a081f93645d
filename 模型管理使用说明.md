# 模型管理使用说明

## 🎯 模型管理的作用

模型管理是AI安全系统的**核心功能**，它负责：

1. **配置和管理多个AI模型**
   - 支持OpenAI、本地部署的开源模型等
   - 统一管理API地址、访问凭证、参数配置

2. **为会话系统提供AI能力**
   - 每个会话必须绑定一个模型
   - 系统通过模型配置调用实际的AI服务
   - 支持不同会话使用不同的模型

3. **安全和权限控制**
   - 只有管理员可以配置模型
   - API密钥加密存储
   - 可以控制模型的激活状态

4. **灵活的模型切换**
   - 可以配置多个模型供选择
   - 支持生产模型和测试模型并存
   - 便于A/B测试和成本控制

## 📝 最新更新

已将 **API 密钥**改为**可选字段**，支持添加无需认证的本地部署模型。

## 🚀 使用方法

### 1. 在前端界面添加模型

1. 登录管理员账号（admin/admin123）
2. 进入"系统管理" → "模型管理"
3. 点击"新建模型"
4. 填写模型信息：
   - **模型名称**：如"本地Llama2"、"ChatGLM-6B"
   - **API地址**：如"http://192.168.1.100:8080/v1/chat/completions"
   - **API密钥**：**可选**，本地部署的模型可以留空
   - **模型标识**：可选，如"llama2-7b"、"chatglm-6b"
   - **Temperature**：可选，控制生成文本的随机性（0-2）
   - **最大Token数**：可选，生成文本的最大长度

### 2. 测试您的大模型服务器

在添加模型之前，建议先测试服务器是否可访问：
```bash
# 方式1：交互式输入
python test_your_llm_server.py

# 方式2：命令行参数
python test_your_llm_server.py http://192.168.1.100:8080/v1/chat/completions
```

### 3. 批量创建本地模型

运行测试脚本创建示例模型：
```bash
python test_create_local_model.py
```

### 3. 模型配置示例

#### 本地部署的模型（无需API密钥）
```json
{
    "name": "本地Llama2",
    "api_url": "http://192.168.1.100:8080/v1/chat/completions",
    "config_params": {
        "temperature": 0.7,
        "max_tokens": 2048,
        "model": "llama2-7b"  // 模型标识（可选）
    },
    "is_active": true
}
```

#### 需要API密钥的模型
```json
{
    "name": "GPT-3.5",
    "api_url": "https://api.openai.com/v1/chat/completions",
    "api_key": "sk-your-api-key",
    "config_params": {
        "temperature": 0.7,
        "max_tokens": 4096,
        "model": "gpt-3.5-turbo"
    },
    "is_active": true
}
```

## ⚠️ 注意事项

1. **API地址**必须以 `http://` 或 `https://` 开头
2. 确保模型服务器地址可以从后端服务器访问
3. 本地部署的模型通常使用内网IP地址
4. API密钥现在是可选的，适合本地部署的开源模型

## 🔧 常见本地模型部署地址格式

- Llama.cpp: `http://localhost:8080/v1/chat/completions`
- FastChat: `http://localhost:21002/v1/chat/completions`
- vLLM: `http://localhost:8000/v1/chat/completions`
- Ollama: `http://localhost:11434/api/generate`

## 🎯 下一步

创建模型配置后，您可以：
1. 在创建会话时选择使用哪个模型
2. 在会话中与选定的模型进行对话
3. 根据需要调整模型的配置参数

## 🧪 集成测试

### 测试完整流程
```bash
# 运行集成测试，演示完整的模型调用流程
python test_llm_integration.py
```

该测试会：
1. 登录系统并获取可用模型
2. 创建绑定模型的会话
3. 发送消息并获取AI响应
4. 展示安全检查结果
5. 查看会话消息历史

### 诊断提示
如果AI响应失败：
- 使用 `test_your_llm_server.py` 测试模型连接
- 检查模型服务器是否运行
- 确认API地址是否正确
- 查看后端日志了解详细错误

## 📊 系统架构中的位置

```
用户 → 会话管理 → 选择模型 → 发送消息
                           ↓
                      模型管理系统
                           ↓
                    调用配置的AI模型
                           ↓
                      返回AI响应
                           ↓
                       安全检查
                           ↓
                      返回给用户
``` 