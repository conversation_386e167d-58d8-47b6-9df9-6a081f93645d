# AI 安全系统 - macOS 启动脚本说明

## 📁 macOS 脚本文件列表

| 文件名 | 功能 | 说明 |
|--------|------|------|
| **start_all.sh** | 一键启动所有服务 | 同时启动后端和前端，推荐使用 |
| **stop_all.sh** | 一键关闭所有服务 | 关闭所有相关进程 |
| **start_backend_only.sh** | 只启动后端 | 用于后端开发调试 |
| **start_frontend_only.sh** | 只启动前端 | 用于前端开发调试 |
| **check_status.sh** | 检查服务状态 | 查看服务是否正在运行 |

## 🚀 快速开始

### 1. 首次使用 - 环境准备
确保已经完成以下准备工作：

```bash
# 检查 Python 版本 (需要 3.8+)
python3 --version

# 检查 Node.js 版本 (需要 16+)
node --version
npm --version

# 创建 Python 虚拟环境
python3 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate

# 安装 Python 依赖
pip install -r requirements.txt

# 安装前端依赖
cd ai-security-frontend
npm install
cd ..

# 初始化数据库
python init_db.py
```

### 2. 启动系统
```bash
# 一键启动所有服务
./start_all.sh
```

### 3. 访问系统
- 前端界面：http://localhost:5173
- 后端API文档：http://localhost:8000/docs

### 4. 测试账号
- 管理员：`admin` / `admin123`
- 普通用户：`user1` / `user123`

### 5. 关闭系统
```bash
# 一键关闭所有服务
./stop_all.sh
```

## 🔧 故障排查

### 检查服务状态
```bash
./check_status.sh
```

### 常见问题

#### 1. 权限问题
```bash
# 如果脚本无法执行，设置执行权限
chmod +x *.sh
```

#### 2. 端口被占用
```bash
# 查看端口占用情况
lsof -i :8000  # 后端端口
lsof -i :5173  # 前端端口

# 强制杀死占用端口的进程
sudo kill -9 $(lsof -ti:8000)
sudo kill -9 $(lsof -ti:5173)
```

#### 3. Python 虚拟环境问题
```bash
# 重新创建虚拟环境
rm -rf .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

#### 4. Node.js 依赖问题
```bash
# 清理和重新安装
cd ai-security-frontend
rm -rf node_modules package-lock.json
npm install
cd ..
```

#### 5. 数据库未初始化
```bash
# 重新初始化数据库
python init_db.py
```

## 📝 开发说明

### 单独启动服务
```bash
# 仅启动后端（前台运行，适合调试）
./start_backend_only.sh

# 仅启动前端（前台运行，适合调试）
./start_frontend_only.sh
```

### 手动启动（调试用）
```bash
# 手动启动后端
source .venv/bin/activate
python start_backend.py

# 手动启动前端（新终端窗口）
cd ai-security-frontend
npm run dev
```

### 查看日志
- 后端日志：直接在启动后端的终端窗口查看
- 前端日志：在浏览器开发者工具的控制台查看

## ⚠️ 注意事项

1. **Terminal.app 权限**：macOS 可能需要授权 Terminal.app 控制其他应用
2. **防火墙设置**：首次运行可能需要允许 Python 和 Node.js 访问网络
3. **后台运行**：`start_all.sh` 会在新的终端窗口中启动服务，保持这些窗口开启
4. **环境隔离**：建议在 Python 虚拟环境中运行，避免依赖冲突

## 🛡️ 安全提示

- 这是开发环境配置，不要在生产环境使用
- 测试账号仅供开发使用
- 生产环境需要配置适当的安全措施
- 定期更新依赖包以修复安全漏洞

## 🔄 与 Windows 版本的区别

| 功能 | Windows (.bat) | macOS (.sh) |
|------|----------------|-------------|
| 启动方式 | cmd 窗口 | Terminal 窗口 |
| 进程管理 | taskkill | kill/pkill |
| 端口检查 | PowerShell | nc/lsof |
| 虚拟环境 | .venv\Scripts\activate | source .venv/bin/activate |
| 颜色输出 | 有限支持 | 完整ANSI颜色支持 | 