#!/usr/bin/env python3
"""
测试前端RAG查询功能
模拟前端发送的请求格式
"""

import requests
import json

def test_frontend_rag():
    """测试前端RAG查询"""
    print("🔍 测试前端RAG查询功能")
    print("=" * 60)
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_response = requests.post(
        "http://localhost:8000/api/v1/login/access-token",
        data={"username": "admin", "password": "admin123"}  # 使用form data而不是JSON
    )
    
    if login_response.status_code != 200:
        print(f"   ❌ 登录失败: {login_response.status_code}")
        print(f"   响应: {login_response.text}")
        return
    
    token = login_response.json()["access_token"]
    print("   ✅ 登录成功")
    
    # 2. 模拟前端RAG查询
    print("\n2. 测试本地模型路径查询...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 使用与前端完全相同的参数
    query_data = {
        "query": "本地模型路径是什么？",
        "model_id": 13,
        "top_k": 10,  # 修改后的值
        "temperature": 0.7,
        "max_tokens": 800,
        "include_sources": True
    }
    
    print(f"   查询参数: {json.dumps(query_data, ensure_ascii=False, indent=2)}")
    
    response = requests.post(
        "http://localhost:8000/api/v1/rag/query",
        headers=headers,
        json=query_data
    )
    
    print(f"   响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ 查询成功")
        print(f"   success: {data.get('success')}")
        print(f"   context_used: {data.get('context_used')}")
        print(f"   documents_retrieved: {data.get('documents_retrieved')}")
        print(f"   sources数量: {len(data.get('sources', []))}")
        
        # 检查文档2的sources
        doc2_sources = [s for s in data.get('sources', []) if s.get('document_id') == 2]
        print(f"   文档2 sources数量: {len(doc2_sources)}")
        
        if doc2_sources:
            print("   ✅ 找到文档2的sources")
            for i, source in enumerate(doc2_sources[:3], 1):
                print(f"     {i}. 相似度: {source.get('similarity', 0):.4f}")
                content = source.get('content', '')[:100] + "..." if len(source.get('content', '')) > 100 else source.get('content', '')
                print(f"        内容: {content}")
        else:
            print("   ❌ 未找到文档2的sources")
        
        # 检查答案
        answer = data.get('answer', '')
        print(f"\n   💬 答案长度: {len(answer)} 字符")
        if '模型路径' in answer or 'CLIP' in answer or '/root/pic_detect' in answer:
            print("   ✅ 答案包含模型路径相关信息")
            print(f"   答案预览: {answer[:200]}...")
        else:
            print("   ❌ 答案不包含模型路径相关信息")
            print(f"   完整答案: {answer}")
    else:
        print(f"   ❌ 查询失败: {response.status_code}")
        print(f"   错误信息: {response.text}")

if __name__ == "__main__":
    test_frontend_rag()
