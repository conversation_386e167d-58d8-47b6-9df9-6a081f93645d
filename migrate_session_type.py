#!/usr/bin/env python3
"""
为sessions表添加session_type字段的迁移脚本
"""
import sqlite3
import os

def migrate_session_type():
    """执行session_type字段迁移"""
    
    # 数据库文件路径
    db_path = "./ai_security.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔗 已连接到数据库")
        
        # 检查session_type字段是否已存在
        cursor.execute("PRAGMA table_info(sessions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'session_type' in columns:
            print("✅ session_type字段已存在，跳过迁移")
            return
        
        print("📝 开始添加session_type字段...")
        
        # 添加session_type字段
        cursor.execute("""
            ALTER TABLE sessions 
            ADD COLUMN session_type VARCHAR(50) DEFAULT 'chat'
        """)
        
        print("✅ session_type字段添加成功")
        
        # 更新现有会话的类型
        cursor.execute("""
            UPDATE sessions 
            SET session_type = 'rag' 
            WHERE title LIKE '%知识库%' OR title LIKE '%RAG%' OR title LIKE '%问答%'
        """)
        
        updated_rows = cursor.rowcount
        print(f"✅ 更新了 {updated_rows} 个现有会话的类型")
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_sessions_type_user 
            ON sessions(session_type, user_id, is_deleted)
        """)
        
        print("✅ 索引创建成功")
        
        # 验证迁移结果
        cursor.execute("""
            SELECT 
                session_type,
                COUNT(*) as count,
                MIN(created_at) as earliest,
                MAX(created_at) as latest
            FROM sessions 
            WHERE is_deleted = 0
            GROUP BY session_type
        """)
        
        results = cursor.fetchall()
        print("\n📊 迁移结果验证:")
        for row in results:
            session_type, count, earliest, latest = row
            print(f"  - {session_type}: {count} 个会话 (最早: {earliest}, 最新: {latest})")
        
        # 提交更改
        conn.commit()
        print("\n🎉 迁移完成！")
        
    except sqlite3.Error as e:
        print(f"❌ 数据库错误: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
    finally:
        if conn:
            conn.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    migrate_session_type()
