#!/usr/bin/env python3
"""
测试知识库文档列表API
"""

import requests
import json

def test_document_list_api():
    """测试文档列表API的返回数量"""
    print("=== 测试知识库文档列表API ===\n")
    
    # API基础URL
    base_url = "http://localhost:8000"
    
    # 首先尝试登录获取token（使用admin账户）
    login_data = {
        "username": "admin", 
        "password": "admin123"
    }
    
    try:
        # 登录
        login_response = requests.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print(f"✅ 登录成功，获取到token")
        else:
            print(f"❌ 登录失败: {login_response.status_code}, {login_response.text}")
            return
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # 测试1: 默认限制（应该返回10个文档）
        print("\n1. 测试默认限制:")
        response1 = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents",
            headers=headers
        )
        
        if response1.status_code == 200:
            data1 = response1.json()
            print(f"   默认查询返回: {data1['total']} 总数, {len(data1['items'])} 实际返回")
            print(f"   文档列表:")
            for i, doc in enumerate(data1['items'], 1):
                print(f"     {i}. {doc['title']} (状态: {doc['status']})")
        else:
            print(f"   ❌ 默认查询失败: {response1.status_code}, {response1.text}")
        
        # 测试2: 指定更大的限制
        print("\n2. 测试指定limit=20:")
        response2 = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?limit=20",
            headers=headers
        )
        
        if response2.status_code == 200:
            data2 = response2.json()
            print(f"   limit=20查询返回: {data2['total']} 总数, {len(data2['items'])} 实际返回")
        else:
            print(f"   ❌ limit=20查询失败: {response2.status_code}, {response2.text}")
        
        # 测试3: 指定最大限制
        print("\n3. 测试指定limit=100:")
        response3 = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?limit=100",
            headers=headers
        )
        
        if response3.status_code == 200:
            data3 = response3.json()
            print(f"   limit=100查询返回: {data3['total']} 总数, {len(data3['items'])} 实际返回")
        else:
            print(f"   ❌ limit=100查询失败: {response3.status_code}, {response3.text}")
        
        # 测试4: 测试状态过滤
        print("\n4. 测试状态过滤 (只查询COMPLETED状态):")
        response4 = requests.get(
            f"{base_url}/api/v1/knowledge-base/documents?status=COMPLETED&limit=50",
            headers=headers
        )
        
        if response4.status_code == 200:
            data4 = response4.json()
            print(f"   COMPLETED状态查询返回: {data4['total']} 总数, {len(data4['items'])} 实际返回")
        else:
            print(f"   ❌ 状态过滤查询失败: {response4.status_code}, {response4.text}")
        
        # 测试5: 检查权限配置
        print("\n5. 检查当前用户权限:")
        response5 = requests.get(
            f"{base_url}/api/v1/knowledge-base/permissions",
            headers=headers
        )
        
        if response5.status_code == 200:
            permissions = response5.json()
            print(f"   权限检查成功:")
            print(f"     模块访问权限: {permissions.get('module_access', 'N/A')}")
            print(f"     可访问分类: {permissions.get('accessible_categories', [])}")
            print(f"     可访问分级: {permissions.get('accessible_grades', [])}")
        else:
            print(f"   ❌ 权限检查失败: {response5.status_code}, {response5.text}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

def test_direct_database_query():
    """直接查询数据库验证数据"""
    print("\n=== 直接数据库查询验证 ===\n")
    
    import sqlite3
    
    try:
        conn = sqlite3.connect('ai_security.db')
        cursor = conn.cursor()
        
        # 查询所有文档
        cursor.execute("SELECT COUNT(*) FROM knowledge_documents")
        total_count = cursor.fetchone()[0]
        print(f"数据库中总文档数: {total_count}")
        
        # 按状态分组统计
        cursor.execute("""
            SELECT status, COUNT(*) 
            FROM knowledge_documents 
            GROUP BY status
        """)
        status_stats = cursor.fetchall()
        print("按状态统计:")
        for status, count in status_stats:
            print(f"  {status}: {count}个")
        
        # 查询所有文档详情
        cursor.execute("""
            SELECT id, title, status, uploaded_by, category_id, grade_id, created_at
            FROM knowledge_documents 
            ORDER BY created_at DESC
        """)
        docs = cursor.fetchall()
        print(f"\n所有文档详情:")
        for doc in docs:
            print(f"  ID:{doc[0]} | {doc[1]} | 状态:{doc[2]} | 上传者:{doc[3]} | 分类:{doc[4]} | 分级:{doc[5]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {str(e)}")

if __name__ == "__main__":
    print("🔍 开始测试知识库文档列表问题\n")
    
    # 1. 直接查询数据库
    test_direct_database_query()
    
    # 2. 测试API
    test_document_list_api()
    
    print("\n🎯 总结分析:")
    print("如果API返回的文档数量少于数据库中的实际数量，可能的原因包括:")
    print("1. 前端请求时的limit参数设置过小")
    print("2. 权限过滤导致部分文档不可见")
    print("3. 状态过滤（比如只显示COMPLETED状态的文档）")
    print("4. 分类/分级权限限制")
    print("5. 前端分页逻辑问题")