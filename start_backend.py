#!/usr/bin/env python3
"""
启动后端服务
"""
import uvicorn
import sys
import os

# 确保项目目录在 Python 路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动 AI 安全系统后端服务...")
    print("=" * 60)
    print("📌 服务地址: http://localhost:8000")
    print("📚 API 文档: http://localhost:8000/docs")
    print("🔄 已启用 CORS，允许前端访问")
    print("=" * 60)
    
    # 使用字符串方式导入 app，这样才能启用 reload 功能
    uvicorn.run(
        "app.main:app",  # 字符串方式导入
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )