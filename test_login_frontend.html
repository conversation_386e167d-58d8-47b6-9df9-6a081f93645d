<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI安全系统 - 登录测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .quick-login {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .quick-login button {
            flex: 1;
            padding: 8px;
            font-size: 14px;
        }
        .user-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .user-info h3 {
            margin-top: 0;
            color: #495057;
        }
        .user-info p {
            margin: 5px 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 AI安全系统登录测试</h1>
        
        <div class="quick-login">
            <button onclick="quickLogin('admin', 'admin123')">管理员登录</button>
            <button onclick="quickLogin('user1', 'user123')">普通用户登录</button>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <button onclick="testCurrentUser()" id="testBtn" disabled>测试获取当前用户信息</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let currentToken = null;

        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            login();
        }

        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function showUserInfo(userData) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result success';
            
            const userInfoHtml = `
✅ 登录成功！

用户信息:
- ID: ${userData.id}
- 用户名: ${userData.username}
- 邮箱: ${userData.email || '未设置'}
- 角色ID: ${userData.role_id}
- 级别ID: ${userData.level_id || '未设置'}
- 激活状态: ${userData.is_active ? '是' : '否'}
- 超级用户: ${userData.is_superuser ? '是' : '否'}
- 创建时间: ${userData.created_at ? new Date(userData.created_at).toLocaleString() : '未知'}

访问令牌已保存，可以测试受保护的端点。
            `;
            
            resultDiv.textContent = userInfoHtml;
            
            // 启用测试按钮
            document.getElementById('testBtn').disabled = false;
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }

            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            try {
                showResult('正在登录...', 'info');

                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch(`${API_BASE}/login/access-token`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    currentToken = data.access_token;
                    showUserInfo(data.user);
                } else {
                    showResult(`❌ 登录失败: ${data.detail || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        async function testCurrentUser() {
            if (!currentToken) {
                showResult('❌ 请先登录获取访问令牌', 'error');
                return;
            }

            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';

            try {
                showResult('正在测试受保护端点...', 'info');

                const response = await fetch(`${API_BASE}/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const resultText = `
✅ 受保护端点测试成功！

从 /auth/me 获取的用户信息:
- ID: ${data.id}
- 用户名: ${data.username}
- 邮箱: ${data.email || '未设置'}
- 角色ID: ${data.role_id}
- 级别ID: ${data.level_id || '未设置'}
- 激活状态: ${data.is_active ? '是' : '否'}

JWT令牌验证正常，可以正常访问受保护的API端点。
                    `;
                    showResult(resultText, 'success');
                } else {
                    showResult(`❌ 受保护端点测试失败: ${data.detail || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 测试失败: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '测试获取当前用户信息';
            }
        }

        function clearResults() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }

        // 表单提交事件
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            login();
        });

        // 页面加载时检查服务器状态
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ 服务器运行正常 (版本: ${data.version})`, 'success');
                } else {
                    showResult('⚠️ 服务器响应异常', 'error');
                }
            } catch (error) {
                showResult('❌ 无法连接到服务器，请确保后端服务正在运行 (python3 simple_start.py)', 'error');
            }
        });
    </script>
</body>
</html>