#!/usr/bin/env python3
"""
测试中文语义搜索功能
验证不同的嵌入模型对中文的支持效果
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.semantic_embedding import SemanticEmbeddingService
from app.core.embedding_config import get_recommended_chinese_models, EMBEDDING_MODELS


def test_chinese_semantic_search():
    """测试中文语义搜索"""
    
    # 测试文档
    documents = [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子领域，它使计算机能够从数据中学习而无需明确编程。",
        "深度学习是机器学习的一种方法，使用多层神经网络来学习数据的复杂模式。",
        "自然语言处理使计算机能够理解、解释和生成人类语言。",
        "计算机视觉是人工智能领域，专注于使计算机能够识别和处理图像和视频。",
        "今天天气真好，适合出去散步。",
        "我喜欢吃中国菜，尤其是四川菜。",
        "Python是一种流行的编程语言，广泛用于数据科学和机器学习。"
    ]
    
    # 测试查询
    queries = [
        "什么是AI？",
        "深度神经网络",
        "如何让电脑理解中文？",
        "图像识别技术",
        "今天的天气怎么样？",
        "编程语言"
    ]
    
    print("=" * 80)
    print("中文语义搜索测试")
    print("=" * 80)
    
    # 获取推荐的中文模型
    chinese_models = get_recommended_chinese_models()
    print(f"\n推荐的中文模型：")
    for key, config in chinese_models.items():
        print(f"- {key}: {config['description']}")
    
    # 测试默认模型
    print("\n" + "=" * 80)
    print("使用已下载的模型进行测试")
    print("=" * 80)
    
    try:
        # 创建语义嵌入服务（使用已下载的模型）
        service = SemanticEmbeddingService(model_name="intfloat/multilingual-e5-small")
        print(f"\n模型信息：")
        model_info = service.get_model_info()
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
        # 对每个查询进行搜索
        for query in queries:
            print(f"\n查询: '{query}'")
            print("-" * 40)
            
            # 执行语义搜索
            results = service.semantic_search(
                query=query,
                documents=documents,
                top_k=3,
                threshold=0.3
            )
            
            # 显示结果
            for i, result in enumerate(results, 1):
                print(f"{i}. 相似度: {result['similarity']:.4f}")
                print(f"   文档: {result['document']}")
            
            if not results:
                print("   没有找到相关文档（相似度低于阈值）")
    
    except Exception as e:
        print(f"\n错误: {str(e)}")
        print("\n可能需要安装模型，请稍等...")
    
    # 测试模型对比
    print("\n" + "=" * 80)
    print("测试不同模型的效果对比")
    print("=" * 80)
    
    test_query = "深度学习和神经网络的关系"
    test_models = ["multilingual-e5-small"]  # 只测试已下载的模型
    
    for model_key in test_models:
        if model_key in EMBEDDING_MODELS:
            print(f"\n测试模型: {model_key}")
            print(f"描述: {EMBEDDING_MODELS[model_key]['description']}")
            print("-" * 40)
            
            try:
                # 创建服务实例
                model_config = EMBEDDING_MODELS[model_key]
                service = SemanticEmbeddingService(model_name=model_config["name"])
                
                # 执行搜索
                results = service.semantic_search(
                    query=test_query,
                    documents=documents,
                    top_k=3,
                    threshold=0.3
                )
                
                # 显示结果
                for i, result in enumerate(results, 1):
                    print(f"{i}. 相似度: {result['similarity']:.4f} - {result['document'][:50]}...")
            
            except Exception as e:
                print(f"错误: {str(e)}")


if __name__ == "__main__":
    test_chinese_semantic_search() 